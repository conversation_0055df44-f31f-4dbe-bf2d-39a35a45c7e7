'use server'

import prisma from '@/app/libs/prismadb'
import { Feedback, FeedbackType } from '@prisma/client'
import { auth } from '@/auth'
import { NextResponse } from 'next/server'

export type fetchedFeedbacksType = Pick<Feedback, 'conversation_id' | 'data'>

export const fetchAllFeedbacks = async (
  conversationId: string
): Promise<fetchedFeedbacksType[]> => {
  const feedbacks = await prisma.feedback.findMany({
    where: {
      conversation_id: conversationId,
    },
    select: {
      conversation_id: true,
      data: true,
    },
  })
  console.log('fetched feedbacks from db using server-actions.ts')
  return feedbacks
}

export type createFeedbackType = {
  conversationId: string
  outputObject: Record<string, any>
  canContact: boolean
}

export const createFeedback = async ({
  conversationId,
  outputObject,
  canContact,
}: createFeedbackType) => {
  const session = await auth()
  const userId = session?.user?.id
  if (!userId) {
    return new NextResponse('Unauthorized', { status: 401 })
  }
  const newFeedback = await prisma.feedback.create({
    data: {
      creator_id: userId,
      conversation_id: conversationId,
      type: FeedbackType.ISSUETREE,
      can_contact: canContact,
      data: outputObject,
    },
  })
  return newFeedback
}
