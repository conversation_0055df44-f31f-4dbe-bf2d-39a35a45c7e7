'use client'

import { B<PERSON>bleMenu, BubbleMenuProps, Editor } from '@tiptap/react'
import { FC, useState } from 'react'
import {
  Trash2,
  Columns,
  <PERSON>s,
  ArrowUpFromLine,
  ArrowDownToLine,
  ArrowLeftToLine,
  ArrowRightFromLine,
} from 'lucide-react'
import { cn } from '@/lib/utils'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'

export interface BubbleMenuItem {
  name: string
  isActive: () => boolean
  command: () => void
  icon: React.ElementType
  color?: string
}

type TableBubbleMenuProps = Omit<BubbleMenuProps, 'children'>

const TableBubbleMenu: FC<TableBubbleMenuProps> = props => {
  // Hooks must be called unconditionally at the top of the component
  const [confirmOpen, setConfirmOpen] = useState(false)
  const [pendingDelete, setPendingDelete] = useState<
    'row' | 'column' | 'table' | null
  >(null)

  if (!props.editor) {
    return null
  }
  const editor = props.editor as Editor

  const requestDelete = (target: 'row' | 'column' | 'table') => {
    setPendingDelete(target)
    setConfirmOpen(true)
  }

  const executePendingDelete = () => {
    if (!pendingDelete) return
    const chain = editor.chain().focus()
    if (pendingDelete === 'row') chain.deleteRow().run()
    if (pendingDelete === 'column') chain.deleteColumn().run()
    if (pendingDelete === 'table') chain.deleteTable().run()
    setConfirmOpen(false)
    setPendingDelete(null)
  }

  const items: BubbleMenuItem[] = [
    {
      name: 'Insert Row Above',
      command: () => editor.chain().focus().addRowBefore().run(),
      isActive: () => false,
      icon: ArrowUpFromLine,
      color: 'text-blue-500',
    },
    {
      name: 'Insert Row Below',
      command: () => editor.chain().focus().addRowAfter().run(),
      isActive: () => false,
      icon: ArrowDownToLine,
      color: 'text-blue-500',
    },
    {
      name: 'Delete Row',
      command: () => requestDelete('row'),
      isActive: () => false,
      icon: Rows,
      color: 'text-red-500',
    },
    {
      name: 'Insert Column Before',
      command: () => editor.chain().focus().addColumnBefore().run(),
      isActive: () => false,
      icon: ArrowLeftToLine,
      color: 'text-blue-500',
    },
    {
      name: 'Insert Column After',
      command: () => editor.chain().focus().addColumnAfter().run(),
      isActive: () => false,
      icon: ArrowRightFromLine,
      color: 'text-blue-500',
    },
    {
      name: 'Delete Column',
      command: () => requestDelete('column'),
      isActive: () => false,
      icon: Columns,
      color: 'text-red-500',
    },
    {
      name: 'Delete Table',
      command: () => requestDelete('table'),
      isActive: () => false,
      icon: Trash2,
      color: 'text-red-500',
    },
  ]

  const bubbleMenuProps: Omit<BubbleMenuProps, 'children'> = {
    ...props,
    shouldShow: ({ editor }) => editor.isActive('table'),
    tippyOptions: {
      placement: 'bottom-start',
      moveTransition: 'transform 0.15s ease-out',
      appendTo: 'parent',
    },
  }

  return (
    <BubbleMenu
      {...bubbleMenuProps}
      className="flex w-fit divide-x divide-stone-200 rounded-md border border-stone-200 bg-white/90 backdrop-blur-md shadow-xl"
    >
      <div className="flex">
        {items.map((item, index) => (
          <button
            key={index}
            onClick={item.command}
            className="p-2 text-stone-600 hover:bg-stone-100/80 active:bg-stone-200/80"
            title={item.name}
          >
            <item.icon
              className={cn('h-4 w-4', item.color, {
                'text-blue-500': item.isActive(),
              })}
            />
          </button>
        ))}
      </div>
      {/* Confirm destructive action */}
      <AlertDialog open={confirmOpen} onOpenChange={setConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm delete?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. It will permanently delete the
              {pendingDelete === 'row' && ' selected row.'}
              {pendingDelete === 'column' && ' selected column.'}
              {pendingDelete === 'table' && ' entire table.'}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setPendingDelete(null)}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={executePendingDelete}>
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </BubbleMenu>
  )
}

export default TableBubbleMenu
