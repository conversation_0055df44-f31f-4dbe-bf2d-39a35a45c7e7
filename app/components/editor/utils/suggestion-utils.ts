import { Editor } from '@tiptap/core'
import { Node as ProseMirrorNode } from 'prosemirror-model'

export type SuggestionData = {
  oldText: string
  newText: string
  id?: string
}

export type SuggestionBatch = SuggestionData[]

export type TextOccurrence = {
  pos: number
  node: ProseMirrorNode
}

export type SuggestionPosition = {
  pos: number
  node: ProseMirrorNode
}

/**
 * Generate a unique ID for a suggestion
 */
export const generateSuggestionId = (): string => {
  return `suggestion-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Create a single suggestion node in the editor
 */
export const createSuggestion = (
  editor: Editor,
  suggestion: SuggestionData,
  position?: number
): boolean => {
  try {
    const suggestionId = suggestion.id || generateSuggestionId()

    const suggestionNode = editor.schema.nodes.suggestion.create({
      oldText: suggestion.oldText,
      newText: suggestion.newText,
      id: suggestionId,
    })

    if (position !== undefined) {
      // Insert at specific position
      editor.chain().insertContentAt(position, suggestionNode).run()
    } else {
      // Insert at current selection
      editor.chain().insertContent(suggestionNode).run()
    }

    return true
  } catch (error) {
    console.error('Error creating suggestion:', error)
    return false
  }
}

/**
 * Find all occurrences of text in the document
 */
const findAllTextOccurrences = (
  editor: Editor,
  searchText: string
): TextOccurrence[] => {
  const occurrences: TextOccurrence[] = []
  const { doc } = editor.state

  doc.descendants((node, pos) => {
    if (node.isText && node.text && node.text.includes(searchText)) {
      const text = node.text
      let startIndex = 0

      // Find all occurrences within this text node
      while (true) {
        const foundIndex = text.indexOf(searchText, startIndex)
        if (foundIndex === -1) break

        occurrences.push({
          pos: pos + foundIndex,
          node: node,
        })

        startIndex = foundIndex + 1 // Move past this occurrence
      }
    }
  })

  return occurrences
}

/**
 * Find and replace text with suggestions
 * This handles multiple occurrences of the same text
 */
export const createSuggestionsFromText = (
  editor: Editor,
  suggestions: SuggestionBatch
): boolean => {
  try {
    editor.chain().focus().run()

    // Process each suggestion
    suggestions.forEach(suggestion => {
      const { oldText, newText } = suggestion

      // Find all occurrences of the old text
      const occurrences = findAllTextOccurrences(editor, oldText)

      if (occurrences.length === 0) {
        if (process.env.NODE_ENV === 'development') {
          console.warn(`Could not find text to replace: "${oldText}"`)
        }
        return
      }

      // Process occurrences in reverse order to maintain correct positions
      occurrences.reverse().forEach(occurrence => {
        const startPos = occurrence.pos
        const endPos = startPos + oldText.length

        // Create suggestion node with unique ID
        const suggestionNode = editor.schema.nodes.suggestion.create({
          oldText,
          newText,
          id: generateSuggestionId(),
        })

        // Replace the text with the suggestion node
        editor
          .chain()
          .insertContentAt({ from: startPos, to: endPos }, suggestionNode)
          .run()
      })

      if (process.env.NODE_ENV === 'development') {
        console.log(
          `Created ${occurrences.length} suggestion(s) for "${oldText}"`
        )
      }
    })

    return true
  } catch (error) {
    console.error('Error creating suggestions from text:', error)
    return false
  }
}

/**
 * Get all suggestions in the document
 */
export const getAllSuggestions = (editor: Editor): SuggestionData[] => {
  const suggestions: SuggestionData[] = []

  editor.state.doc.descendants(node => {
    if (node.type.name === 'suggestion') {
      suggestions.push({
        oldText: node.attrs.oldText,
        newText: node.attrs.newText,
        id: node.attrs.id,
      })
    }
  })

  return suggestions
}

/**
 * Accept all suggestions in the document
 * Fixed: Collect positions first, then process in reverse order
 */
export const acceptAllSuggestions = (editor: Editor): void => {
  const suggestionPositions: SuggestionPosition[] = []

  // First pass: collect all suggestion positions
  editor.state.doc.descendants((node, pos) => {
    if (node.type.name === 'suggestion') {
      suggestionPositions.push({ pos, node })
    }
  })

  // Process in reverse order to maintain correct positions
  suggestionPositions.reverse().forEach(({ pos, node }) => {
    const textNode = editor.schema.text(node.attrs.newText)
    editor
      .chain()
      .insertContentAt({ from: pos, to: pos + node.nodeSize }, textNode)
      .run()
  })

  if (process.env.NODE_ENV === 'development') {
    console.log(`Accepted ${suggestionPositions.length} suggestion(s)`)
  }
}

/**
 * Reject all suggestions in the document
 * Fixed: Collect positions first, then process in reverse order
 */
export const rejectAllSuggestions = (editor: Editor): void => {
  const suggestionPositions: SuggestionPosition[] = []

  // First pass: collect all suggestion positions
  editor.state.doc.descendants((node, pos) => {
    if (node.type.name === 'suggestion') {
      suggestionPositions.push({ pos, node })
    }
  })

  // Process in reverse order to maintain correct positions
  suggestionPositions.reverse().forEach(({ pos, node }) => {
    const textNode = editor.schema.text(node.attrs.oldText)
    editor
      .chain()
      .insertContentAt({ from: pos, to: pos + node.nodeSize }, textNode)
      .run()
  })

  if (process.env.NODE_ENV === 'development') {
    console.log(`Rejected ${suggestionPositions.length} suggestion(s)`)
  }
}

/**
 * Remove all suggestions (keeping original text)
 */
export const clearAllSuggestions = (editor: Editor): void => {
  rejectAllSuggestions(editor)
}

/**
 * Demo function to create sample suggestions
 */
export const createDemoSuggestions = (editor: Editor): void => {
  const demoSuggestions: SuggestionBatch = [
    {
      oldText: 'Hello World',
      newText: 'Hello Universe',
      id: 'demo-1',
    },
    {
      oldText: 'simple example',
      newText: 'comprehensive example',
      id: 'demo-2',
    },
    {
      oldText: 'basic functionality',
      newText: 'advanced functionality',
      id: 'demo-3',
    },
  ]

  createSuggestionsFromText(editor, demoSuggestions)
}

/**
 * Example API integration function
 * This shows how you might integrate with an API that returns suggested changes
 */
export const applySuggestionsFromAPI = async (
  editor: Editor,
  documentContent: string,
  apiEndpoint: string
): Promise<boolean> => {
  try {
    // Example API call - replace with your actual API
    const response = await fetch(apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        content: documentContent,
        // Add other parameters as needed
      }),
    })

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status}`)
    }

    const apiResult = await response.json()

    // Assuming API returns an array of suggestions in this format:
    // { suggestions: [{ oldText: string, newText: string }] }
    const suggestions: SuggestionBatch = apiResult.suggestions || []

    if (suggestions.length === 0) {
      if (process.env.NODE_ENV === 'development') {
        console.log('No suggestions returned from API')
      }
      return false
    }

    // Apply the suggestions
    return createSuggestionsFromText(editor, suggestions)
  } catch (error) {
    console.error('Error applying suggestions from API:', error)
    return false
  }
}
