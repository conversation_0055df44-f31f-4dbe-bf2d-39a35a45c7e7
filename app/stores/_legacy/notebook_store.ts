import { create } from 'zustand'

export type FetchedNotebook = {
  id: string
  title: string
  status: string
  updated_at: Date
}

type NotebookState = {
  notebooks: FetchedNotebook[]
  setNotebooks: (notebooks: FetchedNotebook[]) => void
  showFeedbackCollection: boolean
  setShowFeedbackCollection: (showFeedbackCollection: boolean) => void
  showDialog: boolean
  setShowDialog: (showDialog: boolean) => void
  reset: () => void
}

const useNotebookStore = create<NotebookState>(set => ({
  notebooks: [],
  setNotebooks: (notebooks: FetchedNotebook[]) => set({ notebooks: notebooks }),
  reset: () => set({ notebooks: [] }),
  showFeedbackCollection: false,
  setShowFeedbackCollection: (showFeedbackCollection: boolean) =>
    set({ showFeedbackCollection }),
  showDialog: false,
  setShowDialog: (showDialog: boolean) => set({ showDialog }),
}))

export default useNotebookStore
