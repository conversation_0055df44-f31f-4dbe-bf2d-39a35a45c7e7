-- CreateEnum
CREATE TYPE "SubtreeStatus" AS ENUM ('ACTIVE', 'MERGED');

-- AlterEnum
ALTER TYPE "OpenAIUsageType" ADD VALUE 'SUBTREE_GENERATE_QUESTIONS';

-- CreateTable
CREATE TABLE "subtrees" (
    "id" TEXT NOT NULL,
    "creator_id" TEXT NOT NULL,
    "conversation_id" TEXT NOT NULL,
    "issue_tree_id" TEXT NOT NULL,
    "nodes" TEXT DEFAULT '',
    "edges" TEXT DEFAULT '',
    "original_nodes" TEXT DEFAULT '',
    "original_edges" TEXT DEFAULT '',
    "prompt" TEXT DEFAULT '',
    "generation_output" TEXT DEFAULT '',
    "status" "SubtreeStatus" NOT NULL DEFAULT 'ACTIVE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "subtrees_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "subtrees_id_idx" ON "subtrees" USING HASH ("id");

-- CreateIndex
CREATE INDEX "subtrees_creator_id_idx" ON "subtrees" USING HASH ("creator_id");

-- CreateIndex
CREATE INDEX "subtrees_conversation_id_idx" ON "subtrees" USING HASH ("conversation_id");

-- CreateIndex
CREATE INDEX "subtrees_issue_tree_id_idx" ON "subtrees" USING HASH ("issue_tree_id");

-- CreateIndex
CREATE INDEX "subtrees_created_at_idx" ON "subtrees"("created_at");

-- AddForeignKey
ALTER TABLE "subtrees" ADD CONSTRAINT "subtrees_issue_tree_id_fkey" FOREIGN KEY ("issue_tree_id") REFERENCES "issue_trees"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "subtrees" ADD CONSTRAINT "subtrees_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "conversations"("id") ON DELETE CASCADE ON UPDATE CASCADE;
