import { auth } from '@/auth-edge'

export default auth(req => {
  // If user is not authenticated, redirect to sign-in page
  if (!req.auth) {
    const newUrl = new URL('/', req.nextUrl.origin)
    return Response.redirect(newUrl)
  }
})

export const config = {
  matcher: [
    '/conversations/:path*',
    '/users/:path*',
    '/feedback/:path*',
    '/subscription/:path*',
    '/dragTree/:path*',
    '/screening/:path*',
  ],
}
