// Workaround, ref: https://tiptap.dev/api/nodes/code-block-lowlight
// save as scss and call in tailwind css, apparently antipattern but we only use it here for editor codeblock
.pre-code-block {
  .hljs-comment,
  .hljs-quote {
    color: #616161;
  }
  /* ... other specific syntax styles ... */

  .hljs-variable,
  .hljs-template-variable,
  .hljs-attribute,
  .hljs-tag,
  .hljs-name,
  .hljs-regexp,
  .hljs-link,
  .hljs-name,
  .hljs-selector-id,
  .hljs-selector-class {
    color: #f98181;
  }

  .hljs-number,
  .hljs-meta,
  .hljs-built_in,
  .hljs-builtin-name,
  .hljs-literal,
  .hljs-type,
  .hljs-params {
    color: #fbbc88;
  }

  .hljs-string,
  .hljs-symbol,
  .hljs-bullet {
    color: #b9f18d;
  }

  .hljs-title,
  .hljs-section {
    color: #faf594;
  }

  .hljs-keyword,
  .hljs-selector-tag {
    color: #70cff8;
  }

  .hljs-emphasis {
    font-style: italic;
  }

  .hljs-strong {
    font-weight: 700;
  }
}

// Suggestion node styles
.suggestion-node {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  margin: 0.125rem 0;
  padding: 0.125rem 0.25rem;
  border-radius: 0.375rem;
  border: 1px solid #e5e7eb;
  background-color: #f9fafb;

  .old-text {
    background-color: #fee2e2;
    color: #b91c1c;
    text-decoration: line-through;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
  }

  .new-text {
    background-color: #d1fae5;
    color: #065f46;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
  }

  .actions {
    display: flex;
    gap: 0.125rem;
    margin-left: 0.25rem;

    button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 1.25rem;
      height: 1.25rem;
      border-radius: 50%;
      border: none;
      cursor: pointer;
      transition: background-color 0.2s;

      &.accept-btn {
        background-color: #16a34a;
        color: white;

        &:hover:not(:disabled) {
          background-color: #15803d;
        }
      }

      &.reject-btn {
        background-color: #dc2626;
        color: white;

        &:hover:not(:disabled) {
          background-color: #b91c1c;
        }
      }

      &:disabled {
        background-color: #9ca3af;
        cursor: not-allowed;
      }
    }
  }

  // Animation for smooth appearance
  &.suggestion-node-enter {
    opacity: 0;
    transform: scale(0.95);
    animation: suggestionEnter 0.2s ease-out forwards;
  }
}

@keyframes suggestionEnter {
  to {
    opacity: 1;
    transform: scale(1);
  }
}
