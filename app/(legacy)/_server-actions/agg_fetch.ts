'use server'

import {
  fetchedIssueTreeType,
  fetchedFeedbacksType,
  fetchedSubtreesType,
  fetchedSearchesType,
  fetchedRagsType,
} from '@/app/server-actions/_legacy'
import { fetchNotebooks } from './notebook'
import { fetchIssueTree } from '@/app/(legacy)/_server-actions/issue-tree'
import { fetchSubtrees } from '@/app/(legacy)/_server-actions/subtree'
import { fetchAllFeedbacks } from '@/app/(legacy)/_server-actions/feedback'
import { fetchSearches } from '@/app/(legacy)/_server-actions/search'
import { fetchRags } from '@/app/(legacy)/_server-actions/rag'
import { NotebookStatus } from '@prisma/client'

type FetchedNotebook = {
  id: string
  status: NotebookStatus
  title: string
  updated_at: Date
}

export type FetchedIssueTreeData = {
  issueTree: fetchedIssueTreeType | null
  feedbacks: fetchedFeedbacksType[]
  subtrees: fetchedSubtreesType[]
  searches: fetchedSearchesType[]
  rags: fetchedRagsType[]
  notebooks: FetchedNotebook[]
}

export async function fetchAllIssueTreeData(
  conversationId: string
): Promise<FetchedIssueTreeData> {
  const [issueTree, subtrees, feedbacks, searches, rags, notebooks] =
    await Promise.all([
      fetchIssueTree(conversationId),
      fetchSubtrees(conversationId),
      fetchAllFeedbacks(conversationId),
      fetchSearches(conversationId),
      fetchRags(conversationId),
      fetchNotebooks(conversationId),
    ])

  return {
    issueTree,
    subtrees,
    feedbacks,
    searches,
    rags,
    notebooks,
  }
}
