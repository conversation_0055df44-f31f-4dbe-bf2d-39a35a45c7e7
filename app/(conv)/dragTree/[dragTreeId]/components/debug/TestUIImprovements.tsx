'use client'

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useDragTreeStore } from '@/app/stores/dragtree_store'

/**
 * Test component to demonstrate UI improvements:
 * 1. Research button hidden for "New Question" labels
 * 2. Auto-collapsed categories that only contain question nodes
 */
const TestUIImprovements: React.FC = () => {
  const frontendTreeStructure = useDragTreeStore(
    state => state.frontendTreeStructure
  )

  // Helper function to check if a category only has question children
  const getNodeAnalysis = (node: any, depth = 0) => {
    if (!node) return []

    const analysis = []

    if (node.type === 'category') {
      const hasChildCategories = node.children.some(
        (child: any) => child.type === 'category'
      )
      const questionCount = node.children.filter(
        (child: any) => child.type === 'question'
      ).length
      const categoryCount = node.children.filter(
        (child: any) => child.type === 'category'
      ).length

      analysis.push({
        id: node.id,
        label: node.label,
        type: node.type,
        depth,
        hasChildCategories,
        questionCount,
        categoryCount,
        shouldAutoCollapse: !hasChildCategories && node.children.length > 0,
        children: node.children,
      })
    } else if (node.type === 'question') {
      analysis.push({
        id: node.id,
        label: node.label,
        type: node.type,
        depth,
        isNewQuestion: node.label === 'New Question',
        shouldHideResearchButton: node.label === 'New Question',
      })
    }

    // Recursively analyze children
    node.children?.forEach((child: any) => {
      analysis.push(...getNodeAnalysis(child, depth + 1))
    })

    return analysis
  }

  const nodeAnalysis = frontendTreeStructure
    ? getNodeAnalysis(frontendTreeStructure)
    : []
  const categoryNodes = nodeAnalysis.filter(n => n.type === 'category')
  const questionNodes = nodeAnalysis.filter(n => n.type === 'question')
  const autoCollapsedCategories = categoryNodes.filter(
    n => n.shouldAutoCollapse
  )
  const newQuestions = questionNodes.filter(n => n.isNewQuestion)

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          🎨 UI Improvements Test
          <Badge variant="outline">Analysis</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Summary Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="p-3 border rounded-lg text-center">
            <div className="text-2xl font-bold text-blue-600">
              {categoryNodes.length}
            </div>
            <div className="text-sm text-gray-600">Total Categories</div>
          </div>
          <div className="p-3 border rounded-lg text-center">
            <div className="text-2xl font-bold text-green-600">
              {autoCollapsedCategories.length}
            </div>
            <div className="text-sm text-gray-600">Auto-Collapsed</div>
          </div>
          <div className="p-3 border rounded-lg text-center">
            <div className="text-2xl font-bold text-purple-600">
              {questionNodes.length}
            </div>
            <div className="text-sm text-gray-600">Total Questions</div>
          </div>
          <div className="p-3 border rounded-lg text-center">
            <div className="text-2xl font-bold text-orange-600">
              {newQuestions.length}
            </div>
            <div className="text-sm text-gray-600">New Questions</div>
          </div>
        </div>

        {/* Improvement 1: Research Button Logic */}
        <div>
          <h3 className="text-sm font-medium mb-3 flex items-center gap-2">
            🔍 Research Button Logic
            <Badge variant="outline" className="text-xs">
              {newQuestions.length} hidden
            </Badge>
          </h3>
          {newQuestions.length > 0 ? (
            <div className="space-y-2">
              <div className="p-3 bg-orange-50 border border-orange-200 rounded-lg">
                <div className="text-sm font-medium text-orange-800 mb-1">
                  ✅ Research Button Hidden
                </div>
                <div className="text-xs text-orange-700">
                  {newQuestions.length} question(s) with &quot;New
                  Question&quot; label have research button hidden
                </div>
              </div>
              {newQuestions.map((node, index) => (
                <div
                  key={index}
                  className="p-2 bg-white border rounded text-sm"
                >
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="text-xs">
                      {node.id}
                    </Badge>
                    <span className="text-gray-600">
                      &quot;{node.label}&quot;
                    </span>
                    <Badge className="bg-orange-100 text-orange-700 text-xs">
                      No Research Button
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="p-3 bg-gray-50 border rounded-lg text-sm text-gray-600">
              No &quot;New Question&quot; labels found. All questions will show
              research button.
            </div>
          )}
        </div>

        {/* Improvement 2: Auto-Collapse Logic */}
        <div>
          <h3 className="text-sm font-medium mb-3 flex items-center gap-2">
            📁 Auto-Collapse Logic
            <Badge variant="outline" className="text-xs">
              {autoCollapsedCategories.length} collapsed
            </Badge>
          </h3>
          {autoCollapsedCategories.length > 0 ? (
            <div className="space-y-2">
              <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                <div className="text-sm font-medium text-green-800 mb-1">
                  ✅ Categories Auto-Collapsed
                </div>
                <div className="text-xs text-green-700">
                  {autoCollapsedCategories.length} categor(ies) with only
                  question children are collapsed by default
                </div>
              </div>
              {autoCollapsedCategories.map((node, index) => (
                <div
                  key={index}
                  className="p-2 bg-white border rounded text-sm"
                >
                  <div className="flex items-center gap-2">
                    <Badge variant="default" className="text-xs">
                      {node.id}
                    </Badge>
                    <span className="font-medium">{node.label}</span>
                    <Badge className="bg-green-100 text-green-700 text-xs">
                      {node.questionCount} questions only
                    </Badge>
                    <Badge className="bg-blue-100 text-blue-700 text-xs">
                      Auto-collapsed
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="p-3 bg-gray-50 border rounded-lg text-sm text-gray-600">
              No categories with only question children found, or all are
              already manually managed.
            </div>
          )}
        </div>

        {/* Implementation Status */}
        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h4 className="text-sm font-medium text-blue-800 mb-2">
            🎯 Implementation Status
          </h4>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>
              ✅ Research button hidden for &quot;New Question&quot; labels in
              tree view
            </li>
            <li>
              ✅ Research button hidden for &quot;New Question&quot; labels in
              React Flow
            </li>
            <li>
              ✅ Categories with only questions auto-collapsed on initial load
            </li>
            <li>✅ Mixed categories (with child categories) remain expanded</li>
            <li>✅ User expansion preferences preserved</li>
            <li>
              ✅ Delete confirmation dialog shown for both categories and
              questions
            </li>
          </ul>
        </div>

        {/* Delete Functionality Status */}
        <div className="mt-4 p-4 bg-green-50 rounded-lg">
          <h4 className="text-sm font-medium text-green-800 mb-2">
            🗑️ Delete Functionality Status
          </h4>
          <ul className="text-sm text-green-700 space-y-1">
            <li>✅ Questions can be deleted with confirmation dialog</li>
            <li>✅ Categories can be deleted with confirmation dialog</li>
            <li>✅ Shared confirmation component for both types</li>
            <li>
              ✅ Store validation prevents deletion of last child under category
            </li>
            <li>✅ Root node deletion is prevented (no delete button shown)</li>
          </ul>
          <div className="mt-2 text-xs text-green-600">
            💡 All nodes now show confirmation before deletion. The dialog shows
            different information based on whether the node has children.
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Memoize to prevent unnecessary re-renders when tree structure hasn't changed
export default React.memo(TestUIImprovements)
