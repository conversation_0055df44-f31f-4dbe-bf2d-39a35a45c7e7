/**
 * Feature flags configuration
 * Simple boolean flags to control feature rollouts
 */

export const FEATURES = {
  /**
   * Enable visual selection mode in DragTree flow diagram
   * When false, the "Select to Use" button and related UI are hidden.
   */
  ENABLE_NODE_SELECTION: false,
} as const

export type FeatureFlag = keyof typeof FEATURES

/**
 * Check if a feature is enabled
 */
export function isFeatureEnabled(feature: FeatureFlag): boolean {
  return FEATURES[feature]
}
