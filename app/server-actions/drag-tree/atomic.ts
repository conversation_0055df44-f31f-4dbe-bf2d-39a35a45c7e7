'use server'

import prisma from '@/app/libs/prismadb'
import { DragTreeNodeStatus, DragTreeNodeType } from '@prisma/client'
// Local, auth-free alignment validation helpers to keep tests light-weight
const extractIdsFromTreeStructure = (treeStructure: any): Set<string> => {
  if (!treeStructure || typeof treeStructure !== 'object') return new Set()
  const { root_id, hierarchy } = treeStructure as {
    root_id: string
    hierarchy: Record<string, string[]>
  }
  if (!root_id || !hierarchy) return new Set()
  const ids = new Set<string>()
  const stack: string[] = [root_id]
  while (stack.length) {
    const current = stack.pop() as string
    if (!ids.has(current)) {
      ids.add(current)
      const children = hierarchy[current] || []
      children.forEach(child => stack.push(child))
    }
  }
  return ids
}

const validateTreeAlignmentRaw = (
  treeStructure: any,
  activeIds: Set<string>
): boolean => {
  const idsFromTree = extractIdsFromTreeStructure(treeStructure)
  if (idsFromTree.size !== activeIds.size) return false
  for (const id of Array.from(idsFromTree)) {
    if (!activeIds.has(id)) return false
  }
  return true
}

export type CreateNodeAtomic = {
  id: string
  label: string
  node_type: DragTreeNodeType
  metadata?: Record<string, any>
}

export type UpdateStatusAtomic = { id: string; status: DragTreeNodeStatus }
export type UpdateLabelAtomic = { id: string; label: string }

export async function applyTreeUpdateAtomic(input: {
  treeId: string
  creates?: CreateNodeAtomic[]
  updateStatuses?: UpdateStatusAtomic[]
  updateLabels?: UpdateLabelAtomic[]
  // Optional when only labels are updated
  nextTreeStructure?: { root_id: string; hierarchy: Record<string, string[]> }
}) {
  const {
    treeId,
    creates = [],
    updateStatuses = [],
    updateLabels = [],
    nextTreeStructure,
  } = input

  const structureProvided = Boolean(nextTreeStructure)
  const hasCreates = creates.length > 0
  const hasStatusChanges = updateStatuses.length > 0

  // If ACTIVE set changes or topology changes, require structure to validate
  if ((hasCreates || hasStatusChanges) && !structureProvided) {
    const affectsActiveSet = updateStatuses.some(
      u => u.status !== DragTreeNodeStatus.ACTIVE
    )
    if (hasCreates || affectsActiveSet) {
      throw new Error(
        'applyTreeUpdateAtomic: topology or ACTIVE set changed but nextTreeStructure was not provided.'
      )
    }
  }

  return prisma.$transaction(async tx => {
    if (creates.length > 0) {
      await tx.dragTreeNode.createMany({
        data: creates.map(n => ({
          id: n.id,
          drag_tree_id: treeId,
          node_type: n.node_type,
          label: n.label,
          metadata: n.metadata ?? {},
          status: DragTreeNodeStatus.ACTIVE,
        })),
        skipDuplicates: true,
      })
    }

    if (updateStatuses.length > 0) {
      await Promise.all(
        updateStatuses.map(u =>
          tx.dragTreeNode.update({
            where: { id: u.id },
            data: { status: u.status },
          })
        )
      )
    }

    if (updateLabels.length > 0) {
      await Promise.all(
        updateLabels.map(u =>
          tx.dragTreeNode.update({
            where: { id: u.id },
            data: { label: u.label },
          })
        )
      )
    }

    if (structureProvided) {
      await tx.dragTree.update({
        where: { id: treeId },
        data: { tree_structure: nextTreeStructure, updated_at: new Date() },
      })
    }

    // Validate alignment at end only when structure changed
    if (structureProvided) {
      const active = await tx.dragTreeNode.findMany({
        where: { drag_tree_id: treeId, status: 'ACTIVE' },
        select: { id: true },
      })
      const activeIds = new Set(active.map(n => n.id))
      if (!validateTreeAlignmentRaw(nextTreeStructure, activeIds)) {
        throw new Error(
          'Drag tree hierarchy and active node set are out of sync – transaction rolled back.'
        )
      }
    }

    return { success: true }
  })
}
