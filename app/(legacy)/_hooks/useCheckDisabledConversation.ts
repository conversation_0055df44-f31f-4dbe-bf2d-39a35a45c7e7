import { usePathname } from 'next/navigation'
import { useConversationStore } from '@/app/stores/_legacy/conversation_store'
import { ConversationStatus } from '@prisma/client'

const useCheckDisabledConversation = () => {
  const pathname = usePathname()
  const conversationStore = useConversationStore()
  const conversationId = pathname.split('/').pop()

  const shouldDisable =
    conversationStore.getConversationStatus(conversationId || '') !==
    ConversationStatus.ACTIVE

  return shouldDisable
}

export default useCheckDisabledConversation
