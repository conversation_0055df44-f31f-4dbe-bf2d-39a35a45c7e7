import { streamText } from 'ai'
import { NextResponse } from 'next/server'
import { OpenAIUsageType, SubtreeStatus } from '@prisma/client'
import { constructPrompt } from './utils'
import { azure } from '@ai-sdk/azure'
import { logOpenAIUsage_serverAction } from '@/app/(legacy)/_server-actions/log_openai_usage'
import { SubtreeGenerateQuestionsRequestType } from '@/app/types/api'
import prisma from '@/app/libs/prismadb'

export const maxDuration = 60

// Define the POST function
export async function POST(req: Request) {
  // TODO: Re-implement with AI SDK 5 - disabled for now to unblock core features
  return new Response(
    JSON.stringify({ error: 'Subtree generation temporarily disabled' }),
    {
      status: 503,
      headers: { 'Content-Type': 'application/json' },
    }
  )

  const requestData: SubtreeGenerateQuestionsRequestType = await req.json()
  const {
    issueTreeId,
    conversationId,
    userId,
    markdown,
    originalAskText,
    nodeMapping,
    id,
    childs,
    customDirective,
  } = requestData

  if (!userId || !conversationId || !issueTreeId) {
    return NextResponse.json(
      {
        message:
          'Unauthorized. User ID, conversation ID, and issue tree ID are required.',
      },
      { status: 401 }
    )
  }

  // Use more wider model if the input tokens is more than 3000 tokens/ ~12000 characters

  const systemMessage = constructPrompt(
    markdown,
    originalAskText,
    nodeMapping,
    id,
    childs,
    customDirective
  )

  const model_name = 'gpt-4o-mini'
  try {
    const result = streamText({
      model: azure(model_name),
      messages: [
        {
          role: 'system',
          content: systemMessage,
        },
      ],
      temperature: 0.6,
      onFinish: async ({ text }) => {
        console.log('api/subtree/generate_questions: completion:', text)
        console.log(OpenAIUsageType.SUBTREE_GENERATE_QUESTIONS)
        await Promise.all([
          // record the API usage to the database as audit log
          logOpenAIUsage_serverAction({
            open_ai_usage_type: OpenAIUsageType.SUBTREE_GENERATE_QUESTIONS,
            model_name: model_name,
            input_text: systemMessage,
            output_text: text,
            userId: userId,
            conversationId: conversationId,
          }),
          // Save the completion to the database
          prisma.subtree.create({
            data: {
              creator_id: userId,
              conversation_id: conversationId,
              issue_tree_id: issueTreeId,
              prompt: systemMessage,
              generation_output: text,
              status: SubtreeStatus.ACTIVE,
              selected_node_id: id,
            },
          }),
        ])
      },
    })

    // Respond with the stream
    return result.toUIMessageStreamResponse()
  } catch (error) {
    // Log any errors to the console and return a 500 error response
    console.log('api/subtree/generate_questions: error:', error)
    return new NextResponse('Error', { status: 500 })
  }
}
