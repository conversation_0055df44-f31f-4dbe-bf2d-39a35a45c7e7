/**
 * Tutorial utility functions for client-side usage
 */

/**
 * Helper function to check tutorial status from user metadata
 * @param userMetadata - User metadata object from database
 * @returns Tutorial status (completed, skipped, should show)
 */
export function checkTutorialStatusFromMetadata(
  userMetadata: Record<string, any>
) {
  try {
    const tutorialData = userMetadata?.tutorial
    const isCompleted = tutorialData?.is_completed === true
    const isSkipped = tutorialData?.is_skipped === true
    const shouldShow = !isCompleted && !isSkipped // Show if neither completed nor skipped

    return {
      success: true,
      data: {
        isCompleted,
        isSkipped,
        shouldShow,
        createdAt: tutorialData?.created_at || null,
      },
    }
  } catch (error) {
    console.error('Error checking tutorial status from metadata:', error)
    return { success: false, error: 'Failed to check tutorial status' }
  }
}
