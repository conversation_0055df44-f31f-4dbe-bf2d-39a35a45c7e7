import { TreeNode } from '@/app/types'
import { TreeNodeType } from '@/app/(conv)/dragTree/[dragTreeId]/constants/node-types'

export type HierarchicalOutlineType = {
  isStreaming: boolean
  streamingContent: string
  onLoadingChange?: (isLoading: boolean) => void
  onGeneratingNodeChange?: (nodeId: string) => void
}

export type NodeGenerationHandlersType = {
  handleGenerateSimilarQuestions: (
    nodeId: string,
    guidance?: string
  ) => Promise<void>
  handleGenerateSimilarCategories: (
    nodeId: string,
    guidance?: string
  ) => Promise<void>
}

export type OutlineStateHandlersType = {
  handleAddNode: (parentId: string, type: TreeNodeType) => void
  handleSetCollapsedNodes: (nodes: Set<string>) => void
}

export type LoadingStateType = {
  isLoading: boolean
  currentNodeId: string
}

// Layout Types
export type LayoutMode = 'linear' | 'radial'

// VisualFlowDiagram Types
export type VisualFlowDiagramType = {
  isStreaming: boolean
  streamingContent: string
  isLoading?: boolean
  loadingNodeId?: string
  layoutMode?: LayoutMode
  onLayoutModeChange?: (mode: LayoutMode) => void
}

export type DiagramDataResult = {
  nodes: any[]
  edges: any[]
}

export type DiagramViewType = {
  nodes: any[]
  edges: any[]
  layoutMode?: LayoutMode
  onLayoutModeChange?: (mode: LayoutMode) => void
}

export type OutlineViewProps = {
  node: TreeNode
  onAdd: (parentId: string, type: TreeNodeType) => void
  onDelete: (nodeId: string, nodeType: string) => void
  onEdit: (nodeId: string, newLabel: string) => void
  onReorder: (parentId: string, oldIndex: number, newIndex: number) => void
  editingNode: string | null
  setEditingNode: (id: string | null) => void
  collapsedNodes?: Set<string>
  setCollapsedNodes?: (nodes: Set<string>) => void
  targetNodeId?: string | null
  onGenerateSimilarQuestions?: (nodeId: string, guidance: string) => void
  onGenerateSimilarCategories?: (nodeId: string, guidance: string) => void
  isLoading?: boolean
  loadingNodeId?: string
  isGloballyExpanded?: boolean
  // Drag and drop props passed from SortableOutlineItem
  dragAttributes?: any
  dragListeners?: any
  isDragging?: boolean
}

export type NodeContentProps = {
  node: TreeNode
  editingNode: string | null
  onEdit: (nodeId: string, newLabel: string) => void
  setEditingNode: (id: string | null) => void
  isCurrentNodeLoading: boolean
  targetNodeId?: string | null
}

export type NodeActionsProps = {
  node: TreeNode
  onAdd: (parentId: string, type: TreeNodeType) => void
  onEdit: () => void
  onDelete: () => void
  onGenerateSimilarQuestions?: (nodeId: string, guidance: string) => void
  onGenerateSimilarCategories?: (nodeId: string, guidance: string) => void
  isLoading: boolean
  isHovered: boolean
  isExpanding: boolean
  lastExpansionTime: number
}

export type ConfirmDialogProps = {
  isOpen: boolean
  nodeToDelete: TreeNode | null
  onConfirm: () => void
  onCancel: () => void
}

export type GuidanceDialogProps = {
  isOpen: boolean
  guidanceType: 'questions' | 'categories' | null
  node: TreeNode
  onGenerate: (guidance: string) => void
  onCancel: () => void
  isExpanding: boolean
}

export type NodeInteractionProps = {
  isClicked: boolean
  isHovered: boolean
  isExpanding: boolean
  isPersistent: boolean
  lastExpansionTime: number
  lastClickTime: number
  handleNodeClick: (e: React.MouseEvent) => void
  handleToggleCollapse: (e: React.MouseEvent) => void
}

// This represents the state managed by the useNodeInteraction hook's reducer
export type NodeInteractionState = {
  isClicked: boolean
  isHovered: boolean
  isExpanding: boolean
  isPersistent: boolean
  lastExpansionTime: number
  lastClickTime: number
}

export type NodeCardProps = {
  node: TreeNode
  children: React.ReactNode
  isOpacityEnabled?: boolean
  isDraggable?: boolean
  // Drag and drop props
  dragAttributes?: any
  dragListeners?: any
  isDragging?: boolean
}
