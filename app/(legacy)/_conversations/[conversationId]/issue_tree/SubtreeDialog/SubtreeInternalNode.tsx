import React, { memo } from 'react'
import { <PERSON><PERSON>, Position } from 'reactflow'

interface SubtreeInternalNodeProps {
  id: string
  data: {
    label: string
  }
}

const SubtreeInternalNode = memo((props: SubtreeInternalNodeProps) => {
  return (
    <div className="flex justify-center items-center p-2 bg-yellow-200 rounded-lg">
      <div className="text-center font-bold text-m">{props.data.label}</div>

      <Handle type="target" position={Position.Left} className="w-6 h-6" />
      <Handle type="source" position={Position.Right} className="w-6 h-6" />
    </div>
  )
})

SubtreeInternalNode.displayName = 'SubtreeInternalNode'

export default SubtreeInternalNode
