import { PrismaClient } from '@prisma/client'

declare global {
  var prisma: PrismaClient | undefined
}

const client =
  globalThis.prisma ||
  new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['warn', 'error'] : ['error'],
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
  })

if (process.env.NODE_ENV !== 'production') globalThis.prisma = client

// --- DragTree / DragTreeNode alignment middleware ---

// Lightweight helper to avoid circular dependency with validation-utils
const extractIdsFromTreeStructure = (treeStructure: any): Set<string> => {
  if (!treeStructure || typeof treeStructure !== 'object') return new Set()
  const { root_id, hierarchy } = treeStructure as {
    root_id: string
    hierarchy: Record<string, string[]>
  }
  if (!root_id || !hierarchy) return new Set()
  const ids = new Set<string>()
  const stack: string[] = [root_id]
  while (stack.length) {
    const current = stack.pop() as string
    if (!ids.has(current)) {
      ids.add(current)
      const children = hierarchy[current] || []
      children.forEach(child => stack.push(child))
    }
  }
  return ids
}

const mutatingActions = new Set([
  'create',
  'createMany',
  'update',
  'updateMany',
  'delete',
  'deleteMany',
])

// Alignment middleware mode (change here as needed): 'off' | 'warn' | 'enforce'
type AlignMode = 'off' | 'warn' | 'enforce'
// Change here to switch middleware mode easily
// Disable for now to avoid recursive validations causing transaction timeouts (P2028).
const ALIGNMENT_SETTINGS: { mode: AlignMode } = { mode: 'off' }

// Re-entrancy guard to prevent middleware-triggered queries from re-entering the middleware
let __dragTreeAlignValidating = false

client.$use(async (params, next) => {
  // Mode options:
  // - 'off': skip validation entirely
  // - 'warn' (default): log warnings when misalignment is detected
  // - 'enforce': throw on misalignment (NOT recommended; use transactional validation instead)
  const mode: AlignMode = ALIGNMENT_SETTINGS.mode

  const result = await next(params)

  if (
    mode === 'off' ||
    !(params.model === 'DragTree' || params.model === 'DragTreeNode') ||
    !mutatingActions.has(params.action as string)
  ) {
    return result
  }

  let treeId: string | undefined
  if (params.model === 'DragTree') {
    treeId = params.args?.where?.id as string | undefined
  } else if (params.model === 'DragTreeNode') {
    treeId = (params.args?.data?.drag_tree_id ||
      params.args?.where?.drag_tree_id) as string | undefined
    if (!treeId && params.args?.where?.id) {
      const node = await client.dragTreeNode.findUnique({
        where: { id: params.args.where.id as string },
        select: { drag_tree_id: true },
      })
      treeId = node?.drag_tree_id
    }
  }

  if (!treeId) return result

  // Fast, non-transactional observability when in 'warn' mode
  let isValid = true
  if (mode === 'warn') {
    if (__dragTreeAlignValidating) return result
    __dragTreeAlignValidating = true
    try {
      const [tree, activeNodes] = await Promise.all([
        client.dragTree.findUnique({
          where: { id: treeId },
          select: { tree_structure: true },
        }),
        client.dragTreeNode.findMany({
          where: { drag_tree_id: treeId, status: 'ACTIVE' },
          select: { id: true },
        }),
      ])
      if (!tree || !tree.tree_structure) {
        isValid = true
      } else {
        const idsFromTree = extractIdsFromTreeStructure(tree.tree_structure)
        const activeIdSet = new Set<string>(activeNodes.map(n => n.id))
        if (idsFromTree.size !== activeIdSet.size) isValid = false
        else {
          for (const id of Array.from(idsFromTree)) {
            if (!activeIdSet.has(id)) {
              isValid = false
              break
            }
          }
        }
      }
    } catch (e) {
      // Swallow observability errors
      isValid = true
    } finally {
      __dragTreeAlignValidating = false
    }
  } else if (mode === 'enforce') {
    // Keep transactional check for enforce mode only (may be expensive)
    isValid = await client.$transaction(async tx => {
      const [tree, activeNodes] = await Promise.all([
        tx.dragTree.findUnique({
          where: { id: treeId },
          select: { tree_structure: true },
        }),
        tx.dragTreeNode.findMany({
          where: { drag_tree_id: treeId, status: 'ACTIVE' },
          select: { id: true },
        }),
      ])
      if (!tree || !tree.tree_structure) return true
      const idsFromTree = extractIdsFromTreeStructure(tree.tree_structure)
      const activeIdSet = new Set<string>(activeNodes.map(n => n.id))
      if (idsFromTree.size !== activeIdSet.size) return false
      for (const id of Array.from(idsFromTree)) {
        if (!activeIdSet.has(id)) return false
      }
      return true
    })
  }

  if (!isValid) {
    const msg =
      'DragTree alignment warning: hierarchy and ACTIVE node set are out of sync.'
    if (mode === 'enforce') {
      // Note: throwing here is post-commit for this op; prefer transactional validation instead.
      throw new Error(
        'Drag tree hierarchy and active node set are out of sync – mutation cancelled.'
      )
    }
    // warn mode
    if (process.env.NODE_ENV !== 'test') {
      console.warn(`⚠️ ${msg}`, {
        model: params.model,
        action: params.action,
        where: params.args?.where,
      })
    }
  }

  return result
})

export default client
