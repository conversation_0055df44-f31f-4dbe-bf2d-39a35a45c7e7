import posthog from 'posthog-js'

// Constant to avoid re-reading env each call
const POSTHOG_KEY = process.env.NEXT_PUBLIC_POSTHOG_KEY

// Internal state flags
let isInitializing = false
let hasAttemptedInit = false
let isLoaded = false

/**
 * Returns true when PostHog SDK has completed `on_ready` callback.
 */
export const isPostHogReady = (): boolean => isLoaded

/**
 * Attempt to (re)initialize PostHog.
 * Returns current ready state **after** kicking off initialization.
 */
export const ensurePostHogInitialized = (): boolean => {
  // Prevent SSR, parallel, or repeated attempts
  if (typeof window === 'undefined' || isLoaded || isInitializing)
    return isLoaded

  if (!POSTHOG_KEY) {
    if (process.env.NODE_ENV === 'development' && !hasAttemptedInit) {
      console.warn(
        'PostHog API key missing – set NEXT_PUBLIC_POSTHOG_KEY to enable analytics.'
      )
    }
    hasAttemptedInit = true
    return false
  }

  isInitializing = true
  hasAttemptedInit = true

  try {
    posthog.init(POSTHOG_KEY, {
      api_host: '/ingest',
      ui_host: 'https://us.posthog.com',
      capture_pageview: 'history_change',
      capture_pageleave: true,
      capture_exceptions: false, // Disable to prevent AbortError noise
      debug: false, // Disable even in development to reduce noise
      // Reduce aggressive autocapture that can trigger aborts
      autocapture: {
        dom_event_allowlist: ['click'],
        element_allowlist: ['a', 'button'],
        element_ignorelist: ['input', 'textarea', 'select'],
      } as any,
      // Persistence settings for better reliability
      persistence: 'localStorage+cookie',
      persistence_name: '__posthog',
      // Reduce network calls in development
      disable_session_recording: process.env.NODE_ENV === 'development',
      // Official readiness callback – sets local flag & emits event
      loaded: () => {
        isLoaded = true
        isInitializing = false
        if (process.env.NODE_ENV === 'development') {
          // eslint-disable-next-line no-console
          console.log('PostHog ready')
        }
        window.dispatchEvent(new CustomEvent('posthog-ready'))
      },
    })
  } catch (error) {
    isInitializing = false
    if (process.env.NODE_ENV === 'development') {
      console.error('PostHog initialization failed:', error)
    }
  }

  return isLoaded // will still be false until loaded() fires
}

// Global error handler to suppress PostHog AbortError noise
if (typeof window !== 'undefined') {
  const originalConsoleError = console.error

  console.error = (...args: any[]) => {
    // Filter out PostHog AbortError messages
    const errorMessage = args[0]?.toString?.() || ''
    const stackTrace = args[1]?.stack || args[0]?.stack || ''

    // Check if this is a PostHog-related AbortError
    if (
      errorMessage.includes('AbortError') &&
      errorMessage.includes('signal is aborted without reason') &&
      (stackTrace.includes('posthog') ||
        stackTrace.includes('node_modules_posthog-js'))
    ) {
      // Suppress this specific error in development mode
      if (process.env.NODE_ENV === 'development') {
        return // Don't log PostHog abort errors
      }
    }

    // Log all other errors normally
    originalConsoleError.apply(console, args)
  }
}

// Kick-off initialization as early as possible on the client
if (typeof window !== 'undefined') {
  // Debounce initialization to prevent rapid re-init during hot reloads
  let initTimeout: NodeJS.Timeout | null = null

  const debouncedInit = () => {
    if (initTimeout) clearTimeout(initTimeout)
    initTimeout = setTimeout(() => {
      ensurePostHogInitialized()
      initTimeout = null // Clear reference after execution
    }, 100)
  }

  // Cleanup function for test environments
  const cleanup = () => {
    if (initTimeout) {
      clearTimeout(initTimeout)
      initTimeout = null
    }
  }

  // Expose cleanup for test environments
  if (process.env.NODE_ENV === 'test') {
    ;(window as any).__postHogCleanup = cleanup
  }

  debouncedInit()

  // Retry once DOM is fully parsed (covers slow script injection cases)
  if (!isLoaded && document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', debouncedInit, {
      once: true,
    })
  }
}
