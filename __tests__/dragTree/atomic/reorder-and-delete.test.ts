/**
 * Tests for reorder and delete flows using applyTreeUpdateAtomic
 * - Reorder structure-only commit
 * - Delete subtree (INACTIVE propagation via updateStatuses)
 * - Rollback behavior on failure
 */

import { applyTreeUpdateAtomic } from '@/app/server-actions/drag-tree/atomic'

jest.mock('@/app/libs/prismadb', () => ({
  __esModule: true,
  default: { $transaction: jest.fn() },
}))

const prisma = require('@/app/libs/prismadb').default

describe('Reorder and Delete atomic flows', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('reorder: structure-only update succeeds', async () => {
    const updateTree = jest.fn().mockResolvedValue({})
    const findManyActive = jest
      .fn()
      .mockResolvedValue([{ id: 'root' }, { id: 'a' }, { id: 'b' }])

    prisma.$transaction.mockImplementation(async (cb: any) => {
      const tx = {
        dragTreeNode: {
          createMany: jest.fn(),
          update: jest.fn(),
          findMany: findManyActive,
        },
        dragTree: { update: updateTree },
      }
      return cb(tx)
    })

    const nextTreeStructure = {
      root_id: 'root',
      hierarchy: { root: ['b', 'a'], a: [], b: [] },
    }

    const res = await applyTreeUpdateAtomic({
      treeId: 'tree_1',
      nextTreeStructure,
    })

    expect(res).toEqual({ success: true })
    expect(updateTree).toHaveBeenCalled()
  })

  it('delete: marks subtree nodes INACTIVE and validates structure', async () => {
    const updateStatus = jest.fn().mockResolvedValue({})
    const updateTree = jest.fn().mockResolvedValue({})
    const findManyActive = jest
      .fn()
      .mockResolvedValue([{ id: 'root' }, { id: 'b' }]) // 'a' removed

    prisma.$transaction.mockImplementation(async (cb: any) => {
      const tx = {
        dragTreeNode: {
          createMany: jest.fn(),
          update: updateStatus,
          findMany: findManyActive,
        },
        dragTree: { update: updateTree },
      }
      return cb(tx)
    })

    const nextTreeStructure = {
      root_id: 'root',
      hierarchy: { root: ['b'], b: [] },
    }

    const res = await applyTreeUpdateAtomic({
      treeId: 'tree_2',
      updateStatuses: [
        { id: 'a', status: 'INACTIVE' },
        { id: 'a_child', status: 'INACTIVE' },
      ],
      nextTreeStructure,
    })

    expect(res).toEqual({ success: true })
    expect(updateStatus).toHaveBeenCalledTimes(2)
    expect(updateTree).toHaveBeenCalled()
  })

  it('reorder: fails and rolls back when misaligned', async () => {
    const updateTree = jest.fn().mockResolvedValue({})
    // Active does not include child referenced by structure
    const findManyActive = jest.fn().mockResolvedValue([{ id: 'root' }])

    prisma.$transaction.mockImplementation(async (cb: any) => {
      const tx = {
        dragTreeNode: {
          createMany: jest.fn(),
          update: jest.fn(),
          findMany: findManyActive,
        },
        dragTree: { update: updateTree },
      }
      return cb(tx)
    })

    const badStructure = {
      root_id: 'root',
      hierarchy: { root: ['a'], a: [] },
    }

    await expect(
      applyTreeUpdateAtomic({
        treeId: 'tree_3',
        nextTreeStructure: badStructure,
      })
    ).rejects.toThrow(/out of sync|rolled back/i)
  })
})
