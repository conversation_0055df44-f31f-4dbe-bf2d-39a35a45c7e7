import { create } from 'zustand'

type UIState = {
  isHoverCollapseLocked: boolean
  setHoverCollapseLock: (isLocked: boolean) => void
  useRealLLMAPI: boolean
  setUseRealLLMAPI: (useReal: boolean) => void
  researchPreviewNodeId: string | null
  setResearchPreviewNodeId: (nodeId: string | null) => void
}

export const useUIStore = create<UIState>(set => ({
  isHoverCollapseLocked: false,
  setHoverCollapseLock: (isLocked: boolean) =>
    set({ isHoverCollapseLocked: isLocked }),
  useRealLLMAPI: true, // Use real LLM API by default
  setUseRealLLMAPI: (useReal: boolean) => set({ useRealLLMAPI: useReal }),
  researchPreviewNodeId: null,
  setResearchPreviewNodeId: (nodeId: string | null) =>
    set({ researchPreviewNodeId: nodeId }),
}))
