// Initial version with textarea, not used anymore, keep as reference
// "use client";

// import React, { useState, useEffect, useRef, ChangeEvent } from "react";
// import mermaid from "mermaid";

// mermaid.initialize({
//   startOnLoad: true,
//   theme: "dark",
// });

// interface MermaidLiveEditorProps {
//   initialChartCode: string;
// }

// const MermaidLiveEditor: React.FC<MermaidLiveEditorProps> = ({
//   initialChartCode,
// }) => {
//   const [chartCode, setChartCode] = useState<string>(initialChartCode);
//   const [errorMsg, setErrorMsg] = useState<string | null>(null);
//   const [showTextArea, setShowTextArea] = useState<boolean>(true);
//   const ref = useRef<HTMLDivElement>(null);

//   // Define the event listener
//   const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
//     setErrorMsg(event.reason?.message || "An error occurred");
//   };

//   useEffect(() => {
//     // Add the event listener for unhandled promise rejections
//     window.addEventListener("unhandledrejection", handleUnhandledRejection);

//     // Cleanup
//     return () => {
//       window.removeEventListener(
//         "unhandledrejection",
//         handleUnhandledRejection
//       );
//     };
//   }, []);

//   const renderMermaid = () => {
//     if (ref.current) {
//       try {
//         ref.current.innerHTML = chartCode;
//         mermaid.init(undefined, ref.current);
//         setErrorMsg(null);
//       } catch (error: unknown) {
//         if (error instanceof Error) {
//           setErrorMsg(error.message);
//         } else {
//           setErrorMsg("An error occurred");
//         }
//       }
//     }
//   };

//   useEffect(() => {
//     renderMermaid();
//   }, [chartCode]);

//   const handleCodeChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
//     setChartCode(e.target.value);
//   };

//   const downloadSVG = () => {
//     if (ref.current) {
//       const svgElement = ref.current.querySelector("svg");
//       if (svgElement) {
//         const blob = new Blob([svgElement.outerHTML], {
//           type: "image/svg+xml",
//         });
//         const url = URL.createObjectURL(blob);
//         const a = document.createElement("a");
//         a.href = url;
//         a.download = "mermaid-diagram.svg";
//         a.click();
//         URL.revokeObjectURL(url);
//       }
//     }
//   };

//   const toggleTextArea = () => {
//     setShowTextArea(!showTextArea);
//   };

//   return (
//     <div className="flex flex-col">
//       <button onClick={downloadSVG} className="mb-4">
//         Download SVG
//       </button>
//       <button onClick={toggleTextArea} className="mb-4">
//         {showTextArea ? "Hide Code" : "Show Code"}
//       </button>
//       <div className="flex flex-row">
//         {showTextArea && (
//           <div className="flex-1">
//             <textarea
//               value={chartCode}
//               onChange={handleCodeChange}
//               className="w-full h-72 border rounded"
//             />
//           </div>
//         )}
//         <div ref={ref} className="mermaid flex-1" key={chartCode}></div>
//       </div>
//       {/* Render error message */}
//       {errorMsg && <div className="text-red-500 mt-4">{errorMsg}</div>}
//     </div>
//   );
// };

// export default MermaidLiveEditor;
