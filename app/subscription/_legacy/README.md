# Legacy Subscription System

This folder contains the old subscription system components and hooks that have been replaced by the modern subscription system.

## Files Moved Here

### Legacy Components

- `PricingCard.tsx` - Old pricing card component (replaced by ModernPricingCard.tsx)
- `PricingCards.tsx` - Old pricing cards grid component (commented out/unused)
- `PricingCardsContainer.tsx` - Old container component (used only by legacy landing)
- `SubscriptionPageWrapper.tsx` - Old page wrapper component (commented out/unused)

### Legacy Hooks

- `hooks/useSubscription.ts` - Old subscription hook (replaced by useModernSubscription.ts)

## Current Usage

These legacy components are still imported by:

- `/app/(landing)/_legacy/LandingPricing.tsx` - Legacy landing page pricing section

## Modern Alternatives (Active System)

The current subscription system uses:

- `ModernPricingCard.tsx` - New pricing card with updated design
- `ModernPricingConfig.ts` - Configuration for modern pricing
- `ModernSubscriptionHeader.tsx` - Modern subscription page header
- `ModernSubscriptionPage.tsx` - Main modern subscription page component
- `useModernSubscription.ts` - Modern subscription hook with enhanced functionality

## System Evolution

**Legacy Subscription System (v1):**

- Basic pricing cards with simple design
- Limited functionality and configuration
- Tightly coupled components

**Modern Subscription System (v2):**

- Enhanced pricing cards with modern UI/UX
- Flexible configuration system
- Modular component architecture
- Improved subscription management
- Better integration with payment systems

## Components Still Active

- `BillingToggle.tsx` - Billing period toggle (active)
- `ContactInfo.tsx` - Contact information component (active)
- `EnvironmentWarning.tsx` - Environment warning (active)
- `SubscriptionHeader.tsx` - General subscription header (active)
- `SubscriptionStatus.tsx` - Subscription status display (active)
- `UniversalDragTreeLayout.tsx` - Layout component (active)

## Cleanup Strategy

These legacy components can be removed once:

1. Legacy landing page (`app/(landing)/_legacy/`) is fully deprecated
2. All references to old pricing components are removed
3. Migration to modern subscription system is complete across all user touchpoints

## Architecture Benefits

The modern system provides:

- Better maintainability with modular design
- Enhanced user experience with improved UI components
- More flexible configuration and pricing options
- Better integration with modern payment processing
