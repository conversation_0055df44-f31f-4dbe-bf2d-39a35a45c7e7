'use server'

// This module is a thin compatibility wrapper around the
// drag-tree–specific AI generation update actions. It exists so that
// the rest of the codebase can import generic helpers from a stable
// path without needing to know the underlying entity type.
//
// NOTE: If in the future we have multiple AI-generated entity types
// (e.g. conversation, notebook, etc.), you can route the call to the
// appropriate update implementation based on the provided asset ID or
// extra params.

import type {
  AIGenerationUpdateData,
  AIGenerationUpdateResult,
} from '@/app/types/ai-generation'

// Re-export core update helpers from the drag-tree implementation
export {
  updateAIGenerationTitle as updateAiAssetTitle,
  updateAIGenerationFromTiptap as updateA<PERSON>AssetContent,
} from '@/app/server-actions/drag-tree/update_ai_generation'

import { updateAIGeneration as updateAIGenerationInternal } from '@/app/server-actions/drag-tree/update_ai_generation'

/**
 * Generic update action for AI assets – delegates to the drag-tree
 * implementation for now but keeps a neutral name that makes sense in
 * callers outside the DragTree feature.
 */
export async function updateAiAsset(
  assetId: string,
  updateData: AIGenerationUpdateData,
  expectedVersion: number
): Promise<AIGenerationUpdateResult> {
  return updateAIGenerationInternal(assetId, updateData, expectedVersion)
}
