import { formatChatTimestamp } from '@/lib/utils'

describe('formatChatTimestamp', () => {
  beforeEach(() => {
    // Mock the current time to ensure consistent test results
    jest.useFakeTimers()
    jest.setSystemTime(new Date('2024-01-15T10:30:00Z'))
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  it('should return empty string for null or undefined', () => {
    expect(formatChatTimestamp(null)).toBe('')
    expect(formatChatTimestamp(undefined)).toBe('')
  })

  it('should return empty string for invalid dates', () => {
    expect(formatChatTimestamp('invalid-date')).toBe('')
    expect(formatChatTimestamp(new Date('invalid'))).toBe('')
  })

  it('should return "Just now" for very recent messages', () => {
    const now = new Date('2024-01-15T10:30:00Z')
    const thirtySecondsAgo = new Date('2024-01-15T10:29:30Z')

    expect(formatChatTimestamp(now)).toBe('Just now')
    expect(formatChatTimestamp(thirtySecondsAgo)).toBe('Just now')
  })

  it('should return relative time for recent messages (< 1 hour)', () => {
    const fiveMinutesAgo = new Date('2024-01-15T10:25:00Z')
    const thirtyMinutesAgo = new Date('2024-01-15T10:00:00Z')

    expect(formatChatTimestamp(fiveMinutesAgo)).toBe('5 minutes ago')
    expect(formatChatTimestamp(thirtyMinutesAgo)).toBe('30 minutes ago')
  })

  it('should return formatted time for older messages', () => {
    const lastWeek = new Date('2024-01-08T12:00:00Z')
    const lastMonth = new Date('2023-12-15T09:30:00Z')

    // Check that it contains the expected date parts (timezone-agnostic)
    const lastWeekResult = formatChatTimestamp(lastWeek)
    const lastMonthResult = formatChatTimestamp(lastMonth)

    expect(lastWeekResult).toMatch(/Jan 8, 2024/)
    expect(lastMonthResult).toMatch(/Dec 15, 2023/)

    // Should contain time format
    expect(lastWeekResult).toMatch(/\d{1,2}:\d{2} [AP]M/)
    expect(lastMonthResult).toMatch(/\d{1,2}:\d{2} [AP]M/)
  })

  it('should handle string dates correctly', () => {
    const dateString = '2024-01-15T10:25:00Z'
    expect(formatChatTimestamp(dateString)).toBe('5 minutes ago')
  })

  it('should handle Date objects correctly', () => {
    const dateObj = new Date('2024-01-15T10:25:00Z')
    expect(formatChatTimestamp(dateObj)).toBe('5 minutes ago')
  })

  it('should handle different time ranges appropriately', () => {
    const veryRecent = new Date('2024-01-15T10:29:30Z') // 30 seconds ago
    const recent = new Date('2024-01-15T10:25:00Z') // 5 minutes ago
    const older = new Date('2024-01-08T12:00:00Z') // Last week

    expect(formatChatTimestamp(veryRecent)).toBe('Just now')
    expect(formatChatTimestamp(recent)).toBe('5 minutes ago')
    expect(formatChatTimestamp(older)).toMatch(/Jan 8, 2024/)
  })
})
