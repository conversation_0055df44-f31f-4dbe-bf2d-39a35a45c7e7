import * as fs from 'fs'
import * as path from 'path'
import { EVENT_DEFINITIONS, type EventName } from '@/app/configs/logging-config'

function listAppFiles(root: string): string[] {
  const results: string[] = []
  const exts = new Set(['.ts', '.tsx'])
  const skipDirs = new Set([
    'node_modules',
    '.next',
    '.git',
    'dist',
    'build',
    'utils',
    'configs',
    '__tests__',
  ])

  const walk = (dir: string) => {
    let entries: fs.Dirent[]
    try {
      entries = fs.readdirSync(dir, { withFileTypes: true })
    } catch {
      return
    }
    for (const entry of entries) {
      const full = path.join(dir, entry.name)
      if (entry.isDirectory()) {
        if (skipDirs.has(entry.name)) continue
        walk(full)
      } else if (entry.isFile()) {
        const ext = path.extname(entry.name)
        if (exts.has(ext)) results.push(full)
      }
    }
  }

  walk(path.join(root, 'app'))
  return results
}

describe('Logging events presence (simple scan under /app)', () => {
  it('every defined event appears at least once in some app/*.ts(x) file (excluding definition files)', () => {
    const repoRoot = path.resolve(__dirname, '../..')
    const files = listAppFiles(repoRoot)

    // Exclude files that define or list all events to avoid false positives
    const excluded = new Set([
      path.join(repoRoot, 'app/configs/logging-config.ts'),
      path.join(repoRoot, 'app/libs/logging.ts'),
      path.join(repoRoot, 'app/libs/logging-validation.ts'),
    ])

    const scanFiles = files.filter(f => !excluded.has(f))

    const readCache = new Map<string, string>()
    const getContent = (file: string) => {
      if (!readCache.has(file)) {
        try {
          readCache.set(file, fs.readFileSync(file, 'utf-8'))
        } catch {
          readCache.set(file, '')
        }
      }
      return readCache.get(file) as string
    }

    const allEvents = Object.keys(EVENT_DEFINITIONS) as EventName[]
    const missing: Array<{ event: EventName; exampleHint?: string }> = []

    for (const evt of allEvents) {
      let found = false
      for (const file of scanFiles) {
        const content = getContent(file)
        if (content.includes(evt)) {
          // Check if the event is not commented out
          const lines = content.split('\n')
          for (const line of lines) {
            if (line.includes(evt)) {
              const trimmedLine = line.trim()
              // Skip if line starts with // or is inside /* */ comment
              if (
                !trimmedLine.startsWith('//') &&
                !trimmedLine.startsWith('*')
              ) {
                found = true
                break
              }
            }
          }
          if (found) break
        }
      }
      if (!found) missing.push({ event: evt })
    }

    if (missing.length > 0) {
      const details = missing.map(m => `- ${m.event}`).join('\n')
      throw new Error(
        `Events not found anywhere under /app (excluding definition files):\n${details}`
      )
    }

    expect(true).toBe(true)
  })
})
