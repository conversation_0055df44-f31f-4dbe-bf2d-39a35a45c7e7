/**
 * Tests for AbortController handling in useAiConversation hook
 * Specifically tests the fix for the AbortError during page load
 */

import React from 'react'
import { render, act, waitFor } from '@testing-library/react'
import { useAiConversation } from '@/app/(conv)/dragTree/[dragTreeId]/hooks/useAiConversation'

// Mock fetch to simulate network requests
const mockFetch = jest.fn()
global.fetch = mockFetch

// Mock useChat from @ai-sdk/react
jest.mock('@ai-sdk/react', () => ({
  useChat: jest.fn(() => ({
    messages: [],
    input: '',
    handleInputChange: jest.fn(),
    handleSubmit: jest.fn(),
    status: 'idle',
    sendMessage: jest.fn(),
  })),
}))

// Test component that uses the hook
const TestComponent: React.FC<{
  conversationId?: string
  shouldUnmount?: boolean
}> = ({ conversationId, shouldUnmount = false }) => {
  const conversation = useAiConversation({
    conversationId,
    apiEndpoint: '/api/test',
    model: 'gpt-4.1',
    context: 'test context',
    settings: {},
    contextEntityType: 'drag_tree',
    contextEntityId: 'test-tree',
    initialLimit: 50,
  })

  if (shouldUnmount) {
    return null
  }

  return (
    <div data-testid="conversation-component">
      <div data-testid="loading">
        {conversation.isLoadingConversation ? 'loading' : 'loaded'}
      </div>
      <div data-testid="error">
        {conversation.conversationError || 'no-error'}
      </div>
      <div data-testid="messages-count">{conversation.messages.length}</div>
    </div>
  )
}

describe('useAiConversation AbortController Handling', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockFetch.mockClear()
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  it('should handle AbortError gracefully without setting error state', async () => {
    // Mock fetch to simulate an aborted request
    mockFetch.mockImplementation(() => {
      const abortError = new Error('signal is aborted without reason')
      abortError.name = 'AbortError'
      return Promise.reject(abortError)
    })

    const { getByTestId } = render(
      <TestComponent conversationId="thread_test123" />
    )

    // Wait for the delayed effect to run and handle the abort error
    await waitFor(
      () => {
        expect(mockFetch).toHaveBeenCalled()
      },
      { timeout: 500 } // Reduced from 2000ms to 500ms
    )

    // Wait a bit for error handling
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 50)) // Reduced from 100ms to 50ms
    })

    // Verify that no error state was set despite the AbortError
    expect(getByTestId('error')).toHaveTextContent('no-error')
  })

  it('should set error state for non-AbortError exceptions', async () => {
    // Mock fetch to simulate a real network error
    mockFetch.mockImplementation(() => {
      return Promise.reject(new Error('Network error'))
    })

    const { getByTestId } = render(
      <TestComponent conversationId="thread_test123" />
    )

    // Wait for the error to be set
    await waitFor(
      () => {
        expect(getByTestId('error')).not.toHaveTextContent('no-error')
      },
      { timeout: 500 } // Reduced from 2000ms to 500ms
    )

    // Verify that the error state was set for real errors
    expect(getByTestId('error')).toHaveTextContent('Network error')
  })

  it('should not make requests when component unmounts quickly', async () => {
    let resolvePromise: (value: any) => void = () => {}
    const fetchPromise = new Promise(resolve => {
      resolvePromise = resolve
    })

    // Mock fetch to return a promise that we can control
    mockFetch.mockImplementation(() => fetchPromise)

    const { rerender } = render(
      <TestComponent conversationId="thread_test123" />
    )

    // Immediately unmount the component
    rerender(
      <TestComponent conversationId="thread_test123" shouldUnmount={true} />
    )

    // Wait a bit to ensure any timeouts have fired
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 100))
    })

    // Resolve the fetch promise
    resolvePromise({
      ok: true,
      json: () =>
        Promise.resolve({
          conversation: { id: 'thread_test123', title: 'Test' },
          messages: [],
          pagination: { hasMore: false, limit: 50, total: 0 },
        }),
    })

    // Wait for any remaining async operations
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 100))
    })

    // The test passes if no errors are thrown during cleanup
    expect(true).toBe(true)
  })

  it('should handle rapid mount/unmount cycles without errors', async () => {
    // Mock successful fetch
    mockFetch.mockImplementation(() =>
      Promise.resolve({
        ok: true,
        json: () =>
          Promise.resolve({
            conversation: { id: 'thread_test123', title: 'Test' },
            messages: [],
            pagination: { hasMore: false, limit: 50, total: 0 },
          }),
      })
    )

    const { rerender, unmount } = render(
      <TestComponent conversationId="thread_test123" />
    )

    // Simulate rapid mount/unmount cycles
    for (let i = 0; i < 3; i++) {
      rerender(
        <TestComponent conversationId="thread_test123" shouldUnmount={true} />
      )
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 10))
      })
      rerender(<TestComponent conversationId="thread_test123" />)
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 10))
      })
    }

    // Final unmount
    unmount()

    // Wait for cleanup
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 50))
    })

    // The test passes if no errors are thrown during rapid cycles
    expect(true).toBe(true)
  })

  it('should delay initial load to prevent race conditions', async () => {
    const startTime = Date.now()
    let fetchCallTime = 0

    // Mock fetch to record when it's called
    mockFetch.mockImplementation(() => {
      fetchCallTime = Date.now()
      return Promise.resolve({
        ok: true,
        json: () =>
          Promise.resolve({
            conversation: { id: 'thread_test123', title: 'Test' },
            messages: [],
            pagination: { hasMore: false, limit: 50, total: 0 },
          }),
      })
    })

    render(<TestComponent conversationId="thread_test123" />)

    // Wait for the fetch to be called
    await waitFor(
      () => {
        expect(mockFetch).toHaveBeenCalled()
      },
      { timeout: 300 } // Reduced from 1000ms to 300ms
    )

    // Verify that there was a delay before the fetch call
    const delay = fetchCallTime - startTime
    expect(delay).toBeGreaterThanOrEqual(40) // Should be at least 50ms delay minus some tolerance
  })
})
