import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import type { ButtonProps } from '@/components/ui/button'
import { cn } from '@/lib/utils'

// Base loading states that buttons can have
type LoadingState = 'idle' | 'loading' | 'success' | 'error'

// Step-based button configuration (for ActionButtons pattern)
type StepConfig = {
  stepNumber: number
  stepText: string
  colorScheme: 'green' | 'blue' | 'purple' | 'red'
}

// Icon configuration for buttons
type IconConfig = {
  icon: React.ReactNode
  position?: 'left' | 'right'
}

type BaseButtonProps = Omit<ButtonProps, 'children' | 'variant'> & {
  // Content
  children: React.ReactNode
  loadingText?: string

  // State management
  loadingState?: LoadingState
  disabled?: boolean

  // Styling variants
  variant?: 'default' | 'gradient' | 'step' | 'research'
  colorScheme?: 'green' | 'blue' | 'purple' | 'red' | 'gray'

  // Step-based button (ActionButtons pattern)
  stepConfig?: StepConfig

  // Icon support
  iconConfig?: IconConfig

  // Research button pattern
  isCompact?: boolean

  // Enhanced interactions
  enableHoverEffects?: boolean
  enableClickAnimation?: boolean

  // Event handlers
  onClick?: (e: React.MouseEvent) => void
  onMouseEnter?: (e: React.MouseEvent) => void
  onMouseLeave?: (e: React.MouseEvent) => void
}

/**
 * BaseButton - Unified button component that abstracts common patterns
 *
 * Features:
 * - Consistent loading states with spinner
 * - Multiple styling variants (gradient, step-based, research)
 * - Built-in hover and click animations
 * - Icon support with positioning
 * - Step number display for multi-step flows
 * - Accessible disabled states
 */
const BaseButton: React.FC<BaseButtonProps> = ({
  children,
  loadingText = 'Loading...',
  loadingState = 'idle',
  disabled = false,
  variant = 'default',
  colorScheme = 'blue',
  stepConfig,
  iconConfig,
  isCompact = false,
  enableHoverEffects = true,
  enableClickAnimation = true,
  onClick,
  onMouseEnter,
  onMouseLeave,
  className = '',
  ...buttonProps
}) => {
  const isLoading = loadingState === 'loading'
  const isDisabled = disabled || isLoading

  // Color scheme mappings
  const colorSchemes = {
    green: {
      gradient:
        'from-green-500 to-green-600 hover:from-green-600 hover:to-green-700',
      solid: 'bg-green-500 hover:bg-green-600',
      text: 'text-green-600',
      ring: 'ring-green-500',
    },
    blue: {
      gradient:
        'from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700',
      solid: 'bg-blue-500 hover:bg-blue-600',
      text: 'text-blue-600',
      ring: 'ring-blue-500',
    },
    purple: {
      gradient:
        'from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700',
      solid: 'bg-purple-500 hover:bg-purple-600',
      text: 'text-purple-600',
      ring: 'ring-purple-500',
    },
    red: {
      gradient: 'from-red-500 to-red-600 hover:from-red-600 hover:to-red-700',
      solid: 'bg-red-500 hover:bg-red-600',
      text: 'text-red-600',
      ring: 'ring-red-500',
    },
    gray: {
      gradient:
        'from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700',
      solid: 'bg-gray-500 hover:bg-gray-600',
      text: 'text-gray-600',
      ring: 'ring-gray-500',
    },
  }

  // Generate button classes based on variant and state
  const getButtonClasses = (): string => {
    const colors = colorSchemes[colorScheme]

    return cn(
      'font-medium transition-all duration-200',
      isCompact ? 'h-8 px-3 text-sm' : 'h-10',
      {
        'shadow-lg hover:shadow-xl transform hover:scale-[1.02]':
          enableHoverEffects && !isDisabled,
        'active:scale-[0.98]': enableClickAnimation && !isDisabled,
        'bg-slate-200 text-slate-400 cursor-not-allowed hover:bg-slate-200':
          isDisabled,
      },
      !isDisabled && {
        'bg-gradient-to-r text-white':
          variant === 'gradient' || variant === 'step',
        [colors.gradient]: variant === 'gradient' || variant === 'step',
        [`${colors.solid} text-white hover:${colors.ring} hover:ring-2`]:
          variant === 'research',
      }
    )
  }

  // Loading spinner component
  const LoadingSpinner = () => (
    <svg
      className="animate-spin h-5 w-5"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      />
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  )

  // Step number badge for step variant
  const StepBadge = ({
    number,
    colorScheme,
  }: {
    number: number
    colorScheme: string
  }) => (
    <span
      className={cn(
        'rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold',
        `bg-white text-${colorScheme}-600`
      )}
    >
      {number}
    </span>
  )

  // Render button content based on state and configuration
  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center space-x-2">
          <LoadingSpinner />
          <span>{loadingText}</span>
        </div>
      )
    }

    const content = (
      <>
        {/* Left icon */}
        {iconConfig?.position === 'left' && iconConfig.icon}

        {/* Step badge for step variant */}
        {variant === 'step' && stepConfig && (
          <StepBadge number={stepConfig.stepNumber} colorScheme={colorScheme} />
        )}

        {/* Main content */}
        <span>{children}</span>

        {/* Right icon */}
        {iconConfig?.position === 'right' && iconConfig.icon}
      </>
    )

    return (variant === 'step' && stepConfig) || iconConfig ? (
      <div className="flex items-center space-x-2">{content}</div>
    ) : (
      content
    )
  }

  return (
    <Button
      className={cn(getButtonClasses(), className)}
      disabled={isDisabled}
      onClick={onClick}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      {...buttonProps}
    >
      {renderContent()}
    </Button>
  )
}

export default BaseButton
