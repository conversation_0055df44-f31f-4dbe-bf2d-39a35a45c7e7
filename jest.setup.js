// Optional: configure or set up a testing framework before each test.
// If you delete this file, remove `setupFilesAfterEnv` from `jest.config.js`

import '@testing-library/jest-dom'

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(() => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
  })),
  useSearchParams: jest.fn(() => new URLSearchParams()),
  usePathname: jest.fn(() => '/'),
}))

// Mock next-auth useSession with a jest.fn to allow tests to override
jest.mock('next-auth/react', () => {
  const useSession = jest.fn(() => ({
    data: { user: { subscription_tier: 'FREE' } },
  }))
  return {
    __esModule: true,
    useSession,
    SessionProvider: props => props.children,
  }
})

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  observe() {
    return null
  }
  disconnect() {
    return null
  }
  unobserve() {
    return null
  }
}

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  observe() {
    return null
  }
  disconnect() {
    return null
  }
  unobserve() {
    return null
  }
}

// Mock TextEncoder/TextDecoder for Node.js environment
if (typeof global.TextEncoder === 'undefined') {
  global.TextEncoder = require('util').TextEncoder
}
if (typeof global.TextDecoder === 'undefined') {
  global.TextDecoder = require('util').TextDecoder
}

// Mock Web Streams API for AI SDK 5 compatibility
if (typeof global.TransformStream === 'undefined') {
  global.TransformStream = class TransformStream {
    constructor() {
      this.readable = new ReadableStream()
      this.writable = new WritableStream()
    }
  }
}

if (typeof global.WritableStream === 'undefined') {
  global.WritableStream = class WritableStream {
    constructor() {}
    getWriter() {
      return {
        write: jest.fn(),
        close: jest.fn(),
        abort: jest.fn(),
      }
    }
  }
}

if (typeof global.structuredClone === 'undefined') {
  global.structuredClone = val => JSON.parse(JSON.stringify(val))
}

// Suppress verbose console.log output during tests while keeping errors/warnings
const originalConsoleLog = console.log
const originalConsoleWarn = console.warn
const originalConsoleError = console.error

beforeAll(() => {
  // Suppress ALL console.log output during tests - only show in CI or when explicitly needed
  console.log = (...args) => {
    // Completely suppress console.log in tests unless it's a critical error
    // This eliminates all the verbose "● Console" sections
    return
  }

  // Suppress ALL console.warn output during tests unless it's a critical issue
  console.warn = (...args) => {
    const message = args[0]?.toString() || ''
    // Only show warnings that indicate actual test failures or critical issues
    if (
      message.includes('FAIL') ||
      message.includes('Test failed') ||
      message.includes('Timeout') ||
      message.includes('Memory leak') ||
      message.includes('Critical')
    ) {
      originalConsoleWarn.apply(console, args)
    }
    // Suppress all other warnings - they're not being read anyway
  }

  // Suppress ALL console.error output during tests unless it's truly critical
  console.error = (...args) => {
    const message = args[0]?.toString() || ''
    // Only show errors that indicate actual test failures or critical system issues
    if (
      message.includes('Test failed') ||
      message.includes('FATAL') ||
      message.includes('Cannot resolve module') ||
      message.includes('Module not found') ||
      message.includes('Syntax error') ||
      message.includes('Jest worker')
    ) {
      originalConsoleError.apply(console, args)
    }
    // Suppress all expected/operational errors - they're not being read anyway
  }
})

afterAll(() => {
  // Restore original console methods
  console.log = originalConsoleLog
  console.warn = originalConsoleWarn
  console.error = originalConsoleError
})

// Clean up PostHog timers after each test to prevent resource leaks
afterEach(() => {
  // Clean up PostHog initialization timer if it exists
  if (typeof window !== 'undefined' && window['__postHogCleanup']) {
    window['__postHogCleanup']()
  }
})

// Mock Request/Response for Next.js server components
global.Request = global.Request || class Request {}
global.Response = global.Response || class Response {}
global.Headers = global.Headers || class Headers {}
global.ReadableStream =
  global.ReadableStream ||
  class ReadableStream {
    constructor() {}
  }

// Mock crypto-js with different hashes for different inputs
jest.mock('crypto-js', () => ({
  SHA256: jest.fn(input => {
    // Simple hash simulation - different inputs get different hashes
    // Create a simple hash based on input string
    let hash = 0
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i)
      hash = (hash << 5) - hash + char
      hash = hash & hash // Convert to 32bit integer
    }

    // Convert to hex-like string
    const hexHash = Math.abs(hash).toString(16).padStart(16, '0')
    const fullHash = hexHash + hexHash // Double for longer hash

    return {
      toString: jest.fn(() => fullHash),
    }
  }),
  enc: {
    Hex: 'hex',
  },
}))

// Mock nanoid with unique counter
let nanoidCounter = 0
jest.mock('nanoid', () => ({
  nanoid: jest.fn(() => `mockNanoId${nanoidCounter++}`),
  customAlphabet: jest.fn(() => {
    let counter = 0
    return jest.fn(() => `mockCustomId${counter++}`)
  }),
}))

// Mock unified and remark
jest.mock('unified', () => ({
  unified: jest.fn(() => ({
    use: jest.fn(() => ({
      parse: jest.fn(() => ({
        children: [],
      })),
    })),
  })),
}))

jest.mock('remark-parse', () => jest.fn())

// Mock @tiptap/html to avoid zeed-dom ES module issues
jest.mock('@tiptap/html', () => ({
  generateJSON: jest.fn((html, extensions) => ({
    type: 'doc',
    content: [
      {
        type: 'paragraph',
        content: [{ type: 'text', text: html }],
      },
    ],
  })),
}))

// Mock highlight.js and lowlight to avoid ES module issues
jest.mock('highlight.js', () => ({}))
jest.mock('lowlight', () => ({
  lowlight: {
    register: jest.fn(),
    highlight: jest.fn(() => ({ value: '' })),
  },
}))

// Mock tiptap extensions that use ES modules
jest.mock('@tiptap/extension-code-block-lowlight', () => jest.fn())
jest.mock('@tiptap/extension-highlight', () => jest.fn())

// Mock mermaid to avoid ES module issues
jest.mock('mermaid', () => ({
  default: {
    initialize: jest.fn(),
    render: jest.fn(),
  },
}))

// Mock the entire editor extensions to avoid complex ES module chains
jest.mock('@/app/components/editor/extensions', () => ({
  TiptapExtensions: [],
}))

// Mock PostHog to prevent initialization in tests
jest.mock('posthog-js', () => ({
  __esModule: true,
  default: {
    init: jest.fn(),
    capture: jest.fn(),
    identify: jest.fn(),
    setPersonProperties: jest.fn(),
    reset: jest.fn(),
  },
}))

// Mock instrumentation-client to prevent timer creation
jest.mock('@/instrumentation-client', () => ({
  ensurePostHogInitialized: jest.fn(() => false),
  isPostHogReady: jest.fn(() => false),
}))

// Mock editor utils to avoid import issues
jest.mock('@/app/components/editor/utils', () => ({
  getInitialContent: jest.fn(() => ({ type: 'doc', content: [] })),
  processMessageContent: jest.fn(() => ({ type: 'doc', content: [] })),
  replaceMermaidTags: jest.fn(html => html),
}))

// Mock AI SDK core modules to prevent TransformStream issues
jest.mock('ai', () => ({
  streamText: jest.fn(),
  createUIMessageStream: jest.fn(),
  DefaultChatTransport: jest.fn(),
}))

jest.mock('@ai-sdk/azure', () => ({
  azure: jest.fn(),
}))
