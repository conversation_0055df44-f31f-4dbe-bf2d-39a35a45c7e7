/*
 * SIMULATOR ROUTE - TEMPORARILY DISABLED
 * This route has been commented out as part of AI SDK cleanup.
 * The code is preserved for potential future use.
 * To re-enable: remove this comment block.
 */

/*
import { streamText } from 'ai'
import { MockLanguageModelV2 } from 'ai/test'
import { NextResponse } from 'next/server'
import { DragTreeNodeContentStatus } from '@prisma/client'

export const maxDuration = 60

export type ResearchGenerateSimulatorRequest = {
  contentId: string
  questionText: string
  researchType?: string
}

// Mock research content for simulation
const getMockResearchContent = (questionText: string): string => {
  return `# Research Results for: ${questionText}

## Overview
Based on the research question "${questionText}", here are the key findings and insights:

## Key Findings
1. **Primary Research Area**: This question falls into the domain of strategic analysis and requires comprehensive investigation.

2. **Market Context**: Current market conditions show significant potential for growth in this area, with several key factors driving development.

3. **Technical Considerations**:
   - Implementation complexity: Medium to High
   - Resource requirements: Moderate
   - Timeline considerations: 3-6 months typical

## Detailed Analysis
The question you've posed touches on several important aspects that require careful consideration:

### Immediate Opportunities
- Quick wins can be achieved through focused efforts
- Low-hanging fruit exists in the current market
- Competitive advantages can be established early

### Long-term Strategic Implications
- Market positioning will be crucial for sustainable growth
- Technology adoption patterns suggest favorable conditions
- Regulatory environment appears stable and supportive

## Recommendations
1. **Phase 1**: Conduct preliminary market research and stakeholder analysis
2. **Phase 2**: Develop minimum viable solution with core features
3. **Phase 3**: Scale and optimize based on user feedback and market response

## Risk Assessment
- **Low Risk**: Market acceptance and user adoption
- **Medium Risk**: Technical implementation challenges
- **High Risk**: Competitive response and market timing

## Next Steps
To move forward effectively, consider:
- Gathering additional stakeholder input
- Conducting user research and validation
- Developing detailed implementation roadmap
- Establishing success metrics and KPIs

*This research was generated to help inform your decision-making process. Please validate key assumptions and findings through additional research as needed.*`
}

export async function POST(req: Request) {
  try {
    const requestBody = await req.json()

    // Handle both chat message format and direct format
    let contentId: string
    let questionText: string
    let researchType: string = 'QUICK_RESEARCH'

    if (requestBody.messages) {
      // Chat message format (from useChat)
      const userMessage = requestBody.messages.find(
        (msg: any) => msg.role === 'user'
      )
      if (!userMessage) {
        return NextResponse.json(
          { error: 'No user message found in chat format' },
          { status: 400 }
        )
      }

      // Extract data from message content or check for body parameters
      if (requestBody.contentId && requestBody.questionText) {
        // Data provided in body alongside messages
        contentId = requestBody.contentId
        questionText = requestBody.questionText
        researchType = requestBody.researchType || 'QUICK_RESEARCH'
      } else {
        // Try to parse from message content (JSON format)
        try {
          const parsedContent = JSON.parse(userMessage.content)
          contentId = parsedContent.contentId
          questionText =
            parsedContent.questionText ||
            parsedContent.question ||
            userMessage.content
          researchType = parsedContent.researchType || 'QUICK_RESEARCH'
        } catch {
          // Plain text message - need contentId from somewhere else
          return NextResponse.json(
            { error: 'Missing contentId in chat message format' },
            { status: 400 }
          )
        }
      }
    } else {
      // Direct format (original)
      const directRequest = requestBody as ResearchGenerateSimulatorRequest
      contentId = directRequest.contentId
      questionText = directRequest.questionText
      researchType = directRequest.researchType || 'QUICK_RESEARCH'
    }

    if (!contentId || !questionText) {
      return NextResponse.json(
        { error: 'Missing required fields: contentId, questionText' },
        { status: 400 }
      )
    }

    console.log(
      `🧪 [Research Simulator] Starting research for content: ${contentId}`
    )

    // Update content status to PROCESSING
    const prisma = (await import('@/app/libs/prismadb')).default
    await prisma.dragTreeNodeContent.update({
      where: { id: contentId },
      data: {
        status: DragTreeNodeContentStatus.PROCESSING,
        generation_metadata: {
          researchType,
          originalQuestion: questionText,
          model: 'simulator',
          startedAt: new Date().toISOString(),
        },
      },
    })

    // Get mock research content
    const completeResponse = getMockResearchContent(questionText)

    // Split into smaller chunks for slower, more granular streaming
    const words = completeResponse.split(' ')
    const chunks: string[] = []

    // Create chunks of 2-3 words each for slower streaming
    for (let i = 0; i < words.length; i += 2) {
      const chunk = words.slice(i, i + 2).join(' ')
      chunks.push(chunk)
    }

    // Create mock model that streams sentence by sentence
    const mockModel = new MockLanguageModelV2({
      doStream: async () => {
        const streamChunks: Array<{
          type: 'text' | 'finish'
          textDelta?: string
          finishReason?: 'stop'
          usage?: { promptTokens: number; completionTokens: number }
        }> = []

        // First chunk
        if (chunks.length > 0) {
          streamChunks.push({
            type: 'text' as const,
            textDelta: chunks[0],
          })
        }

        // Subsequent chunks with space
        for (let i = 1; i < chunks.length; i++) {
          streamChunks.push({
            type: 'text' as const,
            textDelta: ' ' + chunks[i],
          })
        }

        // Final finish chunk
        streamChunks.push({
          type: 'finish' as const,
          finishReason: 'stop' as const,
          usage: {
            promptTokens: 100,
            completionTokens: chunks.length * 5,
          },
        })

        // Create readable stream from chunks
        const stream = new ReadableStream({
          start(controller) {
            let index = 0
            const pushChunk = () => {
              if (index < streamChunks.length) {
                // Add delay between chunks to simulate slower streaming (3 seconds total)
                const totalChunks = streamChunks.length - 1 // Exclude finish chunk
                const totalDuration = 3000 // 3 seconds
                const delayPerChunk = totalDuration / Math.max(totalChunks, 1)

                setTimeout(
                  () => {
                    controller.enqueue(streamChunks[index])
                    index++
                    pushChunk()
                  },
                  index === 0 ? 50 : delayPerChunk
                ) // Minimal initial delay (50ms), then evenly spaced over 3 seconds
              } else {
                controller.close()
              }
            }
            pushChunk()
          },
        })

        return {
          stream,
          rawCall: { rawPrompt: questionText, rawSettings: {} },
        }
      },
    })

    // Use Vercel AI SDK streamText with onFinish callback
    const result = streamText({
      model: mockModel,
      messages: [
        {
          role: 'system',
          content: `Generate comprehensive research for: ${questionText}`,
        },
      ],
      onFinish: async result => {
        try {
          console.log(
            `✅ [Research Simulator] Completed research for content: ${contentId}`
          )

          // Final update to set the content as ACTIVE
          await prisma.dragTreeNodeContent.update({
            where: { id: contentId },
            data: {
              content_text: result.text,
              status: DragTreeNodeContentStatus.ACTIVE,
              messages: [
                {
                  role: 'assistant',
                  content: result.text,
                },
              ],
              generation_metadata: {
                researchType,
                originalQuestion: questionText,
                model: 'simulator',
                startedAt: new Date().toISOString(),
                completedAt: new Date().toISOString(),
                tokenUsage: result.usage,
              },
            },
          })

          console.log(
            `🎉 [Research Simulator] Successfully saved research to DB for content: ${contentId}`
          )
        } catch (error) {
          console.error(`💥 [Research Simulator] Error saving to DB:`, error)

          // Mark as failed if DB update fails
          await prisma.dragTreeNodeContent.update({
            where: { id: contentId },
            data: {
              status: DragTreeNodeContentStatus.INACTIVE,
              generation_metadata: {
                error: error instanceof Error ? error.message : 'Unknown error',
                failedAt: new Date().toISOString(),
              },
            },
          })
        }
      },
    })

    return result.toUIMessageStreamResponse()
  } catch (error) {
    console.error('❌ [Research Simulator] Error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
*/
