#!/usr/bin/env node

/**
 * Test script to set user subscription tiers for testing
 * Usage: node scripts/test-subscription.js <email> <tier>
 *
 * Examples:
 * node scripts/test-subscription.js <EMAIL> PRO
 * node scripts/test-subscription.js <EMAIL> FREE
 * node scripts/test-subscription.js <EMAIL> GUEST
 */

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function setUserTier(email, tier) {
  try {
    console.log(`🔄 Setting user ${email} to ${tier} tier...`)

    const validTiers = ['FREE', 'PRO', 'GUEST', 'ULTRA', 'BUSINESS']
    if (!validTiers.includes(tier)) {
      throw new Error(
        `Invalid tier: ${tier}. Valid tiers: ${validTiers.join(', ')}`
      )
    }

    let updateData = {
      subscription_tier: tier,
      subscription_cancel_pending: false,
    }

    if (tier === 'FREE') {
      updateData = {
        ...updateData,
        subscription_end_date: null,
        subscription_customer_id: null,
        subscription_id: null,
        subscription_service: null,
      }
    } else if (tier === 'PRO') {
      // Set PRO with 1 month expiry
      const oneMonthFromNow = new Date()
      oneMonthFromNow.setMonth(oneMonthFromNow.getMonth() + 1)

      updateData = {
        ...updateData,
        subscription_end_date: oneMonthFromNow,
        subscription_service: 'STRIPE',
        subscription_customer_id: `test_customer_${Date.now()}`,
        subscription_id: `test_sub_${Date.now()}`,
      }
    } else if (tier === 'GUEST') {
      // GUEST doesn't need expiry date
      updateData = {
        ...updateData,
        subscription_end_date: null,
        subscription_customer_id: null,
        subscription_id: null,
        subscription_service: null,
      }
    }

    const result = await prisma.users.update({
      where: { email },
      data: updateData,
    })

    console.log(`✅ Successfully updated user ${email}:`)
    console.log(`   - Tier: ${result.subscription_tier}`)
    console.log(`   - End Date: ${result.subscription_end_date || 'N/A'}`)
    console.log(`   - Cancel Pending: ${result.subscription_cancel_pending}`)

    console.log('\n🔄 Please refresh your browser to see the changes!')
  } catch (error) {
    console.error('❌ Error updating user:', error.message)
  } finally {
    await prisma.$disconnect()
  }
}

// Get command line arguments
const args = process.argv.slice(2)
if (args.length !== 2) {
  console.log('Usage: node scripts/test-subscription.js <email> <tier>')
  console.log('Example: node scripts/test-subscription.js <EMAIL> PRO')
  console.log('Valid tiers: FREE, PRO, GUEST, ULTRA, BUSINESS')
  process.exit(1)
}

const [email, tier] = args
setUserTier(email, tier.toUpperCase())
