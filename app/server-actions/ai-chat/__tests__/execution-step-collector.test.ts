/**
 * @jest-environment node
 */
import { ExecutionStepCollector } from '../execution-step-collector'

describe('ExecutionStepCollector', () => {
  let collector: ExecutionStepCollector

  beforeEach(() => {
    collector = new ExecutionStepCollector()
  })

  describe('Raw Provider Data API', () => {
    it('should add steps with raw provider data', () => {
      // Simulate AI SDK 5 tool call step
      collector.addStep({
        providerType: 'tool-call',
        metadata: {
          raw: {
            type: 'tool-call',
            toolCallId: 'call_123',
            toolName: 'web_search',
            args: { query: 'test query' },
          },
        },
      })

      // Simulate AI SDK 5 tool result step
      collector.addStep({
        providerType: 'tool-result',
        metadata: {
          raw: {
            type: 'tool-result',
            toolCallId: 'call_123',
            toolName: 'web_search',
            result: { results: ['result1', 'result2'] },
          },
        },
      })

      const steps = collector.getSteps()
      expect(steps).toHaveLength(2)

      expect(steps[0].type).toBe('tool-call')
      expect(steps[0].stepOrder).toBe(0)
      expect(steps[0].metadata.raw.toolName).toBe('web_search')
      expect(steps[0].metadata.raw.args).toEqual({ query: 'test query' })

      expect(steps[1].type).toBe('tool-result')
      expect(steps[1].stepOrder).toBe(1)
      expect(steps[1].metadata.raw.result).toEqual({
        results: ['result1', 'result2'],
      })
    })

    it('should handle parallel steps', () => {
      collector.addStep({
        providerType: 'tool-call',
        metadata: { raw: { type: 'tool-call', toolName: 'tool1' } },
        parallelKey: 'batch-1',
      })

      collector.addStep({
        providerType: 'tool-call',
        metadata: { raw: { type: 'tool-call', toolName: 'tool2' } },
        parallelKey: 'batch-1',
      })

      const steps = collector.getSteps()
      expect(steps).toHaveLength(2)
      expect(steps[0].parallelKey).toBe('batch-1')
      expect(steps[1].parallelKey).toBe('batch-1')
    })

    it('should handle hierarchical steps', () => {
      collector.addStep({
        providerType: 'reasoning',
        metadata: { raw: { type: 'reasoning', content: 'parent step' } },
      })

      collector.addStep({
        providerType: 'sub-reasoning',
        metadata: { raw: { type: 'sub-reasoning', content: 'child step' } },
        parentStepId: 'parent-123',
      })

      const steps = collector.getSteps()
      expect(steps).toHaveLength(2)
      expect(steps[1].parentStepId).toBe('parent-123')
    })
  })

  describe('Analytics Methods', () => {
    it('should group steps by type', () => {
      collector.addStep({
        providerType: 'tool-call',
        metadata: { raw: { toolName: 'tool1' } },
      })

      collector.addStep({
        providerType: 'tool-call',
        metadata: { raw: { toolName: 'tool2' } },
      })

      collector.addStep({
        providerType: 'text-delta',
        metadata: { raw: { delta: 'hello' } },
      })

      const stepsByType = collector.getStepsByType()
      expect(stepsByType['tool-call']).toHaveLength(2)
      expect(stepsByType['text-delta']).toHaveLength(1)
    })

    it('should return step count', () => {
      expect(collector.getStepCount()).toBe(0)

      collector.addStep({
        providerType: 'test',
        metadata: { raw: {} },
      })

      expect(collector.getStepCount()).toBe(1)
    })

    it('should clear all steps', () => {
      collector.addStep({
        providerType: 'test',
        metadata: { raw: {} },
      })

      expect(collector.getStepCount()).toBe(1)

      collector.clear()

      expect(collector.getStepCount()).toBe(0)
      expect(collector.getSteps()).toHaveLength(0)
    })
  })

  describe('Step Ordering', () => {
    it('should maintain correct step order', () => {
      for (let i = 0; i < 5; i++) {
        collector.addStep({
          providerType: `step-${i}`,
          metadata: { raw: { order: i } },
        })
      }

      const steps = collector.getSteps()
      expect(steps).toHaveLength(5)

      steps.forEach((step, index) => {
        expect(step.stepOrder).toBe(index)
        expect(step.type).toBe(`step-${index}`)
      })
    })
  })
})
