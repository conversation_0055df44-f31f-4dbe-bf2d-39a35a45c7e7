'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import type { UIMessage } from 'ai'
import { ENABLE_CHAT_DEBUG_LOGGING } from '@/app/configs/feature-flags'

/**
 * Convert database messages to UIMessage format
 * Uses proper role lowercasing and parts array structure as per AI SDK v5
 */
function toUIMessages(dbMessages: any[]): UIMessage[] {
  return (dbMessages || [])
    .filter(msg => msg.role !== 'SYSTEM') // Filter out system messages
    .map(msg => ({
      id: msg.id || `msg_${Date.now()}_${Math.random()}`,
      role: (msg.role || '').toLowerCase() as 'user' | 'assistant',
      parts: [{ type: 'text', text: msg.content }],
    }))
}

export type ConversationMeta = {
  id: string
  title?: string
  createdAt: string
  updatedAt: string
  metadata?: any
}

export type UseConversationHistoryOptions = {
  conversationId?: string
  enabled?: boolean
  limit?: number
  includeSteps?: boolean
}

export type UseConversationHistoryReturn = {
  messages: UIMessage[] | null
  conversationMeta: ConversationMeta | null
  isLoading: boolean
  error: string | null
  refresh: (() => Promise<void>) | null
}

/**
 * Hook for fetching and managing conversation history separately from streaming.
 *
 * Handles:
 * - Loading existing messages from the conversation API
 * - Converting database messages to UIMessage format
 * - Providing refresh functionality for post-stream updates
 * - Proper cleanup and abort handling
 */
export function useConversationHistory({
  conversationId,
  enabled = true,
  limit = 50,
  includeSteps = false,
}: UseConversationHistoryOptions): UseConversationHistoryReturn {
  const [messages, setMessages] = useState<UIMessage[] | null>(null)
  const [conversationMeta, setConversationMeta] =
    useState<ConversationMeta | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)

  const isMountedRef = useRef(true)
  const abortControllerRef = useRef<AbortController | null>(null)

  // Load conversation history with defensive error handling
  const loadHistory = useCallback(async () => {
    if (!conversationId || !enabled) {
      setMessages(null)
      setConversationMeta(null)
      setIsLoading(false)
      setError(null)
      return
    }

    let aborted = false
    const abortController = new AbortController()

    // Abort any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    abortControllerRef.current = abortController

    setIsLoading(true)
    setError(null)

    try {
      // Build API URL with query parameters
      const url = new URL(
        `/api/aipane/conversations/${conversationId}`,
        window.location.origin
      )
      url.searchParams.set('limit', limit.toString())
      url.searchParams.set('includeSteps', includeSteps.toString())

      if (ENABLE_CHAT_DEBUG_LOGGING) {
        console.log('📚 [useConversationHistory] Loading history:', {
          conversationId,
          limit,
          includeSteps,
        })
      }

      const response = await fetch(url.toString(), {
        signal: abortController.signal,
      })

      if (response.status === 404) {
        // Conversation doesn't exist yet - this is normal for new conversations
        if (!aborted && isMountedRef.current) {
          setMessages([])
          setConversationMeta(null)
        }

        if (ENABLE_CHAT_DEBUG_LOGGING) {
          console.log(
            '📚 [useConversationHistory] Conversation not found (new conversation)'
          )
        }
        return
      }

      if (response.status === 429) {
        // Rate limited - retry after delay
        const retryAfter = Number(response.headers.get('Retry-After') || '1')
        if (ENABLE_CHAT_DEBUG_LOGGING) {
          console.log(
            `📚 [useConversationHistory] Rate limited, retrying in ${retryAfter}s`
          )
        }
        setTimeout(() => {
          if (!aborted && isMountedRef.current) {
            loadHistory()
          }
        }, retryAfter * 1000)
        return
      }

      if (!response.ok) {
        throw new Error(`Failed to load conversation: ${response.status}`)
      }

      const data = await response.json()

      // Convert database messages to UIMessage format using the utility function
      const uiMessages = toUIMessages(data.messages)

      if (!aborted && isMountedRef.current) {
        setMessages(uiMessages)
        setConversationMeta(data.conversation || null)
      }

      if (ENABLE_CHAT_DEBUG_LOGGING) {
        console.log(
          `📚 [useConversationHistory] Loaded ${uiMessages.length} messages for ${conversationId}`
        )
      }
    } catch (err: any) {
      if (err.name === 'AbortError') {
        // Request was aborted, don't update state
        return
      }

      console.error('❌ [useConversationHistory] Failed to load history:', err)

      if (!aborted && isMountedRef.current) {
        setError(err.message || 'Failed to load conversation history')
        setMessages(null)
        setConversationMeta(null)
      }
    } finally {
      // CRITICAL: Always clear loading state to prevent forever loading
      if (!aborted && isMountedRef.current) {
        setIsLoading(false)
      }
    }
  }, [conversationId, enabled, limit, includeSteps])

  // Load history when conversation ID or options change
  useEffect(() => {
    loadHistory()
  }, [loadHistory])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [])

  // Return null refresh function if not enabled
  const refresh = enabled ? loadHistory : null

  return {
    messages,
    conversationMeta,
    isLoading,
    error,
    refresh,
  }
}
