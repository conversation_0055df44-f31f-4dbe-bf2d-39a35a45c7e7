import { Montser<PERSON> } from "next/font/google";
import Image from "next/image";
import Link from "next/link";

import { cn } from "@/lib/utils";
import AuthForm from "@/app/(landing)/components/AuthForm";

const font = Montserrat({ weight: "600", subsets: ["latin"] });

export const LandingNavbar = () => {
  return (
    <nav className="p-4 flex items-center justify-between w-full">
      <Link href="/" className="flex items-center">
        <div className="relative h-8 w-8 mr-4 flex items-center justify-center">
          <Image src="/images/icon.ico" alt="Logo" width={32} height={32} />
        </div>

        <h1 className={cn("text-2xl font-bold text-white", font.className)}>
          Clarify AI
        </h1>
      </Link>
      {/* <a
        href="https://www.producthunt.com/posts/clarify-ai?utm_source=badge-featured&utm_medium=badge&utm_souce=badge-clarify&#0045;ai"
        target="_blank"
      >
        <img
          src="https://api.producthunt.com/widgets/embed-image/v1/featured.svg?post_id=421464&theme=light"
          alt="Clarify&#0032;AI - Transform&#0032;vague&#0032;ideas&#0032;into&#0032;structured&#0032;insights | Product Hunt"
          style={{ width: "250px", height: "54px" }} // Changed this line
          width="250"
          height="54"
        />
      </a> */}
      <AuthForm />
    </nav>
  );
};
