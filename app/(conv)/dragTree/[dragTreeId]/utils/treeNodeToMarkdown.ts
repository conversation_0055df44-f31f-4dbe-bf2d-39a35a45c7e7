import { TreeNode } from "@/app/types";

/**
 * Converts a TreeNode structure back to markdown format
 * @param node The TreeNode to convert
 * @param level The current heading level (defaults to 1)
 * @returns A string containing the markdown representation
 */
const treeNodeToMarkdown = (node: TreeNode, level: number = 1): string => {
  // Skip the root node's label since it's typically just a container
  if (node.id === "root") {
    return node.children
      .map((child) => treeNodeToMarkdown(child, level))
      .join("\n");
  }

  // Create heading with appropriate number of #
  const heading = "#".repeat(level) + " " + node.label;

  // If this is a leaf node (question), just return the heading
  if (node.type === "question") {
    return heading;
  }

  // For categories, process children and add them with an extra level of depth
  const childrenMarkdown = node.children
    .map((child) => treeNodeToMarkdown(child, level + 1))
    .join("\n");

  // Return heading followed by children's markdown
  return heading + "\n" + childrenMarkdown;
};

export default treeNodeToMarkdown;
