import React from 'react'
import { AlertDialog, AlertDialogTrigger } from '@/components/ui/alert-dialog'
import { Badge } from '@/components/ui/badge'
import { Conversation, ConversationStatus } from '@prisma/client'
import { AnalysisPromptDialog } from './AnalysisPromptDialog'
import { useRouter } from 'next/navigation'

interface ConversationStatusGuideProps {
  conversation: Omit<Conversation, 'is_hidden' | 'config'>
  onFinish: (
    conversation: Omit<Conversation, 'is_hidden' | 'config'>,
    prompt: string
  ) => void
  showFinish: boolean
  analysisPrompt?: string
  setShowProgressBar: () => void
  conversationStatus: ConversationStatus
}

const ConversationStatusGuide: React.FC<ConversationStatusGuideProps> = ({
  conversation,
  onFinish,
  showFinish,
  analysisPrompt,
  setShowProgressBar,
  conversationStatus,
}) => {
  const router = useRouter()
  return (
    <>
      {/* Converation guide when conversation status is active */}
      {conversationStatus === ConversationStatus.ACTIVE && (
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Badge
              className={`flex flex-col justify-center items-center text-black bg-purple-200 py-3 px-16 text-base text-center max-w-screen-lg ${
                showFinish ? 'hover:bg-purple-300' : 'hover:bg-purple-200'
              }`}
              onClick={showFinish ? undefined : event => event.preventDefault()}
            >
              {showFinish ? (
                'CLICK HERE when you feel good about the details!'
              ) : (
                <>
                  YOUR TURN to answer the follow-up questions!
                  <br />
                  Providing more context is preferred, you get what you give!
                </>
              )}
            </Badge>
          </AlertDialogTrigger>
          <AnalysisPromptDialog
            onFinish={(conversation, prompt) => onFinish(conversation, prompt)}
            analysisPrompt={analysisPrompt}
            conversation={conversation}
            setShowProgressBar={setShowProgressBar}
          />
        </AlertDialog>
      )}
      {/* Button to the draft link after completion */}
      {(conversationStatus === ConversationStatus.COMPLETED ||
        conversationStatus === ConversationStatus.EXAMPLE) && (
        <Badge
          className="flex flex-col justify-center items-center text-black bg-green-200 py-3 px-16 text-base text-center max-w-screen-lg hover:bg-green-300"
          onClick={() =>
            router.push(`/conversations/${conversation.id}/result`)
          }
        >
          DONE! Click here to view the draft
        </Badge>
      )}
    </>
  )
}

export default ConversationStatusGuide
