import {
  ConversationStatus,
  ConversationType,
  DraftStatus,
  DraftType,
  OpenAIUsageType,
} from '@prisma/client'
import { Position } from 'reactflow'

export type LogAPIUsageDataType = {
  open_ai_usage_type: OpenAIUsageType
  model_name: string
  input_text: string
  output_text: string
  currentUser: any
  conversationId: string
}

export type createDraftType = {
  status: DraftStatus
  type: DraftType
  content: string
  original_content: string
  currentUser: any
  conversation_id: string
  prompt_id: string
  exact_prompt: string
}

export enum NodeType {
  CustomLeafNode = 'customLeafNode',
  CustomInternalNode = 'customInternalNode',
  SubtreeLeafNode = 'subtreeLeafNode',
  SubtreeInternalNode = 'subtreeInternalNode',
}
export type Node = {
  id: string
  // Custom data
  data: {
    label: string
    example?: string
    resolved?: boolean
    skipped?: boolean
    level?: number
    parentId?: string | null
    /**
     * Pre-computed width of the node.  Added for performance so that the layout
     * engine (Dagre/ELK) doesn't need to repeatedly calculate dimensions.
     */
    width?: number
    /**
     * Pre-computed height of the node.  Added for performance so that the layout
     * engine (Dagre/ELK) doesn't need to repeatedly calculate dimensions.
     */
    height?: number
  }
  position: { x: number; y: number }
  sourcePosition?: Position
  targetPosition?: Position
  type?: NodeType | string
}

export type Edge = {
  id: string
  source: string
  target: string
  label?: string
}

export type ServiceProvider = {
  id: string
  display_text: string
  url: string
}

export type ConversationLayoutType = {
  id: string
  creator_id: string
  conversation_status: ConversationStatus
  conversation_type: ConversationType
  created_at: Date
  updated_at: Date
  is_hidden: boolean
  issuetree_updated_at: Date | null
  config_prompt_description: string | null
  title: string | null
}

export type TreeNode = {
  id: string
  label: string
  type: 'category' | 'question' // Keep as string literals for now to avoid breaking changes
  children: TreeNode[]
  isInterestedIn?: boolean
}
