'use client'

import React, { useState } from 'react'
import { User } from '@prisma/client'
import { useRouter } from 'next/navigation'
import { toast } from 'react-hot-toast'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import mixpanel from '@/app/libs/mixpanel'
import { createStripePortal } from '@/lib/stripe/server'
import { FiUser, FiMail, FiCreditCard } from 'react-icons/fi'

type SettingsPageProps = {
  currentUser: User
}

const SettingsPage: React.FC<SettingsPageProps> = ({ currentUser }) => {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState<boolean>(false)

  const showStripePortal = currentUser?.subscription_customer_id

  const handleStripePortalRequest = async () => {
    if (!currentUser?.subscription_customer_id) {
      toast.error('You need to subscribe first to connect to your portal!')
      return
    }

    mixpanel.track('click_manage_subscription')
    setIsLoading(true)
    const redirectUrl = await createStripePortal(currentUser)
    setIsLoading(false)

    if (typeof redirectUrl === 'string') {
      toast.success('Redirecting to Stripe...')
      router.push(redirectUrl)
    } else if (redirectUrl instanceof Error) {
      toast.error(redirectUrl.message)
    }
  }

  return (
    <main className="container mx-auto px-4 py-12 max-w-3xl">
      <h1 className="text-3xl font-bold mb-8 text-center">Account Settings</h1>
      <section className="bg-white rounded-lg shadow-lg p-8 space-y-8">
        <h2 className="text-2xl font-semibold text-gray-800 mb-6 pb-2 border-b">
          Profile Information
        </h2>

        <div className="space-y-6">
          {/* Photo */}
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Image
                width="80"
                height="80"
                className="rounded-full border-4 border-gray-200"
                src={currentUser?.image || '/images/placeholder.jpg'}
                alt="Avatar"
              />
            </div>
            <div>
              <h3 className="font-semibold text-lg">
                {currentUser?.name || 'User Name'}
              </h3>
              <p className="text-gray-500 text-sm">
                {currentUser?.email || '<EMAIL>'}
              </p>
            </div>
          </div>

          {/* Name */}
          <div className="flex items-center space-x-4">
            <FiUser className="text-gray-400 w-6 h-6" />
            <div>
              <label
                htmlFor="name"
                className="block text-sm font-medium text-gray-700"
              >
                Name
              </label>
              <p className="mt-1 text-gray-900">
                {currentUser?.name || '<User Name>'}
              </p>
            </div>
          </div>

          {/* Email */}
          <div className="flex items-center space-x-4">
            <FiMail className="text-gray-400 w-6 h-6" />
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700"
              >
                Email
              </label>
              <p className="mt-1 text-gray-900">
                {currentUser?.email || '<User Email>'}
              </p>
            </div>
          </div>

          {/* Subscription */}
          {showStripePortal && (
            <div className="flex items-start space-x-4">
              <FiCreditCard className="text-gray-400 w-6 h-6 mt-1" />
              <div>
                <label
                  htmlFor="subscription"
                  className="block text-sm font-medium text-gray-700"
                >
                  Subscription
                </label>
                <p className="mt-1 text-gray-900">
                  Ends on:{' '}
                  {currentUser?.subscription_end_date?.toLocaleDateString() ||
                    'N/A'}
                </p>
                <Button
                  className="mt-4 bg-gradient-to-r from-purple-500 to-indigo-600 text-white rounded-full
                             hover:from-purple-600 hover:to-indigo-700 transition-all duration-300 ease-in-out"
                  onClick={handleStripePortalRequest}
                  disabled={isLoading}
                >
                  {isLoading ? 'Loading...' : 'Manage Subscription'}
                </Button>
              </div>
            </div>
          )}
        </div>
      </section>
    </main>
  )
}

export default SettingsPage
