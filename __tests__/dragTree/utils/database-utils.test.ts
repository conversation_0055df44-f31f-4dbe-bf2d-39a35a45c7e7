import {
  reconstructTreeFromDatabase,
  convertTreeToDbStructure,
  DatabaseDragTree,
} from '@/app/stores/dragtree_store/utils/database-utils'
import { TreeNode } from '@/app/types'

// Mock data for testing
const mockDbData: DatabaseDragTree = {
  tree_structure: {
    root_id: 'root',
    hierarchy: {
      root: ['cat1', 'cat2'],
      cat1: ['que1'],
      cat2: [],
      que1: [],
    },
  },
  nodes: [
    {
      id: 'root',
      label: 'Root',
      node_type: 'CATEGORY',
      is_interested_in: false,
      status: 'ACTIVE',
    },
    {
      id: 'cat1',
      label: 'Category 1',
      node_type: 'CATEGORY',
      is_interested_in: true,
      status: 'ACTIVE',
    },
    {
      id: 'que1',
      label: 'Question 1',
      node_type: 'QUESTION',
      is_interested_in: false,
      status: 'ACTIVE',
    },
    {
      id: 'cat2',
      label: 'Category 2',
      node_type: 'CATEGORY',
      is_interested_in: false,
      status: 'ACTIVE',
    },
    {
      id: 'inactive1',
      label: 'Inactive Cat',
      node_type: 'CATEGORY',
      is_interested_in: false,
      status: 'INACTIVE',
    },
  ],
}

const mockTreeNode: TreeNode = {
  id: 'root',
  label: 'Root',
  type: 'category',
  isInterestedIn: false,
  children: [
    {
      id: 'cat1',
      label: 'Category 1',
      type: 'category',
      isInterestedIn: true,
      children: [
        {
          id: 'que1',
          label: 'Question 1',
          type: 'question',
          isInterestedIn: false,
          children: [],
        },
      ],
    },
    {
      id: 'cat2',
      label: 'Category 2',
      type: 'category',
      isInterestedIn: false,
      children: [],
    },
  ],
}

describe('Database Utils', () => {
  beforeAll(() => {
    jest.spyOn(console, 'log').mockImplementation(() => {})
    jest.spyOn(console, 'warn').mockImplementation(() => {})
  })

  afterAll(() => {
    jest.restoreAllMocks()
  })

  describe('reconstructTreeFromDatabase', () => {
    it('should correctly reconstruct a tree from database data', () => {
      const reconstructedTree = reconstructTreeFromDatabase(mockDbData)
      expect(reconstructedTree).toEqual(mockTreeNode)
    })

    it('should filter out inactive nodes', () => {
      const reconstructedTree = reconstructTreeFromDatabase(mockDbData)
      expect(JSON.stringify(reconstructedTree)).not.toContain('inactive1')
    })

    it('should return null if tree_structure is missing', () => {
      const invalidData = { ...mockDbData, tree_structure: undefined }
      expect(reconstructTreeFromDatabase(invalidData)).toBeNull()
    })

    it('should return null if nodes are missing', () => {
      const invalidData = { ...mockDbData, nodes: undefined }
      expect(reconstructTreeFromDatabase(invalidData)).toBeNull()
    })
  })

  describe('convertTreeToDbStructure', () => {
    it('should correctly convert a tree node to database structure', () => {
      const dbStructure = convertTreeToDbStructure(mockTreeNode)
      expect(dbStructure.root_id).toBe('root')
      expect(dbStructure.hierarchy).toEqual({
        root: ['cat1', 'cat2'],
        cat1: ['que1'],
        cat2: [],
        que1: [],
      })
    })
  })
})
