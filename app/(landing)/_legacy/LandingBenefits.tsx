type BenefitProps = {
  icon: string
  title: string
  description: string
}

const BenefitCard = ({
  icon,
  title,
  description,
}: BenefitProps): React.JSX.Element => (
  <div className="flex flex-col items-center p-5 bg-zinc-300 rounded-lg shadow-md space-y-5">
    <div className="w-16 h-16 rounded-full flex items-center justify-center text-4xl">
      {icon}
    </div>
    <h3 className="text-xl font-semibold text-gray-800">{title}</h3>
    <p className="text-gray-700">{description}</p>
  </div>
)

const benefitsData: BenefitProps[] = [
  {
    icon: '⏱️',
    title: 'Clarity in Minutes',
    description:
      'Transform ideas into clear concepts in MINUTES to maintain the momentum. Save hours on unfocused research and unnecessary meetings.',
  },
  {
    icon: '🪄',
    title: 'Effortless Structuring',
    description:
      'Our REAL user feedback: "I could do it better if I spent a few hours, but achieving this with such little effort is amazing!"',
  },
  {
    icon: '🏗️',
    title: 'Solid Start',
    description:
      'Create a robust foundation for your next steps, whether it is documentation, brainstorming, collaboration, or enhancing other AI tools with rich context.',
  },
]

const Benefits = (): React.JSX.Element => {
  return (
    <div className="py-20 px-8">
      <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-10 text-white text-center">
        Benefits
      </h2>
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-10">
        {benefitsData.map((benefit, index) => (
          <BenefitCard key={index} {...benefit} />
        ))}
      </div>
    </div>
  )
}

export default Benefits
