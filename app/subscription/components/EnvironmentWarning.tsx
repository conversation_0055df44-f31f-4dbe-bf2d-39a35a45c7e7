'use client'

import React, { useState, useEffect } from 'react'

export const EnvironmentWarning: React.FC = () => {
  const [isLocalOrDev, setIsLocalOrDev] = useState(false)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    // Check if we're in local/dev environment on client side only
    const isDev =
      process.env.NODE_ENV === 'development' ||
      (typeof window !== 'undefined' &&
        (window.location.hostname === 'localhost' ||
          window.location.hostname === '127.0.0.1' ||
          window.location.hostname.includes('vercel.app')))

    setIsLocalOrDev(isDev)
    setMounted(true)
  }, [])

  // Don't render anything until mounted to avoid hydration mismatch
  if (!mounted) {
    return null
  }

  if (!isLocalOrDev) {
    return null
  }

  return (
    <div className="text-center py-4 px-4 bg-red-50 border-b border-red-200">
      <h2 className="text-lg font-bold text-red-600 mb-2">
        🚨 Development Environment
      </h2>
      <p className="text-sm text-red-700">
        You are in local/dev environment. DO NOT subscribe with a real card!{' '}
        <a
          href="https://docs.stripe.com/testing#cards"
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-600 hover:text-blue-800 underline"
        >
          Use Stripe testing cards instead
        </a>
      </p>
    </div>
  )
}
