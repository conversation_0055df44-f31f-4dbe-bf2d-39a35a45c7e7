import React from 'react'
import { ENABLE_CHAT_V2 } from '@/app/configs/feature-flags'
import type { Tab } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useTabStore'

// Dynamic imports for both versions
const ChatTabContentV1 = React.lazy(
  () =>
    import('@/app/(conv)/dragTree/[dragTreeId]/components/tabs/ChatTabContent')
)

const ChatTabContentV2 = React.lazy(
  () =>
    import(
      '@/app/(conv)/dragTree/[dragTreeId]/components/tabs/ChatTabContentV2'
    )
)

type ChatTabContentWrapperProps = {
  tab: Tab
  dragTreeId: string
}

/**
 * Wrapper component that dynamically renders the appropriate chat version
 * based on the feature flag configuration
 */
export default function ChatTabContentWrapper({
  tab,
  dragTreeId,
}: ChatTabContentWrapperProps) {
  if (ENABLE_CHAT_V2) {
    return <ChatTabContentV2 tab={tab} dragTreeId={dragTreeId} />
  } else {
    return <ChatTabContentV1 tab={tab} dragTreeId={dragTreeId} />
  }
}
