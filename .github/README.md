# GitHub Actions CI/CD Pipeline

This directory contains optimized GitHub Actions workflows designed for fast execution and minimal resource usage while providing comprehensive testing coverage.

## 🚀 Workflows Overview

### 1. PR Validation (`pr-validation.yml`)

**Trigger**: Pull Request opened/updated
**Duration**: ~3-5 minutes
**Purpose**: Fast feedback loop for developers

- ✅ **Quick dependency installation** with retry mechanisms
- ✅ **Parallel execution** of linting and unit tests
- ✅ **Build verification** (quick start check)
- ✅ **Automatic PR comments** with status updates

### 2. CI/CD Pipeline (`ci.yml`)

**Trigger**: Push to main/develop, Pull Request
**Duration**: ~8-12 minutes (with E2E when needed)
**Purpose**: Comprehensive testing and deployment

- ✅ **Smart E2E execution** (only when source files change)
- ✅ **Parallel browser testing** (Chromium + WebKit)
- ✅ **Combined test & build job** (saves runner overhead)
- ✅ **Security scanning** (PR only)
- ✅ **Coverage reporting** with Codecov integration

## 🛠️ Key Optimizations

### Network Resilience

```yaml
# Enhanced npm configuration for network issues
npm config set fetch-retries 5
npm config set fetch-retry-mintimeout 20000
npm config set fetch-retry-maxtimeout 120000
npm config set fetch-timeout 600000
```

### Retry Mechanisms

- **3 retry attempts** for dependency installation
- **30-second wait** between retries
- **Prefer offline** mode to use cache when possible

### Resource Efficiency

- **Combined jobs** reduce runner overhead
- **Smart caching** for dependencies and browsers
- **Conditional E2E tests** based on file changes
- **Reduced browser matrix** (Chromium + WebKit only)

### Parallel Execution

```bash
# Background processes for parallel execution
npm run lint &
LINT_PID=$!
npm test
wait $LINT_PID
```

## 📊 Expected Performance

| Workflow         | Duration | Resource Usage | Triggers       |
| ---------------- | -------- | -------------- | -------------- |
| PR Validation    | 3-5 min  | 1 runner       | All PRs        |
| CI/CD (no E2E)   | 5-7 min  | 1 runner       | Code changes   |
| CI/CD (with E2E) | 8-12 min | 3 runners      | Source changes |

## 🔧 Configuration

### Required Secrets

```bash
CODECOV_TOKEN=your_codecov_token  # Optional for coverage
```

### Branch Protection Rules

Recommended status checks:

- `Quick Validation`
- `Test & Build`
- `E2E Tests (chromium)` (when applicable)

## 📈 Smart Features

### 1. File Change Detection

E2E tests only run when these files change:

- `app/**`
- `components/**`
- `e2e/**`
- `package*.json`
- `playwright.config.ts`

### 2. Automatic PR Comments

- ✅ Real-time status updates
- 📊 Test result summaries
- 🔗 Links to detailed reports

### 3. Artifact Management

- **7-day retention** for test results
- **Coverage reports** for analysis
- **Playwright reports** for E2E debugging

## 🚨 Troubleshooting

### Network Issues

If you see npm install failures:

1. Check if it's a temporary npm registry issue
2. Re-run the workflow (retry logic will help)
3. The workflow automatically uses `--prefer-offline` mode

### Environment Variables

The workflows provide dummy environment variables for build processes:

- `OPENAI_API_KEY`: Required for API route compilation
- `NEXTAUTH_SECRET`: Required for NextAuth configuration
- `NEXTAUTH_URL`: Required for NextAuth URL configuration
- `DATABASE_URL`: Required for Prisma schema validation
- `NEXTAUTH_GITHUB_ID`: Required for GitHub OAuth configuration
- `NEXTAUTH_GITHUB_SECRET`: Required for GitHub OAuth configuration
- `UPSTASH_REDIS_REST_URL`: Required for Redis configuration
- `UPSTASH_REDIS_REST_TOKEN`: Required for Redis authentication
- `STRIPE_SECRET_KEY`: Required for Stripe integration
- `STRIPE_WEBHOOK_SECRET`: Required for Stripe webhooks

### Deprecated npm Options

If you see `cache-min` deprecation warnings:

- This has been fixed in the latest workflows
- The workflows now use `--prefer-offline` instead
- Re-run the workflow to use the updated configuration

### E2E Test Failures

1. Check the Playwright report in artifacts
2. Review screenshots in test-results
3. Consider if tests need updating for UI changes

### Build Failures

1. Check if TypeScript errors exist locally
2. Verify all dependencies are properly installed
3. Review the build logs in the workflow

### Database-Related Build Issues

If you see Prisma or database errors during build:

- Pages with database calls are configured as `dynamic` to avoid static generation
- Dummy environment variables are provided for build-time validation
- The subscription page uses `export const dynamic = 'force-dynamic'`

### Tailwind CSS Warnings

If you see `@tailwindcss/line-clamp` deprecation warnings:

- This plugin is now included by default in Tailwind CSS v3.3+
- The plugin has been removed from the configuration
- Re-run the build to use the updated configuration

## 📝 Best Practices

### For Developers

1. **Run tests locally** before pushing
2. **Use meaningful commit messages** for better workflow tracking
3. **Keep PRs focused** to reduce CI time

### For Maintainers

1. **Monitor workflow costs** in GitHub usage dashboard
2. **Update dependencies** regularly to prevent conflicts
3. **Review failed workflows** to identify patterns

## 🔄 Maintenance

### Regular Tasks

- [ ] Update Node.js version in workflows
- [ ] Review and update browser versions
- [ ] Monitor test execution times
- [ ] Clean up old workflow runs

### Performance Monitoring

Track these metrics:

- Average workflow duration
- Success/failure rates
- Resource usage trends
- E2E test execution frequency

---

## 📚 Additional Resources

- [GitHub Actions Best Practices](https://docs.github.com/en/actions/learn-github-actions/usage-limits-billing-and-administration)
- [Playwright Testing Guide](https://playwright.dev/docs/best-practices)
- [Jest Testing Framework](https://jestjs.io/docs/getting-started)

_Last updated: $(date)_
