# PR Quality Checks - TEMPORARILY DISABLED
# Uncomment this file when you're ready to re-enable GitHub Actions testing

# name: PR Quality Checks
#
# on:
#   pull_request:
#     branches: [main, develop]
#     types: [opened, synchronize, reopened]
#
# permissions:
#   contents: read
#   pull-requests: write
#   issues: write
#
# # Cancel previous runs for the same PR
# concurrency:
#   group: pr-checks-${{ github.event.number }}
#   cancel-in-progress: true
#
# jobs:
#   pr-summary:
#     name: PR Summary
#     runs-on: ubuntu-latest
#     outputs:
#       # Bypass E2E tests for now
#       should-run-e2e: 'false'
#
#     steps:
#       - name: Checkout code
#         uses: actions/checkout@v4
#         with:
#           fetch-depth: 0
#
#       # TODO: This action needs special permissions or doesn't work in forks
#       # Uncomment when permission issues are resolved
#       # - name: Check for relevant changes
#       #   uses: dorny/paths-filter@v3
#       #   id: changes
#       #   with:
#       #     filters: |
#       #       src:
#       #         - 'app/**'
#       #         - 'components/**'
#       #         - 'e2e/**'
#       #         - 'package*.json'
#
#       - name: Create PR summary
#         run: |
#           echo "## Test Plan for PR #${{ github.event.number }}" >> $GITHUB_STEP_SUMMARY
#           echo "" >> $GITHUB_STEP_SUMMARY
#           echo "### Changes detected" >> $GITHUB_STEP_SUMMARY
#           echo "- Source code changes: ✅" >> $GITHUB_STEP_SUMMARY
#           echo "- Will run E2E tests: ❌ (temporarily disabled)" >> $GITHUB_STEP_SUMMARY
#           echo "" >> $GITHUB_STEP_SUMMARY
#           echo "### PR Info" >> $GITHUB_STEP_SUMMARY
#           echo "- Title: ${{ github.event.pull_request.title }}" >> $GITHUB_STEP_SUMMARY
#           echo "- Author: ${{ github.event.pull_request.user.login }}" >> $GITHUB_STEP_SUMMARY
#           echo "- Base: ${{ github.event.pull_request.base.ref }}" >> $GITHUB_STEP_SUMMARY
#           echo "" >> $GITHUB_STEP_SUMMARY
#
#       - name: Comment PR
#         if: github.event_name == 'pull_request'
#         uses: marocchino/sticky-pull-request-comment@v2
#         with:
#           header: test-plan
#           message: |
#             ## 🧪 Test Plan for PR #${{ github.event.number }}
#
#             **Running tests for selected file changes**
#
#             | Category | Will Run | Details |
#             | -------- | -------- | ------- |
#             | Unit Tests | ✅ | Always run |
#             | E2E Tests | ❌ | Temporarily disabled |
#
#             Tests will run automatically as part of this PR.
#
#   dependencies:
#     name: Setup Dependencies
#     runs-on: ubuntu-latest
#     outputs:
#       cache-hit: ${{ steps.cache-node-modules.outputs.cache-hit }}
#     steps:
#       - name: Checkout code
#         uses: actions/checkout@v4
#
#       - name: Setup Node.js
#         uses: actions/setup-node@v4
#         with:
#           node-version: '20.17'
#           cache: 'npm'
#           registry-url: 'https://registry.npmjs.org'
#
#       # Configure npm for better stability
#       - name: Configure npm
#         run: |
#           npm config set fetch-retries 5
#           npm config set fetch-retry-mintimeout 20000
#           npm config set fetch-retry-maxtimeout 120000
#
#       # Check if node_modules are already cached
#       # Use a cache key that persists across workflow runs
#       - name: Cache node_modules
#         id: cache-node-modules
#         uses: actions/cache@v3
#         with:
#           path: |
#             node_modules
#             ~/.npm
#           key: ${{ runner.os }}-node-modules-${{ hashFiles('**/package-lock.json') }}
#           restore-keys: |
#             ${{ runner.os }}-node-modules-
#
#       # Only install deps if cache miss - with retries for npm registry issues
#       - name: Install dependencies
#         if: steps.cache-node-modules.outputs.cache-hit != 'true'
#         uses: nick-fields/retry@v2
#         with:
#           timeout_minutes: 10
#           max_attempts: 3
#           command: npm ci --prefer-offline --no-audit
#
#       # Upload node_modules as an artifact to share between jobs
#       - name: Upload node_modules artifact
#         if: steps.cache-node-modules.outputs.cache-hit != 'true'
#         uses: actions/upload-artifact@v4
#         with:
#           name: node_modules
#           path: node_modules
#           retention-days: 1
#
#   # Quick check for PR validation
#   validate-pr:
#     name: PR Validation
#     runs-on: ubuntu-latest
#     needs: dependencies
#     steps:
#       - name: Checkout code
#         uses: actions/checkout@v4
#
#       - name: Setup Node.js
#         uses: actions/setup-node@v4
#         with:
#           node-version: '20.17'
#
#       # Restore cached node_modules
#       - name: Restore node_modules from cache
#         id: cache-node-modules
#         uses: actions/cache@v3
#         with:
#           path: node_modules
#           key: ${{ runner.os }}-node-modules-${{ hashFiles('**/package-lock.json') }}
#
#       # Download node_modules from artifact if cache miss
#       - name: Download node_modules
#         if: steps.cache-node-modules.outputs.cache-hit != 'true'
#         uses: actions/download-artifact@v4
#         with:
#           name: node_modules
#           path: node_modules
#
#       - name: Run linting
#         run: npm run lint
#
#       - name: Run type checking
#         run: npm run typecheck || echo "Type check failed but continuing"
#
#       - name: Run unit tests
#         run: npm test
#
#       - name: Update PR status
#         if: ${{ always() }}
#         run: |
#           if [ "${{ job.status }}" == "success" ]; then
#             echo "## ✅ Validation Passed" >> $GITHUB_STEP_SUMMARY
#           else
#             echo "## ❌ Validation Failed" >> $GITHUB_STEP_SUMMARY
#           fi
#           echo "" >> $GITHUB_STEP_SUMMARY
#           echo "- Linting: ${{ steps.lint.outcome || 'skipped' }}" >> $GITHUB_STEP_SUMMARY
#           echo "- Type checking: ${{ steps.typecheck.outcome || 'skipped' }}" >> $GITHUB_STEP_SUMMARY
#           echo "- Unit tests: ${{ steps.test.outcome || 'skipped' }}" >> $GITHUB_STEP_SUMMARY
#
#   # Status check jobs that GitHub requires
#   unit-tests-check:
#     name: ✅ Unit Tests
#     runs-on: ubuntu-latest
#     needs: dependencies
#     steps:
#       - name: Checkout code
#         uses: actions/checkout@v4
#
#       - name: Setup Node.js
#         uses: actions/setup-node@v4
#         with:
#           node-version: '20.17'
#           cache: 'npm'
#
#       # Restore cached node_modules
#       - name: Restore node_modules from cache
#         id: cache-node-modules
#         uses: actions/cache@v3
#         with:
#           path: node_modules
#           key: ${{ runner.os }}-node-modules-${{ hashFiles('**/package-lock.json') }}
#
#       # Download node_modules from artifact if cache miss
#       - name: Download node_modules
#         if: steps.cache-node-modules.outputs.cache-hit != 'true'
#         uses: actions/download-artifact@v4
#         with:
#           name: node_modules
#           path: node_modules
#
#       - name: Run unit tests with coverage
#         run: npm run test:coverage
#
#       - name: Upload unit test results
#         uses: actions/upload-artifact@v4
#         if: always()
#         with:
#           name: unit-test-results
#           path: |
#             coverage/
#             test-results/junit.xml
#           retention-days: 7
#
#   build-check:
#     name: ✅ Build
#     runs-on: ubuntu-latest
#     needs: dependencies
#     steps:
#       - name: Checkout code
#         uses: actions/checkout@v4
#
#       - name: Setup Node.js
#         uses: actions/setup-node@v4
#         with:
#           node-version: '20.17'
#           cache: 'npm'
#
#       # Restore cached node_modules
#       - name: Restore node_modules from cache
#         id: cache-node-modules
#         uses: actions/cache@v3
#         with:
#           path: node_modules
#           key: ${{ runner.os }}-node-modules-${{ hashFiles('**/package-lock.json') }}
#
#       # Download node_modules from artifact if cache miss
#       - name: Download node_modules
#         if: steps.cache-node-modules.outputs.cache-hit != 'true'
#         uses: actions/download-artifact@v4
#         with:
#           name: node_modules
#           path: node_modules
#
#       - name: Build application
#         run: npm run build
#
#   # Commenting out E2E tests for now
#   # e2e-check:
#   #   name: ✅ E2E Tests
#   #   runs-on: ubuntu-latest
#   #   needs: pr-summary
#   #   if: needs.pr-summary.outputs.should-run-e2e == 'true'
#   #   strategy:
#   #     fail-fast: false
#   #     matrix:
#   #       browser: [chromium, firefox, webkit]
#   #
#   #   steps:
#   #     - name: Checkout code
#   #       uses: actions/checkout@v4
#   #
#   #     - name: Setup Node.js
#   #       uses: actions/setup-node@v4
#   #       with:
#   #         node-version: '20.17'
#   #         cache: 'npm'
#   #
#   #     - name: Install dependencies
#   #       run: npm ci
#   #
#   #     - name: Install Playwright browsers
#   #       run: npx playwright install --with-deps ${{ matrix.browser }}
#   #
#   #     - name: Run E2E tests
#   #       run: npx playwright test --project=${{ matrix.browser }}
#   #       env:
#   #         CI: true
#   #
#   #     - name: Upload E2E results
#   #       uses: actions/upload-artifact@v4
#   #       if: always()
#   #       with:
#   #         name: e2e-results-${{ matrix.browser }}
#   #         path: |
#   #           playwright-report/
#   #           test-results/
#   #         retention-days: 7
#
#   # Final status and reporting
#   pr-status:
#     name: 📊 PR Status Report
#     runs-on: ubuntu-latest
#     needs: [pr-summary, unit-tests-check, build-check]
#     if: always()
#
#     steps:
#       - name: Download test artifacts
#         uses: actions/download-artifact@v4
#         continue-on-error: true
#
#       - name: Generate status report
#         uses: actions/github-script@v7
#         with:
#           script: |
#             const unitStatus = '${{ needs.unit-tests-check.result }}'
#             const buildStatus = '${{ needs.build-check.result }}'
#             // E2E tests are disabled, so we don't reference the results
#             const shouldRunE2E = false  // Hardcoded to false since E2E is disabled
#
#             const statusIcon = (status) => {
#               switch(status) {
#                 case 'success': return '✅'
#                 case 'failure': return '❌'
#                 case 'cancelled': return '⏹️'
#                 case 'skipped': return '⏭️'
#                 default: return '🔄'
#               }
#             }
#
#             const getStatusText = (status) => {
#               switch(status) {
#                 case 'success': return 'Passed'
#                 case 'failure': return 'Failed'
#                 case 'cancelled': return 'Cancelled'
#                 case 'skipped': return 'Skipped'
#                 default: return 'In Progress'
#               }
#             }
#
#             const overallStatus = (unitStatus === 'success' && buildStatus === 'success')
#               ? '✅ Ready to merge'
#               : '❌ Needs attention'
#
#             const report = `
#             ## 📊 PR Quality Check Results
#
#             ### Overall Status: ${overallStatus}
#
#             | Check | Status | Result |
#             |-------|--------|---------|
#             | Unit Tests | ${statusIcon(unitStatus)} | ${getStatusText(unitStatus)} |
#             | Build | ${statusIcon(buildStatus)} | ${getStatusText(buildStatus)} |
#             | E2E Tests | ⏭️ | Temporarily Disabled |
#
#             ### Details:
#             ${unitStatus === 'failure' ? '❌ **Unit tests failed** - Check the logs for details\n' : ''}
#             ${buildStatus === 'failure' ? '❌ **Build failed** - Check the logs for details\n' : ''}
#             ${(unitStatus === 'success' && buildStatus === 'success') ? '🎉 **All checks passed!** This PR is ready for review.\n' : ''}
#
#             📁 **Artifacts**: Test reports and coverage data are available in the Actions tab.
#             `
#
#             // Find existing comment to update
#             const comments = await github.rest.issues.listComments({
#               issue_number: context.issue.number,
#               owner: context.repo.owner,
#               repo: context.repo.repo,
#             })
#
#             const botComment = comments.data.find(comment =>
#               comment.user.type === 'Bot' && comment.body.includes('📊 PR Quality Check Results'))
#
#             if (botComment) {
#               await github.rest.issues.updateComment({
#                 comment_id: botComment.id,
#                 owner: context.repo.owner,
#                 repo: context.repo.repo,
#                 body: report
#               })
#             } else {
#               await github.rest.issues.createComment({
#                 issue_number: context.issue.number,
#                 owner: context.repo.owner,
#                 repo: context.repo.repo,
#                 body: report
#               })
#             }
