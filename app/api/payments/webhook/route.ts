import Stripe from 'stripe'
import { stripe } from '@/lib/stripe/config'
import { NextRequest, NextResponse } from 'next/server'
import { handleWebhookEvent } from './handlers'

// Set of Stripe events we're interested in handling
const relevantEvents = new Set([
  'product.created',
  'product.updated',
  'price.created',
  'price.updated',
  'checkout.session.completed',
  'customer.subscription.created',
  'customer.subscription.updated',
  'customer.subscription.deleted',
  // Keep for observability; no tier change on first failure
  'invoice.payment_failed',
  // For audit trail snapshots
  'invoice.created',
  'invoice.finalized',
  'invoice.payment_succeeded',
  'invoice.voided',
  'invoice.marked_uncollectible',
  'invoice.updated',
  'payment_intent.created',
  'payment_intent.succeeded',
  'payment_intent.payment_failed',
  'payment_intent.processing',
  'payment_intent.canceled',
  'payment_intent.requires_action',
  'payment_intent.partially_funded',
])

export const dynamic = 'force-dynamic'
export const runtime = 'nodejs'

export async function POST(req: NextRequest) {
  const rawBody = await req.text()
  const sig = req.headers.get('stripe-signature')
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET

  if (!sig || !webhookSecret) {
    console.error('Missing stripe-signature or STRIPE_WEBHOOK_SECRET')
    return NextResponse.json(
      { error: 'Webhook secret not found.' },
      { status: 400 }
    )
  }

  let event: Stripe.Event

  try {
    // Verify the webhook signature
    event = stripe.webhooks.constructEvent(rawBody, sig, webhookSecret)
    console.log(`🔔 Webhook received: ${event.type}`)
  } catch (err: any) {
    console.error(`❌ Webhook signature verification failed:`, err.message)
    return NextResponse.json(
      { error: `Webhook Error: ${err.message}` },
      { status: 400 }
    )
  }

  // Only process relevant events
  if (relevantEvents.has(event.type)) {
    try {
      await handleWebhookEvent(event)
    } catch (error) {
      console.error(`Error processing ${event.type}:`, error)
      return NextResponse.json(
        { error: `Webhook handler failed. View your Next.js function logs.` },
        { status: 400 }
      )
    }
  } else {
    console.warn(`Unhandled event type: ${event.type}`)
  }

  return NextResponse.json({ received: true })
}
