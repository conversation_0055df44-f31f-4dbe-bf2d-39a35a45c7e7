import { useRef, type RefObject } from 'react'
import { toast } from 'react-hot-toast'

export function useShiftEnterSubmit(
  input: string,
  minCharCount: number
): {
  formRef: RefObject<HTMLFormElement | null>
  onKeyDown: (event: React.KeyboardEvent<HTMLTextAreaElement>) => void
} {
  const formRef = useRef<HTMLFormElement>(null)

  const handleKeyDown = (
    event: React.KeyboardEvent<HTMLTextAreaElement>
  ): void => {
    if (event.key === 'Enter' && !event.shiftKey) {
      if (input === '' || input.length < minCharCount) {
        toast.error(`Minimum character count is ${minCharCount}`)
        event.preventDefault() // prevent the default enter behavior
      } else {
        formRef.current?.requestSubmit()
        event.preventDefault() // prevent the default enter behavior
      }
    }
    // Shift+<PERSON><PERSON> creates a new line (default behavior when not prevented)
  }

  return { formRef, onKeyDown: handleKeyDown }
}
