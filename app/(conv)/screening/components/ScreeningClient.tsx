'use client'

import React, { useEffect, useRef } from 'react'
import { User } from '@prisma/client'
import ScreeningLayout from '@/app/(conv)/screening/components/ScreeningLayout'
import { useScreeningActions } from '@/app/(conv)/screening/hooks'

type ScreeningClientProps = {
  currentUser?: User | null
  prefilledQuestion?: string | null
}

/**
 * Main Screening Client Component
 *
 * This component orchestrates the screening workflow by:
 * - Managing all state and actions through the useScreeningActions hook
 * - Handling prefilled questions from URL parameters
 * - Delegating layout rendering to ScreeningLayout
 *
 * The component follows the separation of concerns principle:
 * - State and business logic are handled by the useScreeningActions hook
 * - UI rendering is delegated to layout components
 */
const ScreeningClient: React.FC<ScreeningClientProps> = ({
  // currentUser: unused
  prefilledQuestion = null,
}) => {
  // Centralized state and action management
  const screeningHook = useScreeningActions()

  // Track if we've already set the prefilled question to prevent infinite loops
  const hasSetPrefilledQuestion = useRef(false)

  // Set prefilled question if provided (only once)
  useEffect(() => {
    if (
      prefilledQuestion &&
      !screeningHook.description &&
      !hasSetPrefilledQuestion.current
    ) {
      console.log(
        '🔧 [SCREENING CLIENT] Setting prefilled question:',
        prefilledQuestion
      )
      screeningHook.setDescription(prefilledQuestion)
      hasSetPrefilledQuestion.current = true
    }
  }, [prefilledQuestion, screeningHook.description]) // Removed setDescription from deps

  return <ScreeningLayout screeningHook={screeningHook} />
}

export default ScreeningClient
