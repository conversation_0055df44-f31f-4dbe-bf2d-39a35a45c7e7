import {
  generateSuggestionId,
  createSuggestionsFromText,
  getAllSuggestions,
  acceptAllSuggestions,
  rejectAllSuggestions,
  clearAllSuggestions,
  createDemoSuggestions,
  applySuggestionsFromAPI,
  type SuggestionBatch,
} from '@/app/components/editor/utils/suggestion-utils'

// Mock editor interface for testing
interface MockEditor {
  state: { doc: any }
  schema: any
  chain: () => any
  _mockDoc: any
  _mockSchema: any
  _mockChain: any
}

// Mock editor for testing
const createMockEditor = (content = 'Hello World test content'): MockEditor => {
  const mockDoc = {
    descendants: jest.fn((callback: any) => {
      // Simulate text nodes
      callback({ isText: true, text: content, type: { name: 'text' } }, 0)
      return true
    }),
  }

  const mockSchema = {
    nodes: {
      suggestion: {
        create: jest.fn(attrs => ({
          type: { name: 'suggestion' },
          attrs,
          nodeSize: 1,
        })),
      },
    },
    text: jest.fn((text: string) => ({ type: { name: 'text' }, text })),
  }

  const mockChain: any = {
    focus: jest.fn(() => mockChain),
    insertContentAt: jest.fn(() => mockChain),
    insertContent: jest.fn(() => mockChain),
    run: jest.fn(),
  }

  return {
    state: { doc: mockDoc },
    schema: mockSchema,
    chain: () => mockChain,
    _mockDoc: mockDoc,
    _mockSchema: mockSchema,
    _mockChain: mockChain,
  }
}

describe('Suggestion Utils', () => {
  const originalNodeEnv = process.env.NODE_ENV

  beforeEach(() => {
    jest.clearAllMocks()
    // Set development environment for console logging tests
    process.env = { ...process.env, NODE_ENV: 'development' }
  })

  afterEach(() => {
    jest.restoreAllMocks()
    // Restore original NODE_ENV
    process.env = { ...process.env, NODE_ENV: originalNodeEnv }
  })

  describe('generateSuggestionId', () => {
    it('generates unique IDs', () => {
      const id1 = generateSuggestionId()
      const id2 = generateSuggestionId()

      expect(id1).toMatch(/^suggestion-\d+-[a-z0-9]+$/)
      expect(id2).toMatch(/^suggestion-\d+-[a-z0-9]+$/)
      expect(id1).not.toBe(id2)
    })

    it('includes timestamp in ID', () => {
      const beforeTime = Date.now()
      const id = generateSuggestionId()
      const afterTime = Date.now()

      const timestamp = parseInt(id.split('-')[1])
      expect(timestamp).toBeGreaterThanOrEqual(beforeTime)
      expect(timestamp).toBeLessThanOrEqual(afterTime)
    })
  })

  describe('createSuggestionsFromText', () => {
    it('creates suggestions for existing text', () => {
      const editor = createMockEditor('Hello World test content')
      const suggestions: SuggestionBatch = [
        { oldText: 'Hello', newText: 'Hi' },
        { oldText: 'World', newText: 'Universe' },
      ]

      const result = createSuggestionsFromText(editor as any, suggestions)

      expect(result).toBe(true)
      expect(editor._mockChain.focus).toHaveBeenCalled()
      expect(editor._mockSchema.nodes.suggestion.create).toHaveBeenCalledTimes(
        2
      )
      expect(editor._mockChain.insertContentAt).toHaveBeenCalledTimes(2)
    })

    it('handles multiple occurrences of the same text', () => {
      const editor = createMockEditor('test test test')
      const suggestions: SuggestionBatch = [
        { oldText: 'test', newText: 'TEST' },
      ]

      // Mock doc.descendants to simulate multiple occurrences
      editor._mockDoc.descendants.mockImplementation((callback: any) => {
        // Simulate a text node with multiple 'test' occurrences
        callback(
          { isText: true, text: 'test test test', type: { name: 'text' } },
          0
        )
        return true
      })

      const result = createSuggestionsFromText(editor as any, suggestions)

      expect(result).toBe(true)
      expect(editor._mockChain.insertContentAt).toHaveBeenCalledTimes(3) // 3 occurrences
    })

    it('warns when text is not found', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation()
      const editor = createMockEditor('Hello World')
      const suggestions: SuggestionBatch = [
        { oldText: 'NotFound', newText: 'Replacement' },
      ]

      const result = createSuggestionsFromText(editor as any, suggestions)

      expect(result).toBe(true)
      expect(consoleSpy).toHaveBeenCalledWith(
        'Could not find text to replace: "NotFound"'
      )
      expect(editor._mockSchema.nodes.suggestion.create).not.toHaveBeenCalled()

      consoleSpy.mockRestore()
    })

    it('handles empty suggestions array', () => {
      const editor = createMockEditor('Hello World')
      const suggestions: SuggestionBatch = []

      const result = createSuggestionsFromText(editor as any, suggestions)

      expect(result).toBe(true)
      expect(editor._mockSchema.nodes.suggestion.create).not.toHaveBeenCalled()
    })

    it('handles errors gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      const editor = createMockEditor('Hello World')
      editor._mockChain.insertContentAt.mockImplementation(() => {
        throw new Error('Mock error')
      })

      const suggestions: SuggestionBatch = [{ oldText: 'Hello', newText: 'Hi' }]

      const result = createSuggestionsFromText(editor as any, suggestions)

      expect(result).toBe(false)
      expect(consoleSpy).toHaveBeenCalledWith(
        'Error creating suggestions from text:',
        expect.any(Error)
      )

      consoleSpy.mockRestore()
    })

    it('does not log warnings in production', () => {
      process.env = { ...process.env, NODE_ENV: 'production' }
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation()
      const editor = createMockEditor('Hello World')
      const suggestions: SuggestionBatch = [
        { oldText: 'NotFound', newText: 'Replacement' },
      ]

      createSuggestionsFromText(editor as any, suggestions)

      expect(consoleSpy).not.toHaveBeenCalled()
      consoleSpy.mockRestore()
    })
  })

  describe('getAllSuggestions', () => {
    it('returns all suggestions in the document', () => {
      const editor = createMockEditor()
      const mockSuggestions = [
        {
          type: { name: 'suggestion' },
          attrs: { oldText: 'old1', newText: 'new1', id: 'id1' },
        },
        {
          type: { name: 'suggestion' },
          attrs: { oldText: 'old2', newText: 'new2', id: 'id2' },
        },
        { type: { name: 'text' }, text: 'regular text' },
      ]

      editor._mockDoc.descendants.mockImplementation((callback: any) => {
        mockSuggestions.forEach((node, pos) => callback(node, pos))
        return true
      })

      const suggestions = getAllSuggestions(editor as any)

      expect(suggestions).toHaveLength(2)
      expect(suggestions[0]).toEqual({
        oldText: 'old1',
        newText: 'new1',
        id: 'id1',
      })
      expect(suggestions[1]).toEqual({
        oldText: 'old2',
        newText: 'new2',
        id: 'id2',
      })
    })

    it('returns empty array when no suggestions exist', () => {
      const editor = createMockEditor()
      editor._mockDoc.descendants.mockImplementation((callback: any) => {
        callback({ type: { name: 'text' }, text: 'regular text' }, 0)
        return true
      })

      const suggestions = getAllSuggestions(editor as any)

      expect(suggestions).toHaveLength(0)
    })
  })

  describe('acceptAllSuggestions', () => {
    it('accepts all suggestions in reverse order', () => {
      const editor = createMockEditor()
      const mockSuggestions = [
        {
          type: { name: 'suggestion' },
          attrs: { newText: 'accepted1' },
          nodeSize: 1,
        },
        {
          type: { name: 'suggestion' },
          attrs: { newText: 'accepted2' },
          nodeSize: 1,
        },
      ]

      editor._mockDoc.descendants.mockImplementation((callback: any) => {
        mockSuggestions.forEach((node, pos) => callback(node, pos * 10))
        return true
      })

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation()

      acceptAllSuggestions(editor as any)

      expect(editor._mockSchema.text).toHaveBeenCalledWith('accepted2')
      expect(editor._mockSchema.text).toHaveBeenCalledWith('accepted1')
      expect(editor._mockChain.insertContentAt).toHaveBeenCalledTimes(2)
      expect(consoleSpy).toHaveBeenCalledWith('Accepted 2 suggestion(s)')

      consoleSpy.mockRestore()
    })

    it('handles documents with no suggestions', () => {
      const editor = createMockEditor()
      editor._mockDoc.descendants.mockImplementation((callback: any) => {
        callback({ type: { name: 'text' }, text: 'no suggestions' }, 0)
        return true
      })

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation()

      acceptAllSuggestions(editor as any)

      expect(editor._mockChain.insertContentAt).not.toHaveBeenCalled()
      expect(consoleSpy).toHaveBeenCalledWith('Accepted 0 suggestion(s)')

      consoleSpy.mockRestore()
    })
  })

  describe('rejectAllSuggestions', () => {
    it('rejects all suggestions in reverse order', () => {
      const editor = createMockEditor()
      const mockSuggestions = [
        {
          type: { name: 'suggestion' },
          attrs: { oldText: 'original1' },
          nodeSize: 1,
        },
        {
          type: { name: 'suggestion' },
          attrs: { oldText: 'original2' },
          nodeSize: 1,
        },
      ]

      editor._mockDoc.descendants.mockImplementation((callback: any) => {
        mockSuggestions.forEach((node, pos) => callback(node, pos * 10))
        return true
      })

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation()

      rejectAllSuggestions(editor as any)

      expect(editor._mockSchema.text).toHaveBeenCalledWith('original2')
      expect(editor._mockSchema.text).toHaveBeenCalledWith('original1')
      expect(editor._mockChain.insertContentAt).toHaveBeenCalledTimes(2)
      expect(consoleSpy).toHaveBeenCalledWith('Rejected 2 suggestion(s)')

      consoleSpy.mockRestore()
    })
  })

  describe('clearAllSuggestions', () => {
    it('calls rejectAllSuggestions', () => {
      const editor = createMockEditor()
      const mockSuggestions = [
        {
          type: { name: 'suggestion' },
          attrs: { oldText: 'original' },
          nodeSize: 1,
        },
      ]

      editor._mockDoc.descendants.mockImplementation((callback: any) => {
        mockSuggestions.forEach((node, pos) => callback(node, pos))
        return true
      })

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation()

      clearAllSuggestions(editor as any)

      expect(editor._mockChain.insertContentAt).toHaveBeenCalledTimes(1)
      expect(consoleSpy).toHaveBeenCalledWith('Rejected 1 suggestion(s)')

      consoleSpy.mockRestore()
    })
  })

  describe('createDemoSuggestions', () => {
    it('creates predefined demo suggestions', () => {
      const editor = createMockEditor(
        'Hello World simple example basic functionality'
      )

      createDemoSuggestions(editor as any)

      expect(editor._mockChain.focus).toHaveBeenCalled()
      expect(editor._mockSchema.nodes.suggestion.create).toHaveBeenCalledTimes(
        3
      )

      // Check that all demo suggestions were created with correct content
      const calls = editor._mockSchema.nodes.suggestion.create.mock.calls
      expect(calls).toContainEqual([
        expect.objectContaining({
          oldText: 'Hello World',
          newText: 'Hello Universe',
        }),
      ])
      expect(calls).toContainEqual([
        expect.objectContaining({
          oldText: 'simple example',
          newText: 'comprehensive example',
        }),
      ])
      expect(calls).toContainEqual([
        expect.objectContaining({
          oldText: 'basic functionality',
          newText: 'advanced functionality',
        }),
      ])
    })
  })

  describe('applySuggestionsFromAPI', () => {
    beforeEach(() => {
      global.fetch = jest.fn()
    })

    afterEach(() => {
      jest.restoreAllMocks()
    })

    it('successfully applies suggestions from API', async () => {
      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({
          suggestions: [{ oldText: 'old', newText: 'new' }],
        }),
      }

      ;(global.fetch as jest.Mock).mockResolvedValue(mockResponse)

      const editor = createMockEditor('old content')
      const result = await applySuggestionsFromAPI(
        editor as any,
        'test content',
        'https://api.example.com/suggestions'
      )

      expect(result).toBe(true)
      expect(global.fetch).toHaveBeenCalledWith(
        'https://api.example.com/suggestions',
        expect.objectContaining({
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ content: 'test content' }),
        })
      )
    })

    it('handles API errors gracefully', async () => {
      const mockResponse = {
        ok: false,
        status: 500,
      }

      ;(global.fetch as jest.Mock).mockResolvedValue(mockResponse)

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      const editor = createMockEditor('content')

      const result = await applySuggestionsFromAPI(
        editor as any,
        'test content',
        'https://api.example.com/suggestions'
      )

      expect(result).toBe(false)
      expect(consoleSpy).toHaveBeenCalledWith(
        'Error applying suggestions from API:',
        expect.any(Error)
      )

      consoleSpy.mockRestore()
    })

    it('handles empty suggestions from API', async () => {
      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({ suggestions: [] }),
      }

      ;(global.fetch as jest.Mock).mockResolvedValue(mockResponse)

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation()
      const editor = createMockEditor('content')

      const result = await applySuggestionsFromAPI(
        editor as any,
        'test content',
        'https://api.example.com/suggestions'
      )

      expect(result).toBe(false)
      expect(consoleSpy).toHaveBeenCalledWith(
        'No suggestions returned from API'
      )

      consoleSpy.mockRestore()
    })

    it('handles network errors', async () => {
      ;(global.fetch as jest.Mock).mockRejectedValue(new Error('Network error'))

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      const editor = createMockEditor('content')

      const result = await applySuggestionsFromAPI(
        editor as any,
        'test content',
        'https://api.example.com/suggestions'
      )

      expect(result).toBe(false)
      expect(consoleSpy).toHaveBeenCalledWith(
        'Error applying suggestions from API:',
        expect.any(Error)
      )

      consoleSpy.mockRestore()
    })

    it('does not log in production environment', async () => {
      process.env = { ...process.env, NODE_ENV: 'production' }

      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({ suggestions: [] }),
      }

      ;(global.fetch as jest.Mock).mockResolvedValue(mockResponse)

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation()
      const editor = createMockEditor('content')

      await applySuggestionsFromAPI(
        editor as any,
        'test content',
        'https://api.example.com/suggestions'
      )

      expect(consoleSpy).not.toHaveBeenCalled()
      consoleSpy.mockRestore()
    })
  })
})
