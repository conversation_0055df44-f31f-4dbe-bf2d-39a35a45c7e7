-- Create<PERSON><PERSON>
CREATE TYPE "DragTreeStatus" AS ENUM ('INITIALIZED', 'GENERATING', 'ACTIVE', 'INACTIVE');

-- CreateEnum
CREATE TYPE "DragTreeNodeType" AS ENUM ('CATEGORY', 'QUESTION');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "DragTreeNodeStatus" AS ENUM ('ACTIVE', 'INACTIVE');

-- CreateEnum
CREATE TYPE "DragTreeNodeContentStatus" AS ENUM ('INITIALIZED', 'PROCESSING', 'COMPLETED', 'INACTIVE');

-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "OpenAIUsageType" ADD VALUE 'DRAGTREE_GENERATE_SIMILAR_QUESTIONS';
ALTER TYPE "OpenAIUsageType" ADD VALUE 'DRAGTREE_GENERATE_SIMILAR_CATEGORIES';
ALTER TYPE "OpenAIUsageType" ADD VALUE 'DRAGTREE_NODE_ANSWER';

-- CreateTable
CREATE TABLE "drag_tree" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "title" TEXT,
    "status" "DragTreeStatus" NOT NULL DEFAULT 'INITIALIZED',
    "tree_structure" JSONB,
    "tree_structure_version" INTEGER DEFAULT 1,
    "user_prompt" TEXT,
    "raw_markdown" TEXT,
    "metadata" JSONB NOT NULL DEFAULT '{}',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "drag_tree_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "drag_tree_node" (
    "id" TEXT NOT NULL,
    "drag_tree_id" TEXT NOT NULL,
    "node_type" "DragTreeNodeType" NOT NULL,
    "label" TEXT NOT NULL,
    "status" "DragTreeNodeStatus" NOT NULL DEFAULT 'ACTIVE',
    "is_interested_in" BOOLEAN NOT NULL DEFAULT false,
    "ui_state" JSONB NOT NULL DEFAULT '{}',
    "metadata" JSONB NOT NULL DEFAULT '{}',
    "version" TEXT NOT NULL DEFAULT 'v1',
    "content_summary" JSONB NOT NULL DEFAULT '{}',
    "content_updated_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "drag_tree_node_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "drag_tree_node_content" (
    "id" TEXT NOT NULL,
    "drag_tree_id" TEXT NOT NULL,
    "drag_tree_node_id" TEXT NOT NULL,
    "status" "DragTreeNodeContentStatus" NOT NULL DEFAULT 'INITIALIZED',
    "content_type" TEXT DEFAULT 'TEXT',
    "content_version" TEXT DEFAULT 'v1',
    "content_text" TEXT DEFAULT '',
    "content_metadata" JSONB DEFAULT '{}',
    "messages" JSONB DEFAULT '[]',
    "generation_metadata" JSONB DEFAULT '{}',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "drag_tree_node_content_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "drag_tree_id_idx" ON "drag_tree" USING HASH ("id");

-- CreateIndex
CREATE INDEX "drag_tree_user_id_idx" ON "drag_tree" USING HASH ("user_id");

-- CreateIndex
CREATE INDEX "drag_tree_status_idx" ON "drag_tree" USING HASH ("status");

-- CreateIndex
CREATE INDEX "drag_tree_created_at_idx" ON "drag_tree"("created_at");

-- CreateIndex
CREATE INDEX "drag_tree_node_id_idx" ON "drag_tree_node" USING HASH ("id");

-- CreateIndex
CREATE INDEX "drag_tree_node_drag_tree_id_idx" ON "drag_tree_node" USING HASH ("drag_tree_id");

-- CreateIndex
CREATE INDEX "drag_tree_node_created_at_idx" ON "drag_tree_node"("created_at");

-- CreateIndex
CREATE INDEX "drag_tree_node_content_id_idx" ON "drag_tree_node_content" USING HASH ("id");

-- CreateIndex
CREATE INDEX "drag_tree_node_content_drag_tree_id_idx" ON "drag_tree_node_content" USING HASH ("drag_tree_id");

-- CreateIndex
CREATE INDEX "drag_tree_node_content_drag_tree_node_id_idx" ON "drag_tree_node_content" USING HASH ("drag_tree_node_id");

-- CreateIndex
CREATE INDEX "drag_tree_node_content_status_idx" ON "drag_tree_node_content" USING HASH ("status");

-- CreateIndex
CREATE INDEX "drag_tree_node_content_created_at_idx" ON "drag_tree_node_content"("created_at");

-- AddForeignKey
ALTER TABLE "drag_tree" ADD CONSTRAINT "drag_tree_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "drag_tree_node" ADD CONSTRAINT "drag_tree_node_drag_tree_id_fkey" FOREIGN KEY ("drag_tree_id") REFERENCES "drag_tree"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "drag_tree_node_content" ADD CONSTRAINT "drag_tree_node_content_drag_tree_id_fkey" FOREIGN KEY ("drag_tree_id") REFERENCES "drag_tree"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "drag_tree_node_content" ADD CONSTRAINT "drag_tree_node_content_drag_tree_node_id_fkey" FOREIGN KEY ("drag_tree_node_id") REFERENCES "drag_tree_node"("id") ON DELETE CASCADE ON UPDATE CASCADE;
