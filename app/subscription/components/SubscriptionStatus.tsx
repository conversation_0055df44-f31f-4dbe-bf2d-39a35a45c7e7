'use client'

import { useSession } from 'next-auth/react'
import { SubscriptionTier } from '@prisma/client'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  AlertTriangle,
  Crown,
  Users,
  Zap,
  Building,
  Eye,
  TestTube,
} from 'lucide-react'
import { format } from 'date-fns'

const tierIcons = {
  [SubscriptionTier.VIEWER]: Eye,
  [SubscriptionTier.DUMMY]: TestTube,
  [SubscriptionTier.FREE]: null,
  [SubscriptionTier.PRO]: Crown,
  [SubscriptionTier.GUEST]: Users,
  [SubscriptionTier.ULTRA]: Zap,
  [SubscriptionTier.BUSINESS]: Building,
}

const tierColors = {
  [SubscriptionTier.VIEWER]: 'bg-slate-100 text-slate-800',
  [SubscriptionTier.DUMMY]: 'bg-orange-100 text-orange-800',
  [SubscriptionTier.FREE]: 'bg-gray-100 text-gray-800',
  [SubscriptionTier.PRO]: 'bg-purple-100 text-purple-800',
  [SubscriptionTier.GUEST]: 'bg-blue-100 text-blue-800',
  [SubscriptionTier.ULTRA]: 'bg-yellow-100 text-yellow-800',
  [SubscriptionTier.BUSINESS]: 'bg-green-100 text-green-800',
}

export function SubscriptionStatus() {
  const { data: session } = useSession()

  if (!session?.user) {
    return null
  }

  const { subscription } = session.user
  const tier = subscription?.tier || SubscriptionTier.FREE
  const expiry = subscription?.expiry
  const cancelPending = subscription?.cancelPending || false

  const TierIcon = tierIcons[tier]
  const tierColorClass = tierColors[tier]

  const formatExpiryDate = (dateString: string | null) => {
    if (!dateString) return null
    try {
      return format(new Date(dateString), 'MMM dd, yyyy')
    } catch {
      return null
    }
  }

  const isExpiringSoon =
    expiry && new Date(expiry) < new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          {TierIcon && <TierIcon className="h-5 w-5" />}
          Subscription Status
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Current Plan:</span>
          <Badge className={tierColorClass}>
            {tier.charAt(0) + tier.slice(1).toLowerCase()}
          </Badge>
        </div>

        {expiry && tier !== SubscriptionTier.GUEST && (
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">
              {cancelPending ? 'Expires:' : 'Renews:'}
            </span>
            <span className="text-sm text-gray-600">
              {formatExpiryDate(expiry)}
            </span>
          </div>
        )}

        {cancelPending && (
          <div className="flex items-start gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="font-medium text-yellow-800">Subscription Ending</p>
              <p className="text-yellow-700">
                Your Pro features will end on {formatExpiryDate(expiry)}. You
                can reactivate anytime before then.
              </p>
            </div>
          </div>
        )}

        {isExpiringSoon && !cancelPending && (
          <div className="flex items-start gap-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <AlertTriangle className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="font-medium text-blue-800">Renewal Coming Up</p>
              <p className="text-blue-700">
                Your subscription will renew on {formatExpiryDate(expiry)}.
              </p>
            </div>
          </div>
        )}

        {tier === SubscriptionTier.GUEST && (
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-700">
              <span className="font-medium">Guest Access:</span> You have
              complimentary access to all Pro features. This access doesn&apos;t
              expire.
            </p>
          </div>
        )}

        {tier === SubscriptionTier.FREE && (
          <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
            <p className="text-sm text-gray-600">
              Upgrade to Pro to unlock advanced features and higher limits.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
