import { TreeNode } from "@/app/types";

/**
 * Core tree manipulation operations
 * Functions for creating, modifying, and manipulating tree structures
 */

/**
 * Recursively find a node by ID in a tree
 */
export const findNodeById = (
  root: TreeNode,
  targetId: string
): TreeNode | null => {
  if (root.id === targetId) {
    return root;
  }

  for (const child of root.children) {
    const found = findNodeById(child, targetId);
    if (found) {
      return found;
    }
  }

  return null;
};

/**
 * Add a new node to a parent node
 */
export const addNodeToParent = (
  root: TreeNode,
  parentId: string,
  newNode: TreeNode
): TreeNode => {
  if (root.id === parentId) {
    return {
      ...root,
      children: [...root.children, newNode],
    };
  }

  return {
    ...root,
    children: root.children.map((child) =>
      addNodeToParent(child, parentId, newNode)
    ),
  };
};

/**
 * Remove a node from the tree by ID
 */
export const removeNodeById = (
  root: TreeNode,
  targetId: string
): TreeNode | null => {
  // Don't remove the root node
  if (root.id === targetId) {
    return null;
  }

  return {
    ...root,
    children: root.children
      .filter((child) => child.id !== targetId)
      .map((child) => removeNodeById(child, targetId))
      .filter(Boolean) as TreeNode[],
  };
};

/**
 * Update a node's properties by ID
 */
export const updateNodeById = (
  root: TreeNode,
  targetId: string,
  updates: Partial<TreeNode>
): TreeNode => {
  if (root.id === targetId) {
    return { ...root, ...updates };
  }

  return {
    ...root,
    children: root.children.map((child) =>
      updateNodeById(child, targetId, updates)
    ),
  };
};

/**
 * Get all nodes in the tree as a flat array
 */
export const flattenTree = (root: TreeNode): TreeNode[] => {
  const result: TreeNode[] = [root];

  for (const child of root.children) {
    result.push(...flattenTree(child));
  }

  return result;
};

/**
 * Count total nodes in the tree
 */
export const countNodes = (root: TreeNode): number => {
  return 1 + root.children.reduce((sum, child) => sum + countNodes(child), 0);
};

/**
 * Get the depth of the tree
 */
export const getTreeDepth = (root: TreeNode): number => {
  if (root.children.length === 0) {
    return 1;
  }

  const childDepths = root.children.map((child) => getTreeDepth(child));
  return 1 + Math.max(...childDepths);
};

/**
 * Clone a tree node deeply
 */
export const cloneTreeNode = (node: TreeNode): TreeNode => {
  return {
    ...node,
    children: node.children.map((child) => cloneTreeNode(child)),
  };
};
