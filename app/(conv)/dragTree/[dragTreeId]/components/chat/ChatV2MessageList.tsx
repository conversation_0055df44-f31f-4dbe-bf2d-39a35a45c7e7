'use client'

import React from 'react'
import { <PERSON><PERSON><PERSON>, FiArrowDown } from 'react-icons/fi'
import type { UIMessage } from 'ai'
import {
  Conversation,
  ConversationContent,
  ConversationScrollButton,
} from '@/components/ai-elements/conversation'
import { Message, MessageContent } from '@/components/ai-elements/message'
import { Response } from '@/components/ai-elements/response'
import {
  Tool,
  ToolHeader,
  ToolContent,
  ToolInput,
} from '@/components/ai-elements/tool'
import {
  Reasoning,
  ReasoningTrigger,
  ReasoningContent,
} from '@/components/ai-elements/reasoning'

type ChatV2MessageListProps = {
  messages: UIMessage[]
  isLoading?: boolean
  className?: string
}

// Helper function to render tool calls compactly, aligned with chat-demo style
const renderCursorStyleTools = (toolParts: any[], messageId: string) => {
  // Group by toolCallId and normalize unknown tool-* parts
  type Group = {
    name: string
    args?: any
    complete: boolean
    errorText?: string
  }

  const groups = new Map<string, Group>()

  for (const part of toolParts) {
    const toolCallId = part.toolCallId || part.id || `unknown-${messageId}`
    const name = part.toolName || part.type || 'tool'
    const group = groups.get(toolCallId) || {
      name,
      args: undefined,
      complete: false,
      errorText: undefined,
    }

    if (part.type === 'tool-call') {
      group.args = part.args
    } else if (part.type === 'tool-result') {
      group.complete = true
      group.errorText = part.errorText
    } else if (typeof part.type === 'string' && part.type.startsWith('tool-')) {
      // Custom tool parts may include both input and output; show only input and state
      group.args = part.input ?? group.args
      group.complete = part.state === 'output-available' || group.complete
      group.errorText = part.errorText ?? group.errorText
    }

    groups.set(toolCallId, group)
  }

  return Array.from(groups.entries()).map(([toolCallId, g], index) => (
    <div
      key={`${messageId}-tool-group-${toolCallId}-${index}`}
      className="border border-gray-200 rounded-md bg-gray-50/30 text-xs"
    >
      <Tool>
        <ToolHeader
          // accept unknown custom types by casting to any to match component typing
          type={g.name as any}
          state={
            g.errorText
              ? 'output-error'
              : g.complete
                ? 'output-available'
                : 'input-available'
          }
          className="px-2 py-1.5 text-xs hover:bg-gray-100/30"
        />
        <ToolContent>
          {g.args !== undefined && (
            <div className="px-2 pb-1">
              {/* show only the input/params; hide full outputs */}
              <ToolInput input={g.args} />
            </div>
          )}
          {/* Intentionally hide ToolOutput to avoid dumping large results */}
        </ToolContent>
      </Tool>
    </div>
  ))
}

export default function ChatV2MessageList({
  messages,
  isLoading = false,
  className = '',
}: ChatV2MessageListProps) {
  // StickToBottom handles scrolling within the chat container.
  // Avoid window-level scrollIntoView to prevent the entire page from jumping.

  // Filter out system messages for display
  const displayMessages = messages.filter(message => message.role !== 'system')

  // Helper function to group tool calls and results together
  const groupToolParts = (parts: any[]) => {
    const textParts: any[] = []
    const toolParts: any[] = []
    const reasoningParts: any[] = []

    parts.forEach(part => {
      // Treat standard tool-call/result as tool parts
      if (part.type === 'tool-call' || part.type === 'tool-result') {
        toolParts.push(part)
      } else if (
        typeof part.type === 'string' &&
        part.type.startsWith('tool-')
      ) {
        // Also treat custom tool parts like 'tool-webSearch' as tool parts so they render
        toolParts.push(part)
      } else if (part.type === 'reasoning') {
        reasoningParts.push(part)
      } else {
        textParts.push(part)
      }
    })

    return { textParts, toolParts, reasoningParts }
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      <Conversation className="flex-1">
        <ConversationContent>
          {displayMessages.length === 0 ? (
            <div className="flex items-center justify-center h-full text-gray-500">
              <div className="text-center">
                <FiCpu className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p className="text-lg font-medium">Start a conversation</p>
                <p className="text-sm">
                  Send a message to begin chatting with AI
                </p>
              </div>
            </div>
          ) : (
            displayMessages.map((message, messageIndex) => {
              // Normalize to parts array for rendering. Some client messages may only have `content`.
              const partsForGrouping: any[] = (() => {
                const m: any = message as any
                if (Array.isArray(m.parts) && m.parts.length > 0) return m.parts
                if (
                  typeof m.content === 'string' &&
                  m.content.trim().length > 0
                ) {
                  return [{ type: 'text', text: m.content }]
                }
                if (Array.isArray(m.content)) {
                  return m.content.map((c: any) =>
                    typeof c === 'string' ? { type: 'text', text: c } : c
                  )
                }
                return []
              })()

              // Group message parts for better organization
              const { textParts, toolParts, reasoningParts } =
                groupToolParts(partsForGrouping)

              return (
                <Message
                  key={message.id}
                  from={message.role}
                  className=""
                  data-msg-idx={(message as any)._idx}
                >
                  <MessageContent className="p-2">
                    {/* Render tool calls first to show logical flow */}
                    {toolParts.length > 0 && (
                      <div className="mb-3 space-y-1">
                        {renderCursorStyleTools(toolParts, message.id)}
                      </div>
                    )}

                    {/* Render reasoning parts (for o1-series models) */}
                    {reasoningParts.map((part, partIndex) => (
                      <Reasoning
                        key={`${message.id}-reasoning-${partIndex}`}
                        isStreaming={
                          isLoading &&
                          messageIndex === displayMessages.length - 1
                        }
                        defaultOpen={false}
                      >
                        <ReasoningTrigger />
                        <ReasoningContent>{part.text}</ReasoningContent>
                      </Reasoning>
                    ))}

                    {/* Render main text content */}
                    {textParts.map((part, partIndex) => {
                      // Handle text parts
                      if (part.type === 'text') {
                        return (
                          <Response
                            key={`${message.id}-text-${partIndex}`}
                            defaultOrigin={
                              typeof window !== 'undefined'
                                ? window.location.origin
                                : undefined
                            }
                          >
                            {part.text}
                          </Response>
                        )
                      }

                      // Handle text-delta parts (streaming text)
                      if ((part as any).type === 'text-delta') {
                        return (
                          <Response
                            key={`${message.id}-text-delta-${partIndex}`}
                            defaultOrigin={
                              typeof window !== 'undefined'
                                ? window.location.origin
                                : undefined
                            }
                          >
                            {(part as any).textDelta}
                          </Response>
                        )
                      }

                      // Handle unknown part types gracefully
                      console.warn(
                        'Unknown message part type:',
                        part.type,
                        part
                      )
                      return null
                    })}

                    {/* Show loading indicator for streaming messages */}
                    {isLoading &&
                      messageIndex === displayMessages.length - 1 && (
                        <div className="flex items-center gap-2 text-xs text-gray-500 mt-2">
                          <div className="flex space-x-1">
                            <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" />
                            <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce delay-100" />
                            <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce delay-200" />
                          </div>
                          <span>AI is thinking...</span>
                        </div>
                      )}
                  </MessageContent>
                </Message>
              )
            })
          )}

          {/* StickToBottom internally manages the scroll anchor */}
        </ConversationContent>

        {/* Scroll to bottom button */}
        <ConversationScrollButton>
          <FiArrowDown className="w-4 h-4" />
        </ConversationScrollButton>
      </Conversation>
    </div>
  )
}
