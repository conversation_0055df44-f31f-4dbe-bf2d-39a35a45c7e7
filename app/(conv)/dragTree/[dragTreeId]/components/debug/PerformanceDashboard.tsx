'use client'

import React, { useState, useEffect } from 'react'
import { performanceMonitor } from '@/app/utils/performance-monitor'

/**
 * Performance Dashboard Component
 *
 * Displays real-time performance metrics for the DragTree system
 * Shows payload sizes, load times, and optimization improvements
 * Only visible in development mode
 */
export const PerformanceDashboard: React.FC<{
  isVisible?: boolean
  onToggle?: () => void
}> = ({ isVisible = false, onToggle }) => {
  const [dashboardData, setDashboardData] = useState<any>(null)
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(
    null
  )

  // Refresh dashboard data
  const refreshData = () => {
    const data = performanceMonitor.getDashboardData()
    setDashboardData(data)
  }

  useEffect(() => {
    if (isVisible) {
      // Initial load
      refreshData()

      // Set up auto-refresh
      const interval = setInterval(refreshData, 2000) // Refresh every 2 seconds
      setRefreshInterval(interval)

      return () => {
        if (interval) clearInterval(interval)
      }
    } else {
      if (refreshInterval) {
        clearInterval(refreshInterval)
        setRefreshInterval(null)
      }
    }
  }, [isVisible])

  if (!isVisible || !dashboardData) {
    return null
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatTime = (ms: number) => {
    return `${ms.toFixed(2)}ms`
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-200 rounded-lg shadow-xl p-4 max-w-md max-h-96 overflow-y-auto z-50">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-semibold text-gray-900">
          Performance Dashboard
        </h3>
        <button
          onClick={onToggle}
          className="text-gray-500 hover:text-gray-700 text-xl"
          title="Hide Dashboard"
        >
          ×
        </button>
      </div>

      {/* Summary Statistics */}
      <div className="space-y-3">
        <div className="bg-gray-50 rounded-lg p-3">
          <h4 className="font-medium text-gray-700 mb-2">Operation Summary</h4>
          <div className="space-y-2 text-sm">
            {Object.entries(dashboardData.summary).map(
              ([operation, stats]: [string, any]) => (
                <div
                  key={operation}
                  className="flex justify-between items-center"
                >
                  <span className="text-gray-600">{operation}:</span>
                  <div className="text-right">
                    <div className="text-gray-900">
                      {formatTime(stats.averageDuration)} avg
                    </div>
                    <div className="text-gray-500 text-xs">
                      {formatBytes(stats.averagePayloadSize)} | {stats.count}{' '}
                      calls
                    </div>
                  </div>
                </div>
              )
            )}
          </div>
        </div>

        {/* Recent Metrics */}
        <div className="bg-gray-50 rounded-lg p-3">
          <h4 className="font-medium text-gray-700 mb-2">Recent Activity</h4>
          <div className="space-y-1 text-xs max-h-32 overflow-y-auto">
            {dashboardData.recentMetrics
              .slice(-10)
              .reverse()
              .map((metric: any, index: number) => (
                <div
                  key={index}
                  className="flex justify-between items-center py-1 border-b border-gray-200 last:border-b-0"
                >
                  <span className="text-gray-600 truncate flex-1 mr-2">
                    {metric.operation}
                  </span>
                  <div className="text-right flex-shrink-0">
                    <span
                      className={`font-medium ${metric.errorCount > 0 ? 'text-red-600' : 'text-green-600'}`}
                    >
                      {formatTime(metric.duration)}
                    </span>
                    {metric.payloadSize > 0 && (
                      <div className="text-gray-500">
                        {formatBytes(metric.payloadSize)}
                      </div>
                    )}
                  </div>
                </div>
              ))}
          </div>
        </div>

        {/* Performance Trends */}
        <div className="bg-gray-50 rounded-lg p-3">
          <h4 className="font-medium text-gray-700 mb-2">Trends</h4>
          <div className="space-y-2 text-sm">
            {Object.entries(dashboardData.trends).map(([operation, times]) => {
              const timesArray = times as number[]
              if (timesArray.length < 2) return null

              const recent =
                timesArray.slice(-3).reduce((a, b) => a + b, 0) /
                Math.min(3, timesArray.length)
              const older =
                timesArray.slice(0, -3).reduce((a, b) => a + b, 0) /
                Math.max(1, timesArray.length - 3)
              const trend = recent - older
              const trendPercent = older > 0 ? (trend / older) * 100 : 0

              return (
                <div
                  key={operation}
                  className="flex justify-between items-center"
                >
                  <span className="text-gray-600 truncate flex-1 mr-2">
                    {operation}:
                  </span>
                  <div className="text-right flex-shrink-0">
                    <span
                      className={`font-medium ${trend < 0 ? 'text-green-600' : trend > 0 ? 'text-red-600' : 'text-gray-600'}`}
                    >
                      {trend < 0 ? '↓' : trend > 0 ? '↑' : '→'}{' '}
                      {Math.abs(trendPercent).toFixed(1)}%
                    </span>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex space-x-2">
          <button
            onClick={() => performanceMonitor.clearMetrics()}
            className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded text-sm font-medium transition-colors"
          >
            Clear Metrics
          </button>
          <button
            onClick={() => {
              const metrics = performanceMonitor.exportMetrics()
              const blob = new Blob([JSON.stringify(metrics, null, 2)], {
                type: 'application/json',
              })
              const url = URL.createObjectURL(blob)
              const a = document.createElement('a')
              a.href = url
              a.download = `performance-metrics-${new Date().toISOString().slice(0, 19)}.json`
              a.click()
              URL.revokeObjectURL(url)
            }}
            className="flex-1 bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-2 rounded text-sm font-medium transition-colors"
          >
            Export Data
          </button>
        </div>
      </div>
    </div>
  )
}

/**
 * Hook to manage performance dashboard visibility
 */
export const usePerformanceDashboard = () => {
  const [isVisible, setIsVisible] = useState(false)

  // Only show in development
  const isDevelopment = process.env.NODE_ENV === 'development'

  const toggle = () => {
    if (isDevelopment) {
      setIsVisible(prev => !prev)
    }
  }

  return {
    isVisible: isVisible && isDevelopment,
    toggle,
    isDevelopment,
  }
}
