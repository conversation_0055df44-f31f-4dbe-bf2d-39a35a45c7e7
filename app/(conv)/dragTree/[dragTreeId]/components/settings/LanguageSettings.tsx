'use client'

import React, { useState } from 'react'
import { toast } from 'react-hot-toast'
import { Globe } from 'lucide-react'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { updateDragTreeLanguage } from '@/app/server-actions/drag-tree'
import LanguageSelector from '@/app/components/ui/LanguageSelector'
import LanguageChangeConfirmDialog from './LanguageChangeConfirmDialog'
import {
  getLanguageName,
  type SupportedLanguageCode,
  DEFAULT_LANGUAGE,
} from '@/app/constants/languages'
import { getAccessPermissions } from '@/app/configs/tier-permissions'
import { useSession } from 'next-auth/react'

type LanguageSettingsProps = {
  dragTreeId: string
}

/**
 * Language Settings Component
 *
 * Provides interface for users to change their drag tree language preference.
 * Includes confirmation dialog warning that changes only apply to future AI generations.
 */
const LanguageSettings: React.FC<LanguageSettingsProps> = ({ dragTreeId }) => {
  // Get user session and permissions
  const { data: session } = useSession()
  const userTier = session?.user?.subscription?.tier
  const permissions = getAccessPermissions(userTier || 'FREE')
  const canEditLanguagePreference = permissions.canEditLanguagePreference

  // Get current language from store
  const preferredLanguage = useDragTreeStore(state => state.preferredLanguage)
  const setPreferredLanguage = useDragTreeStore(
    state => state.setPreferredLanguage
  )

  // Local state for confirmation dialog
  const [showConfirmDialog, setShowConfirmDialog] = useState<boolean>(false)
  const [pendingLanguage, setPendingLanguage] =
    useState<SupportedLanguageCode>(DEFAULT_LANGUAGE)
  const [isUpdating, setIsUpdating] = useState<boolean>(false)

  // Get current language with fallback
  const currentLanguage =
    (preferredLanguage as SupportedLanguageCode) || DEFAULT_LANGUAGE

  const handleLanguageChange = (newLanguage: SupportedLanguageCode) => {
    // If same language, do nothing
    if (newLanguage === currentLanguage) {
      return
    }

    // Show confirmation dialog
    setPendingLanguage(newLanguage)
    setShowConfirmDialog(true)
  }

  const handleConfirmLanguageChange = async () => {
    if (!dragTreeId) {
      toast.error('No drag tree selected')
      return
    }

    setIsUpdating(true)
    try {
      const result = await updateDragTreeLanguage(dragTreeId, pendingLanguage)

      if (result.success) {
        // Update the store
        setPreferredLanguage(pendingLanguage)

        // Show success message
        const languageName = getLanguageName(pendingLanguage)
        toast.success(`Language preference updated to ${languageName}`)

        // Close dialog
        setShowConfirmDialog(false)
      } else {
        toast.error('Failed to update language preference')
      }
    } catch (error) {
      console.error('Error updating language preference:', error)
      toast.error('Failed to update language preference')
    } finally {
      setIsUpdating(false)
    }
  }

  const handleCancelLanguageChange = () => {
    setShowConfirmDialog(false)
    setPendingLanguage(DEFAULT_LANGUAGE)
  }

  return (
    <>
      <div className="space-y-4">
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <Globe className="h-5 w-5 text-gray-400" />
          </div>
          <div className="flex-1">
            <h3 className="text-sm font-medium text-gray-900">
              Language Preference
            </h3>
            <p className="text-xs text-gray-500 mt-1">
              Choose the language for AI-generated content in this drag tree
            </p>
          </div>
        </div>

        <div className="pl-8">
          <LanguageSelector
            value={currentLanguage}
            onChange={handleLanguageChange}
            disabled={isUpdating || !canEditLanguagePreference}
            compact={false}
          />
          {!canEditLanguagePreference && (
            <p className="text-xs text-gray-500 mt-2">
              Language preferences cannot be changed
            </p>
          )}
        </div>
      </div>

      {/* Confirmation Dialog */}
      <LanguageChangeConfirmDialog
        isOpen={showConfirmDialog}
        onClose={handleCancelLanguageChange}
        onConfirm={handleConfirmLanguageChange}
        newLanguage={pendingLanguage}
        isLoading={isUpdating}
      />
    </>
  )
}

export default LanguageSettings
