'use server'

import { auth } from '@/auth'
import prisma from '@/app/libs/prismadb'

export type NodeContentBatchItem = {
  id: string
  node_id: string
  content_text: string
  content_metadata: any
  messages: any
}

/**
 * Batch-fetch drag-tree research content by contentId list.
 * Returns only items the logged-in user owns; foreign ids are silently ignored.
 */
export async function getNodeContentsBatch(
  contentIds: string[]
): Promise<NodeContentBatchItem[]> {
  if (!Array.isArray(contentIds) || contentIds.length === 0) return []

  const session = await auth()
  if (!session?.user?.id) throw new Error('Unauthorized')

  // Fetch all requested items + their tree ownership
  const contents = await prisma.dragTreeNodeContent.findMany({
    where: {
      id: { in: contentIds },
      drag_tree: { user_id: session.user.id },
    },
    select: {
      id: true,
      drag_tree_node_id: true,
      content_text: true,
      content_metadata: true,
      messages: true,
    },
  })

  return contents.map(c => ({
    id: c.id,
    node_id: c.drag_tree_node_id,
    content_text: c.content_text ?? '',
    content_metadata: c.content_metadata ?? {},
    messages: c.messages ?? [],
  }))
}
