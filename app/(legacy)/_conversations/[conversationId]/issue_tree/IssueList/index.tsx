import React, { useState } from 'react'
import useIssueTreeStore from '@/app/stores/_legacy/issuetree_store'
import { IssueTreeStoreType } from '@/app/stores/_legacy/issuetree_store'
import IssueListItem from './IssueListItem'
import { Node, Edge } from '@/app/types'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { ServiceProvider } from '@/app/types/index'

import { SearchServiceDropdown } from './SearchServiceDropdown'
import useNotebookStore from '@/app/stores/_legacy/notebook_store'
import { Button } from '@/components/ui/button'

// Extended Node type with categories
export type ExtendedNode = Node & {
  categories: string[]
}

// Available search service providers
export const searchServiceProviders: ServiceProvider[] = [
  {
    id: 'ms_copilot',
    display_text: 'MS Copilot',
    url: 'https://copilot.microsoft.com/',
  },
  { id: 'you', display_text: 'You.com', url: 'https://you.com/' },
  {
    id: 'perplexity',
    display_text: 'Perplexity AI',
    url: 'https://www.perplexity.ai/',
  },
]

const Component: React.FC = () => {
  const issueTreeStore = useIssueTreeStore()
  const notebookStore = useNotebookStore()
  const nodesWithCategories = getValidQuestionsWithCategories(issueTreeStore)
  const [unresolvedQsOnly, setUnresolvedQsOnly] = useState(false)
  const [selectedSearchService, setSelectedSearchService] =
    useState('ms_copilot')

  // Filter nodes based on resolution status and type
  const filteredNodesWithCategories = nodesWithCategories.filter(
    node =>
      (!unresolvedQsOnly || (node.data && !node.data.resolved)) &&
      node.type === 'customLeafNode'
  )

  const handleSwitchChange = () => setUnresolvedQsOnly(!unresolvedQsOnly)

  const handleVisitNotebookDialog = () => {
    notebookStore.setShowDialog(true)
    issueTreeStore.setTreeMode()
  }

  return (
    <div className="flex flex-col h-full">
      <div className="p-4 bg-white flex items-center justify-items-start space-x-4">
        <SearchServiceDropdown
          selectedSearchService={selectedSearchService}
          setSelectedSearchService={setSelectedSearchService}
        />
        <ResolutionStatusSwitch
          unresolvedQsOnly={unresolvedQsOnly}
          handleSwitchChange={handleSwitchChange}
        />
      </div>
      <IssueListContent
        filteredNodesWithCategories={filteredNodesWithCategories}
        selectedSearchService={selectedSearchService}
        issueTreeStore={issueTreeStore}
        handleVisitNotebookDialog={handleVisitNotebookDialog}
      />
    </div>
  )
}

// Resolution Status Switch Component
const ResolutionStatusSwitch: React.FC<{
  unresolvedQsOnly: boolean
  handleSwitchChange: () => void
}> = ({ unresolvedQsOnly, handleSwitchChange }) => (
  <div className="flex items-center">
    <Switch
      id="valid-only"
      checked={unresolvedQsOnly}
      onClick={handleSwitchChange}
    />
    <Label htmlFor="valid-only" className="ml-2 w-56">
      {unresolvedQsOnly ? 'Showing unreviewed only' : 'Showing all'}
    </Label>
  </div>
)

// Issue List Content Component
const IssueListContent: React.FC<{
  filteredNodesWithCategories: ExtendedNode[]
  selectedSearchService: string
  issueTreeStore: IssueTreeStoreType
  handleVisitNotebookDialog: () => void
}> = ({
  filteredNodesWithCategories,
  selectedSearchService,
  issueTreeStore,
  handleVisitNotebookDialog,
}) => (
  <div className="flex-grow overflow-auto p-6 bg-gray-100">
    {filteredNodesWithCategories.length > 0 ? (
      filteredNodesWithCategories.map(node =>
        node.type === 'customLeafNode' ? (
          <IssueListItem
            key={node.id}
            node={node}
            categories={node.categories}
            selected_search_service={selectedSearchService}
          />
        ) : null
      )
    ) : (
      <NoUnresolvedQuestionsView
        issueTreeStore={issueTreeStore}
        handleVisitNotebookDialog={handleVisitNotebookDialog}
      />
    )}
  </div>
)

// No Unresolved Questions View Component
const NoUnresolvedQuestionsView: React.FC<{
  issueTreeStore: IssueTreeStoreType
  handleVisitNotebookDialog: () => void
}> = ({ issueTreeStore, handleVisitNotebookDialog }) => (
  <div className="flex flex-col space-y-4">
    <h1 className="text-2xl font-bold text-center">
      🎉 No unresolved questions found!
    </h1>
    <Button
      className="flex flex-row items-center justify-center text-2xl text-blue-500 bg-white hover:bg-blue-100 p-3 py-10"
      onClick={() => issueTreeStore.setTreeMode()}
    >
      Back to issue tree
    </Button>
    <Button
      className="flex flex-row items-center justify-center text-2xl text-purple-500 bg-white hover:bg-purple-100 p-3 py-10"
      onClick={handleVisitNotebookDialog}
    >
      OR generate a new notebook
    </Button>
  </div>
)

export default Component

// Function to find the path from root to each node, excluding CustomLeafNode label
const findPath = (nodeId: string, nodes: Node[], edges: Edge[]): string[] => {
  const path = []
  let currentNodeId = nodeId

  while (currentNodeId !== 'L1:1') {
    const node = nodes.find(n => n.id === currentNodeId)
    if (node && node.type !== 'customLeafNode') {
      path.unshift(node.data?.label || currentNodeId)
    }
    const parentEdge = edges.find(edge => edge.target === currentNodeId)
    if (parentEdge) {
      currentNodeId = parentEdge.source
    } else {
      break
    }
  }

  // Add the root node at the beginning of the path
  const rootNode = nodes.find(n => n.id === 'L1:1')
  if (rootNode) {
    path.unshift(rootNode.data?.label || 'L1:1')
  }

  return path
}

// Function to get valid questions with categories
const getValidQuestionsWithCategories = (
  issueTreeStore: IssueTreeStoreType
): ExtendedNode[] => {
  const nodes = issueTreeStore.nodes
  const edges = issueTreeStore.edges

  return nodes
    .filter(node => node.type === 'customLeafNode')
    .map(question => {
      const categories = findPath(question.id, nodes, edges)
      return {
        ...question,
        categories,
      }
    })
}
