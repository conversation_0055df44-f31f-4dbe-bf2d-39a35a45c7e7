import posthog from 'posthog-js'
import {
  ensurePostHogInitialized,
  isPostHogReady,
} from '@/instrumentation-client'

// Proxy that retries initialization on demand
const ensureReady = (): boolean => {
  if (isPostHogReady()) return true
  // Try to (re)initialize – return whatever state we get
  return ensurePostHogInitialized()
}

// Utility functions for consistent event tracking
export const trackEvent = (
  eventName: string,
  properties?: Record<string, any>
) => {
  if (ensureReady()) {
    try {
      posthog.capture(eventName, properties)
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('PostHog trackEvent failed:', error)
      }
    }
  } else if (process.env.NODE_ENV === 'development') {
    console.warn('PostHog not initialized, skipping event:', eventName)
  }
}

export const identifyUser = (
  userId: string,
  properties?: Record<string, any>
) => {
  if (ensureReady()) {
    try {
      posthog.identify(userId, properties)
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('PostHog identifyUser failed:', error)
      }
    }
  } else if (process.env.NODE_ENV === 'development') {
    console.warn(
      'PostHog not initialized, skipping user identification:',
      userId
    )
  }
}

export const setUserProperties = (properties: Record<string, any>) => {
  if (ensureReady()) {
    try {
      posthog.setPersonProperties(properties)
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('PostHog setUserProperties failed:', error)
      }
    }
  } else if (process.env.NODE_ENV === 'development') {
    console.warn(
      'PostHog not initialized, skipping user properties:',
      properties
    )
  }
}

// Export the initialization check for external use
export { isPostHogReady }
