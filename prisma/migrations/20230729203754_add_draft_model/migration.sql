-- CreateEnum
CREATE TYPE "DraftStatus" AS ENUM ('ACTIVE', 'INACTIVE');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "DraftType" AS ENUM ('BASE');

-- AlterEnum
ALTER TYPE "OpenAIUsageType" ADD VALUE 'DRAFTED_DOCUMENT';

-- CreateTable
CREATE TABLE "drafts" (
    "id" TEXT NOT NULL,
    "status" "DraftStatus" NOT NULL DEFAULT 'ACTIVE',
    "type" "DraftType" NOT NULL DEFAULT 'BASE',
    "content" TEXT,
    "original_content" TEXT,
    "config" JSONB NOT NULL DEFAULT '{}',
    "user_id" TEXT,
    "conversation_id" TEXT,
    "prompt_id" TEXT,
    "exact_prompt" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "drafts_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "drafts_id_idx" ON "drafts" USING HASH ("id");

-- CreateIndex
CREATE INDEX "drafts_conversation_id_idx" ON "drafts" USING HASH ("conversation_id");

-- CreateIndex
CREATE INDEX "drafts_user_id_idx" ON "drafts" USING HASH ("user_id");

-- CreateIndex
CREATE INDEX "drafts_prompt_id_idx" ON "drafts" USING HASH ("prompt_id");

-- AddForeignKey
ALTER TABLE "drafts" ADD CONSTRAINT "drafts_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "drafts" ADD CONSTRAINT "drafts_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "conversations"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "drafts" ADD CONSTRAINT "drafts_prompt_id_fkey" FOREIGN KEY ("prompt_id") REFERENCES "prompts"("id") ON DELETE SET NULL ON UPDATE CASCADE;
