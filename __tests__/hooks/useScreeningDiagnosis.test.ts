import { renderHook, act } from '@testing-library/react'
import { useScreeningDiagnosis } from '@/app/(conv)/screening/hooks/useScreeningDiagnosis'
import { useSession } from 'next-auth/react'
import { toast } from 'react-hot-toast'

// Mock dependencies for AI SDK 5 - using central mock
jest.mock('next-auth/react')
jest.mock('react-hot-toast')

// Get the mock from the central mock file
const { experimental_useObject } = require('@ai-sdk/react')
const useObjectMock = experimental_useObject as jest.Mock
const useSessionMock = useSession as jest.Mock

describe('useScreeningDiagnosis', () => {
  let mockSubmit: jest.Mock
  let onScreenObjectUpdate: jest.Mock

  beforeEach(() => {
    jest.clearAllMocks()
    jest.useFakeTimers()
    mockSubmit = jest.fn()
    onScreenObjectUpdate = jest.fn()

    useObjectMock.mockReturnValue({
      object: null,
      submit: mockSubmit,
      isLoading: false,
    })
    useSessionMock.mockReturnValue({ data: { user: { id: 'test-user' } } })
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  it('should call submit with the correct data', () => {
    const { result } = renderHook(() =>
      useScreeningDiagnosis({ onScreenObjectUpdate })
    )
    const testData = {
      description: 'test description',
      preferredLanguage: 'en' as const,
      userId: 'test-user-id',
    }

    act(() => {
      result.current.submit(testData)
    })

    // Advance timers to handle debounced behavior
    act(() => {
      jest.advanceTimersByTime(500)
    })

    expect(mockSubmit).toHaveBeenCalledWith(testData)
  })

  it('should show an error if user is not authenticated', () => {
    useSessionMock.mockReturnValue({ data: null })
    const { result } = renderHook(() =>
      useScreeningDiagnosis({ onScreenObjectUpdate })
    )
    const testData = {
      description: 'test description',
      preferredLanguage: 'en' as const,
      userId: 'test-user-id',
    }

    act(() => {
      result.current.submit(testData)
    })

    expect(toast.error).toHaveBeenCalledWith(
      'Please log in to use the real API.'
    )
    expect(mockSubmit).not.toHaveBeenCalled()
  })

  it('should call onScreenObjectUpdate when the object result changes', () => {
    const { rerender } = renderHook(() =>
      useScreeningDiagnosis({ onScreenObjectUpdate })
    )

    const newObject = { problem_ambiguity: 'High' }
    useObjectMock.mockReturnValue({
      object: newObject,
      submit: mockSubmit,
      isLoading: false,
    })

    rerender({})

    // Advance timers to handle debounced behavior
    act(() => {
      jest.advanceTimersByTime(500)
    })

    expect(onScreenObjectUpdate).toHaveBeenCalledWith(newObject)
  })

  it('should map isLoading to isStreaming', () => {
    useObjectMock.mockReturnValue({
      object: null,
      submit: mockSubmit,
      isLoading: true,
    })
    const { result } = renderHook(() =>
      useScreeningDiagnosis({ onScreenObjectUpdate })
    )

    expect(result.current.isStreaming).toBe(true)
  })
})
