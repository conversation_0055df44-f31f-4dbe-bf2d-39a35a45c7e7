import { renderHook, act } from '@testing-library/react'
import { useScreeningActions } from '@/app/(conv)/screening/hooks/useScreeningActions'
import { useSession } from 'next-auth/react'
import { useScreeningDiagnosis } from '@/app/(conv)/screening/hooks/useScreeningDiagnosis'
import { useScreeningRephrase } from '@/app/(conv)/screening/hooks/useScreeningRephrase'
import { toast } from 'react-hot-toast'

// Mock the dependencies
jest.mock('next-auth/react')
jest.mock('@/app/(conv)/screening/hooks/useScreeningDiagnosis')
jest.mock('@/app/(conv)/screening/hooks/useScreeningRephrase')
jest.mock('react-hot-toast')

// Typecast the mocked hooks
const useSessionMock = useSession as jest.Mock
const useScreeningDiagnosisMock = useScreeningDiagnosis as jest.Mock
const useScreeningRephraseMock = useScreeningRephrase as jest.Mock

describe('useScreeningActions', () => {
  let mockDiagnosisSubmit: jest.Mock
  let mockRephraseStart: jest.Mock
  let consoleLogSpy: jest.SpyInstance
  let consoleWarnSpy: jest.SpyInstance

  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks()

    // Suppress console output for cleaner test results
    consoleLogSpy = jest.spyOn(console, 'log').mockImplementation(() => {})
    consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation(() => {})

    // Default mock implementation
    mockDiagnosisSubmit = jest.fn()
    mockRephraseStart = jest.fn()

    useSessionMock.mockReturnValue({ data: { user: { id: 'test-user' } } })
    useScreeningDiagnosisMock.mockReturnValue({
      submit: mockDiagnosisSubmit,
      isStreaming: false,
    })
    useScreeningRephraseMock.mockReturnValue({
      startRephrase: mockRephraseStart,
      isStreaming: false,
    })
  })

  afterEach(() => {
    // Restore console output after each test
    consoleLogSpy.mockRestore()
    consoleWarnSpy.mockRestore()
  })

  it('should initialize with default values', () => {
    const { result } = renderHook(() => useScreeningActions())

    expect(result.current.description).toBe('')
    expect(result.current.screenObject).toBeNull()
    expect(result.current.rephrased).toEqual([])
    expect(result.current.isProcessing).toBe(false)
    expect(result.current.canStartClarification).toBe(false)
    expect(result.current.hasUserMadeSelection).toBe(false)
  })

  it('should update description when setDescription is called', () => {
    const { result } = renderHook(() => useScreeningActions())

    act(() => {
      result.current.setDescription('New description')
    })

    expect(result.current.description).toBe('New description')
  })

  it('should call diagnosis and rephrase hooks when handleParaphraseAndAnalyze is called', () => {
    const { result } = renderHook(() => useScreeningActions())

    act(() => {
      result.current.setDescription('A valid long description for testing')
    })

    act(() => {
      result.current.handleParaphraseAndAnalyze()
    })

    expect(mockDiagnosisSubmit).toHaveBeenCalled()
    expect(mockRephraseStart).toHaveBeenCalled()
  })

  it('should not call APIs if description is empty', () => {
    const { result } = renderHook(() => useScreeningActions())

    act(() => {
      result.current.handleParaphraseAndAnalyze()
    })

    expect(mockDiagnosisSubmit).not.toHaveBeenCalled()
    expect(mockRephraseStart).not.toHaveBeenCalled()
    expect(toast.error).toHaveBeenCalledWith(
      'Please enter a description first.'
    )
  })

  it('should update description and selectedSuggestion on handleSuggestionSelection', () => {
    const { result } = renderHook(() => useScreeningActions())
    const suggestion = 'This is a selected suggestion.'

    act(() => {
      result.current.handleSuggestionSelection(suggestion)
    })

    expect(result.current.selectedSuggestion).toBe(suggestion)
    expect(result.current.description).toBe(suggestion)
    expect(result.current.hasUserMadeSelection).toBe(true)
  })

  it('should enable clarification after a selection is made', () => {
    const { result } = renderHook(() => useScreeningActions())

    // Simulate receiving analysis results
    act(() => {
      const { onScreenObjectUpdate } =
        useScreeningDiagnosisMock.mock.calls[0][0]
      onScreenObjectUpdate({
        problem_ambiguity: 'test',
        intention_count: 1,
        entity_count: 1,
      })
    })

    // Simulate receiving rephrase suggestions
    act(() => {
      const { onRephrasedUpdate } = useScreeningRephraseMock.mock.calls[0][0]
      onRephrasedUpdate(['A suggestion'])
    })

    act(() => {
      result.current.setDescription('This is a test description over 10 chars.')
    })

    act(() => {
      result.current.handleSuggestionSelection('A suggestion')
    })

    expect(result.current.canStartClarification).toBe(true)
  })

  it('should correctly handle the "Use My Input" action', () => {
    const { result } = renderHook(() => useScreeningActions())

    act(() => {
      result.current.setDescription('My original input text.')
    })

    act(() => {
      result.current.handleUseMyInput()
    })

    expect(result.current.hasUserMadeSelection).toBe(true)
    expect(result.current.selectedSuggestion).toBe('My original input text.')
  })

  it('should set isProcessing to true when diagnosis is streaming', () => {
    useScreeningDiagnosisMock.mockReturnValue({
      submit: mockDiagnosisSubmit,
      isStreaming: true,
    })
    const { result } = renderHook(() => useScreeningActions())
    expect(result.current.isProcessing).toBe(true)
  })

  it('should set isProcessing to true when rephrase is streaming', () => {
    useScreeningRephraseMock.mockReturnValue({
      startRephrase: mockRephraseStart,
      isStreaming: true,
    })
    const { result } = renderHook(() => useScreeningActions())
    expect(result.current.isProcessing).toBe(true)
  })

  it('should handle errors from the diagnosis hook', () => {
    const consoleErrorSpy = jest
      .spyOn(console, 'error')
      .mockImplementation(() => {})
    const error = new Error('Diagnosis failed')
    mockDiagnosisSubmit.mockImplementation(() => {
      throw error
    })
    const { result } = renderHook(() => useScreeningActions())

    act(() => {
      result.current.setDescription('A valid description')
    })

    act(() => {
      result.current.handleParaphraseAndAnalyze()
    })

    expect(toast.error).toHaveBeenCalledWith(
      'Failed to start real API analysis. Please try again.'
    )
    consoleErrorSpy.mockRestore()
  })
})
