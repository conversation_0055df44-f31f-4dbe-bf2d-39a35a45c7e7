'use client'

import { useState, useCallback, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { toast } from 'react-hot-toast'
import { ConversationStatus } from '@prisma/client'
import SuggestedRefinedQuestions from './SuggestedRefinedQuestions'
import Header from './Header'
import DescriptionInput from './DescriptionInput'
import ActionButtons from './ActionButtons'
import { useConversationStore } from '@/app/stores/_legacy/conversation_store'
import { User } from '@prisma/client'
import { minChars, activeConversationCutoffDate } from '@/app/configs'
import {
  countActiveConversationsAfterDate,
  calculateMaxActiveConversations,
} from '@/lib/utils'
import { identifyUser } from '@/app/libs/logging'
import { useSession } from 'next-auth/react'
import { createConversation } from '@/app/server-actions/_legacy/conversation'
import useRephrase from '@/app/(legacy)/_hooks/useRephrase'
import useScreen from '@/app/(legacy)/_hooks/useScreen'
import { z } from 'zod'
import { screenSchema } from '@/app/api/_legacy/rephrase/screen/utils'

type StarterProps = {
  currentUser: User | null
}

const Starter: React.FC<StarterProps> = ({ currentUser }) => {
  const [description, setDescription] = useState<string>('')
  // Ensure after selection, the previous description is still there, the first item in the array
  const [lastDescription, setLastDescription] = useState<string>('')
  const [rephrasedDescriptions, setRephrasedDescriptions] = useState<string[]>(
    []
  )
  const [screenObject, setScreenObject] = useState<Partial<
    z.infer<typeof screenSchema>
  > | null>(null)
  const [canStartParaphrase, setCanStartParaphrase] = useState<boolean>(false)
  const [canStartClarify, setCanStartClarify] = useState<boolean>(false)

  const conversationStore = useConversationStore()
  const router = useRouter()
  const { data: session } = useSession()

  useEffect(() => {
    if (currentUser) {
      identifyUser(currentUser.id || 'anonymous', {
        user_status: currentUser.status,
        subscription_tier: currentUser.subscription_tier,
      })
    }
  }, [currentUser])

  const windowMaxActiveConversations =
    calculateMaxActiveConversations(currentUser)
  const currentActiveConversations = countActiveConversationsAfterDate(
    conversationStore.conversationList,
    ConversationStatus.ACTIVE,
    activeConversationCutoffDate
  )

  const handleRephraseSuccess = useCallback((rephrased: string[]) => {
    setRephrasedDescriptions(rephrased)
  }, [])

  const { handleRephrase, isLoading: isLoadingRephrase } = useRephrase({
    description,
    userId: session?.user?.id || '',
    onRephraseSuccess: handleRephraseSuccess,
    lastDescription,
  })

  const handleOnScreenSuccess = useCallback(
    (screenObject: Partial<z.infer<typeof screenSchema>>) => {
      setScreenObject(screenObject)
    },
    []
  )

  const { handleScreen, isLoading: isLoadingScreen } = useScreen({
    description,
    onScreenSuccess: handleOnScreenSuccess,
  })

  const isLoading = isLoadingRephrase || isLoadingScreen

  const handleSuggestionClick = (suggestion: string) => {
    setDescription(suggestion)
    setCanStartParaphrase(false)
    setCanStartClarify(true)
  }

  const handleStartRephrase = () => {
    if (description.length >= minChars) {
      // TODO: Add logging for start_rephrase_click event when starter component is refactored
      console.log(
        '[DEPRECATED] start_rephrase_click event - use centralized logging system'
      )
      setLastDescription(description)
      // Reset the paraphrase button until the description changes again
      setCanStartParaphrase(false)
      handleRephrase()
      handleScreen()
    }
  }

  const handleStartClarify = async () => {
    // Avoid double click
    setCanStartClarify(false)

    if (currentActiveConversations >= windowMaxActiveConversations) {
      toast.error('You have reached the max ACTIVE conversations')
      return
    }

    if (description.trim() === '') {
      toast.error('Please enter a description to proceed.')
      return
    }

    if (description.length < minChars) {
      toast.error(`Less than ${minChars} characters`)
      return
    }

    toast.success('Started, please wait a few seconds!', { duration: 10000 })
    // TODO: Add logging for start_clarify_click event when starter component is refactored
    console.log(
      '[DEPRECATED] start_clarify_click event - use centralized logging system'
    )
    try {
      const { conversation, error } = await createConversation(
        description,
        screenObject
      )
      if (conversation) {
        conversationStore.addConversation(conversation)
        router.push(`/conversations/${conversation.id}`)
      } else if (error) {
        toast.error(error.message)
      }
    } catch (error: any) {
      console.error('Error in API call:', error.message)
      toast.error(`Something went wrong. Please try again. ${error.message}`)
    }
  }

  return (
    <div className="flex flex-col min-h-screen">
      <div className="container mx-auto px-6 py-2 flex-shrink-0">
        <Header
          currentActiveConversations={currentActiveConversations}
          windowMaxActiveConversations={windowMaxActiveConversations}
        />
      </div>
      <div className="flex-grow overflow-auto">
        <div className="container mx-auto px-6 py-2 h-full flex flex-col">
          <div className="max-w-5xl mx-auto w-full">
            <div className="flex flex-row w-full space-x-4">
              <div className="flex-grow flex flex-col">
                <div className="flex-grow">
                  <DescriptionInput
                    description={description}
                    setDescription={setDescription}
                    isDisabled={
                      currentActiveConversations >= windowMaxActiveConversations
                    }
                    setCanStartParaphrase={setCanStartParaphrase}
                    showExamples={rephrasedDescriptions.length === 0}
                  />
                </div>
                <div className="mt-4">
                  <ActionButtons
                    description={description}
                    canStartClarify={canStartClarify}
                    canStartParaphrase={canStartParaphrase}
                    isLoading={isLoading}
                    onRephrase={handleStartRephrase}
                    onClarify={handleStartClarify}
                  />
                </div>
              </div>
              {screenObject && (
                <div
                  className="w-1/3 flex-shrink-0 overflow-y-auto"
                  style={{ maxHeight: '24rem' }}
                >
                  <ScreenWarningComponent {...screenObject} />
                </div>
              )}
            </div>
          </div>
          {rephrasedDescriptions.length > 0 && (
            <div className="mt-6 w-full max-w-5xl mx-auto">
              <SuggestedRefinedQuestions
                suggestions={rephrasedDescriptions}
                onSuggestionClick={handleSuggestionClick}
                isLoading={isLoading}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default Starter

const ScreenWarningComponent = (
  screenObject: Partial<z.infer<typeof screenSchema>> | null
): React.ReactNode => {
  if (!screenObject) {
    return null
  }
  console.log('screenObject', screenObject)
  const { is_problem_clear, problem_ambiguity, intention, entity, pass } =
    screenObject

  return (
    <div
      className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-2 mb-4 rounded overflow-auto"
      style={{ maxHeight: 'calc(120px + 6rem)' }}
    >
      <div className="text-xs">
        {pass === false && (
          <p className="font-bold text-red-600 mb-2">
            The problem statement may not be clear enough. It is suggested to
            rephrase.
          </p>
        )}
        {is_problem_clear === false && problem_ambiguity && (
          <p className="font-bold mb-1 underline">{problem_ambiguity}</p>
        )}
        {intention && intention.length > 0 && (
          <div className="mb-1">
            <p className="font-semibold">Intentions:</p>
            <ul className="list-disc list-inside">
              {intention.map((intent: string, index: number) => (
                <li key={index}>{intent}</li>
              ))}
            </ul>
          </div>
        )}
        {entity && entity.length > 0 && (
          <div className="mb-1">
            <p className="font-semibold">Entities:</p>
            <ul className="list-disc list-inside">
              {entity.map((item: string, index: number) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  )
}
