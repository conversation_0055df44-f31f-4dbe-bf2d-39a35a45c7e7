#!/bin/bash
set -a
source .env
set +a

# Enhanced Telegram notification script for claudecode hook system
# Supports branch-based forum topics for organized notifications

MSG="$1"
HOOK_TYPE="${2:-Notification}"  # Default to "Notification" if not provided
RAW_JSON="${3:-}"               # Optional raw JSON payload

# Get the current git branch
BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null)
if [ -z "$BRANCH" ]; then
  BRANCH="unknown"
fi

# Function to get repository name
get_repo_name() {
  local repo_name=""
  if git remote get-url origin >/dev/null 2>&1; then
    repo_name=$(git remote get-url origin | sed 's/.*\///' | sed 's/\.git$//')
  else
    repo_name=$(basename "$(git rev-parse --show-toplevel 2>/dev/null || pwd)")
  fi
  echo "$repo_name"
}

# Git-tracked topic cache for team collaboration
TOPIC_CACHE_FILE=".telegram_topic_cache"

# Function to get cached topic from git-tracked file
get_cached_topic() {
  local branch="$1"
  local repo_name="$2"
  local topic_name="${repo_name}:branch:${branch}"

  if [ -f "$TOPIC_CACHE_FILE" ]; then
    # Look for exact topic name match and extract topic ID
    grep "^$topic_name:" "$TOPIC_CACHE_FILE" | cut -d: -f4 | head -1
  fi
}

# Function to cache topic in git-tracked file
cache_topic() {
  local branch="$1"
  local repo_name="$2"
  local topic_id="$3"
  local topic_name="${repo_name}:branch:${branch}"

  echo "Caching topic mapping: $topic_name -> $topic_id" >&2

  # Remove any existing entry for this topic to avoid duplicates
  if [ -f "$TOPIC_CACHE_FILE" ]; then
    grep -v "^$topic_name:" "$TOPIC_CACHE_FILE" > "${TOPIC_CACHE_FILE}.tmp" 2>/dev/null || true
    mv "${TOPIC_CACHE_FILE}.tmp" "$TOPIC_CACHE_FILE" 2>/dev/null || true
  fi

  # Add new entry in sorted order for clean git diffs
  echo "$topic_name:$topic_id" >> "$TOPIC_CACHE_FILE"

  # Sort the file to keep it organized
  if command -v sort >/dev/null 2>&1; then
    sort "$TOPIC_CACHE_FILE" -o "$TOPIC_CACHE_FILE" 2>/dev/null || true
  fi

  echo "💡 Remember to commit .telegram_topic_cache to share topic mappings with your team!" >&2
}

# Function to create topic (simple approach - let Telegram handle duplicates)
create_topic() {
  local branch="$1"
  local repo_name="$2"
  local topic_name="${repo_name}:branch:${branch}"

  CREATE_RESPONSE=$(curl -s -X POST "https://api.telegram.org/bot$TG_TOKEN/createForumTopic" \
    -H "Content-Type: application/json" \
    -d "{
      \"chat_id\": \"$TG_CHAT\",
      \"name\": \"$topic_name\",
      \"icon_color\": 0x6FB9F0
    }")

  if echo "$CREATE_RESPONSE" | grep -q '"ok":true'; then
    local topic_id=$(echo "$CREATE_RESPONSE" | grep -o '"message_thread_id":[0-9]*' | cut -d: -f2)
    if [ -n "$topic_id" ]; then
      echo "Created topic $topic_id with name: $topic_name" >&2
      echo "$topic_id"
    fi
  else
    echo "Topic creation failed: $CREATE_RESPONSE" >&2
  fi
}

# Function to validate topic still exists
validate_topic() {
  local topic_id="$1"

  # Try to send a test message to see if topic still exists
  TEST_RESPONSE=$(curl -s -X POST "https://api.telegram.org/bot$TG_TOKEN/sendMessage" \
    -H "Content-Type: application/json" \
    -d "{
      \"chat_id\": \"$TG_CHAT\",
      \"message_thread_id\": $topic_id,
      \"text\": \"validation_test\",
      \"disable_notification\": true
    }")

  # If successful, topic exists; if failed, topic was deleted
  if echo "$TEST_RESPONSE" | grep -q '"ok":true'; then
    # Delete the test message immediately
    MSG_ID=$(echo "$TEST_RESPONSE" | grep -o '"message_id":[0-9]*' | cut -d: -f2)
    if [ -n "$MSG_ID" ]; then
      curl -s -X POST "https://api.telegram.org/bot$TG_TOKEN/deleteMessage" \
        -H "Content-Type: application/json" \
        -d "{\"chat_id\": \"$TG_CHAT\", \"message_id\": $MSG_ID}" > /dev/null
    fi
    return 0
  else
    return 1
  fi
}

# Function to get or create topic for branch (with optional local cache)
get_or_create_topic() {
  local branch="$1"
  local repo_name=$(get_repo_name)
  local topic_id=""

  echo "Processing topic for branch: $branch (repo: $repo_name)" >&2

  # First check optional local cache for efficiency
  topic_id=$(get_cached_topic "$branch" "$repo_name")

  if [ -n "$topic_id" ]; then
    echo "Found cached topic $topic_id, validating..." >&2

    # Validate that the cached topic still exists
    if validate_topic "$topic_id"; then
      echo "Using cached topic $topic_id for branch: $branch" >&2
      echo "$topic_id"
      return
    else
      echo "Cached topic $topic_id no longer exists, will create new one..." >&2
    fi
  fi

  echo "Creating topic for branch: $branch (repo: $repo_name)" >&2

  # Create topic (will create duplicate if needed - user can manually manage)
  topic_id=$(create_topic "$branch" "$repo_name")

  if [ -n "$topic_id" ]; then
    # Cache the topic ID for team collaboration via git
    cache_topic "$branch" "$repo_name" "$topic_id"

    # Check if this is a new cache entry that should be committed
    if git status --porcelain 2>/dev/null | grep -q "$TOPIC_CACHE_FILE"; then
      echo "" >&2
      echo "📝 New topic mapping created! Consider committing to share with your team:" >&2
      echo "   git add $TOPIC_CACHE_FILE" >&2
      echo "   git commit -m \"Add Telegram topic mapping for branch: $branch\"" >&2
    fi
  else
    echo "Failed to create topic for branch: $branch" >&2
  fi

  echo "$topic_id"
}

# Get current timestamp
TIMESTAMP=$(date '+%H:%M:%S')

# Get current operation context (try to detect what's happening)
OPERATION_CONTEXT=""
if [[ "$MSG" == *"finished"* ]]; then
  OPERATION_CONTEXT="🏁 Task Completion"
elif [[ "$MSG" == *"started"* ]] || [[ "$MSG" == *"beginning"* ]]; then
  OPERATION_CONTEXT="🚀 Task Started"
elif [[ "$MSG" == *"error"* ]] || [[ "$MSG" == *"failed"* ]]; then
  OPERATION_CONTEXT="❌ Error Occurred"
elif [[ "$MSG" == *"tool"* ]]; then
  OPERATION_CONTEXT="🔧 Tool Execution"
else
  OPERATION_CONTEXT="📋 General Update"
fi

# Get repository name for message content
REPO_NAME=$(get_repo_name)

# Build the enhanced message text with repo name, branch, and all details
ENHANCED_MSG="$OPERATION_CONTEXT - $REPO_NAME

📁 Repository: $REPO_NAME
🌿 Branch: $BRANCH
📝 Message: $MSG
🕒 Time: $TIMESTAMP
🎯 Hook: $HOOK_TYPE"

# Get or create topic for this branch
TOPIC_ID=$(get_or_create_topic "$BRANCH")

# Send message to the appropriate forum topic
if [ -n "$TOPIC_ID" ] && [ "$TOPIC_ID" != "" ]; then
  # Send to specific forum topic
  curl -s -X POST "https://api.telegram.org/bot$TG_TOKEN/sendMessage" \
    -H "Content-Type: application/json" \
    -d "{
      \"chat_id\": \"$TG_CHAT\",
      \"message_thread_id\": $TOPIC_ID,
      \"text\": \"$ENHANCED_MSG\"
    }"
else
  # Fallback: send to main chat if topic creation failed
  curl -s -X POST "https://api.telegram.org/bot$TG_TOKEN/sendMessage" \
    -H "Content-Type: application/json" \
    -d "{
      \"chat_id\": \"$TG_CHAT\",
      \"text\": \"$ENHANCED_MSG\"
    }"
fi
