# Screening Module

A comprehensive problem clarification assistant that helps users refine their problem statements through AI-powered analysis and suggestions.

## 📁 Architecture Overview

```
screening/
├── page.tsx                 # Main entry point with Suspense wrapper
├── components/              # React components
├── hooks/                   # Custom hooks for state management
├── constants/               # Static data and configurations
└── README.md               # This file
```

## 🎯 Core Functionality

The screening module implements a **two-step workflow**:

1. **Step 1**: AI analyzes user input and generates refined problem statements
2. **Step 2**: User selects a suggestion or uses original input, then proceeds to clarification

### Visual Focus System

- **Before selection**: Suggestions section glows green (user should pick one)
- **After selection**: Input section glows emerald (user should refine description)
- **Opacity-based focus**: Non-focused sections become semi-transparent (40% opacity)

## 🧩 Component Architecture

### Layout Components

- **`ScreeningLayout`**: Main layout orchestrator, conditionally renders views
- **`CenteredInitialView`**: Initial state with centered card layout
- **`ExpandedAnalysisView`**: Expanded grid layout after analysis

### Core Components

- **`DescriptionInput`**: Problem description textarea with examples and validation
- **`ActionButtons`**: Step 1 & 2 buttons with language/mode selection
- **`SuggestedRefinedQuestions`**: 2x2 grid of AI-generated suggestions with smart selection
- **`ScreenWarning`**: Problem analysis panel (intentions, entities, clarity issues)
- **`LanguageSelector`**: Multi-language support dropdown

### Client Components

- **`ScreeningClient`**: Main client wrapper with prefilled question support

## 🎣 Hooks System

### Primary Hook

- **`useScreeningActions`**: Master hook that orchestrates the entire workflow
  - Combines diagnosis and rephrase hooks
  - Manages workflow state (selection, focus, buttons)
  - Handles real/static data modes
  - Returns complete interface for components

### Specialized Hooks

- **`useScreeningDiagnosis`**: Handles problem analysis (Step 1 part A)
- **`useScreeningRephrase`**: Handles suggestion generation (Step 1 part B)

### Hook Export Pattern

```typescript
// Clean imports via index.ts
import {
  useScreeningActions,
  type UseScreeningActionsReturn,
} from '@/app/(conv)/screening/hooks'
```

## 🔧 State Management

### Workflow States

```typescript
// Selection workflow
hasUserMadeSelection: boolean // Has user picked a suggestion?
selectedSuggestion: string // Which suggestion was selected
showUseMyInputButton: boolean // Show "My Original Input" option

// Step progression
canStartClarification: boolean // Can proceed to Step 2?
isProcessing: boolean // Is any API call in progress?

// Mode selection
useStaticData: boolean // Real API vs mock data
selectedLanguage: SupportedLanguageCode // Interface language
```

### Data Flow

1. User inputs description → `description` state
2. Click "Paraphrase & Analyze" → Triggers both diagnosis + rephrase APIs
3. Results populate `screenObject` + `rephrased` arrays
4. User selects suggestion → Updates selection states + triggers focus change
5. Click "Start Clarification" → Navigates to drag tree with enhanced context

## 🌐 API Integration

### Endpoints

- **`/api/screening/diagnose`**: Problem analysis (structured JSON response)
- **`/api/screening/rephrase`**: Question generation (streaming text response)

### API Features

- **Real-time streaming**: Uses Vercel AI SDK (`streamObject`, `streamText`)
- **Language support**: All endpoints accept language parameter
- **Model**: GPT-4o-mini for cost efficiency
- **Error handling**: Graceful fallbacks with toast notifications

### Data Enhancement

Original user prompt gets enhanced with problem analysis context before drag tree creation:

```typescript
// Clean user input stored separately
userPrompt: string

// Enhanced context for AI processing
metadata: {
  problemAnalysisItems: string[] // Structured analysis for better AI context
}
```

## 📊 Data Structures

### Core Types

```typescript
// Problem analysis response
type ScreenObjectType = {
  is_problem_clear?: boolean
  problem_ambiguity?: string
  intention?: string[] // Detected user intentions
  entity?: string[] // Key entities/topics
  pass?: boolean // Overall clarity assessment
}

// Supported languages
type SupportedLanguageCode = 'en' | 'cn' | 'es' | 'jp'
```

### Mock Data System

- **Static mode**: Uses predefined mock responses for testing
- **Language-aware**: Mock data can be extended per language
- **Consistent API**: Same interface whether using real or mock data

## 🎨 Styling System

### Visual Design

- **Card-based layout**: Clean white cards with subtle shadows
- **Focus indicators**: Green/emerald highlights with glow effects
- **Smooth animations**: 300ms transitions for state changes
- **Responsive design**: Mobile-first with lg: breakpoints

### Class Management

Uses `cn` utility (clsx + tailwind-merge) for conditional classes:

```typescript
className={cn('base-classes', {
  'conditional-classes': condition,
  'alternative-classes': !condition,
})}
```

### Animation System

- **Attention-grabbing**: Slow 4s pulse for suggestions
- **Selection feedback**: 3x emerald pulse when input becomes focus
- **Tooltip delays**: 1s hover delay to prevent UI noise

## 🔄 User Experience Flow

### Initial State

```
┌─────────────────────────────────────┐
│         Centered Card Layout        │
│  "Problem Clarification Assistant"  │
│                                     │
│  [ Large textarea for input ]       │
│  [ Paraphrase & Analyze Button ]    │
└─────────────────────────────────────┘
```

### After Analysis

```
┌─────────────────────┬─────────────────┐
│ Input Section       │ Problem         │
│ [Textarea + Buttons]│ Analysis        │
│                     │ Panel           │
├─────────────────────┤                 │
│ 🟢 Suggestions     │ • Intentions    │
│ [2x2 Grid Glowing] │ • Entities      │
│ Choose one...       │ • Issues        │
└─────────────────────┴─────────────────┘
```

### After Selection

```
┌─────────────────────┬─────────────────┐
│ 🟢 Input Section   │ 😴 Problem     │
│ [Emerald Highlight] │ Analysis        │
│ Now refine your...  │ (Dimmed)        │
├─────────────────────┤                 │
│ 😴 Suggestions     │                 │
│ (Dimmed)            │                 │
│                     │                 │
└─────────────────────┴─────────────────┘
```

## 🚀 Integration Points

### Navigation Flow

1. **Entry**: `/screening` or `/screening?question=prefilled`
2. **Exit**: Drag tree creation with enhanced problem context
3. **Context preservation**: Problem analysis travels with user through workflow

### External Dependencies

- **Vercel AI SDK**: Streaming responses and structured outputs
- **Next.js App Router**: File-based routing and server actions
- **Tailwind CSS + cn utility**: Styling and conditional classes
- **Toast notifications**: User feedback for errors/success

## 💡 Development Notes

### Adding New Languages

1. Update `SUPPORTED_LANGUAGES` in `constants/languages.ts`
2. Add language option to `LANGUAGE_NAMES`
3. Extend mock data helpers for language-specific responses
4. Update API endpoints to handle new language code

### Extending Analysis

1. Modify `ScreenObjectType` in `constants/mockData.ts`
2. Update API response parsing in diagnosis hook
3. Enhance `ScreenWarning` component display logic
4. Update drag tree context enhancement

### Performance Considerations

- **Streaming responses**: Provides immediate user feedback
- **Mock data mode**: Instant responses for development/testing
- **Optimistic updates**: UI state changes before API completion
- **Memoized components**: Prevents unnecessary re-renders

---

**For Future AI Models**: This module is a complete problem clarification system with clear separation of concerns, comprehensive state management, and intuitive user experience patterns. The architecture supports easy extension and modification while maintaining type safety and performance.
