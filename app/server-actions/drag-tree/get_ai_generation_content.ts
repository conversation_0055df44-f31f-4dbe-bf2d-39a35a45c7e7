'use server'

import prisma from '@/app/libs/prismadb'
import { auth } from '@/auth'

// Import and re-export types from shared location
import type { AIGenerationContent } from '@/app/types/ai-generation'
export type { AIGenerationContent }

/**
 * Get full AI generation content by ID for lazy loading
 * This is used when the user clicks on an asset to view/edit the full content
 *
 * @param generationId - The AI generation ID to fetch content for
 * @returns Full AI generation content or null if not found
 */
export async function getAIGenerationContent(
  generationId: string
): Promise<AIGenerationContent | null> {
  try {
    // Verify user session
    const session = await auth()
    if (!session?.user?.id) {
      throw new Error('Unauthorized: User session required')
    }

    const userId = session.user.id

    // Atomic query: verify generation exists, user owns it, and dragTree ownership (if applicable)
    // Uses relation to validate ownership in a single database query
    const generation = await prisma.aIGeneration.findFirst({
      where: {
        id: generationId,
        user_id: userId, // Ensure user can only access their own generations
        // For drag_tree_v1 generations, use relation to validate dragTree ownership atomically
        OR: [
          // Non-drag-tree generations (no additional validation needed)
          { entity_type: { not: 'drag_tree_v1' } },
          // drag_tree_v1 generations must have dragTree owned by the same user
          {
            entity_type: 'drag_tree_v1',
            dragTree: {
              user_id: userId,
            },
          },
        ],
      },
      select: {
        id: true,
        title: true,
        content: true,
        generation_input: true,
        generation_output: true,
        status: true,
        version: true,
        config: true,
        metadata: true,
        created_at: true,
        updated_at: true,
      },
    })

    if (!generation) {
      return null
    }

    // Convert database fields to camelCase for frontend
    return {
      id: generation.id,
      title: generation.title,
      content: generation.content,
      generation_input: generation.generation_input,
      generation_output: generation.generation_output,
      status: generation.status,
      version: generation.version,
      config: generation.config,
      metadata: generation.metadata,
      createdAt: generation.created_at,
      updatedAt: generation.updated_at,
    }
  } catch (error) {
    console.error('Error fetching AI generation content:', error)
    throw new Error('Failed to fetch AI generation content')
  }
}

/**
 * Check if an AI generation exists and belongs to the current user
 * Useful for permission checking before operations
 *
 * @param generationId - The AI generation ID to check
 * @returns Boolean indicating if generation exists and user has access
 */
export async function checkAIGenerationAccess(
  generationId: string
): Promise<boolean> {
  try {
    // Verify user session
    const session = await auth()
    if (!session?.user?.id) {
      return false
    }

    const userId = session.user.id

    // Atomic query: verify generation exists, user owns it, and dragTree ownership (if applicable)
    const generation = await prisma.aIGeneration.findFirst({
      where: {
        id: generationId,
        user_id: userId,
        // For drag_tree_v1 generations, use relation to validate dragTree ownership atomically
        OR: [
          // Non-drag-tree generations (no additional validation needed)
          { entity_type: { not: 'drag_tree_v1' } },
          // drag_tree_v1 generations must have dragTree owned by the same user
          {
            entity_type: 'drag_tree_v1',
            dragTree: {
              user_id: userId,
            },
          },
        ],
      },
      select: {
        id: true,
      },
    })

    return !!generation
  } catch (error) {
    console.error('Error checking AI generation access:', error)
    return false
  }
}
