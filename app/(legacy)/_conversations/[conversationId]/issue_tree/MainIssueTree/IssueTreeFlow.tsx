import React from 'react'
import <PERSON>act<PERSON><PERSON>, {
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
} from 'reactflow'
import FlowControls from '../FlowControls'
import 'reactflow/dist/style.css'
import CustomLeafNode from '../CustomLeafNode'
import CustomInternalNode from '../CustomInternalNode'
import { Node, Edge } from '@/app/types'

const nodeTypes = {
  default: CustomInternalNode,
  customLeafNode: CustomLeafNode,
}

// Define props interface for IssueTreeFlow
type IssueTreeFlowType = {
  nodes: Node[]
  edges: Edge[]
  onNodesChange: any
}

const IssueTreeFlow: React.FC<IssueTreeFlowType> = React.memo(
  ({ nodes, edges, onNodesChange }) => {
    const proOptions = React.useMemo(() => ({ hideAttribution: true }), [])
    // Memoize fitViewOptions to prevent re-creation on each render
    const fitViewOptions = React.useMemo(
      () => ({
        padding: 15,
        minZoom: 0.45,
        maxZoom: 2,
        duration: 1000,
        nodes: nodes.filter(node => node.id === 'L1:1'),
      }),
      [nodes]
    )
    return (
      <ReactFlow
        className="flex-grow"
        nodes={nodes}
        edges={edges}
        nodeTypes={nodeTypes}
        onNodesChange={onNodesChange}
        attributionPosition="top-right"
        panOnScroll
        fitView
        fitViewOptions={fitViewOptions}
        proOptions={proOptions}
        minZoom={0.1}
        maxZoom={2}
      >
        <FlowControls />
        <MiniMap zoomable pannable />
        <Controls />
        <Background color="#555" variant={BackgroundVariant.Dots} />
      </ReactFlow>
    )
  }
)

IssueTreeFlow.displayName = 'IssueTreeFlow'

export default IssueTreeFlow
