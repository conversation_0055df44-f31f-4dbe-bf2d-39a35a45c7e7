// import { User } from "@prisma/client";
// import { create } from "zustand";

// type CurrentUserState = {
//   currentUser: User | null;
//   setCurrentUser: (user: User | null) => void;
//   reset: () => void;
// };

// const useCurrentUserStore = create<CurrentUserState>((set) => ({
//   currentUser: null,
//   setCurrentUser: (user: User | null) => set({ currentUser: user }),
//   reset: () => set({ currentUser: null }),
// }));

// export default useCurrentUserStore;
