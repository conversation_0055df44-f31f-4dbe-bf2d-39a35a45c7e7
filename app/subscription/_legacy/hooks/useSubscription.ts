import { useState, useEffect } from 'react'
import { Product, Price } from '@prisma/client'
import { isLocalOrDevEnv, userHasPaidFeatures } from '@/lib/utils'
import { useUser } from '@/app/context/UserContext'
import { useSession } from 'next-auth/react'
import { maxActiveConversations } from '@/app/configs'
import { hasPaidFeatures } from '@/app/configs/tier-permissions'
import { checkoutWithStripe } from '@/lib/stripe/server'
import { getStripe } from '@/lib/stripe/client'
import { NextResponse } from 'next/server'
import toast from 'react-hot-toast'
import mixpanel from '@/app/libs/mixpanel'

export const useSubscription = (products: Product[], prices: Price[]) => {
  const currentUser = useUser()
  const { data: session } = useSession()
  const [isLocalOrDev, setIsLocalOrDev] = useState<boolean>(false)

  useEffect(() => {
    setIsLocalOrDev(isLocalOrDevEnv())
  }, [])

  // Use session data for more accurate subscription info
  const subscriptionInfo = session?.user?.subscription
  const tier = subscriptionInfo?.tier || currentUser?.subscription_tier
  const isSubscribed = userHasPaidFeatures(currentUser || null)
  const isPro = tier ? hasPaidFeatures(tier) : false
  const cancelPending = subscriptionInfo?.cancelPending || false

  const handleStripeCheckout = async (priceId: string) => {
    if (!currentUser) {
      toast.error('Please sign in/refresh to subscribe.')
      return
    }

    try {
      mixpanel.track('click_subscribe_button')
      const sessionId = await checkoutWithStripe(currentUser, priceId)
      if (sessionId instanceof NextResponse) {
        toast.error('Failed to create checkout session, please try again.')
        return
      } else {
        const stripe = await getStripe()
        stripe?.redirectToCheckout(sessionId)
      }
    } catch (error) {
      console.error('Error creating checkout session:', error)
      toast.error('Failed to create checkout session, please try again.')
    }
  }

  const cardData = [
    {
      image: 'https://i.imgur.com/pJNFEHR.png',
      title: 'Free User',
      price_id: '',
      price: '-',
      features: [
        `✅ Up to ${maxActiveConversations} conversations`,
        '✅ Search capability',
        '✅ Well-curated prompt starters',
      ],
      show_button: false,
    },
    ...products.map(product => {
      const productPrice = prices.find(price => price.product_id === product.id)
      const featuresArray = parseProductMetadata(product)

      return {
        image: 'https://i.imgur.com/Ql4jRdB.png',
        title: product.name,
        price_id: productPrice?.id || '',
        price: formatPrice(
          productPrice?.unit_amount
            ? Number(productPrice.unit_amount)
            : undefined
        ),
        features: featuresArray,
        show_button: !isPro || cancelPending, // Hide button if already subscribed and not canceling
      }
    }),
  ]

  return {
    isLocalOrDev,
    isSubscribed,
    isPro,
    tier,
    cancelPending,
    cardData,
    handleStripeCheckout,
  }
}

function parseProductMetadata(product: Product): string[] {
  if (product.metadata && typeof product.metadata === 'string') {
    try {
      const metadataObj = JSON.parse(product.metadata)
      if (metadataObj.features && typeof metadataObj.features === 'string') {
        return metadataObj.features.split('||')
      }
    } catch (error) {
      console.error('Error parsing metadata:', error)
    }
  }
  return []
}

function formatPrice(unitAmount: number | null | undefined): string {
  return unitAmount ? `$${(unitAmount / 100).toFixed(2)}` : '-'
}
