// Types for feedback widget system

export type FeedbackType = 'bug' | 'suggestion'

export type EngagementLevel = 'connect' | 'open' | 'listening'

export type RatingKeys = 'smoothness' | 'quality' | 'effort'

export type NullableRating = Record<RatingKeys, number | null>

export type FeedbackSubmissionData = {
  feedbackType: FeedbackType
  ratings: NullableRating
  feedback: string
  engagementLevel: EngagementLevel
  dragTreeId: string
  timestamp?: string
}

export type FeedbackTypeOption = {
  value: FeedbackType
  label: string
  description: string
  icon: string
}

export type EngagementOption = {
  value: EngagementLevel
  label: string
  description: string
  icon: string
  size: 'large' | 'medium' | 'small'
  colorScheme: string
}
