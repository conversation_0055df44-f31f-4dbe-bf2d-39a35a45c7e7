import { OpenAI } from 'openai'
import { kv } from '@vercel/kv'
import { Ratelimit } from '@upstash/ratelimit'
import { OpenAIUsageType } from '@prisma/client'
import { NextResponse } from 'next/server'
import { UserStatus } from '@prisma/client'
import { logAPIUsage } from '@/app/api/utils'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export const runtime = 'edge'

export async function POST(req: Request): Promise<Response> {
  const { conversationId, currentUser, prompt } = await req.json()
  // Check if the user is authorized
  if (
    !currentUser?.id ||
    !currentUser?.email ||
    currentUser?.status !== UserStatus.ACTIVE
  ) {
    return new NextResponse('Unauthorized', { status: 401 })
  }
  // This part is for rate limiting
  if (
    process.env.NODE_ENV != 'development' &&
    process.env.KV_REST_API_URL &&
    process.env.KV_REST_API_TOKEN
  ) {
    const ratelimit = new Ratelimit({
      redis: kv,
      limiter: Ratelimit.slidingWindow(10, '1 d'),
    })

    const { success, limit, reset, remaining } = await ratelimit.limit(
      `clarify_ratelimit_${currentUser.id}`
    )

    console.log(limit, remaining, reset)

    if (!success) {
      return new Response('You have reached your request limit for the day.', {
        status: 429,
        headers: {
          'X-RateLimit-Limit': limit.toString(),
          'X-RateLimit-Remaining': remaining.toString(),
          'X-RateLimit-Reset': reset.toString(),
        },
      })
    }
  }

  const model_name = 'gpt-4o-mini'
  const systemPrompt = `
You are an AI writing assistant that continues existing text based on context from prior text.
Focus more on the later characters than the beginning ones.
Limit your response to no more than 200 characters, but make sure to construct complete sentences in plain text, avoid point form
`
  const trimmedPrompt = prompt.slice(-1000)

  const response = await openai.chat.completions.create({
    model: model_name,
    messages: [
      {
        role: 'system',
        content: systemPrompt,
        // we're disabling markdown for now until we can figure out a way to stream markdown text with proper formatting: https://github.com/steven-tey/novel/discussions/7
        // "Use Markdown formatting when appropriate.",
      },
      {
        role: 'user',
        content: trimmedPrompt,
      },
    ],
    stream: true,
  })

  const stream = new ReadableStream({
    async start(controller) {
      const encoder = new TextEncoder()
      let completion = ''
      for await (const chunk of response) {
        const content = chunk.choices[0]?.delta?.content ?? ''
        if (content) {
          completion += content
          controller.enqueue(encoder.encode(content))
        }
      }

      await logAPIUsage({
        open_ai_usage_type: OpenAIUsageType.EDITOR_GENERATE,
        model_name: model_name,
        input_text: systemPrompt + trimmedPrompt,
        output_text: completion,
        currentUser: currentUser,
        conversationId: conversationId,
      })

      controller.close()
    },
  })

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/plain; charset=utf-8',
    },
  })
}
