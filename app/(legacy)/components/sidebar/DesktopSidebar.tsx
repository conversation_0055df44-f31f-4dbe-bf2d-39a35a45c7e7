// Importing the necessary modules and components
'use client'
import DesktopItem from './DesktopItem'
import useRoutes from '@/app/(legacy)/_hooks/useRoutes'
import SettingsModal from './SettingsModal'
import { useState } from 'react'
import Avatar from '@/app/components/Avatar'
import { SubscriptionTier, User } from '@prisma/client'
import mixpanel from '@/app/libs/mixpanel'
import { useRouter } from 'next/navigation'
// Defining the props for the DesktopSidebar component
interface DesktopSidebarProps {
  currentUser: User
}

// Defining the DesktopSidebar component
const DesktopSidebar: React.FC<DesktopSidebarProps> = ({ currentUser }) => {
  // Getting the routes using the useRoutes hook
  const routes = useRoutes()
  // Setting the initial state of the settings modal to closed
  const [isOpen, setIsOpen] = useState(false)
  const router = useRouter()

  // Returning the DesktopSidebar component
  return (
    <>
      <SettingsModal
        currentUser={currentUser}
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
      />
      <div className="hidden lg:fixed lg:inset-y-0 lg:left-0 lg:z-40 lg:w-20 xl:px-6 lg:overflow-y-auto lg:bg-white lg:border-r-[1px] lg:pb-4 lg:flex lg:flex-col justify-between">
        {/* Navigation menu */}
        <nav className="mt-4 flex flex-col justify-between">
          <ul role="list" className="flex flex-col items-center space-y-1">
            {/* Mapping through the routes and rendering the DesktopItem component for each */}
            {routes.map(item => (
              <DesktopItem
                key={item.label}
                href={item.href}
                label={item.label}
                icon={item.icon}
                active={item.active}
                onClick={item.onClick}
              />
            ))}
          </ul>
        </nav>
        <div>
          {/* Buy me a coffee link */}
          <nav className="mt-4 flex flex-col justify-between items-center">
            <div className="relative cursor-pointer hover:opacity-75 transition group">
              <a
                href="https://www.buymeacoffee.com/clarifyai"
                target="_blank"
                rel="noreferrer"
                className="text-5xl z-10"
                onClick={() => {
                  mixpanel.track('buy_me_a_coffee_click', {
                    location: 'desktop_sidebar',
                  })
                }}
              >
                ☕
              </a>
              <span className="hidden group-hover:inline absolute -top-10 left-1/2 transform -translate-x-1/2 text-xs z-20">
                Buy me coffee
              </span>
            </div>
          </nav>

          {/* User avatar */}
          <nav className="mt-4 flex flex-col justify-between items-center w-full">
            <div className="flex flex-col items-center gap-2 w-full px-2">
              <div
                onClick={() => {
                  mixpanel.track('open_settings')
                  router.push('/settings')
                }}
                className="cursor-pointer hover:opacity-75 transition"
              >
                <Avatar user={currentUser} />
              </div>
              <div className="text-[10px] text-gray-600 text-center">
                <div className="uppercase tracking-wide font-medium">
                  {currentUser?.name || 'User'}
                </div>
                <div className="text-[9px] text-gray-500 truncate max-w-[60px]">
                  {currentUser?.email?.split('@')[0] || 'user'}
                </div>
                {currentUser?.subscription_tier && (
                  <div className="mt-1 inline-flex items-center rounded-full bg-blue-100 px-2 py-0.5 text-[9px] font-semibold text-blue-700 uppercase">
                    {currentUser.subscription_tier}
                  </div>
                )}
              </div>
              <a
                href="/subscription"
                className="mt-1 inline-flex items-center rounded bg-red-600/90 hover:bg-red-700 text-white text-[10px] px-2 py-1"
              >
                Upgrade
              </a>
            </div>
          </nav>
        </div>
      </div>
    </>
  )
}

// Exporting the DesktopSidebar component
export default DesktopSidebar
