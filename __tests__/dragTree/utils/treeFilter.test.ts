import { pruneTreeByFilter } from '@/app/(conv)/dragTree/[dragTreeId]/utils/treeFilter'
import type { TreeNode } from '@/app/types'

const makeQuestion = (id: string): TreeNode => ({
  id,
  label: id,
  type: 'question',
  children: [],
})
const makeCategory = (id: string, children: TreeNode[]): TreeNode => ({
  id,
  label: id,
  type: 'category',
  children,
})

const getNodeContentMock = (map: Record<string, any[]>) => (nodeId: string) => {
  const arr = map[nodeId] || []
  const m = new Map<string, any>()
  arr.forEach((item, i) => m.set(`${nodeId}-${i}`, item))
  return m
}

describe('treeFilter utilities', () => {
  const tree: TreeNode = makeCategory('root', [
    makeCategory('c1', [makeQuestion('q1'), makeQuestion('q2')]),
    makeCategory('c2', [makeQuestion('q3')]),
  ])

  it('prunes to only researchable leaves', () => {
    const content = {
      q1: [{ status: 'ACTIVE' }],
      q2: [],
      q3: [{ status: 'PROCESSING' }],
    }
    const pruned = pruneTreeByFilter(
      tree,
      'researchable',
      getNodeContentMock(content)
    )!
    const leaves = (n: TreeNode, acc: string[] = []) => {
      if (n.type === 'question') acc.push(n.id)
      n.children.forEach(c => leaves(c, acc))
      return acc
    }
    expect(leaves(pruned).sort()).toEqual(['q2'])
  })

  it('keeps only researched leaves', () => {
    const content = {
      q1: [{ status: 'ACTIVE', metadata: { isRead: true } }],
      q2: [],
      q3: [{ status: 'PROCESSING' }],
    }
    const pruned = pruneTreeByFilter(
      tree,
      'researched',
      getNodeContentMock(content)
    )!
    const leaves = (n: TreeNode, acc: string[] = []) => {
      if (n.type === 'question') acc.push(n.id)
      n.children.forEach(c => leaves(c, acc))
      return acc
    }
    expect(leaves(pruned).sort()).toEqual(['q1'])
  })

  it('keeps unread researched leaves', () => {
    const content = {
      q1: [{ status: 'ACTIVE', metadata: { isRead: true } }],
      q2: [{ status: 'ACTIVE', metadata: { isRead: false } }],
      q3: [],
    }
    const pruned = pruneTreeByFilter(
      tree,
      'unread',
      getNodeContentMock(content)
    )!
    const leaves = (n: TreeNode, acc: string[] = []) => {
      if (n.type === 'question') acc.push(n.id)
      n.children.forEach(c => leaves(c, acc))
      return acc
    }
    expect(leaves(pruned).sort()).toEqual(['q2'])
  })
})
