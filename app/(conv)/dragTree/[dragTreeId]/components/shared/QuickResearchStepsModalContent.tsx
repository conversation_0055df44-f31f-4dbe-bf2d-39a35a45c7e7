'use client'

import React, { useMemo } from 'react'
import {
  Conversation,
  ConversationContent,
} from '@/components/ai-elements/conversation'
import { Message, MessageContent } from '@/components/ai-elements/message'
import {
  Reasoning,
  ReasoningTrigger,
  ReasoningContent,
} from '@/components/ai-elements/reasoning'

export type QuickResearchStepsModalContentProps = {
  messages: any[]
}

// Detect whether a message has final assistant text (parts-based or legacy)
const hasAssistantText = (m: any): boolean => {
  if (Array.isArray(m?.parts)) {
    const textParts = m.parts.filter(
      (p: any) =>
        p?.type === 'text' && typeof p?.text === 'string' && p.text.trim()
    )
    if (textParts.length > 0) return true
  }
  if (typeof m?.content === 'string' && m.content.trim()) return true
  if (Array.isArray(m?.content)) {
    const text = m.content
      .filter(
        (p: any) =>
          (p?.type === 'text' && typeof p?.text === 'string') ||
          (p?.type === 'text-delta' &&
            typeof (p as any)?.textDelta === 'string')
      )
      .map((p: any) =>
        p.type === 'text-delta' ? (p as any).textDelta : p.text
      )
      .join('')
      .trim()
    if (text) return true
  }
  return false
}

const hasReasoningParts = (m: any): boolean => {
  return (
    Array.isArray(m?.parts) && m.parts.some((p: any) => p?.type === 'reasoning')
  )
}

const hasToolParts = (m: any): boolean => {
  return (
    Array.isArray(m?.parts) &&
    m.parts.some((p: any) => p?.type?.startsWith('tool-'))
  )
}

export default function QuickResearchStepsModalContent({
  messages,
}: QuickResearchStepsModalContentProps) {
  const filtered = useMemo(() => {
    if (!Array.isArray(messages)) return [] as any[]

    return messages
      .filter((msg, idx) => {
        // Exclude final assistant message if it only contains text (the main answer)
        if (msg.role === 'assistant' && idx === messages.length - 1) {
          if (
            hasAssistantText(msg) &&
            !hasReasoningParts(msg) &&
            !hasToolParts(msg)
          ) {
            return false
          }
        }
        // Include reasoning or tool messages; legacy reasoning supported
        const legacyHasReasoning = Boolean((msg as any).reasoning)
        return (
          (msg.role === 'assistant' &&
            (hasReasoningParts(msg) || hasToolParts(msg))) ||
          legacyHasReasoning
        )
      })
      .map(m => m)
  }, [messages])

  if (!filtered.length) {
    return (
      <Conversation>
        <ConversationContent>
          <Message from="assistant">
            <MessageContent>
              <div className="text-sm text-gray-500">
                No steps available for display.
              </div>
            </MessageContent>
          </Message>
        </ConversationContent>
      </Conversation>
    )
  }

  return (
    <Conversation>
      <ConversationContent>
        {filtered.map((message: any, index: number) => (
          <Message
            key={index}
            from={message.role as 'user' | 'system' | 'assistant'}
          >
            <MessageContent>
              {/* Parts-based rendering - reasoning expanded; tools collapsed */}
              {Array.isArray(message.parts) &&
                message.parts.map((part: any, partIndex: number) => {
                  if (part.type === 'reasoning') {
                    return (
                      <Reasoning key={partIndex} defaultOpen={true}>
                        <ReasoningTrigger />
                        <ReasoningContent>{part.text}</ReasoningContent>
                      </Reasoning>
                    )
                  }
                  if (part.type?.startsWith('tool-')) {
                    return (
                      <details key={partIndex} className="mb-3">
                        <summary className="cursor-pointer select-none text-sm font-medium flex items-center gap-2">
                          <span className="inline-block px-2 py-0.5 rounded bg-gray-100 text-gray-700">
                            {part.type === 'tool-web_search_preview'
                              ? 'Web Search'
                              : part.type
                                  .replace('tool-', '')
                                  .replace('_', ' ')}
                          </span>
                          {/* Show concise search summary if available */}
                          {part.type === 'tool-web_search_preview' && (
                            <span className="text-xs text-gray-500">
                              {(() => {
                                const q =
                                  (part.input &&
                                    (part.input.query || part.input.keyword)) ||
                                  ''
                                return q ? `Searched: ${q}` : 'Search step'
                              })()}
                            </span>
                          )}
                          {part.state && (
                            <span className="text-xs text-gray-500">
                              {part.state}
                            </span>
                          )}
                        </summary>
                        <div className="mt-2 space-y-2 text-sm">
                          {part.input && Object.keys(part.input).length > 0 && (
                            <div>
                              <div className="text-xs text-gray-600 mb-1">
                                Input:
                              </div>
                              <pre className="text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                                {JSON.stringify(part.input, null, 2)}
                              </pre>
                            </div>
                          )}
                        </div>
                      </details>
                    )
                  }
                  return null
                })}

              {/* Legacy format fallback */}
              {!Array.isArray(message.parts) && (
                <>
                  {(message as any).reasoning && (
                    <Reasoning defaultOpen={true}>
                      <ReasoningTrigger />
                      <ReasoningContent>
                        {(message as any).reasoning}
                      </ReasoningContent>
                    </Reasoning>
                  )}
                  {'toolInvocations' in (message as any) &&
                    (message as any).toolInvocations && (
                      <div className="space-y-3">
                        {(message as any).toolInvocations.map(
                          (invocation: any, toolIndex: number) => (
                            <details key={toolIndex}>
                              <summary className="cursor-pointer select-none text-sm font-medium flex items-center gap-2">
                                <span className="inline-block px-2 py-0.5 rounded bg-gray-100 text-gray-700">
                                  {invocation.toolName === 'web_search_preview'
                                    ? 'Web Search'
                                    : invocation.toolName}
                                </span>
                                {invocation.toolName ===
                                  'web_search_preview' && (
                                  <span className="text-xs text-gray-500">
                                    {(() => {
                                      const q =
                                        (invocation.args &&
                                          (invocation.args.query ||
                                            invocation.args.keyword)) ||
                                        ''
                                      return q
                                        ? `Searched: ${q}`
                                        : 'Search step'
                                    })()}
                                  </span>
                                )}
                              </summary>
                              <div className="mt-2 space-y-2 text-sm">
                                {invocation.args && (
                                  <div>
                                    <div className="text-xs text-gray-600 mb-1">
                                      Input:
                                    </div>
                                    <pre className="text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                                      {JSON.stringify(invocation.args, null, 2)}
                                    </pre>
                                  </div>
                                )}
                              </div>
                            </details>
                          )
                        )}
                      </div>
                    )}
                </>
              )}
            </MessageContent>
          </Message>
        ))}
      </ConversationContent>
    </Conversation>
  )
}
