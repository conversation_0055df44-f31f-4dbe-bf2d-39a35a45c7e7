import { Node, Edge } from '@/app/types'
import { MarkdownItem } from './DocumentTree'

// Define the NodeMapping type based on the nodesToDict function
export interface NodeMapping {
  [key: string]: {
    type: string
    label: string
    // example?: string; // Uncomment if you need the example in your metadata
  }
}

export function findNeighbors(
  current_node: string,
  edges: Edge[]
): { path: string[]; childs: string[]; peers: string[] } {
  const path: string[] = []
  let currentNodeId = current_node

  // Find path from root to current node
  while (currentNodeId !== 'L1:1') {
    path.unshift(currentNodeId)
    const parentEdge = edges.find(edge => edge.target === currentNodeId)
    if (!parentEdge) break
    currentNodeId = parentEdge.source
  }
  path.unshift('L1:1') // Add root node

  // Find children of the current node
  const childs = edges
    .filter(edge => edge.source === current_node)
    .map(edge => edge.target)

  // Find peers of the current node
  let peers: string[] = []
  if (current_node !== 'L1:1') {
    const parentEdge = edges.find(edge => edge.target === current_node)
    if (parentEdge) {
      peers = edges
        .filter(edge => edge.source === parentEdge.source)
        .map(edge => edge.target)
      peers = peers.filter(node => node !== current_node) // Remove current node from peers
    }
  }

  return { path, childs, peers }
}

export function nodesToDict(nodes: Node[]): NodeMapping {
  const dict: {
    [key: string]: {
      type: string
      label: string
      // example: string;
    }
  } = {}
  nodes.forEach(node => {
    dict[node.id] = {
      type: node.type ?? '',
      label: node.data.label ?? '',
      // example: node.type === "customLeafNode" ? node.data.example ?? "" : "",
    }
  })
  return dict
}

// Function to construct markdown
export const constructMarkdown = (
  path: string[],
  childs: string[],
  peers: string[],
  nodeMetadata: any,
  currentId: string
) => {
  let markdown = ''

  // Add path to markdown
  path.forEach((nodeId, index) => {
    const node = nodeMetadata[nodeId]
    if (node) {
      markdown += `${'#'.repeat(index + 1)} ${node.label}`
      if (nodeId === currentId) {
        markdown += ' ***<- current selection***'
      }
      markdown += '\n'
    }
  })

  // Add children to markdown
  childs.forEach(childId => {
    const childNode = nodeMetadata[childId]
    if (childNode) {
      const prefix = `${'#'.repeat(path.length + 1)}`
      markdown += `${prefix} ${childNode.label}\n`
    }
  })

  // Add peers to markdown
  peers.forEach(peerId => {
    const peerNode = nodeMetadata[peerId]
    if (peerNode) {
      const prefix = `${'#'.repeat(path.length)}`
      markdown += `${prefix} ${peerNode.label}\n`
    }
  })

  return markdown
}

export function mergeSubtreeIntoIssueTree(
  expandId: string,
  originalNodes: Node[],
  originalEdges: Edge[],
  subtreeNodes: Node[],
  subtreeEdges: Edge[]
): [Node[], Edge[]] {
  // Helper to extract layer and index from a node ID
  const extractLayerIndex = (nodeId: string): [number, number] => {
    const match = nodeId.match(/L(\d+):(\d+)/)
    return match ? [parseInt(match[1], 10), parseInt(match[2], 10)] : [NaN, NaN]
  }

  // Calculate max index per layer from original nodes
  const maxIndexPerLayer = originalNodes.reduce(
    (acc, node) => {
      const [layer, index] = extractLayerIndex(node.id)
      acc[layer] = Math.max(acc[layer] || 0, index)
      return acc
    },
    {} as Record<number, number>
  )

  // Create node mapping
  const nodeMapping = subtreeNodes.reduce(
    (acc, node) => {
      const [subtreeLayer] = extractLayerIndex(node.id)
      const [expandLayer] = extractLayerIndex(expandId)

      if (node.id === 'L1:1') {
        // Special handling for root of subtree
        acc[node.id] = expandId
      } else {
        const targetLayer = subtreeLayer + expandLayer - 1
        const newIndex = (maxIndexPerLayer[targetLayer] || 0) + 1
        maxIndexPerLayer[targetLayer] = newIndex // Update max index for the layer
        const newId = `L${targetLayer}:${newIndex}`
        acc[node.id] = newId
      }
      return acc
    },
    {} as Record<string, string>
  )

  // Apply mapping to subtree nodes, excluding the root if it's intended to map to expandId
  const mergedNodes = [
    ...originalNodes,
    ...subtreeNodes
      .map(node => ({
        ...node,
        id: nodeMapping[node.id] || node.id,
      }))
      .filter(node => node.id !== expandId),
  ]

  // Apply mapping to subtree edges
  const mergedEdges: Edge[] = [
    ...originalEdges,
    ...subtreeEdges.map(edge => {
      const newEdge: Edge = {
        id: `e-${nodeMapping[edge.source]}-${nodeMapping[edge.target]}`,
        source: nodeMapping[edge.source],
        target: nodeMapping[edge.target],
      }

      // Conditionally copy over the label if it exists
      if (edge.label !== undefined) {
        newEdge.label = edge.label
      }

      return newEdge
    }),
  ]

  return [mergedNodes, mergedEdges]
}

export function constructDisplayMarkdown(
  nodeMapping: NodeMapping,
  edges: Edge[],
  currentId: string
): MarkdownItem[] {
  // Helper to recursively build the markdown structure
  function buildMarkdownItem(nodeId: string): MarkdownItem {
    const node = nodeMapping[nodeId]
    const childrenEdges = edges.filter(edge => edge.source === nodeId)
    const childrenItems = childrenEdges.map(edge =>
      buildMarkdownItem(edge.target)
    )
    return {
      title: node.label + (nodeId === currentId ? ' <- Current Selection' : ''),
      children: childrenItems.length > 0 ? childrenItems : undefined,
      isCurrent: nodeId === currentId,
    }
  }

  const rootItem = buildMarkdownItem('L1:1')

  // The rootItem itself is the displayMarkdown structure needed
  return [rootItem]
}
