import React, { useCallback, lazy, Suspense } from 'react'
import { useResearchState } from './hooks/useResearchState'
import { useResearchActions } from './hooks/useResearchActions'
import { DragTreeNodeContentStatus } from '@prisma/client'

type ResearchButtonProps = {
  questionText: string
  questionNodeId: string
  variant?: 'reactflow' | 'treeview'
  className?: string
  onMouseEnter?: () => void
  onMouseLeave?: () => void
}

/**
 * Research Button Container Component
 *
 * Clean separation of concerns:
 * - Manages mouse interactions for research preview
 * - Uses hooks for state and actions
 * - Renders presentational ResearchButtonUI component
 */
const ResearchButton: React.FC<ResearchButtonProps> = ({
  questionText,
  questionNodeId,
  variant = 'treeview',
  className = '',
  onMouseEnter,
  onMouseLeave,
}) => {
  // Removed unused isShowingPreview state - not needed in current implementation

  // Removed setResearchPreviewNodeId to fix shared state issues

  // Use the state hook to get all research-related state
  const researchState = useResearchState({
    nodeId: questionNodeId,
    questionText,
  })

  // Derive computed values from research state
  const hasExistingContent =
    (researchState.nodeContentMap && researchState.nodeContentMap.size > 0) ||
    researchState.storeContent.trim().length > 0
  const isStreaming =
    researchState.status === DragTreeNodeContentStatus.PROCESSING

  // Use the actions hook to get all action handlers
  const actionHandlers = useResearchActions({
    nodeId: questionNodeId,
    questionText,
    hasExistingContent,
    isStreaming,
  })

  // Handle mouse enter - simple passthrough without state changes
  const handleMouseEnter = useCallback(() => {
    // Removed local state changes to fix unused variable warning
    // Each button should be independent and not affect others
    onMouseEnter?.()
  }, [onMouseEnter])

  // Handle mouse leave - simple passthrough without state changes
  const handleMouseLeave = useCallback(() => {
    // Removed local state changes to fix unused variable warning
    onMouseLeave?.()
  }, [onMouseLeave])

  // No cleanup needed since we're using local state only
  // Removed global state cleanup to ensure buttons are independent

  // Lazy load research UI
  const ResearchButtonUI = lazy(() => import('./ResearchButtonUI'))

  return (
    <Suspense
      fallback={
        <div className="h-6 w-6 bg-purple-100 rounded-full animate-pulse" />
      }
    >
      <ResearchButtonUI
        variant={variant}
        className={className}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        actionHandlers={actionHandlers}
        hasExistingContent={hasExistingContent}
        isStreaming={isStreaming}
        searchResults={researchState.searchResults}
      />
    </Suspense>
  )
}

export default React.memo(ResearchButton)
