'use client'

import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  FiX,
  FiFileText,
  FiMessageSquare,
  FiTrash2,
  FiFile,
} from 'react-icons/fi'
import {
  useAssetStore,
  type Asset,
} from '@/app/(conv)/dragTree/[dragTreeId]/stores/useAssetStore'
import { useAssetTabActions } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useAssetTabManager'
import { useAiPaneStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useAiPaneStore'
import { getAssetType } from '@/lib/asset-types'
import { formatDistanceToNow } from 'date-fns'
import AssetDeleteDialog from './AssetDeleteDialog'
import { useSession } from 'next-auth/react'
import { SubscriptionTier } from '@prisma/client'
import { canPerformAction, hasPermission } from '@/app/configs/tier-permissions'
import { toast } from 'react-hot-toast'

type AssetSidebarProps = {
  dragTreeId: string
  isOpen: boolean
  onClose: () => void
}

const AssetSidebar: React.FC<AssetSidebarProps> = ({
  dragTreeId,
  isOpen,
  onClose,
}) => {
  const { data: session } = useSession()
  const tier = ((session?.user as any)?.subscription?.tier ||
    (session?.user as any)?.subscription_tier ||
    SubscriptionTier.FREE) as SubscriptionTier
  const { assets, deleteAsset, markAssetAsViewed, markAllAssetsAsViewed } =
    useAssetStore()
  const { addAssetTab } = useAssetTabActions()

  // AI Pane controls for empty state links
  const { setIsOpen: setAiPaneOpen, setType: setAiPaneType } = useAiPaneStore()

  // Delete confirmation dialog state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [assetToDelete, setAssetToDelete] = useState<Asset | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)

  // Filter and sort assets for current drag tree (latest first)
  const dragTreeAssets = assets
    .filter(
      asset => asset.dragTreeId === dragTreeId || asset.dragTreeId === 'current'
    )
    .sort((a, b) => {
      // Sort by updatedAt first (for recently modified assets), then by createdAt
      const aTime = a.updatedAt || a.createdAt
      const bTime = b.updatedAt || b.createdAt
      return new Date(bTime).getTime() - new Date(aTime).getTime()
    })

  // Get unviewed count
  const unviewedCount = dragTreeAssets.filter(asset => !asset.viewed).length

  const handleAssetClick = (asset: Asset) => {
    // Mark as viewed
    if (!asset.viewed) {
      markAssetAsViewed(asset.id)
    }

    // Open asset in new tab using registry-driven logic
    addAssetTab(asset)

    // Close sidebar after opening tab
    onClose()
  }

  // Helper function to check if delete button should be shown
  const canDeleteAsset = (asset: Asset): boolean => {
    if (asset.type === 'chat') {
      return hasPermission(tier, 'canDeleteAiChat')
    } else if (asset.type === 'generate') {
      return hasPermission(tier, 'canDeleteAiGeneration')
    } else {
      // Fallback to general asset deletion permission
      return canPerformAction(tier, 'canDeleteAsset', false)
    }
  }

  // Handle clicking on empty state links
  const handleAiGenerateClick = () => {
    // Close asset sidebar
    onClose()
    // Open AI pane and set to generate mode
    setAiPaneType('generate')
    setAiPaneOpen(true)
  }

  const handleAiChatClick = () => {
    // Close asset sidebar
    onClose()
    // Open AI pane and set to chat mode
    setAiPaneType('chat')
    setAiPaneOpen(true)
  }

  const handleDeleteAsset = (asset: Asset, e: React.MouseEvent) => {
    e.stopPropagation()

    // Check specific permissions based on asset type
    let hasDeletePermission = false
    let permissionMessage = ''

    if (asset.type === 'chat') {
      hasDeletePermission = hasPermission(tier, 'canDeleteAiChat')
      permissionMessage = 'You cannot delete AI chats with your current tier.'
    } else if (asset.type === 'generate') {
      hasDeletePermission = hasPermission(tier, 'canDeleteAiGeneration')
      permissionMessage =
        'You cannot delete AI generations with your current tier.'
    } else {
      // Fallback to general asset deletion permission
      hasDeletePermission = canPerformAction(tier, 'canDeleteAsset', false)
      permissionMessage = 'You cannot delete assets with your current tier.'
    }

    if (!hasDeletePermission) {
      toast.error(`Permission denied: ${permissionMessage}`)
      return
    }

    setAssetToDelete(asset)
    setDeleteDialogOpen(true)
  }

  const handleConfirmDelete = async () => {
    if (!assetToDelete) return

    setIsDeleting(true)
    try {
      const result = await deleteAsset(assetToDelete.id)
      if (result.success) {
        console.log(`✅ Asset "${assetToDelete.title}" deleted successfully`)
        // Close any tabs related to this asset
        // TODO: Add tab cleanup logic if needed
      } else {
        console.error('Failed to delete asset:', result.error)
        // TODO: Show error toast
      }
    } catch (error) {
      console.error('Error deleting asset:', error)
      // TODO: Show error toast
    } finally {
      setIsDeleting(false)
      setDeleteDialogOpen(false)
      setAssetToDelete(null)
    }
  }

  const handleCancelDelete = () => {
    setDeleteDialogOpen(false)
    setAssetToDelete(null)
    setIsDeleting(false)
  }

  const handleMarkAllAsViewed = () => {
    markAllAssetsAsViewed()
  }

  const getAssetIcon = (type: Asset['type']) => {
    const assetType = getAssetType(type)
    return assetType?.icon || FiFile // fallback icon
  }

  const getAssetTypeColor = (type: Asset['type']) => {
    const assetType = getAssetType(type)
    const baseColor = assetType?.getAssetTypeColor() || 'text-gray-600'
    // Convert text color to include background
    if (baseColor.includes('blue')) return 'text-blue-600 bg-blue-100'
    if (baseColor.includes('green')) return 'text-green-600 bg-green-100'
    return 'text-gray-600 bg-gray-100' // fallback
  }

  const getAssetTypeLabel = (type: Asset['type']) => {
    const assetType = getAssetType(type)
    return assetType?.displayName || type // fallback to type string
  }

  const formatTimestamp = (date: Date) => {
    return formatDistanceToNow(date, { addSuffix: true })
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black bg-opacity-25"
        onClick={onClose}
      />

      {/* Sidebar */}
      <div className="relative ml-auto w-96 h-full bg-white shadow-xl flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Assets</h2>
            <p className="text-sm text-gray-600">
              {dragTreeAssets.length} total • {unviewedCount} new
            </p>
          </div>
          <div className="flex items-center space-x-2">
            {unviewedCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleMarkAllAsViewed}
                className="text-xs"
              >
                Mark all read
              </Button>
            )}
            <Button variant="ghost" size="sm" onClick={onClose}>
              <FiX className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 flex flex-col min-h-0">
          {/* Asset List */}
          <div className="flex-1 overflow-y-auto">
            {dragTreeAssets.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                <div className="flex items-center justify-center mb-6">
                  <div className="relative">
                    <FiFileText className="w-16 h-16 text-gray-300" />
                    <FiMessageSquare className="w-8 h-8 text-gray-300 absolute -bottom-1 -right-1 bg-white rounded-full p-1" />
                  </div>
                </div>
                <h3 className="text-lg font-medium text-gray-700 mb-2">
                  No Assets Yet
                </h3>
                <p className="text-sm text-gray-500 mb-6 max-w-sm mx-auto leading-relaxed">
                  Assets are generated content and AI conversations that get
                  saved for easy access. Start creating to see them here!
                </p>
                <div className="space-y-3 text-xs">
                  <button
                    onClick={handleAiGenerateClick}
                    className="flex items-center justify-center space-x-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 px-3 py-2 rounded-md transition-colors cursor-pointer group"
                  >
                    <FiFileText className="w-4 h-4 group-hover:scale-110 transition-transform" />
                    <span className="group-hover:underline">
                      Use AI Generate to create content
                    </span>
                  </button>
                  <button
                    onClick={handleAiChatClick}
                    className="flex items-center justify-center space-x-2 text-green-600 hover:text-green-700 hover:bg-green-50 px-3 py-2 rounded-md transition-colors cursor-pointer group"
                  >
                    <FiMessageSquare className="w-4 h-4 group-hover:scale-110 transition-transform" />
                    <span className="group-hover:underline">
                      Start AI Chat conversations
                    </span>
                  </button>
                </div>
              </div>
            ) : (
              <div className="p-2 space-y-1">
                {dragTreeAssets.map(asset => {
                  const Icon = getAssetIcon(asset.type)
                  const typeColor = getAssetTypeColor(asset.type)

                  return (
                    <div
                      key={asset.id}
                      onClick={() => handleAssetClick(asset)}
                      className={cn(
                        'p-3 rounded-lg border cursor-pointer transition-all hover:bg-gray-50',
                        'border-gray-200',
                        !asset.viewed && 'bg-blue-25 border-blue-200'
                      )}
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2 flex-1 min-w-0">
                          <div className={cn('p-1 rounded', typeColor)}>
                            <Icon className="w-3 h-3" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h3 className="text-sm font-medium text-gray-900 truncate">
                              {asset.title}
                            </h3>
                            <div className="flex items-center space-x-2 mt-1">
                              <span
                                className={cn(
                                  'text-xs px-2 py-0.5 rounded-full font-medium',
                                  typeColor
                                )}
                              >
                                {getAssetTypeLabel(asset.type)}
                              </span>
                              {!asset.viewed && (
                                <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
                              )}
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center space-x-1 flex-shrink-0 ml-2">
                          {canDeleteAsset(asset) && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={e => handleDeleteAsset(asset, e)}
                              className="p-1 h-6 w-6 text-red-600 hover:text-red-700"
                              title="Delete asset"
                            >
                              <FiTrash2 className="w-3 h-3" />
                            </Button>
                          )}
                        </div>
                      </div>

                      <div className="text-xs text-gray-500 mb-1 capitalize">
                        Updated{' '}
                        {formatTimestamp(asset.updatedAt || asset.createdAt)}
                      </div>
                    </div>
                  )
                })}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Delete confirmation dialog */}
      <AssetDeleteDialog
        isOpen={deleteDialogOpen}
        asset={assetToDelete}
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
        isDeleting={isDeleting}
      />
    </div>
  )
}

export default AssetSidebar
