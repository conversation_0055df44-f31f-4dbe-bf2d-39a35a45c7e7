import { useEffect, useState } from 'react'

const useLocalStorage = <T>(
  key: string,
  initialValue: T
  // eslint-disable-next-line no-unused-vars
): [T, (value: T) => void] => {
  const [storedValue, setStoredValue] = useState(initialValue)

  useEffect(() => {
    try {
      // Retrieve from localStorage
      const item = window.localStorage.getItem(key)
      if (item) {
        // Parse stored json or if none return initialValue
        setStoredValue(JSON.parse(item))
      }
    } catch (error) {
      console.error(`Error parsing localStorage key "${key}":`, error)
      // If parsing fails, fall back to the initial value to prevent a crash.
      setStoredValue(initialValue)
    }
  }, [key, initialValue])

  const setValue = (value: T) => {
    try {
      // Save state
      setStoredValue(value)
      // Save to localStorage
      window.localStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error)
    }
  }
  return [storedValue, setValue]
}

export default useLocalStorage
