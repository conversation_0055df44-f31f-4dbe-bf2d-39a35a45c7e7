import React from 'react'
import type { ScreenObjectType } from '@/app/(conv)/screening/constants/mockData'
import { cn } from '@/lib/utils'

type ScreenWarningProps = {
  screenObject: ScreenObjectType
}

const ScreenWarning: React.FC<ScreenWarningProps> = ({ screenObject }) => {
  const { is_problem_clear, problem_ambiguity, intention, entity, pass } =
    screenObject

  if (!screenObject) return null

  return (
    <div className="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
      {/* Header */}
      <div
        className={cn('px-4 py-3 border-b', {
          'bg-red-50 border-red-200': pass === false,
          'bg-blue-50 border-blue-200': pass !== false,
        })}
      >
        <div className="flex items-center space-x-2">
          {pass === false ? (
            <svg
              className="w-5 h-5 text-red-500"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
          ) : (
            <svg
              className="w-5 h-5 text-blue-500"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clipRule="evenodd"
              />
            </svg>
          )}
          <h3
            className={cn('font-semibold', {
              'text-red-700': pass === false,
              'text-blue-700': pass !== false,
            })}
          >
            Problem Analysis
          </h3>
        </div>
      </div>

      {/* Content */}
      <div className="p-4 space-y-4">
        {/* Status Message */}
        {pass === false && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <p className="text-red-700 font-medium text-sm">
              ⚠️ The problem statement may not be clear enough. Consider
              refining it for better results.
            </p>
          </div>
        )}

        {/* Problem Ambiguity */}
        {is_problem_clear === false && problem_ambiguity && (
          <div className="space-y-2">
            <h4 className="font-medium text-slate-700 text-sm">
              Key Issues Identified:
            </h4>
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
              <p className="text-amber-800 text-sm font-medium">
                {problem_ambiguity}
              </p>
            </div>
          </div>
        )}

        {/* Intentions */}
        {intention && intention.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-medium text-slate-700 text-sm flex items-center space-x-2">
              <svg
                className="w-4 h-4 text-green-500"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
              <span>Detected Intentions:</span>
            </h4>
            <div className="space-y-1">
              {intention.map((intent, index) => (
                <div key={index} className="flex items-start space-x-2 text-sm">
                  <span className="text-green-500 font-bold mt-1">•</span>
                  <span className="text-slate-600">{intent}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Entities */}
        {entity && entity.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-medium text-slate-700 text-sm flex items-center space-x-2">
              <svg
                className="w-4 h-4 text-purple-500"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                  clipRule="evenodd"
                />
              </svg>
              <span>Key Entities:</span>
            </h4>
            <div className="flex flex-wrap gap-2">
              {entity.map((item, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-700 border border-purple-200"
                >
                  {item}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="px-4 py-3 bg-slate-50 border-t border-slate-200">
        <p className="text-xs text-slate-500">
          This analysis helps identify potential areas for improvement in your
          problem statement.
        </p>
      </div>
    </div>
  )
}

export default ScreenWarning
