import React, { useEffect, useState } from 'react'
import { Progress } from '@/components/ui/progress'

interface LoadingScreenProps {
  title: string
}

// Fake progress bar to simulate loading
const LoadingDisplay: React.FC<LoadingScreenProps> = ({ title }) => {
  const [progress, setProgress] = useState(0)

  // Define the total duration, take around 80 seconds
  // We tell user it takes round 60 seconds, in our testing for draft generation, it takes around 30-45 seconds
  // depending on the output length, hence we add 30 seconds buffer, afterall, it is just a UI trick
  const totalDurationMs = 90000 // Total duration in milliseconds

  // Calculate the base increment and interval based on total duration
  const intervalMs = totalDurationMs / 100 // Update interval in milliseconds
  const baseIncrement = 100 / (totalDurationMs / intervalMs) // Amount to increment progress by each update

  useEffect(() => {
    const incrementProgress = () => {
      // Increment progress if not already at 100
      if (progress < 100) {
        // Add more randomness to the increment
        const randomIncrement = baseIncrement * (0.5 + Math.random() * 1.5)
        setProgress(prevProgress =>
          Math.min(prevProgress + randomIncrement, 100)
        )
      }
    }

    const randomIntervalMs = intervalMs * (0.5 + Math.random() * 1.5)
    const timerId = setInterval(incrementProgress, randomIntervalMs)

    return () => {
      clearInterval(timerId)
    }
  }, [progress, baseIncrement, intervalMs])

  // Set color of progress bar
  const backgroundColor = 'pink'

  return (
    <div className="flex flex-col justify-center items-center max-w-screen-lg h-full">
      <Progress
        value={progress}
        className="w-[60%]"
        style={{
          backgroundColor: backgroundColor,
        }}
      />
      <br />
      <h1>{title}</h1>
    </div>
  )
}

export default LoadingDisplay
