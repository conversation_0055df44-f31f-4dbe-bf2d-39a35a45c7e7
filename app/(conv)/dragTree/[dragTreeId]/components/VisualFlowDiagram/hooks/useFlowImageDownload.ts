'use client'

import { useCallback, useState } from 'react'
import { useReactFlow, getRectOfNodes, getTransformForBounds } from 'reactflow'
import { toPng } from 'html-to-image'
import { toast } from 'react-hot-toast'

/**
 * ReactFlow native download hook using official APIs
 * Uses getRectOfNodes and getTransformForBounds for proper full diagram capture
 * Follows ReactFlow's official download image example pattern
 * Supports downloading in different layout modes (linear/radial)
 */
type DownloadHookReturn = {
  downloadImage: (requestedLayoutMode?: 'linear' | 'radial') => Promise<void>
  isDownloading: boolean
}

export const useFlowImageDownload = (
  currentLayoutMode: 'linear' | 'radial',
  setLayoutMode: (mode: 'linear' | 'radial') => void
): DownloadHookReturn => {
  const [isDownloading, setIsDownloading] = useState<boolean>(false)
  const { getNodes } = useReactFlow()

  const downloadImage = useCallback(
    async (requestedLayoutMode?: 'linear' | 'radial') => {
      setIsDownloading(true)
      const targetLayoutMode = requestedLayoutMode || currentLayoutMode
      const needsLayoutSwitch = targetLayoutMode !== currentLayoutMode

      try {
        if (needsLayoutSwitch) {
          setLayoutMode(targetLayoutMode)
          // Give ReactFlow time to recompute positions; empirical value
          await new Promise(resolve => setTimeout(resolve, 1200))
        }

        const nodes = getNodes()

        if (nodes.length === 0) {
          toast.error('No diagram to download')
          return
        }

        // Get bounds of all nodes using ReactFlow's native function
        const nodesBounds = getRectOfNodes(nodes)

        // Calculate image dimensions with padding for better visual appearance
        const padding = 100
        const imageWidth = nodesBounds.width + padding * 2
        const imageHeight = nodesBounds.height + padding * 2

        // Calculate the transform needed to position the diagram correctly
        // This is ReactFlow's official way to handle viewport transformation for image export
        const transform = getTransformForBounds(
          nodesBounds,
          imageWidth,
          imageHeight,
          0.5, // min zoom
          2, // max zoom
          padding // padding
        )

        // Target the viewport element which contains both nodes and edges
        const viewportElement = document.querySelector(
          '.react-flow__viewport'
        ) as HTMLElement

        if (!viewportElement) {
          throw new Error('ReactFlow viewport not found')
        }

        // Generate PNG using ReactFlow's official pattern
        const dataUrl = await toPng(viewportElement, {
          backgroundColor: '#ffffff',
          width: imageWidth,
          height: imageHeight,
          style: {
            width: `${imageWidth}px`,
            height: `${imageHeight}px`,
            // Apply the computed transform - this is the key for capturing all nodes
            transform: `translate(${transform[0]}px, ${transform[1]}px) scale(${transform[2]})`,
          },
          filter: node => {
            // Exclude UI elements but keep all flow content (nodes and edges)
            if (node.classList?.contains('react-flow__minimap')) return false
            if (node.classList?.contains('react-flow__controls')) return false
            if (node.classList?.contains('react-flow__attribution'))
              return false
            return true
          },
        })

        // Create download with full timestamp and layout mode
        const now = new Date()
        const timestamp = now
          .toISOString()
          .replace('T', '-')
          .replace(/:/g, '-')
          .replace(/\..+/, '') // Remove milliseconds

        const layoutSuffix =
          targetLayoutMode === 'radial' ? 'circular' : 'hierarchical'
        const link = document.createElement('a')
        link.download = `flow-diagram-${layoutSuffix}-${timestamp}.png`
        link.href = dataUrl
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        toast.success(
          `${layoutSuffix.charAt(0).toUpperCase() + layoutSuffix.slice(1)} diagram downloaded successfully!`
        )
      } catch (error) {
        console.error('Failed to download image:', error)
        toast.error('Failed to download diagram. Please try again.')
      } finally {
        // Restore original layout if we switched
        if (needsLayoutSwitch) {
          setTimeout(() => {
            setLayoutMode(currentLayoutMode)
          }, 100)
        }
        setIsDownloading(false)
      }
    },
    [getNodes, currentLayoutMode, setLayoutMode]
  )

  return {
    downloadImage,
    isDownloading,
  }
}
