# Validation Utils

This folder contains reusable validation utilities and schemas for API endpoints.

## Files

- `validation-utils.ts` - Centralized validation functions for authentication, ownership, and request validation
- `api-schemas.ts` - Zod schemas for API request/response validation

## Key Benefits

1. **Reduced Code Duplication** - Validation logic went from ~50% of endpoint code to ~10%
2. **Consistent Error Handling** - Standardized error responses across all endpoints
3. **Type Safety** - Full TypeScript integration with Zod schemas
4. **Better Security** - Enhanced authentication with active user checks
5. **Safer Operations** - Combined validation prevents race conditions
6. **Frontend/Backend Consistency** - Same schemas can be used on both sides

## Frontend Usage

The same Zod schemas can be used on the frontend for client-side validation:

```typescript
// In a React component or hook
import { initializeDragTreeSchema } from '@/app/libs/api-schemas'

function MyComponent() {
  const handleSubmit = (data: unknown) => {
    const result = initializeDragTreeSchema.safeParse(data)
    if (!result.success) {
      // Handle validation errors on the frontend
      setErrors(result.error.flatten())
      return
    }

    // Data is now type-safe and validated
    submitToAPI(result.data)
  }
}
```

## API Endpoint Usage

```typescript
// In an API route
import {
  validateRequestBody,
  validateAuthentication,
} from '@/app/libs/validation-utils'
import { initializeDragTreeSchema } from '@/app/libs/api-schemas'

export async function POST(request: NextRequest) {
  // Validate authentication
  const authResult = await validateAuthentication()
  if (!authResult.success) {
    return authResult.error
  }

  // Validate request body
  const bodyResult = await validateRequestBody(
    request,
    initializeDragTreeSchema
  )
  if (!bodyResult.success) {
    return bodyResult.error
  }

  // Both auth and data are now validated
  const { userId } = authResult
  const validatedData = bodyResult.data
}
```

## Available Validation Functions

- `validateAuthentication()` - User auth + active status check
- `validateRequestBody(request, schema)` - Parse and validate JSON body
- `validateSearchParams(params, schema)` - Validate URL search parameters
- `validateDragTreeOwnership(userId, dragTreeId)` - Resource ownership
- `validateDragTreeStatus(dragTreeId, status)` - Status validation
- `validateDragTreeOperation(dragTreeId, status?)` - Combined validation

## Available Schemas

### Drag Tree Schemas

- `initializeDragTreeSchema` - For creating new drag trees
- `generateQuestionsSchema` - For generating questions
- `createNodeContentSchema` - For creating node content
- `updateNodeContentSchema` - For updating node content
- `researchGenerateSchema` - For research generation

### Screening Schemas

- `screeningRequestSchema` - For screening operations
- `screenAnalysisSchema` - For analysis responses

### Common Schemas

- `userIdParamSchema` - User ID validation
- `dragTreeIdParamSchema` - Drag tree ID validation
- `paginationSchema` - Pagination parameters
- `languageSchema` - Language preferences
