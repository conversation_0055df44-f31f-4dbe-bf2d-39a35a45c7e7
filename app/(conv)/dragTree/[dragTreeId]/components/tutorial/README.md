# Tutorial System - User-Level Persistence

## Overview

The tutorial system uses **user-level persistence** through the User model's `metadata` JSON field to track tutorial completion status across all devices and sessions.

## Architecture

### **1. Data Flow (Optimized)**

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Client.tsx    │◄───│ useDragTreeLoader │◄───│  getDragTree()  │
│                 │    │                  │    │   + metadata    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  MainContent    │    │   tutorialState  │    │ User.metadata   │
│                 │    │   (no API call)  │    │  (single query) │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │
         ▼
┌─────────────────┐
│ TutorialOverlay │
└─────────────────┘
```

### **2. Database Schema**

```sql
-- User model already has metadata field
User {
  metadata Json @default("{}")  -- Stores tutorial preferences
}
```

**Tutorial Metadata Structure:**

```json
{
  "tutorial": {
    "is_completed": true,
    "is_skipped": false,
    "created_at": "2024-01-15T10:30:00Z"
  }
}
```

### **3. Key Components**

#### **Server Actions (`app/server-actions/user.ts`)**

- `markTutorialCompleted(userId)` - Mark tutorial as completed
- `markTutorialSkipped(userId)` - Mark tutorial as skipped
- `checkTutorialStatus(userId)` - Check completion status
- `checkTutorialStatusFromMetadata(metadata)` - Helper for cached checking
- `testTutorialPersistence(userId)` - Development testing function

#### **Optimized Data Loading (`useDragTreeLoader`)**

- **Single Query**: `getDragTree()` now includes user metadata
- **No Separate API Calls**: Tutorial state determined from cached metadata
- **Returns**: `tutorialState { isCompleted, shouldShow, checked }`

#### **Tutorial Logic (`Client.tsx`)**

```typescript
// Tutorial shows when:
const shouldShowTutorial = Boolean(
  tutorialState.checked && // Data loaded
    tutorialState.shouldShow && // Not completed AND not skipped
    frontendTreeStructure && // Content ready
    !isDragTreeLoading // Not loading
)
```

## Usage

### **1. For New Users**

- Tutorial shows automatically on first dragTree visit
- Completion is tracked in user metadata
- Persists across devices/sessions

### **2. For Returning Users**

- Tutorial state loads with dragTree data (optimized)
- No separate API calls needed
- Instant decision on whether to show tutorial

### **3. Tutorial Actions**

- **Complete**: `onTutorialComplete()` → marks completed in DB → sets `is_completed: true, is_skipped: false`
- **Skip**: `onTutorialSkip()` → marks skipped in DB → sets `is_completed: false, is_skipped: true`

Both actions update the database and persist the user's preference.

## Benefits

### **✅ Performance Optimized**

- **Single Query**: User metadata loaded with dragTree data
- **No useEffect**: Tutorial state comes from optimized loader
- **No Separate API Calls**: Everything in one database hit

### **✅ User Experience**

- **Cross-Device Sync**: Works on mobile, desktop, anywhere user logs in
- **Persistent**: Survives browser data clearing, cookie deletion
- **Smart Logic**: Only shows when content is ready
- **Dual State Tracking**: Distinguishes between completed and skipped states

### **✅ Maintainable**

- **Type Safe**: Full TypeScript support
- **Clear Separation**: Server actions, hooks, components well-defined
- **Extensible**: Easy to add more tutorial types or preferences

## Development Testing

1. **Debug Button** (development only):

```tsx
// In Header.tsx - test tutorial persistence
<button onClick={() => testTutorialPersistence(userId)}>Test Tutorial</button>
```

2. **Console Logging**:

```javascript
// Check browser console for tutorial logic
🎯 [DragTreeClient] Tutorial logic: {
  checked: true,
  shouldShow: false,
  hasContent: true,
  finalDecision: false
}
```

3. **Database Verification**:

```sql
-- Check user metadata directly
SELECT metadata FROM users WHERE id = 'user_id';
```

## Implementation Checklist

- [x] Database schema (User.metadata field exists)
- [x] Server actions for tutorial CRUD operations
- [x] Optimized getDragTree() with user metadata
- [x] useDragTreeLoader with tutorial state logic
- [x] Client.tsx tutorial integration
- [x] MainContent.tsx props handling
- [x] TutorialOverlay.tsx compatibility
- [x] Development testing utilities
- [x] Production-ready (debug logs removed)

## Future Enhancements

1. **Multiple Tutorial Types**:

```json
{
  "tutorials": {
    "dragTree": { "completed": true, "skipped": false, "date": "..." },
    "conversations": { "completed": false, "skipped": false },
    "screening": { "completed": true, "skipped": false, "date": "..." }
  }
}
```

2. **Tutorial Analytics**:

- Track completion rates
- A/B testing for tutorial flow
- User engagement metrics

3. **Progressive Disclosure**:

- Different tutorial depth based on user experience
- Advanced features tutorials for power users
