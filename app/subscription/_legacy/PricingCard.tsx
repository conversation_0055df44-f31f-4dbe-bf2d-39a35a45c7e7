import React from 'react'
import { Button } from '@/components/ui/button'
import Image from 'next/image'

type PricingCardProps = {
  image: string
  title: string
  price: string
  features: string[]
  showButton: boolean
  isSubscribed: boolean
  priceId: string
  onSubscribe: (priceId: string) => void
}

export const PricingCard: React.FC<PricingCardProps> = ({
  image,
  title,
  price,
  features,
  showButton,
  isSubscribed,
  priceId,
  onSubscribe,
}) => (
  <div className="w-80 shadow-xl flex flex-col m-4 my-4 rounded-lg hover:scale-105 duration-300">
    <Image
      className="w-20 mx-auto"
      src={image}
      alt={title}
      width={80}
      height={80}
    />
    <h2 className="text-2xl font-bold text-center py-8">{title}</h2>
    <p className="text-center text-4xl font-bold">{price}</p>
    <div className="font-medium">
      {features.map((feature, index) => (
        <p
          key={index}
          className={`py-2 border-b mx-8 ${index === 0 ? 'mt-8' : ''}`}
        >
          {feature}
        </p>
      ))}
    </div>
    {showButton &&
      (isSubscribed ? (
        <Button
          className="bg-gray-500 duration-150 w-[200px] rounded-md font-medium my-6 mx-auto px-6 py-3"
          disabled
        >
          Subscribed, thanks!
        </Button>
      ) : (
        <Button
          className="bg-[#00df9a] hover:text-[#00df9a] hover:bg-gray-50 duration-150 w-[200px] rounded-md font-medium my-6 mx-auto px-6 py-3"
          onClick={() => onSubscribe(priceId)}
        >
          Subscribe
        </Button>
      ))}
  </div>
)
