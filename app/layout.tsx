import './globals.css'
import '@/styles/prosemirror.css'
import type { Metadata } from 'next'

import AuthContext from './context/AuthContext'
import ToasterContext from './context/ToasterContext'
import { Inter } from 'next/font/google'
import { Analytics } from '@vercel/analytics/react'
import Providers from './providers'
import UTMCaptureClient from '@/app/components/UTMCaptureClient'
import { Suspense } from 'react'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'ThinkGraph - AI-Powered Structured Thinking',
  description:
    'Transform complex problems into clear, actionable insights with ThinkGraph. Build interactive thinking graphs that help you explore every angle before making decisions. Perfect for deep thinkers, consultants, and knowledge workers.',
  keywords: [
    'ThinkGraph',
    'structured thinking',
    'AI-powered decision making',
    'interactive thinking graphs',
    'problem exploration tool',
    'decision support system',
    'complex problem solving',
    'AI-assisted brainstorming',
    'strategic planning tool',
    'thought organization',
    'knowledge worker productivity',
    'deep thinking tool',
    'comprehensive analysis',
    'decision tree builder',
    'AI for consultants',
    'management decision support',
    'systematic thinking',
    'problem decomposition',
    'insight generation',
    'ChatGPT enhancement',
    'AI productivity tool',
    'INTP thinking tool',
    'overthinking solution',
  ],
  icons: {
    // Use the latest app icon consistently
    icon: '/images/thinkgraph-favicon.svg',
    shortcut: '/images/thinkgraph-favicon.svg',
    apple: '/images/thinkgraph-icon.svg',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="h-full w-full">
      <head>
        <title>{'ThinkGraph - AI-Powered Structured Thinking'}</title>
        <meta name="description" content={metadata.description || ''} />
        <meta
          name="keywords"
          content={
            Array.isArray(metadata.keywords) ? metadata.keywords.join(', ') : ''
          }
        />
      </head>
      <body
        className={`${inter.className} h-full w-full flex flex-col`}
        suppressHydrationWarning={true}
      >
        <AuthContext>
          <Analytics />
          <ToasterContext />
          <Providers>
            {children}
            {/* Globally capture UTM on pages that render client-side */}
            <Suspense fallback={null}>
              <UTMCaptureClient />
            </Suspense>
          </Providers>
        </AuthContext>
      </body>
    </html>
  )
}
