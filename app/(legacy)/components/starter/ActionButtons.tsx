import { Button } from '@/components/ui/button'
import { minChars } from '@/app/configs'

type ActionButtonsProps = {
  description: string
  canStartClarify: boolean
  canStartParaphrase: boolean
  isLoading: boolean
  onRephrase: () => void
  onClarify: () => void
}

const ActionButtons: React.FC<ActionButtonsProps> = ({
  description,
  canStartClarify,
  canStartParaphrase,
  isLoading,
  onRephrase,
  onClarify,
}) => {
  const isRephraseDisabled =
    description.length < minChars || isLoading || !canStartParaphrase

  const isClarifyDisabled = !canStartClarify || isLoading
  const rephraseButtonClass = `w-1/2 mr-2 py-2 text-white rounded-lg transition-colors duration-300 ${
    isRephraseDisabled
      ? 'bg-gray-400 hover:bg-gray-500'
      : 'bg-green-500 hover:bg-green-600'
  }`

  const clarifyButtonClass = `w-1/2 ml-2 py-2 text-white rounded-lg transition-colors duration-300 ${
    isClarifyDisabled
      ? 'bg-gray-400 hover:bg-gray-500'
      : 'bg-blue-500 hover:bg-blue-600'
  }`

  return (
    <div className="flex flex-row justify-center items-center w-full max-w-2xl mx-auto mb-2">
      <Button
        className={rephraseButtonClass}
        onClick={onRephrase}
        disabled={isRephraseDisabled}
      >
        1. Paraphrase
      </Button>
      <Button
        className={clarifyButtonClass}
        onClick={onClarify}
        disabled={isClarifyDisabled}
      >
        2. Start Clarify
      </Button>
    </div>
  )
}

export default ActionButtons
