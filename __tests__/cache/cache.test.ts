import { createCache } from '@/libs/cache'

/**
 * Basic sanity checks for the generic in-memory cache
 */
describe('Generic Cache', () => {
  it('stores and retrieves values before expiry', () => {
    const cache = createCache<string>({ defaultTtlMs: 1000 })
    cache.set('foo', 'bar')
    expect(cache.get('foo')).toBe('bar')
  })

  it('evicts values after ttl', () => {
    jest.useFakeTimers()
    const cache = createCache<string>({ defaultTtlMs: 100 })
    cache.set('key', 'value')
    jest.advanceTimersByTime(150)
    expect(cache.get('key')).toBeNull()
    jest.useRealTimers()
  })

  it('sliding expiration extends ttl', () => {
    jest.useFakeTimers()
    const cache = createCache<string>({ defaultTtlMs: 100 })
    cache.set('k', 'v')
    jest.advanceTimersByTime(50)
    // touch via get should extend
    expect(cache.get('k')).toBe('v')
    jest.advanceTimersByTime(70)
    // 50+70 =120 >100 but we touched, should still exist
    expect(cache.get('k')).toBe('v')
    jest.advanceTimersByTime(120)
    expect(cache.get('k')).toBeNull()
    jest.useRealTimers()
  })
})
