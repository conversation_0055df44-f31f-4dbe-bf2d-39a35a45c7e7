'use client'

import React, { Suspense, lazy } from 'react'
import { DiagramView } from './DiagramView'
import { useVisualFlowSetup } from './hooks/useVisualFlowSetup'
import { TabContainer } from '@/app/(conv)/dragTree/[dragTreeId]/components/tabs/TabContainer'
import { useParams } from 'next/navigation'

// Dynamically import React<PERSON>lowProvider to complete ReactFlow bundle optimization
const ReactFlowProvider = lazy(() =>
  import('reactflow').then(module => ({
    default: module.ReactFlowProvider,
  }))
)

// Loading skeleton for ReactFlowProvider
const ReactFlowProviderSkeleton: React.FC = () => (
  <div className="w-full h-full flex items-center justify-center bg-gray-50 rounded-lg">
    <div className="text-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
      <p className="text-gray-600 text-sm">Loading...</p>
    </div>
  </div>
)

/**
 * Main VisualFlowDiagram component - orchestrates React Flow visualization
 * Displays tree data as an interactive flow diagram with default loading states
 */
const VisualFlowDiagram = () => {
  const { isInitialLoading, layoutMode, setLayoutMode } = useVisualFlowSetup()
  const params = useParams()
  const dragTreeId = params.dragTreeId as string

  // Show loading skeleton while initial setup is in progress
  if (isInitialLoading) {
    return <ReactFlowProviderSkeleton />
  }

  return (
    <div
      id="tutorial-reactflow-panel"
      className="h-full w-full relative min-h-0"
    >
      <div className="h-full w-full">
        <TabContainer dragTreeId={dragTreeId}>
          <Suspense fallback={<ReactFlowProviderSkeleton />}>
            <ReactFlowProvider>
              <DiagramView
                layoutMode={layoutMode}
                setLayoutMode={setLayoutMode}
              />
            </ReactFlowProvider>
          </Suspense>
        </TabContainer>
      </div>
    </div>
  )
}

// Memoize to prevent unnecessary re-renders when props haven't changed
export const MemoizedVisualFlowDiagram = React.memo(VisualFlowDiagram)
export { MemoizedVisualFlowDiagram as VisualFlowDiagram }
