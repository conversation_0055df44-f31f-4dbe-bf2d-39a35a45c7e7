- Never use db push on for prisma in development environment
- For operations, you will <PERSON>VE<PERSON> do ANYTHING that may delete the data, you MUST ask for permission, or just bypass that

- When we work on backend/database related tasks, you need to pay extreme attention dataflow, enumerate common scenario (CRUD), interaction b/w tables and their impacts on both frontend and backend, also checkbox they are all good before implementation

- I prefer type annotation, please add if possible, eg: useState<bool>(true) instead of useState(true)

- I prefer meaningful semantic html tags than always dev

- I prefer type than interface in typescript

- I prefer tailwind cn rather than many conditional style code

- I prefer using @/app/... instead of relative path if possible, unless they are in the same folder for export

- I prefer DRY principle, reuse/modify the existing components if possible

- I prefer code readability and prevent bulky components

- I prefer clean codebase and remove unused import

- I prefer more comments such that future AI models can get the context quickly

- I want to have a README at feature level, document the key information like components and interactions such that future AI models don't need to read one by one. You will also update the README after some modification

- In this project, we use Vercel AI SDK to bridge usage of LLM in Frontend and Backend, don't reinvent the wheel, use that if possible

- If possible, use defined prisma status instead of hardcoding

- VERY IMPORTANT, you are helping me to write code, you need to give complete implementation, don't just say Replace with existing code
