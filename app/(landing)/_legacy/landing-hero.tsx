'use client'
import dynamic from 'next/dynamic'
import type { YouTubeProps } from 'react-youtube'
import AuthForm from '@/app/(landing)/components/AuthForm'
import Image from 'next/image'
import { youtubeEmbedUrlId } from '@/app/configs'
import TextRotate from './LandingTextRotate'

const StaticLogoCloud = () => {
  return (
    <div className="w-full py-12">
      <div className="flex flex-row w-full items-center justify-center px-4 md:px-8">
        <div className="text-xl sm:text-xl md:text-xl font-medium text-white">
          ❤️ Supported by:
        </div>
        <Image
          src="/images/MS_Startups_FH_lockup_hrz_OnDrk_RGB.png"
          alt="Clarify AI Hero Image"
          width={384}
          height={216}
          style={{ width: 'auto', height: 'auto' }}
        />
      </div>
    </div>
  )
}

const YouTube = dynamic(
  () => import('react-youtube').then(mod => mod.default),
  {
    ssr: false,
    // Skeleton loader while the YouTube component (and iframe) is downloading
    loading: () => (
      <div className="w-[480px] h-[290px] animate-pulse bg-zinc-800 rounded-md" />
    ),
  }
)

export const LandingHero = () => {
  const onPlayerReady: YouTubeProps['onReady'] = event => {
    // access to player in all event handlers via event.target
    event.target.pauseVideo()
  }

  const opts: YouTubeProps['opts'] = {
    height: '290',
    width: '480',
    playerVars: {
      // https://developers.google.com/youtube/player_parameters
      autoplay: 0,
    },
  }

  return (
    <div className="text-white font-bold py-12 text-center space-y-5">
      <div className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl space-y-5 font-extrabold">
        <h2>Have an MBB consultant on your side</h2>
        <TextRotate />
        <h2 className="sm:text-lg md:text-2xl lg:text-3xl">
          Structure your ideas with AI assistance
        </h2>
      </div>
      <div className="md:text-lg text-zinc-400">
        Time To Value In Minutes, Not Days
      </div>

      <div className="flex justify-center items-center video-container rounded-lg overflow-hidden">
        <YouTube
          videoId={youtubeEmbedUrlId}
          opts={opts}
          onReady={onPlayerReady}
        />
      </div>
      <div className="md:text-sm sm:text-sm text-zinc-400">
        No overpromising, what you see is what you get!
      </div>
      <StaticLogoCloud />
      <AuthForm />
    </div>
  )
}
