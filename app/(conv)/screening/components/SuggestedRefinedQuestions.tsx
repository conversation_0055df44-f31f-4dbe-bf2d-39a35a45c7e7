import React, { useState } from 'react'
import { cn } from '@/lib/utils'

type SuggestedRefinedQuestionsProps = {
  suggestions: string[]
  onSuggestionClick: (suggestion: string) => void
  onUseMyInput?: () => void
  showUseMyInputButton?: boolean
  isLoading: boolean
  hasUserMadeSelection?: boolean
  selectedSuggestion?: string
}

const SuggestedRefinedQuestions: React.FC<SuggestedRefinedQuestionsProps> = ({
  suggestions,
  onSuggestionClick,
  onUseMyInput,
  showUseMyInputButton = false,
  isLoading,
  hasUserMadeSelection: _hasUserMadeSelection = false, // Currently unused
  selectedSuggestion,
}) => {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null)
  const [showTooltip, setShowTooltip] = useState<boolean>(false)
  const [hoverTimeout, setHoverTimeout] = useState<NodeJS.Timeout | null>(null)

  if (suggestions.length === 0) return null

  const handleMouseEnter = (index: number) => {
    setHoveredIndex(index)
    // Clear any existing timeout
    if (hoverTimeout) {
      clearTimeout(hoverTimeout)
    }
    // Set a 1-second delay before showing tooltip
    const timeout = setTimeout(() => {
      setShowTooltip(true)
    }, 1000)
    setHoverTimeout(timeout)
  }

  const handleMouseLeave = () => {
    setHoveredIndex(null)
    setShowTooltip(false)
    if (hoverTimeout) {
      clearTimeout(hoverTimeout)
      setHoverTimeout(null)
    }
  }

  return (
    <div className="flex flex-col h-full space-y-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="bg-blue-100 text-blue-600 rounded-full w-8 h-8 flex items-center justify-center font-bold text-sm animate-pulse">
            AI
          </div>
          <h3 className="text-lg font-semibold text-slate-800">
            Choose the one that best captures your intent, or use them as
            inspiration to create your own refined version.
          </h3>
        </div>

        {onUseMyInput && showUseMyInputButton && (
          <button
            onClick={onUseMyInput}
            disabled={isLoading}
            className="px-3 py-2 text-sm font-medium text-blue-700 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 hover:border-blue-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex-shrink-0"
          >
            My Original Input
          </button>
        )}
      </div>

      {/* Question suggestions grid - no animation wrapper here */}
      <div className="flex-1 min-h-0">
        <div className="h-full overflow-y-auto pb-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {suggestions.map((suggestion, index) => {
              const isSelected = selectedSuggestion === suggestion

              return (
                <div
                  key={index}
                  className="relative"
                  onMouseEnter={() => handleMouseEnter(index)}
                  onMouseLeave={handleMouseLeave}
                >
                  {/* Tooltip for full text on hover - show for all items after delay */}
                  {showTooltip && hoveredIndex === index && (
                    <div
                      className={cn(
                        'absolute left-0 right-0 z-50 bg-slate-800 text-white text-sm rounded-lg p-3 shadow-lg transform',
                        {
                          'top-full mt-2': index < 2,
                          'bottom-full mb-2': index >= 2,
                        }
                      )}
                    >
                      <div className="max-h-32 overflow-y-auto">
                        {suggestion}
                      </div>
                      {/* Arrow pointing to the card */}
                      <div
                        className={cn(
                          'absolute left-4 w-0 h-0 border-l-4 border-r-4 border-transparent',
                          {
                            'top-0 -mt-1 border-b-4 border-b-slate-800':
                              index < 2,
                            'bottom-0 -mb-1 border-t-4 border-t-slate-800':
                              index >= 2,
                          }
                        )}
                      />
                    </div>
                  )}

                  <div
                    className={cn(
                      'group relative transition-all duration-200 cursor-pointer rounded-lg p-4 h-32 flex',
                      {
                        'bg-blue-50 border-2 border-blue-400 shadow-md':
                          isSelected,
                        'bg-slate-50 hover:bg-slate-100 border border-slate-200 hover:border-slate-300 hover:shadow-sm':
                          !isSelected,
                      }
                    )}
                    onClick={() => !isLoading && onSuggestionClick(suggestion)}
                  >
                    <div className="flex items-start space-x-2 h-full w-full">
                      <div
                        className={cn(
                          'rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold flex-shrink-0 mt-1',
                          {
                            'bg-blue-500 text-white': isSelected,
                            'bg-blue-400 text-white': !isSelected,
                          }
                        )}
                      >
                        {index + 1}
                      </div>
                      <div className="flex-1 min-w-0 flex items-start">
                        <div
                          className={cn(
                            'text-sm leading-relaxed transition-colors line-clamp-4',
                            {
                              'text-blue-900 font-medium': isSelected,
                              'text-slate-700 group-hover:text-slate-900':
                                !isSelected,
                            }
                          )}
                        >
                          {suggestion}
                        </div>
                      </div>
                    </div>

                    {/* Selection indicator */}
                    {isSelected && (
                      <div className="absolute top-1 right-1">
                        <div className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center">
                          <svg
                            className="w-3 h-3"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                      </div>
                    )}

                    {/* Hover effect border for non-selected items */}
                    {!isSelected && (
                      <div className="absolute inset-0 border-2 border-blue-200 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none" />
                    )}
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>
    </div>
  )
}

export default SuggestedRefinedQuestions
