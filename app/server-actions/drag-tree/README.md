# DragTree Server Actions - AI Generation Persistence

## **Overview**

This module contains production-ready server actions for AI generation persistence, implementing optimistic locking, atomic authorization, and robust error handling. These actions provide the backend foundation for the AI generation system with enterprise-grade reliability and security.

## **Core Server Actions**

### **1. get_ai_generations.ts**

**Purpose**: Fetch AI generation metadata for lazy loading and listing

**Key Features**:

- Atomic ownership validation using Prisma relations
- Lightweight metadata only (excludes heavy content fields)
- Pagination support with offset/limit
- Status-based filtering

```typescript
export async function getAIGenerations(
  dragTreeId: string,
  status: AIGenerationStatus = AIGenerationStatus.ACTIVE,
  limit: number = 50,
  offset: number = 0
): Promise<AIGenerationMeta[]>
```

**Security Implementation**:

```typescript
// Single atomic query validates dragTree ownership
const validation = await validateDragTreeOwnership(userId, dragTreeId)
if (!validation.success) {
  throw new Error('Access denied: You do not own this dragTree')
}
```

### **2. get_ai_generation_content.ts**

**Purpose**: Load full AI generation content for editing and viewing

**Key Features**:

- Atomic relation-based ownership validation
- Lazy loading of heavy content fields
- Version information for optimistic locking
- Access validation helper function

```typescript
export async function getAIGenerationContent(
  generationId: string
): Promise<AIGenerationContent | null>
```

**Atomic Authorization Pattern**:

```typescript
const generation = await prisma.aIGeneration.findFirst({
  where: {
    id: generationId,
    user_id: userId,
    // Atomic dragTree ownership validation
    OR: [
      { entity_type: { not: 'drag_tree_v1' } },
      {
        entity_type: 'drag_tree_v1',
        dragTree: { user_id: userId },
      },
    ],
  },
})
```

### **3. update_ai_generation.ts**

**Purpose**: Update AI generation content with optimistic locking

**Key Features**:

- Optimistic locking with version checking
- Atomic transactions for consistency
- Comprehensive error handling with specific error types
- Rollback capability for version conflicts

```typescript
export async function updateAIGeneration(
  generationId: string,
  updateData: AIGenerationUpdateData,
  expectedVersion: number
): Promise<AIGenerationUpdateResult>
```

**Optimistic Locking Implementation**:

```typescript
const result = await prisma.$transaction(async (tx) => {
  const existingGeneration = await tx.aIGeneration.findFirst({
    where: {
      id: generationId,
      user_id: userId,
      // Atomic ownership validation
    },
    select: { version: true, ... }
  })

  // Version conflict detection
  if (existingGeneration.version !== expectedVersion) {
    throw new Error(`Version conflict: expected ${expectedVersion}, found ${existingGeneration.version}`)
  }

  // Atomic update with version increment
  const updated = await tx.aIGeneration.update({
    where: { id: generationId, version: expectedVersion },
    data: { ...updateData, version: existingGeneration.version + 1 }
  })

  return updated.version
})
```

## **Security Architecture**

### **Multi-Layer Authorization**

1. **Session Validation**: All actions require valid user authentication
2. **User Ownership**: Direct user_id validation on AI generation records
3. **DragTree Ownership**: Atomic validation for drag_tree_v1 entities
4. **Atomic Queries**: Single-query validation prevents TOCTOU vulnerabilities

### **Ownership Validation Patterns**

#### **Direct Validation (get_ai_generations.ts)**

```typescript
// Pre-validated dragTree ownership, then filter by user_id
const validation = await validateDragTreeOwnership(userId, dragTreeId)
const generations = await prisma.aIGeneration.findMany({
  where: { user_id: userId, entity_id: dragTreeId, ... }
})
```

#### **Relation-Based Validation (get_ai_generation_content.ts)**

```typescript
// Single atomic query validates all ownership in one operation
const generation = await prisma.aIGeneration.findFirst({
  where: {
    id: generationId,
    user_id: userId,
    dragTree: { user_id: userId }, // Atomic relation validation
  },
})
```

## **Error Handling Strategies**

### **Version Conflict Resolution**

```typescript
// Specific error types for different scenarios
if (result.error?.includes('Version conflict')) {
  return {
    success: false,
    error:
      'Content was modified by another session. Please refresh and try again.',
  }
}
```

### **Authorization Failures**

```typescript
// Secure error messages (no data leakage)
if (!validation.success) {
  throw new Error('Access denied: You do not own this dragTree')
}
```

### **Database Transaction Failures**

```typescript
// Atomic rollback with specific error context
try {
  const result = await prisma.$transaction(async tx => {
    // All operations must succeed or all rollback
  })
} catch (error) {
  // Comprehensive error logging without exposing internals
  console.error('Transaction failed:', error)
  return { success: false, error: 'Update failed' }
}
```

## **Performance Optimizations**

### **Query Efficiency**

- **Atomic Queries**: Single database round trips for complex validations
- **Selective Fields**: Only fetch required data (metadata vs full content)
- **Indexed Lookups**: Efficient access patterns using composite indexes

### **Memory Management**

- **Lazy Loading**: Heavy content fields loaded only when needed
- **Pagination**: Controlled result set sizes with offset/limit
- **Connection Pooling**: Efficient database connection reuse

### **Caching Strategy**

- **Metadata Caching**: Lightweight generation lists cached locally
- **Content On-Demand**: Full content loaded only for active editing
- **Version-Based Invalidation**: Cache invalidation using version numbers

## **Type Safety & Data Validation**

### **Shared Type Definitions**

```typescript
// Centralized types in app/types/ai-generation.ts
export type AIGenerationMeta = {
  id: string
  title: string | null
  status: AIGenerationStatus
  version: number // For optimistic locking
  createdAt: Date
  updatedAt: Date
  config: any
  metadata: any
}

export type AIGenerationUpdateResult = {
  success: boolean
  newVersion?: number
  error?: string
}
```

### **Input Validation**

- **Session Verification**: Mandatory authentication checks
- **Parameter Validation**: Type-safe parameter handling
- **Content Sanitization**: Safe handling of user-generated content

## **Integration Patterns**

### **Frontend Integration**

```typescript
// Optimistic update with rollback
const originalContent = asset.content
updateUIOptimistically(newContent)

try {
  const result = await updateAIGeneration(id, updateData, currentVersion)
  if (result.success) {
    confirmUpdate(result.newVersion)
  } else {
    rollbackToOriginal(originalContent)
    showError(result.error)
  }
} catch (error) {
  rollbackToOriginal(originalContent)
  showNetworkError()
}
```

### **Asset Store Integration**

```typescript
// Safe merging with timestamp comparison
const shouldMerge = serverUpdatedAt > localUpdatedAt || !isContentLoaded
if (shouldMerge) {
  mergeServerData(preserveLocalUIState)
} else {
  keepLocalChanges()
}
```

## **Common Patterns & Best Practices**

### **For Server Action Developers**

1. **Always validate ownership** before any data access
2. **Use atomic transactions** for consistency
3. **Implement specific error types** for different scenarios
4. **Log comprehensively** but securely (no sensitive data)

### **For Frontend Developers**

1. **Handle version conflicts gracefully** with user-friendly messages
2. **Implement optimistic updates** for immediate feedback
3. **Provide rollback mechanisms** for failed operations
4. **Show appropriate loading states** during async operations

### **For System Architects**

1. **Atomic operations prevent race conditions**
2. **Version-based optimistic locking enables collaboration**
3. **Relation-based queries improve security and performance**
4. **Comprehensive error handling ensures system reliability**

## **Monitoring & Debugging**

### **Key Metrics**

- Update success/failure rates
- Version conflict frequency
- Authorization failure patterns
- Query performance metrics

### **Debug Logging Patterns**

```
✅ [AI Generation] Updated generation: doc_12345, new version: 3
❌ [AI Generation] Version conflict: expected 2, found 3
🔒 [AI Generation] Access denied for user: user_789, dragTree: tree_456
```

## **Future Extensions**

### **Prepared Capabilities**

- **Collaboration Features**: Multi-user editing with conflict resolution
- **Audit Logging**: Complete change history tracking
- **Advanced Permissions**: Role-based access control
- **Performance Scaling**: Read replicas and caching layers

**Status**: Production-ready server actions providing enterprise-grade AI generation persistence with atomic operations, optimistic locking, and comprehensive security.
