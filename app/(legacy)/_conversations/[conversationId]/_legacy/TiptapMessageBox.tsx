import { Editor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'

import React, { useEffect, useMemo } from 'react'
import { Markdown } from 'tiptap-markdown'
import clsx from 'clsx'
import { format } from 'date-fns'
import { User } from '@prisma/client'
import Avatar from '@/app/components/Avatar'
import { UIMessage } from 'ai'
import { CodeBlockLowlight } from '@tiptap/extension-code-block-lowlight'
import { lowlight } from 'lowlight'
import css from 'highlight.js/lib/languages/css'
import js from 'highlight.js/lib/languages/javascript'
import ts from 'highlight.js/lib/languages/typescript'
import html from 'highlight.js/lib/languages/xml'
import python from 'highlight.js/lib/languages/python'
import json from 'highlight.js/lib/languages/json'
import 'highlight.js/styles/atom-one-dark.css'
import '@/app/components/editor/extensions/styles.scss'

interface TiptapMessageBoxProps {
  data: UIMessage
  isCurrentUser?: boolean
  currentUser?: User | null
}

lowlight.registerLanguage('html', html)
lowlight.registerLanguage('css', css)
lowlight.registerLanguage('js', js)
lowlight.registerLanguage('ts', ts)
lowlight.registerLanguage('python', python)
lowlight.registerLanguage('json', json)

const TiptapMessageBox: React.FC<TiptapMessageBoxProps> = ({
  data,
  isCurrentUser,
  currentUser,
}) => {
  const container = clsx('flex gap-3 p-4 justify-start')
  const body = clsx('flex flex-col gap-2')
  const message = clsx(
    'text-sm overflow-hidden max-w-xs md:max-w-md lg:max-w-2xl mx-auto stretch whitespace-pre-wrap break-words',
    isCurrentUser ? 'bg-sky-500 text-white' : 'bg-gray-100',
    'rounded-2xl py-2 px-3'
  )

  const addBoldToSectionStarters = (content: string) => {
    // This is a hacky way to bold section starters
    // eg: Requirement Summary: -> **Requirement Summary:**
    // The reason for doing this is we can use tiptap to parse markdown and display
    // But openai doesn't return markdown format sometimes
    // So we observe this pattern: 1. a line ends with : 2. has up to 3 words

    // Match lines ending with : that have up to 3 words (you can adjust this)
    const regex = /^(\w+(?: \w+){0,2}):$/gm

    // Add ** to the start and end
    return content.replace(regex, '**$1:**')
  }

  // Only apply that to machine generated messages
  const preprocessedContent = !isCurrentUser
    ? addBoldToSectionStarters((data as any).content)
    : (data as any).content

  const TiptapMessageExtensions = [
    // EmptyLine,
    Markdown,
    StarterKit.configure({
      bulletList: {
        HTMLAttributes: {
          class: 'list-disc list-outside leading-3 -mt-2 pl-4', // Added pl-4 (padding-left: 1rem)
        },
      },
      orderedList: {
        HTMLAttributes: {
          class: 'list-decimal list-outside leading-3 -mt-2 pl-4', // Added pl-4 (padding-left: 1rem)
        },
      },
      listItem: {
        HTMLAttributes: {
          class: 'leading-normal mb-2', // Changed -mb-2 to mb-2 (margin-bottom: 0.5rem)
        },
      },
      blockquote: {
        HTMLAttributes: {
          class: 'border-l-4 border-stone-700',
        },
      },
      // codeBlock: {
      //   HTMLAttributes: {
      //     class:
      //       "rounded-sm bg-stone-100 p-5 font-mono font-medium text-stone-800",
      //   },
      // },
      bold: {
        HTMLAttributes: {
          class: 'leading-loose block py-1', // Increased line height and made bold texts block-level
        },
      },
      horizontalRule: false,
      dropcursor: {
        color: '#DBEAFE',
        width: 4,
      },
      gapcursor: false,
    }),
    CodeBlockLowlight.configure({
      lowlight,
      HTMLAttributes: {
        class: 'rounded-md p-4 font-mono pre-code-block',
      },
    }),
  ]

  const editor = useMemo(
    () =>
      new Editor({
        content: preprocessedContent,
        // Only apply extensions to machine generated messages
        extensions: !isCurrentUser ? TiptapMessageExtensions : [StarterKit],
        editable: false, // Read-only
      }),
    [preprocessedContent, isCurrentUser, TiptapMessageExtensions]
  )

  // to destroy the editor instance when the component unmounts
  useEffect(() => {
    return () => {
      editor?.destroy()
    }
  }, [editor])

  return (
    <div className={container}>
      <div>
        {isCurrentUser && currentUser && <Avatar user={currentUser} />}
        {!isCurrentUser && <Avatar />}
      </div>
      {/* ... other components */}
      <div className={body}>
        <div className="flex items-center gap-1">
          <div className="text-xs text-gray-400">
            {/* Pp example: 07/03/2023, 6:15 PM */}
            {(data as any).createdAt &&
              format(new Date((data as any).createdAt), 'Pp')}
          </div>
        </div>
        <div className={message}>{<EditorContent editor={editor} />}</div>
      </div>
    </div>
  )
}

export default TiptapMessageBox
