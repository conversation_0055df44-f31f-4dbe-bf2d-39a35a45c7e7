// import { NextResponse } from "next/server";
// import prisma from "@/app/libs/prismadb";
// import { UserStatus } from "@prisma/client";

// // Define the POST request handler function
// export async function POST(request: Request) {
//   try {
//     // Parse the request body as JSON
//     const body = await request.json();

//     // Extract the necessary fields from the request body
//     const { content, role, conversationId, currentUser } = body;
//     console.log("message/route.ts", body);

//     // Check if the user is authorized
//     if (!currentUser?.id || !currentUser?.email) {
//       return new NextResponse("Unauthorized", { status: 401 });
//     }

//     // Block them from accessing API if they are not active
//     if (currentUser?.status !== UserStatus.ACTIVE) {
//       return new NextResponse("Unauthorized", { status: 401 });
//     }

//     // Determine the creator ID based on the user's role
//     // If the role is "user", use the user's ID as the creator ID
//     // For the first message, the role is "system", use "system" as the creator ID
//     // Otherwise, use "assistant" as the creator ID
//     // Basically everything returns from API/system constructed are "system" or "assistant"
//     let creator_id = "assistant";
//     if (role === "user") {
//       creator_id = currentUser.id;
//     }

//     // For the scenario when the conversation is just started
//     if (role === "system") {
//       creator_id = "system";
//     }

//     // Create a new message in the database using Prisma
//     const newMessage = await prisma.message.create({
//       data: {
//         content: content,
//         creator_id: creator_id,
//         conversation: {
//           connect: { id: conversationId },
//         },
//       },
//     });

//     // Update the conversation's updated_at field manually :(
//     // Such that the conversation list will be sorted by the latest messages
//     // Since updating message will not update the conversation's updated_at field
//     const conversation = await prisma.conversation.update({
//       where: { id: conversationId },
//       data: { updated_at: new Date() },
//     });

//     // Return the newly created message as a JSON response
//     return NextResponse.json(newMessage);
//   } catch (error) {
//     console.error(error);

//     // If an error occurs, return a 500 status code with an "Error" message
//     return new NextResponse("Error", { status: 500 });
//   }
// }
