'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { TreePine, Workflow, Bot } from 'lucide-react'

export type MobileTab = 'tree' | 'diagram' | 'ai'

type MobileTabNavigationProps = {
  activeTab: MobileTab
  onTabChange: (tab: MobileTab) => void
  showAiTab: boolean
  className?: string
}

const tabs = [
  {
    id: 'tree' as const,
    label: 'Tree View',
    icon: TreePine,
    description: 'Hierarchical outline',
  },
  {
    id: 'diagram' as const,
    label: 'Diagram',
    icon: Workflow,
    description: 'Visual flow diagram',
  },
  {
    id: 'ai' as const,
    label: 'AI Assistant',
    icon: Bot,
    description: 'AI chat interface',
  },
]

export default function MobileTabNavigation({
  activeTab,
  onTabChange,
  showAiTab,
  className,
}: MobileTabNavigationProps) {
  const visibleTabs = tabs.filter(tab => tab.id !== 'ai' || showAiTab)

  return (
    <div
      className={cn(
        'bg-white border-b border-gray-200 px-1 py-2 safe-area-top',
        className
      )}
    >
      <nav className="flex space-x-0.5" role="tablist">
        {visibleTabs.map(tab => {
          const Icon = tab.icon
          const isActive = activeTab === tab.id

          return (
            <button
              key={tab.id}
              type="button"
              role="tab"
              aria-selected={isActive}
              aria-controls={`${tab.id}-panel`}
              className={cn(
                'relative flex items-center gap-2 px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200 touch-manipulation min-h-[48px] flex-1 justify-center overflow-hidden',
                {
                  'bg-blue-50 text-blue-700 shadow-sm': isActive,
                  'text-gray-600 hover:text-gray-800 hover:bg-gray-50 active:bg-gray-100':
                    !isActive,
                }
              )}
              onClick={() => onTabChange(tab.id)}
              title={tab.description}
            >
              <Icon
                className={cn(
                  'w-5 h-5 flex-shrink-0 transition-colors duration-200',
                  {
                    'text-blue-600': isActive,
                    'text-gray-500': !isActive,
                  }
                )}
              />
              <span className="truncate font-medium">{tab.label}</span>
              {isActive && (
                <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-600" />
              )}
            </button>
          )
        })}
      </nav>
    </div>
  )
}
