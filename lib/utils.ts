import { ConversationStatus, User } from '@prisma/client'
import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'
import { maxActiveConversations } from '@/app/configs'
import { SubscriptionTier } from '@prisma/client'
import { hasPaidFeatures } from '@/app/configs/tier-permissions'
import { linksFromSearchResults } from '@/lib/search-utils'
import { SearchResult } from '@/app/api/rag/utils'
import { ConversationLayoutType } from '@/app/types'
import { formatDistanceToNow, format, isToday, isYesterday } from 'date-fns'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Format timestamp for chat messages with human-readable relative/absolute time
 * @param date - The date to format
 * @returns Formatted timestamp string
 */
export function formatChatTimestamp(
  date: Date | string | null | undefined
): string {
  // Handle null, undefined, or empty values
  if (!date) {
    return ''
  }

  let dateObj: Date

  // Convert string to Date if needed
  if (typeof date === 'string') {
    dateObj = new Date(date)
  } else if (date instanceof Date) {
    dateObj = date
  } else {
    // Handle any other type by returning empty string
    return ''
  }

  // Handle invalid dates
  if (isNaN(dateObj.getTime())) {
    return ''
  }

  const now = new Date()
  const diffInMinutes = Math.floor(
    (now.getTime() - dateObj.getTime()) / (1000 * 60)
  )

  // For very recent messages (< 1 minute), show "Just now"
  if (diffInMinutes < 1) {
    return 'Just now'
  }

  // For recent messages (< 1 hour), show relative time like "2 minutes ago"
  if (diffInMinutes < 60) {
    return formatDistanceToNow(dateObj, { addSuffix: true })
  }

  // For today's messages, show "Today HH:MM AM/PM"
  if (isToday(dateObj)) {
    return `Today ${format(dateObj, 'h:mm a')}`
  }

  // For yesterday's messages, show "Yesterday HH:MM AM/PM"
  if (isYesterday(dateObj)) {
    return `Yesterday ${format(dateObj, 'h:mm a')}`
  }

  // For older messages, show full date and time
  return format(dateObj, 'MMM d, yyyy h:mm a')
}

// This is used to add alert after number to remind users that LLM often BS
// eg: if suffix is (EXAMPLE NUMBER)
// 1. **Standard Numbers**
//    - **Input:** "The population of the city is 3,250."
//    - **Output:** "The population of the city is 3,250 (EXAMPLE NUMBER)."

// 2. **Numbers with Commas (thousands separators)**
//    - **Input:** "The company's revenue reached $2,000,000 last year."
//    - **Output:** "The company's revenue reached $2,000,000 (EXAMPLE NUMBER) last year."

// 3. **Numbers with Decimals**
//    - **Input:** "The area of the office is 250.75 square meters."
//    - **Output:** "The area of the office is 250.75 (EXAMPLE NUMBER) square meters."

// 4. **Percentages**
//    - **Input:** "About 15% of the software installed is outdated."
//    - **Output:** "About 15% (EXAMPLE NUMBER) of the software installed is outdated."

// 5. **Ranges with Hyphens**
//    - **Input:** "The expected attendees are 1,000-1,500 people."
//    - **Output:** "The expected attendees are 1,000-1,500 (EXAMPLE NUMBER) people."

// 6. **Numbers Followed by Punctuation**
//     - **Input:** "The project requires an investment of $1,000,000; xxx"
//     - **Output:** "The project requires an investment of $1,000,000 (EXAMPLE NUMBER); xxx"

export function addSuffixAfterNumber(
  inputText: string,
  suffix: string
): string {
  // Updated regex to handle numbers including commas, periods, percent signs, exclamation marks, and hyphens
  const regex = /(\d[\d,.%!-]*)(\s|\b)/g
  // Replace by inserting the suffix right after the complete number and before the space or word boundary
  return inputText.replace(regex, `$1 ${suffix}$2`)
}

export function countActiveConversationsAfterDate(
  conversationList: ConversationLayoutType[],
  status: ConversationStatus,
  date: Date
): number {
  return (
    conversationList.filter(item => {
      const isActive = item.conversation_status === status
      const isAfterDate = new Date(item.created_at) > new Date(date)

      return isActive && isAfterDate
    }).length || 0
  )
}

export const isLocalOrDevEnv = () => {
  // Localhost or dev environment
  if (typeof window !== 'undefined') {
    return (
      window.location.hostname === 'localhost' ||
      window.location.hostname.includes('dev')
    )
  }
  return false
}

/**
 * Check if user has paid features (PRO, GUEST, ULTRA, BUSINESS)
 * This is the new recommended way to check for premium access
 */
export const userHasPaidFeatures = (user: User | null): boolean => {
  if (!user) return false

  // GUEST tier always has paid features (no expiry check needed)
  if (user.subscription_tier === SubscriptionTier.GUEST) {
    return true
  }

  // For other paid tiers, check if subscription is still valid
  if (hasPaidFeatures(user.subscription_tier)) {
    return Boolean(
      user.subscription_end_date &&
        new Date(user.subscription_end_date) > new Date()
    )
  }

  return false
}

/**
 * Check if user is PRO specifically (excludes GUEST)
 */
export const isUserPro = (user: User | null): boolean => {
  return (
    !!user &&
    user.subscription_tier === SubscriptionTier.PRO &&
    Boolean(
      user.subscription_end_date &&
        new Date(user.subscription_end_date) > new Date()
    )
  )
}

/**
 * Legacy function - kept for backward compatibility
 * @deprecated Use userHasPaidFeatures instead
 */
export const isUserSubscribing = (user: User | null): boolean => {
  return userHasPaidFeatures(user)
}

export function calculateMaxActiveConversations(_user: User | null): number {
  // Simplified: use config default; higher in dev for testing
  if (isLocalOrDevEnv()) return 30
  return maxActiveConversations
}

export const getResponseWithSearchContext = (
  response: string,
  searchResults: SearchResult[] | null,
  numberOfLinksToUse: number
): string => {
  // If there's no response, return an empty string
  if (!response) {
    return ''
  }
  // If there's no search results, return the response
  if (searchResults === null || searchResults.length === 0) {
    return response
  }

  const contextLinks = linksFromSearchResults(searchResults, numberOfLinksToUse)

  return `${response}\n\n=== Context sources: ===\n${contextLinks}`
}
