'use client'

import React from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { FiAlertTriangle, FiTrash2 } from 'react-icons/fi'
import type { Asset } from '../../stores/useAssetStore'

interface AssetDeleteDialogProps {
  isOpen: boolean
  asset: Asset | null
  onConfirm: () => void
  onCancel: () => void
  isDeleting?: boolean
}

export default function AssetDeleteDialog({
  isOpen,
  asset,
  onConfirm,
  onCancel,
  isDeleting = false,
}: AssetDeleteDialogProps) {
  if (!asset) return null

  const assetTypeLabel = asset.type === 'generate' ? 'Generation' : 'Chat'

  return (
    <Dialog open={isOpen} onOpenChange={onCancel}>
      <DialogContent
        className="rounded-2xl border-0 shadow-2xl bg-white/95 backdrop-blur-sm max-w-md"
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <DialogHeader>
          <DialogTitle
            id="delete-dialog-title"
            className="text-xl font-bold bg-gradient-to-r from-red-600 to-red-800 bg-clip-text text-transparent flex items-center gap-2"
          >
            <FiAlertTriangle className="text-red-600 w-5 h-5" />
            Delete {assetTypeLabel}
          </DialogTitle>
          <DialogDescription
            id="delete-dialog-description"
            className="text-gray-600"
          >
            This action cannot be undone. The asset will be permanently removed.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-slate-700 leading-relaxed">
              Are you sure you want to delete{' '}
              <span className="font-semibold text-slate-900">
                &quot;{asset.title}&quot;
              </span>
              ?
            </p>

            <div className="mt-3 text-sm text-slate-600">
              <div className="flex items-center gap-2 mb-1">
                <span className="font-medium">Type:</span>
                <span className="capitalize">{assetTypeLabel}</span>
              </div>
              <div className="flex items-center gap-2 mb-1">
                <span className="font-medium">Model:</span>
                <span>{asset.model}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="font-medium">Created:</span>
                <span>{asset.createdAt.toLocaleDateString()}</span>
              </div>
            </div>
          </div>

          <div className="p-3 bg-amber-50 border border-amber-200 rounded-lg">
            <div className="flex items-start gap-2">
              <FiAlertTriangle className="text-amber-600 w-4 h-4 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-amber-800">
                <p className="font-medium mb-1">This will permanently:</p>
                <ul className="list-disc list-inside space-y-1 text-xs">
                  <li>Remove the asset from your sidebar</li>
                  <li>Close any open tabs for this asset</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end mt-6 gap-3">
          <Button
            variant="ghost"
            onClick={onCancel}
            disabled={isDeleting}
            className="rounded-xl px-6 py-2 font-medium"
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={onConfirm}
            disabled={isDeleting}
            className="rounded-xl px-6 py-2 font-medium bg-gradient-to-r from-red-500 to-red-600 shadow-lg shadow-red-200/50 flex items-center gap-2"
          >
            {isDeleting ? (
              <>
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                Deleting...
              </>
            ) : (
              <>
                <FiTrash2 className="w-4 h-4" />
                Delete {assetTypeLabel}
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
