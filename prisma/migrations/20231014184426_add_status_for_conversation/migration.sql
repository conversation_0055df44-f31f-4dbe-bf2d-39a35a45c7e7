/*
  Warnings:

  - The values [FEEDBACK] on the enum `IssueTreeStatus` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
ALTER TYPE "ConversationStatus" ADD VALUE 'FEEDBACK';

-- AlterEnum
BEGIN;
CREATE TYPE "IssueTreeStatus_new" AS ENUM ('INITIALIZED', 'ACTIVE', 'COMPLETED');
ALTER TABLE "issue_trees" ALTER COLUMN "status" DROP DEFAULT;
ALTER TABLE "issue_trees" ALTER COLUMN "status" TYPE "IssueTreeStatus_new" USING ("status"::text::"IssueTreeStatus_new");
ALTER TYPE "IssueTreeStatus" RENAME TO "IssueTreeStatus_old";
ALTER TYPE "IssueTreeStatus_new" RENAME TO "IssueTreeStatus";
DROP TYPE "IssueTreeStatus_old";
ALTER TABLE "issue_trees" ALTER COLUMN "status" SET DEFAULT 'INITIALIZED';
COMMIT;
