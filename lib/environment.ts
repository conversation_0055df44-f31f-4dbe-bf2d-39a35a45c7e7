/**
 * Checks if the current environment is development.
 * Useful for development-only features like verbose logging.
 *
 * @returns {boolean} True if the app is running in development mode.
 */
export const isDevelopment = (): boolean => {
  return process.env.NODE_ENV === 'development'
}

/**
 * Checks if the current environment is a development or CI/testing environment.
 * This is useful for enabling debug features, simulator modes, etc.,
 * without exposing them in a production environment.
 *
 * @returns {boolean} True if the app is running in a development or CI context.
 */
export const isDevelopmentOrCi = (): boolean => {
  // This environment variable is set to 'development' by Next.js in dev mode.
  const isDev = isDevelopment()

  // This is a standard environment variable set by most CI providers, including GitHub Actions.
  const isCi = !!process.env.CI

  return isDev || isCi
}
