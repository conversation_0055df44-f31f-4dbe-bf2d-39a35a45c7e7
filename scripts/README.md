# 🌿 Fully Portable Branch-Based Telegram Notifications

This system organizes Claude hook notifications by git branch using Telegram Forum Groups. Each branch gets its own dedicated topic thread for clean separation of notifications. **Fully portable** - works on any computer with only environment variables configured.

## 🎯 How It Works

- **One Telegram Group** with Forum/Topics enabled
- **One Topic per Branch** - automatically created with format: `REPO:branch:BRANCH_NAME`
- **Dynamic Topic Discovery** - no local state files required
- **Real-time Telegram API Integration** - topics discovered and created on-demand
- **Repository Name in Messages** - each notification shows both repo and branch info
- **100% Portable** - works immediately on any computer with proper `.env` configuration

## 📁 Files

### `notify_telegram.sh`

Main notification script with fully portable branch-based forum topic support:

- **Dynamic topic discovery** - no local files required
- **Automatic topic creation** for new branches using Telegram API
- **Repository name in messages** - shows both repo and branch info
- **Smart operation detection** (Task Completion, Error, Tool Execution, etc.)
- **Real-time Telegram integration** - works immediately on any computer

### `setup_telegram_forum.sh`

Comprehensive setup validation script that:

- **Validates environment configuration** (.env file and variables)
- **Tests Telegram bot connectivity** and permissions
- **Verifies forum group status** and topic creation abilities
- **Validates Claude hook configuration** in settings files
- **Performs end-to-end testing** of the complete notification flow
- **Provides detailed success/failure reporting** with remediation steps

### `manage_topics.sh`

Portable topic management utilities:

- **Dynamic topic discovery** - queries Telegram API in real-time
- **Create topics** for specific branches
- **Test notifications** for any branch
- **Provides topic management guidance** for manual operations

### `commit_topic_cache.sh`

Git workflow helper for team collaboration:

- **Commits topic cache** with descriptive messages
- **Shows current mappings** before committing
- **Provides push instructions** for team sharing
- **Handles git workflow** automatically

## 🚀 Quick Setup

### Step 1: Prepare Telegram Group

1. **Create/open Telegram group**
2. **Enable Topics**: Group Settings → Topics → Enable
3. **Add your bot** as administrator
4. **Set permissions**: Admin Rights → Manage Topics ✅

### Step 2: Get Chat ID

```bash
# Send message in group, then visit:
https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates
# Look for "chat": {"id": -1001234567890}
```

### Step 3: Configure Environment

```bash
# Add to .env file:
TG_TOKEN=your_bot_token_here
TG_CHAT=your_group_chat_id_here
```

### Step 4: Run Comprehensive Setup Validation

```bash
bash ./scripts/setup_telegram_forum.sh
```

This will perform complete system validation:

- ✅ Environment configuration check
- ✅ Telegram bot connectivity test
- ✅ Forum group and permissions verification
- ✅ Claude hook configuration validation
- ✅ End-to-end notification flow test
- ✅ Portability confirmation

## 🔧 Management Commands

```bash
# Show dynamic topic discovery info
bash ./scripts/manage_topics.sh list

# Test current branch notification (creates topic if needed)
bash ./scripts/manage_topics.sh test

# Test specific branch
bash ./scripts/manage_topics.sh test main

# Create topic for specific branch
bash ./scripts/manage_topics.sh create feature/new-ui

# Get topic management guidance
bash ./scripts/manage_topics.sh delete old-branch

# Commit topic cache for team sharing
bash ./scripts/commit_topic_cache.sh
```

## 📱 Expected Result

Your Telegram group will be organized like this:

```
📱 Your Telegram Group (Forum Mode)
├── REPO_NAME:branch:main
│   └── 🏁 Task Completion - `main`
├── REPO_NAME:branch:gh/editable-tree
│   └── 🏁 Task Completion - `gh/editable-tree`
└── REPO_NAME:branch:feature/new-ui
    └── 📋 General Update - `feature/new-ui`
```

## 📋 Hook Configuration

The system is configured in `.claude/settings.local.json`:

```json
{
  "hooks": {
    "Notification": [
      {
        "hooks": [
          {
            "type": "command",
            "command": "bash ./scripts/notify_telegram.sh \"$CLAUDE_NOTIFICATION\"",
            "run_in_background": true
          }
        ]
      }
    ],
    "Stop": [
      {
        "hooks": [
          {
            "type": "command",
            "command": "bash ./scripts/notify_telegram.sh \"✅ Claude finished.\" \"Stop\"",
            "run_in_background": true
          }
        ]
      }
    ]
  }
}
```

## 📝 Message Format

Each notification appears in the correct branch topic with this enhanced format:

```
🏁 Task Completion - `next13-clarify`

📁 Repository: `next13-clarify`
🌿 Branch: `gh/editable-tree`
📝 Message: ✅ Claude finished.
🕒 Time: 13:39:15
🎯 Hook: Stop
```

## 🎯 Operation Context Detection

The system automatically detects operation types:

- 🏁 **Task Completion**: Messages containing "finished"
- 🚀 **Task Started**: Messages containing "started" or "beginning"
- ❌ **Error Occurred**: Messages containing "error" or "failed"
- 🔧 **Tool Execution**: Messages containing "tool"
- 📋 **General Update**: Default for other messages

## 🛠️ Troubleshooting

### "Chat is not a forum group"

- **Solution**: Enable Topics in your Telegram group settings
- Group Settings → Topics → Enable

### "Topic creation failed"

- **Solution**: Check bot admin permissions
- Ensure bot has "Manage Topics" permission enabled

### "No notifications received"

- **Solution**: Verify environment variables in `.env`
- Check `TG_TOKEN` and `TG_CHAT` are correct
- Test with: `bash ./scripts/manage_topics.sh test`

### Topics not being created automatically

- **Solution**: Run setup script
- `bash ./scripts/setup_telegram_forum.sh`
- Verify bot permissions in Telegram group

## � Portability Features

This system is **fully portable** with git-based team collaboration:

- ✅ **Git-Tracked Topic Cache** - `.telegram_topic_cache` synced via git across team
- ✅ **Team Collaboration** - topic mappings shared automatically through git workflow
- ✅ **Persistent Topic IDs** - Telegram topic IDs are permanent and safe to commit
- ✅ **Cache Validation** - automatically validates cached topics still exist
- ✅ **Self-Healing** - removes invalid cache entries automatically
- ✅ **Sequential Workflow** - perfect for normal branch-based development

**Portability Guarantee**: Only requires:

- `.env` file with `TG_TOKEN` and `TG_CHAT`
- `.claude/settings.local.json` with hook configuration
- `.telegram_topic_cache` committed to git (auto-created and managed)

**Git Workflow Integration**:

- ✅ **First developer** creates topic → caches mapping → commits to git
- ✅ **Other developers** pull cache → reuse existing topics automatically
- ✅ **No duplicates** in normal workflow since cache is shared via git
- ✅ **Topic format**: `REPO:branch:BRANCH_NAME` with permanent Telegram IDs
- ✅ **Automatic prompts** to commit new topic mappings for team sharing
