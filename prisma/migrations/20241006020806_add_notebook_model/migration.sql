-- CreateEnum
CREATE TYPE "NotebookStatus" AS ENUM ('INITIALIZED', 'ACTIVE', 'INACTIVE');

-- CreateEnum
CREATE TYPE "NotebookType" AS ENUM ('BASE');

-- AlterEnum
ALTER TYPE "OpenAIUsageType" ADD VALUE 'NOTEBOOK_GENERATE';

-- CreateTable
CREATE TABLE "notebooks" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "conversation_id" TEXT NOT NULL,
    "issue_tree_id" TEXT NOT NULL,
    "status" "NotebookStatus" NOT NULL DEFAULT 'INITIALIZED',
    "type" "NotebookType" NOT NULL DEFAULT 'BASE',
    "title" TEXT NOT NULL DEFAULT 'Untitled Notebook',
    "content" TEXT NOT NULL DEFAULT '',
    "generation_input" TEXT NOT NULL DEFAULT '',
    "generation_output" TEXT NOT NULL DEFAULT '',
    "config" JSONB,
    "remark" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "notebooks_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "notebooks_id_idx" ON "notebooks" USING HASH ("id");

-- CreateIndex
CREATE INDEX "notebooks_conversation_id_idx" ON "notebooks" USING HASH ("conversation_id");

-- CreateIndex
CREATE INDEX "notebooks_user_id_idx" ON "notebooks" USING HASH ("user_id");

-- AddForeignKey
ALTER TABLE "notebooks" ADD CONSTRAINT "notebooks_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "notebooks" ADD CONSTRAINT "notebooks_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "conversations"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "notebooks" ADD CONSTRAINT "notebooks_issue_tree_id_fkey" FOREIGN KEY ("issue_tree_id") REFERENCES "issue_trees"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
