import { AIGenerationStatus } from '@prisma/client'

/**
 * Type for AI generation metadata (without heavy content field)
 * Used for lightweight listing and client-side state management
 */
export type AIGenerationMeta = {
  id: string
  title: string | null
  status: AIGenerationStatus
  version: number
  createdAt: Date
  updatedAt: Date
  config: any // Contains model_name and settings
  metadata: any
}

/**
 * Type for full AI generation content
 * Used when loading complete generation data for editing
 */
export type AIGenerationContent = {
  id: string
  title: string | null
  content: string
  generation_input: string
  generation_output: string
  status: AIGenerationStatus
  version: number
  config: any
  metadata: any
  createdAt: Date
  updatedAt: Date
}

/**
 * Type for AI generation update data
 * Used when updating generation content via API
 */
export type AIGenerationUpdateData = {
  content?: string
  title?: string
  metadata?: Record<string, any>
}

/**
 * Type for optimistic locking update result
 * Returned by update functions to indicate success/failure and new version
 */
export type AIGenerationUpdateResult = {
  success: boolean
  newVersion?: number
  error?: string
}
