'use client'

import React from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-icons/fi'
import { Button } from '@/components/ui/button'
import {
  useAiPaneStore,
  MAX_TOKENS,
} from '@/app/(conv)/dragTree/[dragTreeId]/stores/useAiPaneStore'
import TypeSelection from './TypeSelection'
import ContextSelection from './ContextSelection'
import SettingsSection from './SettingsSection'
// Lazy-load tokenizer to avoid bundling it up-front
let _encode: ((text: string) => number[]) | null = null
async function getEncode() {
  if (_encode) return _encode
  const mod = await import('gpt-tokenizer')
  _encode = mod.encode
  return _encode
}
import { cn, isLocalOrDevEnv } from '@/lib/utils'
import { useSession } from 'next-auth/react'
import { SubscriptionTier } from '@prisma/client'
import { hasPermission } from '@/app/configs/tier-permissions'
import { toast } from 'react-hot-toast'
import { logEventWithContext } from '@/app/libs/logging'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { extractNodeContentText } from '@/app/utils/nodeContent'

type ControlPanelProps = {
  dragTreeId: string
  onStart: () => void
}

const ControlPanel: React.FC<ControlPanelProps> = ({ dragTreeId, onStart }) => {
  const { data: session } = useSession()
  const tier = ((session?.user as any)?.subscription?.tier ||
    (session?.user as any)?.subscription_tier ||
    SubscriptionTier.FREE) as SubscriptionTier

  const isLoading = useAiPaneStore(state => state.isLoading)
  const settings = useAiPaneStore(state => state.settings)
  const nodeContent = useDragTreeStore(state => state.nodeContent)
  const findNodeById = useDragTreeStore(state => state.findNodeById)
  const getNodePath = useDragTreeStore(state => state.getNodePath)

  // Permission checks
  const canCreateAiChat = hasPermission(tier, 'canCreateAiChat')
  const canCreateAiGeneration = hasPermission(tier, 'canCreateAiGeneration')

  // Check if user has selected context and has a prompt
  const hasSelectedContext = settings.context.some(item => item.selected)
  const hasPrompt = settings.prompt.trim().length > 0

  const contextTokens = useAiPaneStore(state => state.contextTokenCount)
  const [promptTokens, setPromptTokens] = React.useState<number>(0)
  React.useEffect(() => {
    let cancelled = false
    ;(async () => {
      try {
        const enc = await getEncode()
        if (!cancelled) setPromptTokens(enc(settings.prompt).length)
      } catch {
        if (!cancelled) setPromptTokens(0)
      }
    })()
    return () => {
      cancelled = true
    }
  }, [settings.prompt])
  const totalTokens = promptTokens + contextTokens
  const exceedsTokenLimit = totalTokens > MAX_TOKENS

  // Check permissions based on type
  const hasTypePermission =
    settings.type === 'chat' ? canCreateAiChat : canCreateAiGeneration

  const canStart =
    hasSelectedContext &&
    hasPrompt &&
    !isLoading &&
    !exceedsTokenLimit &&
    hasTypePermission

  // Get button text based on type
  const getButtonText = () => {
    if (isLoading) return 'Processing...'
    return settings.type === 'generate' ? 'Start Generation' : 'Start Chat'
  }

  // Handle start button click with permission check
  const handleStart = () => {
    if (!hasTypePermission) {
      const action =
        settings.type === 'chat' ? 'start AI chat' : 'start AI generation'
      const tierName =
        tier.charAt(0).toUpperCase() + tier.slice(1).toLowerCase()
      toast.error(
        `${tierName} tier is not allowed to ${action}. Please upgrade your subscription.`
      )
      return
    }

    // Log the AI pane start event
    const eventName =
      settings.type === 'generate'
        ? 'click_aipane_startGenerate'
        : 'click_aipane_startChat'

    logEventWithContext(eventName, session?.user?.id, dragTreeId, {
      prompt_length: settings.prompt.length,
      context_count: settings.context.filter(item => item.selected).length,
      total_tokens: totalTokens,
      settings_type: settings.type,
    })

    onStart()
  }

  // Get disabled reason for tooltip
  const getDisabledReason = () => {
    if (!hasTypePermission) {
      const action =
        settings.type === 'chat' ? 'start AI chat' : 'start AI generation'
      const tierName =
        tier.charAt(0).toUpperCase() + tier.slice(1).toLowerCase()
      return `${tierName} tier is not allowed to ${action}`
    }
    if (exceedsTokenLimit)
      return `Token limit exceeded (${totalTokens} / ${MAX_TOKENS})`
    if (!hasSelectedContext && !hasPrompt)
      return 'Select context and add a prompt to continue'
    if (!hasSelectedContext) return 'Select context items to continue'
    if (!hasPrompt) return 'Add a prompt to continue'
    return ''
  }

  // Localhost-only helper to copy selected context content to clipboard
  const handleCopyContext = async () => {
    try {
      const selected = settings.context.filter(item => item.selected)
      if (selected.length === 0) {
        toast.error('No context items selected')
        return
      }

      const blocks: string[] = []
      selected.forEach(item => {
        const contentMap = nodeContent.get(item.id)
        if (!contentMap || contentMap.size === 0) return
        // Prefer the first available content item
        const contentItems = Array.from(contentMap.values())
        for (const contentItem of contentItems) {
          const text = extractNodeContentText(contentItem)
          if (text && text.trim().length > 0) {
            const path = getNodePath(item.id)
            const nodeLabel =
              findNodeById(item.id)?.label || item.title || item.id
            const header = path && path.trim().length > 0 ? path : nodeLabel
            blocks.push(`${header}\n\n${text}`)
            break
          }
        }
      })

      const joined = blocks.join('\n\n')
      if (!joined) {
        toast.error('Selected items have no loaded content yet')
        return
      }

      await navigator.clipboard.writeText(joined)
      toast.success(
        `Copied ${blocks.length} context item${blocks.length === 1 ? '' : 's'}`
      )
    } catch (err) {
      toast.error('Failed to copy context')
    }
  }

  return (
    <div className="h-full flex flex-col overflow-hidden">
      {/* AI Type Selection - Compact */}
      <div className="px-3 py-2 border-b border-gray-100">
        <TypeSelection />
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Context Selection - Compact */}
        <div className="px-3 py-2 border-b border-gray-100">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="block text-xs font-medium text-gray-700">
                Context
              </label>
              {isLocalOrDevEnv() && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 px-2 text-xs"
                  onClick={handleCopyContext}
                  title="Copy selected context"
                >
                  <FiCopy className="w-3 h-3 mr-1" /> Copy
                </Button>
              )}
            </div>
            <ContextSelection dragTreeId={dragTreeId} />
            {/* Visual feedback when no context selected */}
            {!hasSelectedContext && (
              <div className="mt-2 p-2 bg-amber-50 border border-amber-200 rounded-md">
                <div className="flex items-center space-x-2">
                  <div className="w-1.5 h-1.5 bg-amber-400 rounded-full animate-pulse"></div>
                  <p className="text-xs text-amber-700 font-medium">
                    Select context items to get started
                  </p>
                </div>
                <p className="text-xs text-amber-600 mt-1">
                  Choose research content from your drag tree
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Settings + Chat Area */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <SettingsSection dragTreeId={dragTreeId} />
        </div>
      </div>

      {/* Action Button - Compact */}
      <div className="px-3 py-2 border-t border-gray-100">
        <div className="relative">
          <Button
            onClick={handleStart}
            disabled={!canStart}
            className="w-full"
            size="sm"
            title={!canStart ? getDisabledReason() : undefined}
          >
            {isLoading ? (
              <>
                <FiLoader className="w-3 h-3 mr-2 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <FiPlay className="w-3 h-3 mr-2" />
                {getButtonText()}
                <span
                  className={cn(
                    'mx-1 font-medium',
                    exceedsTokenLimit ? 'text-red-500' : 'text-green-600'
                  )}
                >
                  • {totalTokens} tokens
                </span>
              </>
            )}
          </Button>
          {/* Tooltip for disabled state */}
          {!canStart && !isLoading && (
            <div className="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 hover:opacity-100 transition-opacity whitespace-nowrap pointer-events-none">
              {getDisabledReason()}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default React.memo(ControlPanel)
