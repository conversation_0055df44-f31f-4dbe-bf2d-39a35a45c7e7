/**
 * Brave Search API integration for dragTree research generation
 * Provides web search functionality with metadata collection for frontend display
 *
 * Note: Enhanced features like extra_snippets and reliable favicon access require
 * Brave Search API "Data for AI" plan subscription (Free: 2,000 queries/month)
 * See: https://brave.com/search/api/ for pricing details
 */

export type SearchResult = {
  title: string
  url: string
  description: string
  favicon?: string
  snippet: string
  extra_snippets?: string[] // Additional snippets for AI analysis
  // Enhanced metadata from Brave API
  page_age?: string // Age of the web page
  language?: string // Language classification
}

export type SearchMetadata = {
  keyword: string
  url: string
  icon?: string
  snippets: string[]
  timestamp: string
  source: 'brave_search'
  // Enhanced metadata for better UX
  domain?: string // Extracted domain name
  page_age?: string // Age of the web page
  language?: string // Language classification
  site_description?: string // Clean description without snippets
}

export type BraveSearchResponse = {
  web?: {
    results: Array<{
      title: string
      url: string
      description: string
      favicon?: string
      extra_snippets?: string[]
      // Enhanced fields from Brave API
      page_age?: string
      language?: string
    }>
  }
}

/**
 * Sleep utility for exponential backoff
 */
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * Performs web search using Brave Search API with enhanced snippet collection and exponential backoff
 * @param query - Search query string
 * @param count - Number of results to return (max 20)
 * @returns Promise<SearchResult[]>
 */
export async function braveWebSearch(
  query: string,
  count: number = 10
): Promise<SearchResult[]> {
  const BRAVE_API_KEY = process.env.BRAVE_SEARCH_API_KEY

  if (!BRAVE_API_KEY) {
    console.warn('⚠️ Brave Search API key not found, skipping web search')
    return []
  }

  const MAX_RETRIES = 4

  for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
    try {
      console.log(
        `🔍 [Brave Search] Searching for: "${query}" (${count} results) - Attempt ${attempt}/${MAX_RETRIES}`
      )

      const response = await fetch(
        `https://api.search.brave.com/res/v1/web/search?q=${encodeURIComponent(query)}&count=${count}`,
        {
          headers: {
            Accept: 'application/json',
            'Accept-Encoding': 'gzip',
            'X-Subscription-Token': BRAVE_API_KEY,
          },
        }
      )

      if (response.ok) {
        // Success path - process response
        const contentType = response.headers.get('content-type')
        if (!contentType || !contentType.includes('application/json')) {
          console.error(
            `❌ [Brave Search] Unexpected content type: ${contentType}`
          )
          return []
        }

        const data: BraveSearchResponse = await response.json()

        if (!data.web?.results || data.web.results.length === 0) {
          console.warn(
            `⚠️ [Brave Search] No results found for query: "${query}"`
          )
          return []
        }

        const results: SearchResult[] = data.web.results.map(result => {
          // Generate fallback favicon if Brave doesn't provide one
          let favicon = result.favicon
          if (!favicon) {
            try {
              const domain = new URL(result.url).hostname
              favicon = `https://www.google.com/s2/favicons?domain=${domain}&sz=16`
            } catch {
              favicon = undefined
            }
          }

          return {
            title: result.title,
            url: result.url,
            description: result.description,
            favicon: favicon,
            snippet: result.description,
            extra_snippets: result.extra_snippets || [],
            // Enhanced metadata from Brave API
            page_age: result.page_age,
            language: result.language,
          }
        })

        console.log(
          `✅ [Brave Search] Found ${results.length} results for: "${query}"`
        )

        // Log favicon availability for debugging
        const withFavicons = results.filter(r => r.favicon).length
        console.log(
          `🎨 [Brave Search] ${withFavicons}/${results.length} results have favicons`
        )

        return results
      }

      // Handle rate limiting and other errors
      if (response.status === 429) {
        // Extract Retry-After header if available
        const retryAfterHeader = response.headers.get('Retry-After')
        const retryAfterSeconds = retryAfterHeader
          ? parseInt(retryAfterHeader, 10)
          : null

        if (attempt === MAX_RETRIES) {
          console.error(
            `❌ [Brave Search] Rate limited after ${MAX_RETRIES} attempts for query: "${query}"`
          )
          return []
        }

        // Use Retry-After if provided, otherwise exponential backoff with jitter
        const baseDelay = retryAfterSeconds
          ? retryAfterSeconds * 1000
          : Math.pow(2, attempt) * 1000
        const jitter = Math.random() * 500 // Add up to 500ms jitter
        const delayMs = baseDelay + jitter

        console.warn(
          `⏳ [Brave Search] Rate limited (429). Retrying in ${Math.round(delayMs)}ms (attempt ${attempt}/${MAX_RETRIES})`
        )

        await sleep(delayMs)
        continue
      }

      // Handle other HTTP errors
      console.error(
        `❌ [Brave Search] API error: ${response.status} ${response.statusText} (attempt ${attempt}/${MAX_RETRIES})`
      )

      if (attempt === MAX_RETRIES) {
        console.error(
          `❌ [Brave Search] Failed after ${MAX_RETRIES} attempts for query: "${query}"`
        )
        return []
      }

      // Exponential backoff for other errors
      const delayMs = Math.pow(2, attempt) * 1000 + Math.random() * 500
      console.warn(
        `⏳ [Brave Search] Retrying in ${Math.round(delayMs)}ms due to ${response.status} error`
      )
      await sleep(delayMs)
    } catch (error) {
      console.error(
        `💥 [Brave Search] Network error on attempt ${attempt}/${MAX_RETRIES}:`,
        error
      )

      if (attempt === MAX_RETRIES) {
        console.error(
          `❌ [Brave Search] Network failed after ${MAX_RETRIES} attempts for query: "${query}"`
        )
        return []
      }

      // Exponential backoff for network errors
      const delayMs = Math.pow(2, attempt) * 1000 + Math.random() * 500
      console.warn(
        `⏳ [Brave Search] Retrying in ${Math.round(delayMs)}ms due to network error`
      )
      await sleep(delayMs)
    }
  }

  // This should never be reached due to the attempt === MAX_RETRIES checks above
  console.error(
    `❌ [Brave Search] Unexpected: Exhausted all retries for query: "${query}"`
  )
  return []
}

/**
 * Creates search metadata for storage in content_metadata with enhanced snippet collection
 * @param query - Original search query
 * @param results - Search results from Brave API
 * @returns SearchMetadata[]
 */
export function createSearchMetadata(
  query: string,
  results: SearchResult[]
): SearchMetadata[] {
  return results.map(result => {
    /*
     * We want **human-readable descriptions** to survive later payload-trimming.
     * Put the official `description` first, then the Brave-generated snippet(s),
     * then any extra_snippets.  This way when we later keep only the first
     * element we always retain the natural description that you'd see on a
     * search-engine results page.
     */

    const allSnippets = [
      result.description, // canonical description first
      // Deduplicate in case description === snippet
      result.snippet !== result.description ? result.snippet : null,
      ...(result.extra_snippets || []),
    ].filter((s): s is string => s !== null && s.trim().length > 0)

    // Remove duplicates
    const uniqueSnippets = Array.from(new Set(allSnippets))

    // Extract domain name for better UX
    let domain: string | undefined
    try {
      domain = new URL(result.url).hostname.replace('www.', '')
    } catch {
      domain = undefined
    }

    return {
      keyword: query,
      url: result.url,
      icon: result.favicon, // Now properly populated from API
      snippets: uniqueSnippets, // Multiple snippets for richer context
      timestamp: new Date().toISOString(),
      source: 'brave_search' as const,
      // Enhanced metadata for better UX
      domain: domain,
      page_age: result.page_age,
      language: result.language,
      site_description: result.description, // Clean description separate from snippets
    }
  })
}

/**
 * Formats search results for AI model consumption with enhanced snippet utilization
 * @param results - Search results from Brave API
 * @returns Formatted string for AI prompt
 */
export function formatSearchResultsForAI(results: SearchResult[]): string {
  if (results.length === 0) {
    return 'No web search results available.'
  }

  return results
    .map((result, index) => {
      // Use the best available snippet for AI consumption
      const bestSnippet =
        result.extra_snippets && result.extra_snippets.length > 0
          ? result.extra_snippets[0] // Use first extra snippet if available
          : result.description

      return `${index + 1}. **${result.title}**
   URL: ${result.url}
   Content: ${bestSnippet}

`
    })
    .join('')
}
