'use server'

import { auth } from '@/auth'
import prisma from '@/app/libs/prismadb'

/**
 * Lightweight tree structure endpoint - fetches only essential data for initial rendering
 * Optimized for minimal payload size and fast initial page loads
 *
 * Performance Target: 50-70% reduction in payload size vs full getDragTree()
 */
export async function getDragTreeStructure(treeId: string) {
  const startTime = performance.now()

  try {
    const session = await auth()
    if (!session?.user?.id) {
      return {
        success: false as const,
        error: 'Unauthorized',
        metrics: { queryTime: 0, payloadSize: 0 },
      }
    }

    console.log(
      `🔍 [getDragTreeStructure] Fetching lightweight tree structure for: ${treeId}`
    )

    // Fetch only essential fields for tree rendering
    const dragTree = await prisma.dragTree.findUnique({
      where: {
        id: treeId,
        user_id: session.user.id, // Ensure user owns this tree
      },
      select: {
        id: true,
        title: true,
        user_prompt: true,
        status: true,
        tree_structure: true,
        metadata: true,
        created_at: true,
        updated_at: true,
        // Only essential node fields - no heavy content
        nodes: {
          select: {
            id: true,
            label: true,
            node_type: true,
            status: true,
            is_interested_in: true,
            ui_state: true,
            version: true,
            created_at: true,
            updated_at: true,
            // Explicitly exclude heavy fields
            // content_items: false,
            // metadata: false (if it becomes heavy)
          },
          where: {
            status: 'ACTIVE', // Only include active nodes
          },
          orderBy: {
            created_at: 'asc', // Consistent ordering
          },
        },
        // Include user metadata for tutorial state
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            metadata: true,
          },
        },
      },
    })

    const queryTime = performance.now() - startTime

    if (!dragTree) {
      console.log(
        `❌ [getDragTreeStructure] Tree not found or unauthorized: ${treeId}`
      )
      return {
        success: false as const,
        error: 'Drag tree not found or unauthorized',
        metrics: { queryTime, payloadSize: 0 },
      }
    }

    // Calculate payload size for monitoring
    const payloadSize = JSON.stringify(dragTree).length

    console.log(`✅ [getDragTreeStructure] Retrieved tree structure:`, {
      treeId: dragTree.id,
      nodeCount: dragTree.nodes.length,
      queryTime: `${queryTime.toFixed(2)}ms`,
      payloadSize: `${(payloadSize / 1024).toFixed(2)}KB`,
      activeNodesOnly: true,
    })

    return {
      success: true as const,
      data: dragTree,
      userMetadata: dragTree.user?.metadata || {},
      metrics: {
        queryTime,
        payloadSize,
        nodeCount: dragTree.nodes.length,
        compressionRatio: 0, // Will be calculated by comparing with full payload
      },
    }
  } catch (error) {
    const queryTime = performance.now() - startTime
    console.error('❌ [getDragTreeStructure] Database error:', error)

    return {
      success: false as const,
      error: error instanceof Error ? error.message : 'Database error',
      metrics: { queryTime, payloadSize: 0 },
    }
  }
}

/**
 * Get node content on-demand when user interacts with specific nodes
 * Implements lazy loading pattern for heavy content data
 */
export async function getNodeContentOnDemand(nodeId: string) {
  const startTime = performance.now()

  try {
    const session = await auth()
    if (!session?.user?.id) {
      return {
        success: false as const,
        error: 'Unauthorized',
        metrics: { queryTime: 0, payloadSize: 0 },
      }
    }

    console.log(
      `🔍 [getNodeContentOnDemand] Fetching content for node: ${nodeId}`
    )

    // Fetch node with its content items
    const nodeWithContent = await prisma.dragTreeNode.findFirst({
      where: {
        id: nodeId,
        drag_tree: {
          user_id: session.user.id, // Ensure user owns this tree
        },
        status: 'ACTIVE',
      },
      select: {
        id: true,
        label: true,
        node_type: true,
        metadata: true,
        content_items: {
          select: {
            id: true,
            status: true,
            content_type: true,
            content_version: true,
            content_text: true,
            content_metadata: true,
            messages: true,
            updated_at: true,
          },
          where: {
            status: {
              not: 'INACTIVE',
            },
          },
          orderBy: {
            updated_at: 'desc',
          },
        },
      },
    })

    const queryTime = performance.now() - startTime

    if (!nodeWithContent) {
      console.log(
        `❌ [getNodeContentOnDemand] Node not found or unauthorized: ${nodeId}`
      )
      return {
        success: false as const,
        error: 'Node not found or unauthorized',
        metrics: { queryTime, payloadSize: 0 },
      }
    }

    const payloadSize = JSON.stringify(nodeWithContent).length

    console.log(`✅ [getNodeContentOnDemand] Retrieved node content:`, {
      nodeId: nodeWithContent.id,
      contentItemCount: nodeWithContent.content_items.length,
      queryTime: `${queryTime.toFixed(2)}ms`,
      payloadSize: `${(payloadSize / 1024).toFixed(2)}KB`,
    })

    return {
      success: true as const,
      data: nodeWithContent,
      metrics: {
        queryTime,
        payloadSize,
        contentItemCount: nodeWithContent.content_items.length,
      },
    }
  } catch (error) {
    const queryTime = performance.now() - startTime
    console.error('❌ [getNodeContentOnDemand] Database error:', error)

    return {
      success: false as const,
      error: error instanceof Error ? error.message : 'Database error',
      metrics: { queryTime, payloadSize: 0 },
    }
  }
}

/**
 * Performance comparison utility - measures payload size difference
 * between lightweight and full tree loading
 */
export async function comparePayloadSizes(treeId: string) {
  const [lightweightResult, fullResult] = await Promise.all([
    getDragTreeStructure(treeId),
    // Import and call the full getDragTree for comparison
    import('./index').then(module =>
      module.getDragTree(treeId, { includeContentItems: true })
    ),
  ])

  if (lightweightResult.success && fullResult.success) {
    const lightweightSize = lightweightResult.metrics.payloadSize
    const fullSize = JSON.stringify(fullResult.data).length
    const reduction = ((fullSize - lightweightSize) / fullSize) * 100

    console.log(`📊 [Payload Comparison] Tree: ${treeId}`, {
      lightweightSize: `${(lightweightSize / 1024).toFixed(2)}KB`,
      fullSize: `${(fullSize / 1024).toFixed(2)}KB`,
      reduction: `${reduction.toFixed(1)}%`,
      compressionRatio: `${(fullSize / lightweightSize).toFixed(2)}x`,
    })

    return {
      lightweightSize,
      fullSize,
      reduction,
      compressionRatio: fullSize / lightweightSize,
    }
  }

  return null
}
