import { useState, useCallback, useRef } from 'react'

// Loading state types that can be used across components
export type LoadingState = 'idle' | 'loading' | 'success' | 'error'

// Configuration for different loading scenarios
type LoadingConfig = {
  // Default timeout for auto-reset (in ms)
  autoResetTimeout?: number
  // Whether to auto-reset to idle after success/error
  autoReset?: boolean
  // Success message to display
  successMessage?: string
  // Error message to display
  errorMessage?: string
}

// Return type for the hook
type UseLoadingStateReturn = {
  // Current loading state
  loadingState: LoadingState
  // Whether currently loading
  isLoading: boolean
  // Whether in success state
  isSuccess: boolean
  // Whether in error state
  isError: boolean
  // Set loading state directly
  setLoadingState: (state: LoadingState) => void
  // Start loading
  startLoading: () => void
  // Mark as success
  markSuccess: (message?: string) => void
  // Mark as error
  markError: (message?: string) => void
  // Reset to idle state
  resetState: () => void
  // Current message (success/error)
  currentMessage?: string
}

/**
 * useLoadingState - Custom hook for managing loading states across components
 *
 * Features:
 * - Unified loading state management (idle, loading, success, error)
 * - Auto-reset functionality for success/error states
 * - Message management for success/error feedback
 * - Convenient boolean flags for state checking
 * - Callback-based state transitions
 *
 * @param config - Configuration options for loading behavior
 * @returns Loading state management object
 */
export const useLoadingState = (
  config: LoadingConfig = {}
): UseLoadingStateReturn => {
  const {
    autoResetTimeout = 3000,
    autoReset = true,
    successMessage = 'Operation completed successfully',
    errorMessage = 'An error occurred',
  } = config

  const [loadingState, setLoadingState] = useState<LoadingState>('idle')
  const [currentMessage, setCurrentMessage] = useState<string | undefined>(
    undefined
  )

  // Auto-reset timer ref - Fixed: using useRef instead of useState
  const resetTimer = useRef<NodeJS.Timeout | null>(null)

  // Clear existing timer
  const clearResetTimer = useCallback(() => {
    if (resetTimer.current) {
      clearTimeout(resetTimer.current)
      resetTimer.current = null
    }
  }, [])

  // Start auto-reset timer
  const startAutoReset = useCallback(() => {
    if (autoReset && autoResetTimeout > 0) {
      clearResetTimer()
      resetTimer.current = setTimeout(() => {
        setLoadingState('idle')
        setCurrentMessage(undefined)
      }, autoResetTimeout)
    }
  }, [autoReset, autoResetTimeout, clearResetTimer])

  // Computed boolean flags
  const isLoading = loadingState === 'loading'
  const isSuccess = loadingState === 'success'
  const isError = loadingState === 'error'

  // Start loading
  const startLoading = useCallback(() => {
    clearResetTimer()
    setLoadingState('loading')
    setCurrentMessage(undefined)
  }, [clearResetTimer])

  // Mark as success
  const markSuccess = useCallback(
    (message?: string) => {
      clearResetTimer()
      setLoadingState('success')
      setCurrentMessage(message || successMessage)
      startAutoReset()
    },
    [clearResetTimer, successMessage, startAutoReset]
  )

  // Mark as error
  const markError = useCallback(
    (message?: string) => {
      clearResetTimer()
      setLoadingState('error')
      setCurrentMessage(message || errorMessage)
      startAutoReset()
    },
    [clearResetTimer, errorMessage, startAutoReset]
  )

  // Reset to idle state
  const resetState = useCallback(() => {
    clearResetTimer()
    setLoadingState('idle')
    setCurrentMessage(undefined)
  }, [clearResetTimer])

  return {
    loadingState,
    isLoading,
    isSuccess,
    isError,
    setLoadingState,
    startLoading,
    markSuccess,
    markError,
    resetState,
    currentMessage,
  }
}

// Specialized hook for node-based loading (common in tree operations)
type NodeLoadingState = {
  loadingNodeId: string | null
  nodeLoadingStates: Record<string, LoadingState>
}

type UseNodeLoadingReturn = NodeLoadingState & {
  setNodeLoading: (nodeId: string) => void
  setNodeSuccess: (nodeId: string, message?: string) => void
  setNodeError: (nodeId: string, message?: string) => void
  resetNodeState: (nodeId: string) => void
  isNodeLoading: (nodeId: string) => boolean
  getNodeState: (nodeId: string) => LoadingState
  getCurrentLoadingNodeId: () => string | null
}

/**
 * useNodeLoadingState - Specialized hook for managing loading states of multiple nodes
 * Useful for tree operations where individual nodes can have different loading states
 */
export const useNodeLoadingState = (): UseNodeLoadingReturn => {
  const [loadingNodeId, setLoadingNodeId] = useState<string | null>(null)
  const [nodeLoadingStates, setNodeLoadingStates] = useState<
    Record<string, LoadingState>
  >({})

  const setNodeLoading = useCallback((nodeId: string) => {
    setLoadingNodeId(nodeId)
    setNodeLoadingStates(prev => ({ ...prev, [nodeId]: 'loading' }))
  }, [])

  const setNodeSuccess = useCallback((nodeId: string, _message?: string) => {
    setLoadingNodeId(null)
    setNodeLoadingStates(prev => ({ ...prev, [nodeId]: 'success' }))
    // Auto-reset after 2 seconds
    setTimeout(() => {
      setNodeLoadingStates(prev => ({ ...prev, [nodeId]: 'idle' }))
    }, 2000)
  }, [])

  const setNodeError = useCallback((nodeId: string, _message?: string) => {
    setLoadingNodeId(null)
    setNodeLoadingStates(prev => ({ ...prev, [nodeId]: 'error' }))
    // Auto-reset after 3 seconds
    setTimeout(() => {
      setNodeLoadingStates(prev => ({ ...prev, [nodeId]: 'idle' }))
    }, 3000)
  }, [])

  const resetNodeState = useCallback(
    (nodeId: string) => {
      setNodeLoadingStates(prev => ({ ...prev, [nodeId]: 'idle' }))
      if (loadingNodeId === nodeId) {
        setLoadingNodeId(null)
      }
    },
    [loadingNodeId]
  )

  const isNodeLoading = useCallback(
    (nodeId: string): boolean => {
      return nodeLoadingStates[nodeId] === 'loading'
    },
    [nodeLoadingStates]
  )

  const getNodeState = useCallback(
    (nodeId: string): LoadingState => {
      return nodeLoadingStates[nodeId] || 'idle'
    },
    [nodeLoadingStates]
  )

  const getCurrentLoadingNodeId = useCallback((): string | null => {
    return loadingNodeId
  }, [loadingNodeId])

  return {
    loadingNodeId,
    nodeLoadingStates,
    setNodeLoading,
    setNodeSuccess,
    setNodeError,
    resetNodeState,
    isNodeLoading,
    getNodeState,
    getCurrentLoadingNodeId,
  }
}
