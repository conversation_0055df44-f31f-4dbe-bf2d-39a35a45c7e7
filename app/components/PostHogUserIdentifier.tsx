'use client'

import { useEffect, useRef } from 'react'
import { useSession } from 'next-auth/react'
import { identifyUser } from '@/app/libs/posthog'

/**
 * Minimal PostHog user identification.
 * Uses NextAuth's useSession to get user.id.
 * Sends only user id and status (optional).
 */
export default function PostHogUserIdentifier() {
  const { data: session, status } = useSession()
  const identifiedRef = useRef(false)

  useEffect(() => {
    if (
      !identifiedRef.current &&
      status === 'authenticated' &&
      session?.user?.id
    ) {
      identifyUser(session.user.id, {
        user_status: session.user.status,
      })
      identifiedRef.current = true
    }
  }, [session, status])

  return null
}
