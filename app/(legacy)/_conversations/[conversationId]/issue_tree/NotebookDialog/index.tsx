import { useState } from 'react'
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog'
import { ScrollArea } from '@/components/ui/scroll-area'
import { NotebookGrid } from './NotebookGrid'
import { CreateNotebookView } from './CreateNotebookView'
import useNotebookStore from '@/app/stores/_legacy/notebook_store'
import FeedbackCollection from '@/app/(legacy)/components/feedback_collection'
import { useParams } from 'next/navigation'
import { notebookCountLimit } from '@/app/configs'
import mixpanel from '@/app/libs/mixpanel'

export default function NotebookDialog() {
  const { conversationId } = useParams()
  const [isCreatingNotebook, setIsCreatingNotebook] = useState<boolean>(false)
  const notebookStore = useNotebookStore()

  const handleNewNotebookClick = () => {
    mixpanel.track('click_new_notebook', {
      location: 'notebook_dialog',
    })
    setIsCreatingNotebook(true)
  }

  const handleCancelCreation = () => {
    setIsCreatingNotebook(false)
  }

  return (
    <Dialog
      open={notebookStore.showDialog}
      onOpenChange={notebookStore.setShowDialog}
    >
      <DialogContent className="sm:max-w-[875px]">
        {notebookStore.showFeedbackCollection ? (
          <FeedbackCollection
            conversationId={conversationId as string}
            setShowFeedback={notebookStore.setShowFeedbackCollection}
          />
        ) : (
          <>
            <DialogTitle>
              {isCreatingNotebook ? 'Create New Notebook' : 'Notebooks'}
            </DialogTitle>
            <DialogDescription>
              {isCreatingNotebook ? '' : 'View and manage your notebooks'}
            </DialogDescription>
            <ScrollArea className="h-[600px] pr-4">
              {isCreatingNotebook ? (
                <CreateNotebookView onCancel={handleCancelCreation} />
              ) : (
                <NotebookGrid
                  onNewNotebookClick={handleNewNotebookClick}
                  notebookCountLimit={notebookCountLimit}
                />
              )}
            </ScrollArea>
          </>
        )}
      </DialogContent>
    </Dialog>
  )
}
