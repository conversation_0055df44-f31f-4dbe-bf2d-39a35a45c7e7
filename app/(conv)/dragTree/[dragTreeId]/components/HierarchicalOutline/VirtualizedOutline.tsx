'use client'

import React, { useMemo, useCallback, useRef } from 'react'
import AutoSizer from 'react-virtualized-auto-sizer'
import { VariableSizeList as List } from 'react-window'
import { TreeNode } from '@/app/types'
import OutlineNode from '@/app/(conv)/dragTree/[dragTreeId]/components/HierarchicalOutline/components/OutlineNode'
import type { TreeNodeType } from '@/app/(conv)/dragTree/[dragTreeId]/constants/node-types'

export type VirtualizedOutlineProps = {
  root: TreeNode | null
  collapsedNodes: Set<string>
  setCollapsedNodes: React.Dispatch<React.SetStateAction<Set<string>>>
  // CRUD
  onAdd: (nodeId: string, type: TreeNodeType) => void
  onDelete: (nodeId: string, nodeType: string) => void
  onEdit: (nodeId: string, newLabel: string) => void
  onReorder: (parentId: string, oldIndex: number, newIndex: number) => void
  // Misc state
  editingNode: string | null
  setEditingNode: React.Dispatch<React.SetStateAction<string | null>>
  targetNodeId?: string
  onGenerateSimilarQuestions?: (nodeId: string, guidance: string) => void
  onGenerateSimilarCategories?: (nodeId: string, guidance: string) => void
  isLoading?: boolean
  loadingNodeId?: string
  isGloballyExpanded?: boolean
}

/** Flatten the visible part of the tree given collapsed state. */
const useFlattenedTree = (
  root: TreeNode | null,
  collapsedNodes: Set<string>
) => {
  return useMemo(() => {
    if (!root) return [] as { node: TreeNode; depth: number }[]

    const result: { node: TreeNode; depth: number }[] = []

    const traverse = (node: TreeNode, depth: number) => {
      result.push({ node, depth })
      const isCollapsed = collapsedNodes.has(node.id)
      if (node.type === 'category' && !isCollapsed) {
        node.children.forEach(child => traverse(child, depth + 1))
      }
    }

    traverse(root, 0)
    return result
  }, [root, collapsedNodes])
}

// Adjusted height to better fit current NodeCard layout and avoid visual overlap
const ROW_HEIGHT = 72

const VirtualizedOutline: React.FC<VirtualizedOutlineProps> = props => {
  const {
    root,
    collapsedNodes,
    setCollapsedNodes,
    onAdd,
    onDelete,
    onEdit,
    onReorder,
    editingNode,
    setEditingNode,
    targetNodeId,
    onGenerateSimilarQuestions,
    onGenerateSimilarCategories,
    isLoading,
    loadingNodeId,
    isGloballyExpanded,
  } = props

  const items = useFlattenedTree(root, collapsedNodes)

  // Cache sizes if we need VariableSizeList; currently fixed height
  const listRef = useRef<List>(null)

  const getItemSize = useCallback(() => ROW_HEIGHT, [])

  const Row = useCallback(
    ({ index, style }: { index: number; style: React.CSSProperties }) => {
      const { node, depth } = items[index]

      // basic indent via paddingLeft proportional to depth
      const indent = depth * 16

      return (
        <div style={{ ...style, paddingLeft: indent }}>
          <OutlineNode
            node={node}
            // CRUD ops
            onAdd={onAdd}
            onDelete={onDelete}
            onEdit={onEdit}
            onReorder={onReorder}
            editingNode={editingNode}
            setEditingNode={setEditingNode}
            collapsedNodes={collapsedNodes}
            setCollapsedNodes={setCollapsedNodes}
            targetNodeId={targetNodeId}
            onGenerateSimilarQuestions={onGenerateSimilarQuestions}
            onGenerateSimilarCategories={onGenerateSimilarCategories}
            isLoading={isLoading}
            loadingNodeId={loadingNodeId}
            isGloballyExpanded={isGloballyExpanded}
            // Interaction props – simple defaults
            isClicked={false}
            isHovered={false}
            isExpanding={false}
            lastExpansionTime={0}
            lastClickTime={0}
            isPersistent={false}
            handleNodeClick={() => {}}
            handleToggleCollapse={() => {
              setCollapsedNodes(prev => {
                const newSet = new Set(prev)
                if (newSet.has(node.id)) {
                  newSet.delete(node.id)
                } else {
                  newSet.add(node.id)
                }
                return newSet
              })
              // We could reset list layout if row heights change
              listRef.current?.resetAfterIndex(index)
            }}
            // Drag props not supported in virtual list (yet)
            dragAttributes={undefined}
            dragListeners={undefined}
            isDragging={false}
          />
        </div>
      )
    },
    [
      items,
      onAdd,
      onDelete,
      onEdit,
      onReorder,
      editingNode,
      setEditingNode,
      collapsedNodes,
      setCollapsedNodes,
      targetNodeId,
      onGenerateSimilarQuestions,
      onGenerateSimilarCategories,
      isLoading,
      loadingNodeId,
      isGloballyExpanded,
    ]
  )

  if (!root) {
    return <div className="p-4 text-sm text-gray-500">No data</div>
  }

  return (
    <AutoSizer>
      {({ height, width }) => (
        <List
          ref={listRef}
          height={height}
          width={width}
          itemCount={items.length}
          itemSize={getItemSize}
          overscanCount={6}
        >
          {Row}
        </List>
      )}
    </AutoSizer>
  )
}

export default VirtualizedOutline
