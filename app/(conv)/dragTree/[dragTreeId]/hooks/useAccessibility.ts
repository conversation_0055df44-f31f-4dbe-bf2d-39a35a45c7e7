/**
 * Accessibility utilities and hooks for the dragtree interface
 * Provides keyboard navigation, focus management, and screen reader support
 */

import { useEffect, useRef, useCallback } from 'react'
import { useNavigationStore } from '@/app/stores/navigation_store'

type AccessibilityOptions = {
  enableKeyboardNavigation?: boolean
  enableFocusManagement?: boolean
  announceNavigationChanges?: boolean
}

/**
 * Hook for managing keyboard navigation between panes
 */
export const useKeyboardNavigation = (options: AccessibilityOptions = {}) => {
  const { enableKeyboardNavigation = true, announceNavigationChanges = true } =
    options

  const { navigateToNode, navigateToTreeNodeFromReactFlow } =
    useNavigationStore()

  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (!enableKeyboardNavigation) return

      // Only handle navigation keys when not in input fields
      const target = event.target as HTMLElement
      if (
        target.tagName === 'INPUT' ||
        target.tagName === 'TEXTAREA' ||
        target.contentEditable === 'true'
      ) {
        return
      }

      switch (event.key) {
        case 'Tab':
          // Let default tab behavior work, but announce focus changes
          if (announceNavigationChanges) {
            setTimeout(() => {
              const focusedElement = document.activeElement as HTMLElement
              if (focusedElement?.dataset?.nodeId) {
                announceToScreenReader(
                  `Focused on ${focusedElement.textContent || 'node'}`
                )
              }
            }, 100)
          }
          break

        case 'Enter':
        case ' ':
          // Activate focused node
          const focusedElement = document.activeElement as HTMLElement
          if (focusedElement?.dataset?.nodeId) {
            event.preventDefault()
            const nodeId = focusedElement.dataset.nodeId
            const isInVisualization = focusedElement.closest(
              '[data-pane="visualization"]'
            )

            if (isInVisualization) {
              navigateToTreeNodeFromReactFlow(nodeId)
            } else {
              navigateToNode(nodeId)
            }

            if (announceNavigationChanges) {
              announceToScreenReader(
                `Navigated to ${focusedElement.textContent || 'node'}`
              )
            }
          }
          break

        case 'ArrowUp':
        case 'ArrowDown':
          // Navigate between nodes in the same pane
          event.preventDefault()
          navigateWithinPane(event.key === 'ArrowUp' ? 'previous' : 'next')
          break

        case 'ArrowLeft':
        case 'ArrowRight':
          // Navigate between panes
          event.preventDefault()
          navigateBetweenPanes(
            event.key === 'ArrowLeft' ? 'outline' : 'visualization'
          )
          break

        case 'Home':
          // Go to first node
          event.preventDefault()
          focusFirstNode()
          break

        case 'End':
          // Go to last node
          event.preventDefault()
          focusLastNode()
          break
      }
    },
    [
      enableKeyboardNavigation,
      announceNavigationChanges,
      navigateToNode,
      navigateToTreeNodeFromReactFlow,
    ]
  )

  useEffect(() => {
    if (enableKeyboardNavigation) {
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
    // Return empty cleanup function when navigation is disabled
    return () => {}
  }, [handleKeyDown, enableKeyboardNavigation])

  return {
    handleKeyDown,
  }
}

/**
 * Hook for managing focus between panes
 */
export const useFocusManagement = () => {
  const lastFocusedElement = useRef<HTMLElement | null>(null)

  const saveFocus = useCallback(() => {
    lastFocusedElement.current = document.activeElement as HTMLElement
  }, [])

  const restoreFocus = useCallback(() => {
    if (
      lastFocusedElement.current &&
      document.contains(lastFocusedElement.current)
    ) {
      lastFocusedElement.current.focus()
    }
  }, [])

  const focusPane = useCallback((paneType: 'outline' | 'visualization') => {
    const pane = document.querySelector(`[data-pane="${paneType}"]`)
    const firstFocusableElement = pane?.querySelector(
      '[tabindex="0"], button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    ) as HTMLElement

    if (firstFocusableElement) {
      firstFocusableElement.focus()
      announceToScreenReader(`Focused on ${paneType} pane`)
    }
  }, [])

  return {
    saveFocus,
    restoreFocus,
    focusPane,
  }
}

/**
 * Utility functions for accessibility
 */

/**
 * Announce text to screen readers
 */
export const announceToScreenReader = (message: string) => {
  const announcement = document.createElement('div')
  announcement.setAttribute('aria-live', 'polite')
  announcement.setAttribute('aria-atomic', 'true')
  announcement.setAttribute('class', 'sr-only')
  announcement.textContent = message

  document.body.appendChild(announcement)

  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(announcement)
  }, 1000)
}

/**
 * Navigate within the current pane
 */
const navigateWithinPane = (direction: 'previous' | 'next') => {
  const focusedElement = document.activeElement as HTMLElement
  const currentPane = focusedElement.closest('[data-pane]')

  if (!currentPane) return

  const focusableElements = Array.from(
    currentPane.querySelectorAll('[data-node-id]')
  ) as HTMLElement[]

  const currentIndex = focusableElements.indexOf(focusedElement)
  if (currentIndex === -1) return

  const nextIndex =
    direction === 'next'
      ? Math.min(currentIndex + 1, focusableElements.length - 1)
      : Math.max(currentIndex - 1, 0)

  const nextElement = focusableElements[nextIndex]
  if (nextElement) {
    nextElement.focus()
    announceToScreenReader(
      `${direction === 'next' ? 'Next' : 'Previous'} node: ${nextElement.textContent || 'node'}`
    )
  }
}

/**
 * Navigate between panes
 */
const navigateBetweenPanes = (targetPane: 'outline' | 'visualization') => {
  const pane = document.querySelector(`[data-pane="${targetPane}"]`)
  const firstNode = pane?.querySelector('[data-node-id]') as HTMLElement

  if (firstNode) {
    firstNode.focus()
    announceToScreenReader(`Switched to ${targetPane} pane`)
  }
}

/**
 * Focus the first node in the current pane
 */
const focusFirstNode = () => {
  const focusedElement = document.activeElement as HTMLElement
  const currentPane = focusedElement.closest('[data-pane]')
  const firstNode = currentPane?.querySelector('[data-node-id]') as HTMLElement

  if (firstNode) {
    firstNode.focus()
    announceToScreenReader(`First node: ${firstNode.textContent || 'node'}`)
  }
}

/**
 * Focus the last node in the current pane
 */
const focusLastNode = () => {
  const focusedElement = document.activeElement as HTMLElement
  const currentPane = focusedElement.closest('[data-pane]')
  const nodes = Array.from(
    currentPane?.querySelectorAll('[data-node-id]') || []
  ) as HTMLElement[]
  const lastNode = nodes[nodes.length - 1]

  if (lastNode) {
    lastNode.focus()
    announceToScreenReader(`Last node: ${lastNode.textContent || 'node'}`)
  }
}

/**
 * Get ARIA attributes for drag and drop operations
 */
export const getDragDropAriaAttributes = (
  isDragging: boolean,
  isDropTarget: boolean,
  nodeLabel: string
) => ({
  'aria-grabbed': isDragging,
  'aria-dropeffect': isDropTarget ? 'move' : 'none',
  'aria-label': `${nodeLabel}${isDragging ? ' (dragging)' : ''}${isDropTarget ? ' (drop target)' : ''}`,
  role: 'button',
  tabIndex: 0,
})

/**
 * Get ARIA attributes for tree nodes
 */
export const getTreeNodeAriaAttributes = (
  nodeType: 'category' | 'question',
  isExpanded: boolean | null,
  level: number,
  positionInSet: number,
  setSize: number,
  nodeLabel: string
) => {
  const attributes: {
    role: string
    'aria-expanded'?: boolean
    'aria-level': number
    'aria-posinset': number
    'aria-setsize': number
    'aria-label': string
    tabIndex: number
  } = {
    role: 'treeitem',
    'aria-level': level,
    'aria-posinset': positionInSet,
    'aria-setsize': setSize,
    'aria-label': `${nodeType === 'category' ? 'Category' : 'Question'}: ${nodeLabel}${
      nodeType === 'category'
        ? isExpanded
          ? ' (expanded)'
          : ' (collapsed)'
        : ''
    }`,
    tabIndex: 0,
  }

  // Only add aria-expanded for categories with a valid boolean value
  if (nodeType === 'category' && typeof isExpanded === 'boolean') {
    attributes['aria-expanded'] = isExpanded
  }

  return attributes
}
