/* eslint-disable react/display-name */
'use client'

import React, { useEffect, useRef, useCallback, useState } from 'react'
import { cn } from '@/lib/utils'
import { FiLoader, FiArrowUp } from 'react-icons/fi'

type InfiniteScrollContainerProps = {
  children: React.ReactNode
  hasMore: boolean
  isLoading: boolean
  onLoadMore: () => Promise<void>
  className?: string
  // Scroll behavior options
  threshold?: number // Pixels from top to trigger load more
  autoScrollToBottom?: boolean
  showLoadMoreButton?: boolean
  onScroll?: (scrollInfo: {
    scrollTop: number
    scrollHeight: number
    clientHeight: number
    isAtTop: boolean
    isAtBottom: boolean
  }) => void
}

export const InfiniteScrollContainer: React.FC<InfiniteScrollContainerProps> =
  React.memo(
    ({
      children,
      hasMore,
      isLoading,
      onLoadMore,
      className,
      threshold = 100,
      autoScrollToBottom = true,
      showLoadMoreButton = true,
      onScroll,
    }) => {
      const containerRef = useRef<HTMLDivElement>(null)
      const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false)
      const [showScrollToTop, setShowScrollToTop] = useState<boolean>(false)
      const [shouldMaintainScrollPosition, setShouldMaintainScrollPosition] =
        useState<boolean>(false)
      const previousScrollHeight = useRef<number>(0)
      const isInitialLoad = useRef<boolean>(true)

      // Handle scroll events
      const handleScroll = useCallback(() => {
        const container = containerRef.current
        if (!container) return

        const { scrollTop, scrollHeight, clientHeight } = container
        const isAtTop = scrollTop <= threshold
        const isAtBottom = scrollHeight - scrollTop - clientHeight <= 10

        // Update scroll indicators
        setShowScrollToTop(scrollTop > 200)

        // Trigger load more when near top
        if (isAtTop && hasMore && !isLoading && !isLoadingMore) {
          setIsLoadingMore(true)
          setShouldMaintainScrollPosition(true)
          previousScrollHeight.current = scrollHeight

          onLoadMore().finally(() => {
            setIsLoadingMore(false)
          })
        }

        // Call external scroll handler
        onScroll?.({
          scrollTop,
          scrollHeight,
          clientHeight,
          isAtTop,
          isAtBottom,
        })
      }, [hasMore, isLoading, isLoadingMore, onLoadMore, onScroll, threshold])

      // Maintain scroll position after loading more messages
      useEffect(() => {
        const container = containerRef.current
        if (!container || !shouldMaintainScrollPosition) return

        const newScrollHeight = container.scrollHeight
        const scrollDiff = newScrollHeight - previousScrollHeight.current

        if (scrollDiff > 0) {
          // Scroll down by the difference to maintain position
          container.scrollTop = container.scrollTop + scrollDiff
          setShouldMaintainScrollPosition(false)
        }
      }, [children, shouldMaintainScrollPosition])

      // Auto-scroll to bottom for new messages (except during pagination)
      useEffect(() => {
        const container = containerRef.current
        if (!container || !autoScrollToBottom || shouldMaintainScrollPosition)
          return

        // For initial load, scroll to bottom immediately
        if (isInitialLoad.current) {
          container.scrollTop = container.scrollHeight
          isInitialLoad.current = false
          return
        }

        // For new messages, smooth scroll to bottom
        const isNearBottom =
          container.scrollHeight -
            container.scrollTop -
            container.clientHeight <=
          100
        if (isNearBottom) {
          container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth',
          })
        }
      }, [children, autoScrollToBottom, shouldMaintainScrollPosition])

      // Reset isInitialLoad when component unmounts
      useEffect(() => {
        return () => {
          isInitialLoad.current = true
        }
      }, [])

      // Scroll to top function
      const scrollToTop = useCallback(() => {
        const container = containerRef.current
        if (container) {
          container.scrollTo({
            top: 0,
            behavior: 'smooth',
          })
        }
      }, [])

      // Manual load more handler
      const handleLoadMore = useCallback(async () => {
        if (hasMore && !isLoading && !isLoadingMore) {
          setIsLoadingMore(true)
          setShouldMaintainScrollPosition(true)
          previousScrollHeight.current = containerRef.current?.scrollHeight || 0

          try {
            await onLoadMore()
          } finally {
            setIsLoadingMore(false)
          }
        }
      }, [hasMore, isLoading, isLoadingMore, onLoadMore])

      return (
        <div className={cn('relative', className)}>
          {/* Load more indicator at top */}
          {(isLoadingMore || (hasMore && showLoadMoreButton)) && (
            <div className="sticky top-0 z-10 bg-white border-b border-gray-200 shadow-sm">
              <div className="flex items-center justify-center py-3">
                {isLoadingMore ? (
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <FiLoader className="w-4 h-4 animate-spin" />
                    <span>Loading more messages...</span>
                  </div>
                ) : hasMore ? (
                  <button
                    onClick={handleLoadMore}
                    className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700 font-medium px-4 py-2 rounded-lg hover:bg-blue-50 transition-colors"
                  >
                    <FiArrowUp className="w-4 h-4" />
                    <span>Load more messages</span>
                  </button>
                ) : null}
              </div>
            </div>
          )}

          {/* Scrollable content */}
          <div
            ref={containerRef}
            onScroll={handleScroll}
            className="overflow-y-auto overflow-x-hidden h-full"
          >
            {children}
          </div>

          {/* Scroll to top button */}
          {showScrollToTop && (
            <button
              onClick={scrollToTop}
              className="absolute top-4 right-4 bg-white hover:bg-gray-50 border border-gray-200 text-gray-600 hover:text-gray-800 rounded-full p-2 shadow-md transition-colors z-20"
              aria-label="Scroll to top"
            >
              <FiArrowUp className="w-4 h-4" />
            </button>
          )}
        </div>
      )
    },
    (prevProps, nextProps) => {
      // Custom comparison for InfiniteScrollContainer memo
      return (
        prevProps.hasMore === nextProps.hasMore &&
        prevProps.isLoading === nextProps.isLoading &&
        prevProps.className === nextProps.className &&
        prevProps.threshold === nextProps.threshold &&
        prevProps.autoScrollToBottom === nextProps.autoScrollToBottom &&
        prevProps.showLoadMoreButton === nextProps.showLoadMoreButton &&
        React.Children.count(prevProps.children) ===
          React.Children.count(nextProps.children)
        // Note: We don't compare onLoadMore and onScroll functions as they may change on every render
        // The parent should use useCallback to memoize these functions
      )
    }
  )

export default InfiniteScrollContainer
