-- Create<PERSON><PERSON>
CREATE TYPE "ConversationType" AS ENUM ('SELF_CLARIFY');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "EntityUserRelationship" AS ENUM ('OWNER', 'EDITOR', 'READER');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "UserStatus" AS ENUM ('ACTIVE', 'INACTIVE');

-- CreateTable
CREATE TABLE "messages" (
    "id" TEXT NOT NULL,
    "creator_id" TEXT NOT NULL,
    "conversation_id" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "is_hidden" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "messages_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "conversations" (
    "id" TEXT NOT NULL,
    "creator_id" TEXT NOT NULL,
    "config" JSONB NOT NULL DEFAULT '{}',
    "conversation_type" "ConversationType" NOT NULL,
    "is_hidden" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "conversations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "conversation_users" (
    "id" TEXT NOT NULL,
    "conversation_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "relationship" "EntityUserRelationship" NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "conversation_users_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "messages_id_idx" ON "messages" USING HASH ("id");

-- CreateIndex
CREATE INDEX "messages_creator_id_idx" ON "messages" USING HASH ("creator_id");

-- CreateIndex
CREATE INDEX "messages_conversation_id_idx" ON "messages" USING HASH ("conversation_id");

-- CreateIndex
CREATE INDEX "messages_created_at_idx" ON "messages"("created_at");

-- CreateIndex
CREATE INDEX "conversations_id_idx" ON "conversations" USING HASH ("id");

-- CreateIndex
CREATE INDEX "conversations_creator_id_idx" ON "conversations" USING HASH ("creator_id");

-- CreateIndex
CREATE INDEX "conversations_created_at_idx" ON "conversations"("created_at");

-- CreateIndex
CREATE INDEX "conversation_users_id_idx" ON "conversation_users" USING HASH ("id");

-- CreateIndex
CREATE INDEX "conversation_users_conversation_id_idx" ON "conversation_users" USING HASH ("conversation_id");

-- CreateIndex
CREATE INDEX "conversation_users_user_id_idx" ON "conversation_users" USING HASH ("user_id");

-- CreateIndex
CREATE INDEX "conversation_users_created_at_idx" ON "conversation_users"("created_at");

-- AddForeignKey
ALTER TABLE "messages" ADD CONSTRAINT "messages_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "conversations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "conversation_users" ADD CONSTRAINT "conversation_users_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "conversations"("id") ON DELETE CASCADE ON UPDATE CASCADE;
