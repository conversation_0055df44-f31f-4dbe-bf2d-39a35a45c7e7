'use client'

import React from 'react'
import { DialogHeader, DialogTitle } from '@/components/ui/dialog'
import QuickResearchStepsModalContent from '@/app/(conv)/dragTree/[dragTreeId]/components/shared/QuickResearchStepsModalContent'

export const QUICK_RESEARCH_STEPS_TITLE = 'Research Steps & Reasoning'

type QuickResearchStepsDialogBodyProps = {
  messages: any[]
  title?: string
}

export default function QuickResearchStepsDialogBody({
  messages,
  title = QUICK_RESEARCH_STEPS_TITLE,
}: QuickResearchStepsDialogBodyProps) {
  return (
    <div className="w-full">
      <div className="sticky top-0 z-10 bg-white border-b p-4">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
      </div>
      <div className="mt-0 p-4 max-h-[70vh] overflow-y-auto">
        <QuickResearchStepsModalContent messages={messages} />
      </div>
    </div>
  )
}
