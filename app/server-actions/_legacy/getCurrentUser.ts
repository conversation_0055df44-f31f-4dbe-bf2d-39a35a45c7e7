import prisma from '@/app/libs/prismadb'
import getSession from '@/app/(legacy)/_server-actions/getSession'

const getCurrentUser = async () => {
  try {
    const session = await getSession()

    if (!session?.user?.email) {
      return null
    }

    const currentUser = await prisma.user.findUnique({
      where: {
        email: session.user.email as string,
      },
    })

    if (!currentUser) {
      return null
    }

    return currentUser
  } catch {
    return null
  }
}

export default getCurrentUser
