# Legacy Hooks

This folder contains React hooks that are no longer part of the main application architecture but are kept for legacy route support.

## Files Moved Here

### Completely Unused Hooks

- `useChatMessages.ts` - Legacy chat messages hook (no active usage)
- `useOnNodeChange.ts` - Legacy node change handler (only self-referential)
- `usePostHog.ts` - Legacy PostHog analytics hook (no active usage)
- `useTreeNavigation.ts` - Legacy tree navigation (no active usage)

### Legacy-Only Hooks

- `useCheckDisabledConversation.ts` - Used only in legacy conversation components
- `useFetchIssueTreeData.ts` - Used only in legacy issue tree system
- `useIssueTreeLogic.ts` - Used only in legacy issue tree system
- `useShiftEnterSubmit.ts` - Used only in legacy components and tests
- `useConversation.ts` - Used only in legacy sidebar and conversation components
- `useRephrase.ts` - Used only in legacy starter component
- `useRoutes.ts` - Used only in legacy sidebar components
- `useScreen.ts` - Used only in legacy starter component (legacy API endpoint)

## Current Usage

These hooks are still imported by:

- Legacy conversation components in `/app/(legacy)/_conversations/`
- Legacy issue tree components
- Some test files for backwards compatibility
- Bridge components that support legacy functionality

## Modern Alternatives

The current application uses these active hooks:

- `useLocalStorage.ts` - Active for font settings (app/providers.tsx)
- `useNodeGeneration.ts` - Active in drag tree system (dragTree Client.tsx)
- `useTreeState.ts` - Active in drag tree outline (HierarchicalOutline)

## Architecture Evolution

The app has evolved from:

- **Legacy**: Conversation-based UI with issue trees and panels
- **Modern**: DragTree-focused interface with screening and AI pane features

## Cleanup Strategy

These legacy hooks can be removed once:

1. Legacy conversation routes are fully deprecated
2. Legacy issue tree system is removed
3. All bridge components are refactored to use modern alternatives
