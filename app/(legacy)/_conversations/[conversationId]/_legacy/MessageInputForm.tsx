import React, { FC, useRef } from 'react'
import { HiPaperAirplane } from 'react-icons/hi'
import Textarea from 'react-textarea-autosize'
import { ConversationStatus } from '@prisma/client'

interface MessageInputFormProps {
  input: string
  setInput: (input: string) => void
  onSubmit: (input: string) => void
  disabled: boolean
  maxMessagesLimit: number
  conversationStatus: ConversationStatus
  messages: any
  formRef: React.RefObject<HTMLFormElement>
  onKeyDown: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void
  minCharCount: number
}

export const MessageInputForm: FC<MessageInputFormProps> = ({
  input,
  setInput,
  onSubmit,
  disabled,
  maxMessagesLimit,
  conversationStatus,
  messages,
  formRef,
  onKeyDown,
  minCharCount,
}) => {
  const inputRef = useRef<HTMLTextAreaElement>(null)

  const charCount = input.length
  let charCountDisplay
  if (charCount < minCharCount) {
    charCountDisplay = `min: ${charCount}/${minCharCount}`
  } else {
    charCountDisplay = 'Can send now'
  }

  return (
    <form
      onSubmit={async e => {
        e.preventDefault()
        if (input === '') {
          return
        }
        setInput('')
        await onSubmit(input)
      }}
      className="flex items-center gap-2 lg:gap-4 w-full p-2 fixed bottom-0 max-w-screen-lg"
      ref={formRef}
    >
      <div className="flex flex-col flex-grow">
        <Textarea
          ref={inputRef}
          tabIndex={0}
          onKeyDown={onKeyDown}
          rows={1}
          maxRows={5}
          maxLength={2000}
          value={input}
          onChange={e => setInput(e.target.value)}
          placeholder={
            conversationStatus === ConversationStatus.COMPLETED
              ? `Conversation is finished, please check your draft later`
              : messages.length >= maxMessagesLimit
                ? `We have conducted ${
                    (maxMessagesLimit - 1) / 2
                  }+ rounds already, let's finish it!`
                : 'Preferred format: 1: ...; 2: ...'
          }
          spellCheck={false}
          disabled={disabled || messages.length === 0}
          className="
        text-black
        font-light
        mb-1
        py-2
        px-4
        bg-neutral-100
        rounded-2xl
        focus:outline-none
        flex-grow
        disabled:bg-gray-200
      "
        />
        <span className="text-xs text-gray-400 pl-4 text-center">
          {'Press Enter to send, Shift+Enter for new line'}
        </span>
      </div>
      <div className="flex flex-col items-center gap-1">
        <button
          type="submit"
          disabled={disabled || input === '' || input.length < minCharCount}
          className="rounded-full p-2 bg-sky-500 cursor-pointer hover:bg-sky-600 transition disabled:bg-red-300"
        >
          <HiPaperAirplane size={18} className="text-white" />
        </button>
        <div className="text-sm text-gray-500">{charCountDisplay}</div>
      </div>
    </form>
  )
}
