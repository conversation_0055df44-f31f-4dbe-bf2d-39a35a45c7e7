-- CreateEnum
CREATE TYPE "PromptStatus" AS ENUM ('ACTIVE', 'INACTIVE');

-- AlterTable
ALTER TABLE "conversations" ADD COLUMN     "prompt_id" TEXT;

-- CreateTable
CREATE TABLE "prompts" (
    "id" TEXT NOT NULL,
    "description" TEXT,
    "content" TEXT NOT NULL,
    "status" "PromptStatus" NOT NULL DEFAULT 'ACTIVE',
    "config" JSONB NOT NULL DEFAULT '{}',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "prompts_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "prompts_id_idx" ON "prompts" USING HASH ("id");

-- CreateIndex
CREATE INDEX "prompts_description_idx" ON "prompts" USING HASH ("description");

-- CreateIndex
CREATE INDEX "prompts_created_at_idx" ON "prompts"("created_at");

-- AddForeign<PERSON>ey
ALTER TABLE "conversations" ADD CONSTRAINT "conversations_prompt_id_fkey" FOREIGN KEY ("prompt_id") REFERENCES "prompts"("id") ON DELETE SET NULL ON UPDATE CASCADE;
