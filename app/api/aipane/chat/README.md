# AI Pane Chat Endpoint

**Path:** `/api/aipane/chat`

A production-grade streaming chat endpoint that combines OpenAI (Azure) LLMs, optional Brave web search tool use, and an extensible persistence layer.

## TL;DR (for future devs & agents)

- **Stream for UX, persist atomically** – we collect `AiExecutionStep`s in-memory during streaming and write a _single_ transaction in the `onFinish` callback.
- **<PERSON>hema used** – `AiConversation`, `AiMessage`, `AiExecutionStep`, `AiAttachment` (see `prisma/schema.prisma`).
- **ID conventions** – `thread_`, `msg_`, and `step_` prefixes (generated in `lib/id.ts`).
- **Rate limits** – 5 requests/min per user (`libs/rateLimiter`).
- **Retries** – Persistence now retries up to 3× before bailing (see source).

## Request Shape (TypeScript)

```ts
export interface AIPaneChatRequest {
  messages: Array<{ role: 'user' | 'assistant'; content: string }>
  model?: string // default: 'gpt-4.1'
  context?: string // optional, injected into system prompt
  conversationId?: string // omit to create a new one
  settings?: Record<string, any>
  // optional context polymorphism
  contextEntityType?: string
  contextEntityId?: string
}
```

## Response

Returns an `ai` SDK `DataStreamResponse` (text/event-stream) with streamed tokens. No JSON envelope.

### Headers

- `Content-Type: text/event-stream`
- Standard SSE headers handled by `ai` SDK.

## Persistence Flow

1. **Conversation** – We call `createAiConversation(...)` when `conversationId` is absent.
2. **Streaming** – `streamText` handles tool calls (`web_search`) and delta events.
3. **Execution Steps** – Collected via `createExecutionStepCollector()`.
4. **onFinish** –
   1. Build a reasoning summary step (if any steps).
   2. Call `persistConversationTurn(...)` (_with retry_).
   3. Log AI usage (`server-actions/log_ai_usage`).

All writes run in their own Prisma transaction for atomicity.

## Tooling

- **Web Search** – Brave Search wrapper (`shared/search-tools.ts`).
- **Rate Limiter** – `libs/rateLimiter` (shorter windows in dev).

## Failure Modes

- **Stream failure** – caught by outer `try/catch`; no DB writes.
- **Persistence failure** – retried 3×, then logged as `FATAL`.
- **Rate limited / auth issues** – returns proper JSON error bodies via `shared/errors.ts`.

---

_Last updated: 2025-07-17_
