import { Inter } from 'next/font/google'

const inter = Inter({ subsets: ['latin'] })

type LandingLayoutProps = {
  children: React.ReactNode
}

const LandingLayout = ({ children }: LandingLayoutProps): React.JSX.Element => {
  return (
    <div
      className={`min-h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-gray-800 text-white ${inter.className}`}
    >
      {children}
    </div>
  )
}

export default LandingLayout
