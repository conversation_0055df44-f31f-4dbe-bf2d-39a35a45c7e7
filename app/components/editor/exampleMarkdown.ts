export const notebookContext = `
# Requirement Summary

Based on our research and interview with the client's core team, there is a significant demand for a yoga studio in San Francisco. The target market consists of approximately 200,000 individuals who are actively practicing yoga or interested in starting yoga as part of their fitness routine. The market is primarily female, with a preference for morning sessions. The yoga industry in San Francisco has experienced steady growth, and there are several competitors in the market, including Yoga Garden SF, The Pad Studios, and Yoga Flow SF. To differentiate from competitors, the new yoga studio should offer a personalized and intimate experience, targeting specific customer segments currently underserved in the market.

# Terminology

- Yoga Studio: A facility that offers yoga classes and related services.
- Target Market: The specific group of individuals that a business aims to attract and serve.
- Competitors: Other businesses in the same industry that offer similar products or services.
- Personalized Experience: Tailoring the yoga classes and services to meet the individual needs and preferences of customers.
- Intimate Experience: Creating a warm and welcoming atmosphere that fosters a sense of community and connection among customers.
- Underserved Market: A segment of the population that is not adequately served by existing businesses in the market.

# Functional Requirements

1. **Class Scheduling and Management**
    - The system should allow customers to view the schedule of yoga classes and book their preferred sessions.
    - The system should enable instructors to manage class schedules, including adding, modifying, and canceling classes.
    - The system should provide a real-time view of class availability and capacity to prevent overbooking.
2. **Membership and Payment Management**
    - The system should allow customers to purchase memberships and class packages.
    - The system should support automatic recurring payments for memberships.
    - The system should provide an easy-to-use interface for customers to update their payment information.
3. **Customer Relationship Management**
    - The system should maintain a database of customer profiles, including contact information, preferences, and purchase history.
    - The system should enable targeted email marketing campaigns based on customer segments and preferences.
    - The system should allow customers to provide feedback and ratings for classes and instructors.
4. **Instructor Management**
    - The system should support the hiring and onboarding process for yoga instructors.
    - The system should provide a platform for instructors to manage their schedules and availability.
    - The system should allow instructors to access customer profiles and track attendance.
5. **Marketing and Promotions**
    - The system should enable the creation and management of promotional offers and discounts.
    - The system should integrate with social media platforms for marketing and advertising purposes.
    - The system should provide analytics and reporting on the effectiveness of marketing campaigns.
6. **Facility Management**
    - The system should enable the management of yoga studio facilities, including room booking and maintenance schedules.
    - The system should track inventory and supplies, such as yoga mats, blocks, and straps.
    - The system should provide real-time updates on studio occupancy and capacity.

# Back-of-the-Envelope Calculations

## Assumptions and Context

- **Target Market**: Approximately 200,000 individuals actively practicing yoga or interested in starting yoga as part of their fitness routine.
- **Competitors**: Yoga Garden SF, The Pad Studios, and Yoga Flow SF.
- **Rental/Real Estate Prices**: $30 to $50 per square foot.
- **Revenue Streams**: Drop-in class fees, monthly memberships, workshops and special events, and retail sales of yoga-related products.

## Market Size and Revenue Potential

- **Market Size**: Assuming a conservative 5% market share, the new yoga studio can potentially serve 10,000 customers.
- **Revenue Potential**: Assuming an average monthly membership fee of $150 and an average drop-in class fee of $20, the new yoga studio can potentially generate $1.5 million in monthly revenue.

## Facility and Operating Costs

- **Facility Size**: Assuming a 2,000 square foot studio space.
- **Rental Costs**: Assuming a rental price of $40 per square foot, the monthly rental cost would be $80,000.
- **Operating Costs**: Including utilities, cleaning services, and payroll, the monthly operating costs would be approximately $30,000.

## Profitability Analysis

- **Gross Profit Margin**: Assuming a conservative 60% gross profit margin, the monthly gross profit would be $900,000.
- **Net Profit Margin**: Assuming a net profit margin of 20%, the monthly net profit would be $300,000.

# Architecture

This section provides an overview of the architecture of the yoga studio system, organized into four layers: User Interface, Application Service, Data Access, and Infrastructure. Each layer has specific responsibilities and interacts with the other layers to provide a seamless experience for customers and staff.

## Context Diagram

${"`".repeat(3)}mermaid
graph TD
	%% Overarching Container
	subgraph "Yoga Studio System Context"
		%% External Actors
		customer["Customer<br>Attends yoga classes."]
		instructor["Instructor<br>Teaches yoga classes."]
		admin["Admin<br>Manages the yoga studio."]

		%% System Boundary
		subgraph "Yoga Studio System"
			yoga_system["Yoga Studio System<br>Manages class schedules and customer information."]
		end

		%% External Systems
		payment_system["Payment System<br>Handles payment processing."]
		email_system["Email System<br>Manages email communications."]

		%% Relationships
		customer -.->|Attends classes| yoga_system
		instructor -.->|Teaches classes| yoga_system
		admin -.->|Manages the system| yoga_system

		yoga_system -->|Processes payments via| payment_system
		yoga_system -->|Sends email notifications via| email_system

		payment_system -->|Handles payment requests from| yoga_system
		email_system -->|Sends email notifications for| yoga_system
	end
${"`".repeat(3)}

### User Interface Layer

- **Customer App**: Provides an interface for customers to view class schedules, book sessions, and manage their memberships.
- **Instructor App**: Allows instructors to manage their schedules, view customer profiles, and track attendance.
- **Admin Dashboard**: Provides administrative staff with tools to manage class schedules, customer information, and marketing campaigns.

### Application Service Layer

- **Class Scheduling Service**: Manages class schedules, availability, and capacity.
- **Membership Service**: Handles membership management, payment processing, and recurring payments.
- **Customer Relationship Management Service**: Maintains customer profiles, handles feedback and ratings, and supports targeted marketing campaigns.
- **Facility Management Service**: Manages studio facilities, room booking, and inventory tracking.

### Data Access Layer

- **Customer Database**: Stores customer profiles, preferences, purchase history, and feedback.
- **Instructor Database**: Stores instructor information, schedules, and availability.
- **Class Database**: Keeps track of class schedules, availability, and attendance.
- **Inventory Database**: Manages inventory and supplies for the yoga studio.

### Infrastructure Layer

- **Message Queue**: Enables asynchronous communication for notifications and order processing.
- **Cache**: Improves system performance by caching frequently accessed data.
- **Security Components**: Ensures system security and prevents unauthorized access.

## Container Diagram

${"`".repeat(3)}mermaid
graph
	%% Custom Class Definition
	classDef database fill:#f9f,stroke:#333,stroke-width:2px;

	%% Big container
	subgraph "Yoga Studio System - Overview"

	%% System Boundary
	subgraph "Yoga Studio System"

	%% Containers
	customer_service["Customer Service<br>Manages customer information and bookings."]
	instructor_service["Instructor Service<br>Manages instructor information and schedules."]
	class_scheduling_service["Class Scheduling Service<br>Handles class schedules and availability."]
	membership_service["Membership Service<br>Manages memberships and payments."]
	crm_service["Customer Relationship Management Service<br>Handles customer profiles and feedback."]
	facility_management_service["Facility Management Service<br>Manages studio facilities and inventory."]

	%% Databases
	customer_db["Customer Database<br>Stores customer data and preferences."]
	instructor_db["Instructor Database<br>Stores instructor data and schedules."]
	class_db["Class Database<br>Stores class schedules and attendance."]
	inventory_db["Inventory Database<br>Stores inventory and supplies."]

	%% Container Relationships
	customer_service --> customer_db
	instructor_service --> instructor_db
	class_scheduling_service --> class_db
	membership_service --> customer_db
	crm_service --> customer_db
	facility_management_service --> inventory_db
	class_scheduling_service --> facility_management_service
end

	%% External Actors
	customer["Customer<br>Attends yoga classes."]
	instructor["Instructor<br>Teaches yoga classes."]
	admin["Admin<br>Manages the yoga studio."]

	%% Relationships with External Actors
	customer -.->|Views schedules and books classes using| customer_service
	instructor -.->|Manages schedules and views customer profiles using| instructor_service
	admin -.->|Manages schedules, customer information, and marketing campaigns using| class_scheduling_service
	admin -.->|Manages memberships and payments using| membership_service
	admin -.->|Manages customer profiles and feedback using| crm_service
	admin -.->|Manages studio facilities and inventory using| facility_management_service
	end

	%% Apply class to databases
	class customer_db,instructor_db,class_db,inventory_db database;
${"`".repeat(3)}

## Sequence Diagram

${"`".repeat(3)}mermaid
sequenceDiagram
	title Class Booking Sequence Diagram
	participant Customer
	participant YogaStudioSystem
	participant PaymentSystem
	participant EmailSystem

	Customer->>YogaStudioSystem: View class schedules
	YogaStudioSystem->>Customer: Provide class schedule
	Customer->>YogaStudioSystem: Book a class
	YogaStudioSystem->>PaymentSystem: Request payment authorization
	PaymentSystem->>YogaStudioSystem: Confirm payment authorization
	YogaStudioSystem->>EmailSystem: Send booking confirmation
	EmailSystem->>Customer: Send booking confirmation
${"`".repeat(3)}

## State Diagram

${"`".repeat(3)}mermaid
stateDiagram
direction LR
    [*] --> NewClass : Create Class
    NewClass --> Ready : Schedule Confirmed
    Ready --> InProgress : Class Starts
    InProgress --> Completed : Class Ends
    Completed --> [*] : End

    NewClass --> Cancelled : Cancel
    Ready --> Cancelled : Cancel
    Cancelled --> [*] : End
${"`".repeat(3)}

# Core Algorithms and Business Logic

## Class Scheduling and Management

### Dynamic Class Scheduling
- **Automated Scheduling**: Utilize algorithms to automatically generate class schedules based on instructor availability, room capacity, and customer demand. This ensures optimal resource utilization and minimizes conflicts. The trade-off is the need for accurate data inputs and the potential for less flexibility in accommodating specific instructor or customer preferences.
- **Real-Time Updates**: Implement real-time updates to class schedules to reflect last-minute changes or cancellations. This ensures accurate and up-to-date information for customers and instructors. However, real-time updates may require additional computational resources and robust error-handling mechanisms.

## Membership and Payment Management

### Recurring Payment Processing
- **Automated Billing**: Implement automated billing systems to handle recurring payments for memberships. This ensures a seamless payment experience for customers and reduces administrative overhead. However, automated billing systems may require integration with payment processors and robust error-handling mechanisms.
- **Payment Reminders**: Send payment reminders to customers before their membership renewal dates. This helps ensure timely payments and reduces the risk of membership cancellations. However, excessive or poorly timed reminders may lead to customer dissatisfaction or unsubscribing.

## Customer Relationship Management

### Personalized Recommendations
- **Machine Learning-based Recommendations**: Utilize machine learning algorithms to provide personalized class recommendations based on customer preferences, past attendance, and feedback. This enhances customer satisfaction and encourages repeat visits. However, implementing machine learning algorithms requires a large dataset and continuous model training and validation.
- **Segmented Marketing Campaigns**: Implement targeted marketing campaigns based on customer segments and preferences. This allows for more effective communication and promotions tailored to specific customer groups. However, segmenting customers accurately requires robust data analysis and segmentation strategies.

## Instructor Management

### Availability Management
- **Dynamic Availability Updates**: Allow instructors to update their availability in real-time to reflect changes in their schedules or personal circumstances. This ensures accurate class scheduling and minimizes conflicts. However, real-time availability updates may require additional computational resources and robust error-handling mechanisms.
- **Instructor Ratings and Feedback**: Collect and analyze instructor ratings and feedback from customers to ensure the quality of instruction. This helps identify top-performing instructors and areas for improvement. However, managing ratings and feedback requires a robust feedback collection and analysis system.

## Marketing and Promotions

### Social Media Integration
- **Social Media Advertising**: Leverage social media platforms for targeted advertising campaigns to reach potential customers. This allows for precise targeting based on demographics, interests, and behaviors. However, social media advertising may require additional budget allocation and ongoing monitoring and optimization.
- **Influencer Partnerships**: Collaborate with fitness influencers or local wellness brands to promote the yoga studio. This helps expand the reach and credibility of the studio among the target market. However, influencer partnerships may require negotiation and ongoing relationship management.

## Facility Management

### Inventory Tracking and Management
- **Real-Time Inventory Updates**: Implement real-time inventory tracking systems to ensure accurate stock levels of yoga supplies and accessories. This helps prevent stockouts and improves operational efficiency. However, real-time inventory tracking requires integration with point-of-sale systems and robust data management practices.
- **Supplier Relationship Management**: Establish strong relationships with suppliers to ensure timely and cost-effective procurement of yoga supplies. This helps maintain a steady supply chain and reduces the risk of inventory shortages. However, managing supplier relationships requires ongoing communication and negotiation.

# Conclusions

Based on our analysis, there is a significant demand for a yoga studio in San Francisco, with a target market of approximately 200,000 individuals actively practicing yoga or interested in starting yoga. The new yoga studio can differentiate itself by offering a personalized and intimate experience, targeting specific customer segments currently underserved in the market. The yoga studio can generate revenue through drop-in class fees, monthly memberships, workshops and special events, and retail sales of yoga-related products. To ensure success, the yoga studio should focus on dynamic class scheduling, efficient membership and payment management, personalized customer relationship management, effective instructor management, targeted marketing and promotions, and streamlined facility management.
`;
