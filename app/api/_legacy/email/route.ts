// // This API route is for sending email to the user after they have completed a conversation
// import { NextResponse } from "next/server";
// import prisma from "@/app/libs/prismadb";
// import { EmailStatus } from "@prisma/client";
// const marked = require("marked");

// export async function POST(request: Request) {
//   try {
//     console.log("api/email: email route");
//     const body = await request.json();
//     const { conversationId, completion, analysisPromptId, currentUser } = body;
//     console.log("api/email: body", body);

//     if (!currentUser?.id || !currentUser?.email) {
//       return new NextResponse("Unauthorized", { status: 400 });
//     }
//     // console.log("api/email: currentUser", currentUser);

//     const baseUrl =
//       process.env.NODE_ENV === "development"
//         ? "http://localhost:3000"
//         : process.env.NEXT_PUBLIC_SERVER_URL;
//     // console.log("api/email: baseUrl", baseUrl);

//     // For email config
//     const sgMail = require("@sendgrid/mail");
//     sgMail.setApiKey(process.env.SENDGRID_API_KEY);

//     // Use the latest conversation from DB to avoid any async issues b/w local and DB
//     const latestConversation = await prisma.conversation.findUnique({
//       where: {
//         id: conversationId,
//       },
//     });
//     // console.log("api/email: latestConversation", latestConversation);
//     const latestConversationConfig = latestConversation?.config as {
//       prompt?: { description?: string };
//     };
//     // console.log(
//     //   "api/email: latestConversationConfig",
//     //   latestConversationConfig
//     // );

//     // Convert OpenAI output from markdown to html for email
//     let markdownContent = completion;
//     const conversationLink = `${baseUrl}/conversations/${conversationId}`;
//     // https://wordtohtml.net/site/index <- this is very helpful to construct html content
//     const htmlContent = `
// <p>Dear ${currentUser?.name},</p>
// <p><strong><u>TL;DR:</u></strong></p>
// <p>we [Clarify AI] have analyzed your conversation titled <strong>${
//       latestConversation?.title ||
//       latestConversation?.created_at?.toLocaleString() ||
//       "Untitled"
//     }</strong> with this to clarify: <strong>${
//       latestConversationConfig.prompt?.description || "First ask to clarify"
//     }</strong></p>
// <p>reference_id: ${latestConversation?.id}</p>
// <p>
//   <a
//     href=${conversationLink}
//     target="_blank"
//     rel="noopener noreferrer"
//   >
//     View the conversation
//   </a>
// </p>
// <p><u>vvv The following information is machine-generated and may contain inaccuracies vvv</u></p>
// <hr class="rounded">
// <p><br></p>
// ${marked.parse(markdownContent)}
// <p><br></p>
// <hr class="rounded">
// <p><u>^^^ The above information is machine-generated and may contain inaccuracies ^^^</u></p>
// <p><strong><u>L</u></strong>ong<strong><u>; R</u></strong>ead [The following text was written by a human]</p>
// <p>Thank you for giving Clarify AI a try. This project aims to address a common pain point we often encounter in the workplace, particularly when collaborating across teams: <strong>UNCLEAR REQUIREMENTS</strong>.</p>
// <p>The root cause of this issue often lies in <strong>communication misalignments&nbsp;</strong>stemming from diverse experiences, educational backgrounds, information gaps, individual priorities, and different thinking patterns. Unfortunately, individuals rarely have the luxury of time to clarify every detail or provide comprehensive briefings.</p>
// <p>Consequently, we have historically relied on necessary but often tedious methods like <u>numerous meetings</u> and <u>extensive documentation</u> to keep things running smoothly. Needless to say, <strong>these tasks are far from enjoyable</strong>, as we humans naturally prefer engaging in value-creating activities. With Clarify AI, we are embarking on a small experiment to explore how AI can bridge this gap.</p>
// <p>We recognize that there is still much <strong>room for improvement</strong>, and we apologize for any inconvenience you may have experienced during the process. We genuinely value your feedback and would be delighted to continue the conversation further, either in person over coffee if you&apos;re in the Bay Area or through an online conversation.</p>
// <p><strong>To chat more, simply reply to this email w/ your feedback, love to chat more :-)</strong></p>
// <p>Best wishes,&nbsp;</p>
// <p>Clarify AI</p>
//     `;

//     // Construct the email context
//     const msg = {
//       status: EmailStatus.DELIVERED,
//       from: process.env.SENDGRID_EMAIL, // Use the email address or domain you verified above
//       to: currentUser?.email,
//       subject: `Clarify AI: ${latestConversation?.title} for ${
//         currentUser?.name
//       } - ref:${latestConversation?.id || "N/A"}`,
//       text: `Analysis of your conversation: ${latestConversation?.title}`,
//       html: htmlContent,
//       conversation_id: conversationId,
//       user_id: currentUser.id,
//       prompt_id: analysisPromptId,
//     };
//     console.log("api/email: msg", msg);

//     // Send the email
//     await sgMail.send(msg).then(
//       async () => {
//         // console.log("api/email: Email sent");
//         // Check if msg.from and msg.to are not undefined
//         if (msg.from && msg.to) {
//           // Save to prisma email as delivered
//           try {
//             await prisma.email.create({
//               data: {
//                 status: EmailStatus.DELIVERED,
//                 from: msg.from,
//                 to: msg.to,
//                 subject: msg.subject,
//                 text: msg.text,
//                 html: msg.html,
//                 conversation_id: conversationId,
//                 user_id: currentUser.id,
//                 prompt_id: analysisPromptId,
//               },
//             });
//             console.log("api/email: Email saved to DB");
//           } catch (error) {
//             console.error("Failed to save email as DELIVERED:", error);
//           }
//         } else {
//           throw new Error("Email From or To field is missing");
//         }
//       },
//       async (error: { response: { body: any } }) => {
//         if (msg.from && msg.to) {
//           // Save to prisma email as failed
//           try {
//             await prisma.email.create({
//               data: {
//                 status: EmailStatus.FAILED,
//                 from: msg.from,
//                 to: msg.to,
//                 subject: msg.subject,
//                 text: msg.text,
//                 html: msg.html,
//                 conversation_id: conversationId,
//                 user_id: currentUser.id,
//                 prompt_id: analysisPromptId,
//               },
//             });
//           } catch (error) {
//             console.error("Failed to save email as FAILED:", error);
//           }
//         }

//         if (error.response) {
//           console.error(error.response.body);
//         }
//       }
//     );

//     return NextResponse.json(null);
//   } catch (error) {
//     console.error(error, "ERROR_MESSAGES");
//     return new NextResponse("Internal Error", { status: 500 });
//   }
// }
