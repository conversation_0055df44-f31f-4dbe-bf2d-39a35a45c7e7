import React, { memo, useState, useEffect, useCallback } from 'react'
import { <PERSON><PERSON>, Position } from 'reactflow'
import useIssueTreeStore, {
  issueTreeStoreStatus,
} from '@/app/stores/_legacy/issuetree_store'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import useCheckDisabledConversation from '@/app/(legacy)/_hooks/useCheckDisabledConversation'

type NodeData = {
  label: string
  example?: string
  resolved?: boolean
  skipped?: boolean
}

type CustomLeafNodeProps = {
  id: string
  data: NodeData
}

const useExampleText = (
  id: string
): [string, React.Dispatch<React.SetStateAction<string>>] => {
  const { nodes } = useIssueTreeStore()
  const [text, setText] = useState<string>('')

  useEffect(() => {
    const currentNode = nodes.find(
      node => node.id === id && node.type === 'customLeafNode'
    )
    if (currentNode?.data?.example !== text) {
      setText(currentNode?.data?.example || '')
    }
  }, [nodes, id, text])

  return [text, setText]
}

const CustomLeafNode: React.FC<CustomLeafNodeProps> = memo(({ id, data }) => {
  const [text] = useExampleText(id)
  const { nodes, status, setLeafNodeData } = useIssueTreeStore()
  const shouldDisable = useCheckDisabledConversation()

  const currentNode = nodes.find(
    node => node.id === id && node.type === 'customLeafNode'
  )
  const { resolved, skipped } = currentNode?.data || {}
  const isLoading = status === issueTreeStoreStatus.Loading

  const handleNodeAction = useCallback(
    (action: 'skip' | 'unskip' | 'resolve' | 'unresolve') => {
      const actions = {
        skip: { resolved: true, skipped: true },
        unskip: { resolved: false, skipped: false },
        resolve: { resolved: true },
        unresolve: { resolved: false },
      }
      setLeafNodeData(id, actions[action])
    },
    [id, setLeafNodeData]
  )

  const preventPropagation = useCallback(
    (e: React.MouseEvent) => e.stopPropagation(),
    []
  )

  return (
    <article
      className="flex p-2 bg-zinc-100 rounded-lg shadow-md transition-all duration-300 hover:shadow-lg"
      style={{ width: '700px', minHeight: '100px' }}
    >
      <Handle
        type="target"
        position={Position.Left}
        style={{ background: '#555' }}
        isConnectable={false}
      />

      <div className="flex flex-col flex-grow mr-2">
        <header className="mb-2 flex-grow">
          <h3
            className={`text-base px-1 font-bold ${
              skipped ? 'line-through text-zinc-500' : 'text-zinc-800'
            }`}
          >
            {data.label}
          </h3>
        </header>
        <Textarea
          value={text}
          className={`w-full h-20 text-base rounded-md transition-all duration-300 overflow-auto
            ${
              resolved
                ? 'bg-green-100 border-green-400 hover:bg-green-200'
                : 'bg-red-100 border-red-400 hover:bg-red-200'
            }
            ${skipped ? 'line-through text-zinc-500' : 'text-zinc-800'}
            focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500`}
          onClick={() => useIssueTreeStore.getState().setSelectedNode(id)}
          onMouseDown={preventPropagation}
          onMouseOver={preventPropagation}
          disabled={isLoading}
          placeholder="Type here..."
          readOnly
        />
      </div>

      <div className="flex flex-col justify-around">
        <Button
          onClick={() => handleNodeAction(skipped ? 'unskip' : 'skip')}
          className={`strike text-sm p-2 rounded w-28 transition-all duration-300
            ${
              isLoading
                ? 'bg-yellow-100 text-zinc-400 cursor-not-allowed'
                : skipped
                  ? 'bg-yellow-200 hover:bg-yellow-300 text-zinc-800'
                  : 'bg-blue-200 hover:bg-blue-300 text-zinc-800'
            }
            hover:font-bold hover:shadow-md`}
          disabled={isLoading || shouldDisable}
        >
          {skipped ? 'Unskip this question' : 'Skip the irrelevant'}
        </Button>

        <Button
          onClick={() => handleNodeAction(resolved ? 'unresolve' : 'resolve')}
          className={`strike text-sm p-2 rounded w-28 transition-all duration-300
            ${
              isLoading
                ? 'bg-yellow-100 text-zinc-400 cursor-not-allowed'
                : resolved
                  ? 'bg-green-200 hover:bg-green-300 text-zinc-800'
                  : 'bg-red-200 hover:bg-red-300 text-zinc-800'
            }
            hover:font-bold hover:shadow-md`}
          disabled={isLoading || shouldDisable}
        >
          {resolved ? 'Mark as Need Review' : 'Mark as Reviewed'}
        </Button>
      </div>
    </article>
  )
})

CustomLeafNode.displayName = 'CustomLeafNode'

export default CustomLeafNode
