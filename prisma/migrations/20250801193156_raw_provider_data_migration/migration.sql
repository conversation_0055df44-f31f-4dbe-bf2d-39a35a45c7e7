/*
  Raw Provider Data Migration

  This migration safely converts the AiExecutionStep.type column from enum to string
  to support raw provider types from AI SDK 5.
*/

-- Step 1: Add new temporary column
ALTER TABLE "ai_execution_steps" ADD COLUMN "type_new" TEXT;

-- Step 2: Migrate existing data by converting enum values to strings
UPDATE "ai_execution_steps" SET "type_new" =
  CASE
    WHEN "type" = 'THOUGHT' THEN 'thought'
    WHEN "type" = 'TOOL_CALL' THEN 'tool-call'
    WHEN "type" = 'TOOL_RESULT' THEN 'tool-result'
    WHEN "type" = 'REASONING_SUMMARY' THEN 'reasoning-summary'
    WHEN "type" = 'SUB_AGENT_INVOCATION' THEN 'sub-agent-invocation'
    WHEN "type" = 'TITLE' THEN 'title'
    WHEN "type" = 'CITATIONS' THEN 'citations'
    WHEN "type" = 'STATUS_UPDATE' THEN 'status-update'
    ELSE 'unknown'
  END;

-- Step 3: Make the new column NOT NULL
ALTER TABLE "ai_execution_steps" ALTER COLUMN "type_new" SET NOT NULL;

-- Step 4: Drop the old column and rename the new one
ALTER TABLE "ai_execution_steps" DROP COLUMN "type";
ALTER TABLE "ai_execution_steps" RENAME COLUMN "type_new" TO "type";

-- Step 5: Recreate the index with the new column type
CREATE INDEX "ai_execution_steps_type_created_at_idx" ON "ai_execution_steps"("type", "created_at");
