"use client";

import { useState, useEffect, useCallback } from "react";
import { TreeNode } from "@/app/types";

type UseTreeStateProps = {
  treeData: TreeNode | null;
};

type UseTreeStateReturn = {
  collapsedNodes: Set<string>;
  userExpandedNodes: Set<string>;
  handleSetCollapsedNodes: (nodes: Set<string>) => void;
  expandNodePath: (nodePath: string[]) => void;
};

/**
 * Custom hook to manage tree state including collapsed/expanded nodes
 * and user interaction tracking
 */
export const useTreeState = ({
  treeData,
}: UseTreeStateProps): UseTreeStateReturn => {
  const [collapsedNodes, setCollapsedNodes] = useState<Set<string>>(new Set());
  const [userExpandedNodes, setUserExpandedNodes] = useState<Set<string>>(
    new Set()
  );

  /**
   * Determines if a category node should be collapsed by default
   * Categories without child categories (only questions) are collapsed by default
   */
  const shouldCollapseByDefault = (node: TreeNode): boolean => {
    if (node.type !== "category") return false;
    const hasChildCategories = node.children.some(
      (child: TreeNode) => child.type === "category"
    );
    return !hasChildCategories && node.children.length > 0;
  };

  /**
   * Recursively collect nodes that should be collapsed by default
   */
  const collectCollapsibleNodes = (
    node: TreeNode,
    initialCollapsed: Set<string>
  ) => {
    if (shouldCollapseByDefault(node) && !userExpandedNodes.has(node.id)) {
      initialCollapsed.add(node.id);
    }
    node.children.forEach((child) =>
      collectCollapsibleNodes(child, initialCollapsed)
    );
  };

  /**
   * Initialize collapsed state when tree data becomes available
   */
  useEffect(() => {
    if (!treeData) return;

    const initialCollapsed = new Set<string>();
    collectCollapsibleNodes(treeData, initialCollapsed);

    setCollapsedNodes((prev) => {
      const newCollapsed = new Set<string>();

      // Keep previously collapsed nodes that user hasn't explicitly expanded
      prev.forEach((nodeId) => {
        if (!userExpandedNodes.has(nodeId)) {
          newCollapsed.add(nodeId);
        }
      });

      // Add new default collapsed nodes
      initialCollapsed.forEach((nodeId) => {
        newCollapsed.add(nodeId);
      });

      return newCollapsed;
    });
  }, [treeData, userExpandedNodes]);

  /**
   * Enhanced setCollapsedNodes that tracks user interactions
   * Memoized to prevent unnecessary re-renders
   */
  const handleSetCollapsedNodes = useCallback(
    (nodes: Set<string>) => {
      // Track which nodes user has explicitly expanded or collapsed
      setUserExpandedNodes((prev) => {
        const newUserExpanded = new Set(prev);

        // Check for newly expanded nodes (user expanded them)
        collapsedNodes.forEach((nodeId) => {
          if (!nodes.has(nodeId)) {
            newUserExpanded.add(nodeId);
          }
        });

        // Check for newly collapsed nodes (user collapsed them)
        nodes.forEach((nodeId) => {
          if (!collapsedNodes.has(nodeId)) {
            newUserExpanded.delete(nodeId);
          }
        });

        return newUserExpanded;
      });

      setCollapsedNodes(nodes);
    },
    [collapsedNodes]
  ); // Depends on collapsedNodes for comparison logic

  /**
   * Expand all nodes in a given path and track as user-expanded
   * Memoized to prevent infinite loops in useEffect dependencies
   */
  const expandNodePath = useCallback((nodePath: string[]) => {
    const categoriesToExpand = nodePath.slice(0, -1);

    setCollapsedNodes((prev) => {
      const newCollapsed = new Set(prev);
      categoriesToExpand.forEach((nodeId) => {
        newCollapsed.delete(nodeId);
      });
      return newCollapsed;
    });

    setUserExpandedNodes((prev) => {
      const newUserExpanded = new Set(prev);
      categoriesToExpand.forEach((nodeId) => {
        newUserExpanded.add(nodeId);
      });
      return newUserExpanded;
    });
  }, []); // Empty dependency array since we use functional updates

  return {
    collapsedNodes,
    userExpandedNodes,
    handleSetCollapsedNodes,
    expandNodePath,
  };
};
