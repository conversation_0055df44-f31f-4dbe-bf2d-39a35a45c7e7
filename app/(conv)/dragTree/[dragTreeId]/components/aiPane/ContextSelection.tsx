'use client'

import React, { useState, useEffect, useMemo, useRef, useCallback } from 'react'
import { cn } from '@/lib/utils'
import {
  useAiPaneStore,
  type ContextItem,
} from '@/app/(conv)/dragTree/[dragTreeId]/stores/useAiPaneStore'
import { useSelectionStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useSelectionStore'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Checkbox } from '@/components/ui/checkbox'
import {
  FiFolder,
  FiFile,
  FiCheck,
  FiX,
  FiChevronDown,
  FiChevronRight,
  FiSearch,
  FiSettings,
} from 'react-icons/fi'
import { encode } from 'gpt-tokenizer'
import { extractNodeContentText } from '@/app/utils/nodeContent'

// Simple in-memory cache for token counts keyed by contentId
const tokenCache = new Map<string, number>()

type ContextSelectionProps = {
  dragTreeId: string
}

const ContextSelection: React.FC<ContextSelectionProps> = ({
  dragTreeId: _dragTreeId,
}) => {
  const settings = useAiPaneStore(state => state.settings)
  const setContext = useAiPaneStore(state => state.setContext)
  const toggleContextItem = useAiPaneStore(state => state.toggleContextItem)
  const selectAllContext = useAiPaneStore(state => state.selectAllContext)
  const clearAllContext = useAiPaneStore(state => state.clearAllContext)
  const setContextTokenCount = useAiPaneStore(
    state => state.setContextTokenCount
  )
  const contextTokenCount = useAiPaneStore(state => state.contextTokenCount)
  const setContextLoading = useAiPaneStore(state => state.setContextLoading)
  const isContextLoading = useAiPaneStore(state => state.isContextLoading)

  const frontendTreeStructure = useDragTreeStore(
    state => state.frontendTreeStructure
  )
  const nodeContent = useDragTreeStore(state => state.nodeContent)
  const fetchNodeContentBatch = useDragTreeStore(
    state => state.fetchNodeContentBatch
  )

  // Selection store for modal trigger
  const showContextSelectionModal = useSelectionStore(
    state => state.showContextSelectionModal
  )
  const setShowContextSelectionModal = useSelectionStore(
    state => state.setShowContextSelectionModal
  )

  const [isModalOpen, setIsModalOpen] = useState(false)
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set())
  const [searchTerm, setSearchTerm] = useState<string>('')
  const hasInitialized = useRef(false)

  // Helper to get the first content ID for a node
  const getFirstContentIdForNode = useCallback(
    (nodeId: string): string | null => {
      const contentMap = nodeContent.get(nodeId)
      if (!contentMap || contentMap.size === 0) return null
      return Array.from(contentMap.keys())[0]
    },
    [nodeContent]
  )

  // Auto-expand all nodes when modal opens
  useEffect(() => {
    if (isModalOpen && frontendTreeStructure) {
      const allNodeIds = new Set<string>()

      const collectNodeIds = (node: any) => {
        if (node?.id) {
          allNodeIds.add(node.id)
        }
        if (node?.children && Array.isArray(node.children)) {
          node.children.forEach(collectNodeIds)
        }
      }

      collectNodeIds(frontendTreeStructure)
      setExpandedNodes(allNodeIds)
    }
  }, [isModalOpen, frontendTreeStructure])

  // Effect to open modal when triggered from selection controls
  useEffect(() => {
    if (showContextSelectionModal) {
      setIsModalOpen(true)
      setShowContextSelectionModal(false) // Reset the trigger
    }
  }, [showContextSelectionModal, setShowContextSelectionModal])

  // Get nodes with research content recursively
  const getNodesWithResearch = useMemo(() => {
    if (!frontendTreeStructure || !nodeContent) return []

    const nodesWithResearch: any[] = []

    const checkNodeRecursively = (node: any) => {
      if (!node) return

      // Check if this node has research content
      const hasResearch =
        nodeContent?.has(node.id) && (nodeContent?.get(node.id)?.size ?? 0) > 0

      if (hasResearch) {
        nodesWithResearch.push({
          ...node,
          hasResearch: true,
        })
      }

      // Recursively check children
      if (node.children && Array.isArray(node.children)) {
        node.children.forEach(checkNodeRecursively)
      }
    }

    checkNodeRecursively(frontendTreeStructure)
    return nodesWithResearch
  }, [frontendTreeStructure, nodeContent])

  // Convert to context items
  const availableItems = useMemo(() => {
    return getNodesWithResearch.map(node => ({
      id: node.id,
      type: node.type as 'category' | 'question',
      title: node.label || 'Untitled',
      content: `Research content available`,
      selected: false,
    }))
  }, [getNodesWithResearch])

  // Get all descendant IDs recursively
  const getAllDescendantIds = useCallback(
    (nodeId: string): string[] => {
      if (!frontendTreeStructure) return []

      const findNode = (node: any, targetId: string): any => {
        if (node.id === targetId) return node
        if (node.children) {
          for (const child of node.children) {
            const found = findNode(child, targetId)
            if (found) return found
          }
        }
        return null
      }

      const collectDescendants = (node: any): string[] => {
        const descendants: string[] = []
        if (node.children) {
          for (const child of node.children) {
            // Only include nodes with research content
            if (
              nodeContent?.has(child.id) &&
              (nodeContent?.get(child.id)?.size ?? 0) > 0
            ) {
              descendants.push(child.id)
            }
            descendants.push(...collectDescendants(child))
          }
        }
        return descendants
      }

      const targetNode = findNode(frontendTreeStructure, nodeId)
      return targetNode ? collectDescendants(targetNode) : []
    },
    [frontendTreeStructure, nodeContent]
  )

  // Initialize context
  useEffect(() => {
    if (!hasInitialized.current && availableItems.length > 0) {
      hasInitialized.current = true
      setContext(availableItems)
    }
  }, [availableItems, setContext])

  // Calculate token count for the selected context whenever it changes
  useEffect(() => {
    try {
      const selectedItems = settings.context.filter(item => item.selected)
      console.log(
        '🔢 [Token Count] Calculating for selected items:',
        selectedItems.length
      )

      if (selectedItems.length === 0) {
        setContextTokenCount(0)
        return
      }

      const contextText = selectedItems
        .map(item => {
          const contentMap = nodeContent.get(item.id)
          if (!contentMap || contentMap.size === 0) {
            console.log(
              `🔢 [Token Count] No content for ${item.id} (${item.title})`
            )
            return ''
          }

          // Try all content items like in getContextMarkdown
          const contentItems = Array.from(contentMap.values())
          let extractedText = ''

          for (const contentItem of contentItems) {
            const text = extractNodeContentText(contentItem)
            if (text && text.trim().length > 0) {
              extractedText = text
              break
            }
          }

          console.log(
            `🔢 [Token Count] Extracted ${extractedText.length} chars for ${item.id} (${item.title})`
          )
          return extractedText
        })
        .filter(Boolean)

      const joinedText = contextText.join('\n\n')
      console.log(
        `🔢 [Token Count] Total text length: ${joinedText.length} chars`
      )

      if (joinedText.length === 0) {
        console.log('🔢 [Token Count] No valid text found, setting count to 0')
        setContextTokenCount(0)
        return
      }

      let totalTokens = 0
      selectedItems.forEach(item => {
        const map = nodeContent.get(item.id)
        if (!map || map.size === 0) return
        const first = Array.from(map.values())[0]
        const cached = tokenCache.get(first.contentId)
        if (cached !== undefined) {
          totalTokens += cached
        } else {
          const t = encode(extractNodeContentText(first)).length
          tokenCache.set(first.contentId, t)
          totalTokens += t
        }
      })

      console.log(`🔢 [Token Count] Final token count: ${totalTokens}`)
      setContextTokenCount(totalTokens)
    } catch (error) {
      console.error('Error calculating context token count:', error)
      setContextTokenCount(0)
    }
  }, [settings.context, nodeContent, setContextTokenCount])

  // Handle node selection with recursive selection
  const handleNodeToggle = useCallback(
    async (nodeId: string) => {
      const targetNode = getNodesWithResearch.find(n => n.id === nodeId)

      if (targetNode?.hasResearch) {
        // If it's a node with research, ensure content is loaded before toggling
        const contentMap = nodeContent.get(nodeId)
        const firstItem = contentMap ? Array.from(contentMap.values())[0] : null
        const needsFetch =
          !contentMap ||
          contentMap.size === 0 ||
          !firstItem?.contentText ||
          firstItem.contentText.trim().length === 0

        if (needsFetch) {
          const firstContentId = getFirstContentIdForNode(nodeId)
          if (firstContentId) {
            setContextLoading(nodeId, true)
            await fetchNodeContentBatch([{ nodeId, contentId: firstContentId }])
            setContextLoading(nodeId, false)
          }
        }
        toggleContextItem(nodeId)
      } else {
        // If it's a category, toggle all descendants with research
        const descendantIds = getAllDescendantIds(nodeId)
        const allIds = [nodeId, ...descendantIds]

        // Fetch content for all nodes that will be selected and don't have content yet
        const batchPairs: { nodeId: string; contentId: string }[] = []
        allIds.forEach(id => {
          const contentMap = nodeContent.get(id)
          const firstItem = contentMap
            ? Array.from(contentMap.values())[0]
            : null
          const needsFetch =
            !contentMap ||
            contentMap.size === 0 ||
            !firstItem?.contentText ||
            firstItem.contentText.trim().length === 0
          if (needsFetch) {
            const firstContentId = getFirstContentIdForNode(id)
            if (firstContentId) {
              setContextLoading(id, true)
              batchPairs.push({ nodeId: id, contentId: firstContentId })
            }
          }
        })

        if (batchPairs.length) {
          await fetchNodeContentBatch(batchPairs)
          batchPairs.forEach(p => setContextLoading(p.nodeId, false))
        }

        allIds.forEach(id => {
          if (availableItems.some(item => item.id === id)) {
            toggleContextItem(id)
          }
        })
      }
    },
    [
      getAllDescendantIds,
      availableItems,
      toggleContextItem,
      getNodesWithResearch,
      nodeContent,
      getFirstContentIdForNode,
      fetchNodeContentBatch,
      setContextLoading,
    ]
  )

  const selectedCount = settings.context.filter(item => item.selected).length
  const totalCount = availableItems.length

  if (availableItems.length === 0) {
    return (
      <div className="text-center py-6">
        <FiFile className="w-8 h-8 text-gray-300 mx-auto mb-3" />
        <p className="text-sm text-gray-700 font-medium mb-2">
          No research content available
        </p>
        <p className="text-xs text-gray-500 mb-3">
          To use context for generation, you need to complete quick research on
          your questions first.
        </p>
        <p className="text-xs text-blue-600 bg-blue-50 px-3 py-2 rounded-md">
          💡 Tip: Look for questions in your tree and start with the research
          buttons
        </p>
      </div>
    )
  }

  return (
    <>
      {/* Compact trigger button */}
      <div className="space-y-2">
        <Button
          onClick={() => setIsModalOpen(true)}
          variant="outline"
          className="w-full justify-between"
        >
          <span className="text-sm flex items-center space-x-1">
            <span>
              {selectedCount > 0
                ? `${selectedCount} items selected`
                : 'Select context items'}
            </span>
            {/* {selectedCount > 0 && (
              <span className="text-green-600 text-xs font-medium">
                • {contextTokenCount} tokens
              </span>
            )} */}
          </span>
          <div className="flex items-center space-x-2">
            <FiSettings className="w-4 h-4" />
          </div>
        </Button>

        {selectedCount > 0 && (
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>
              {selectedCount} of {totalCount} selected
              <span className="mx-1">•</span>
              <span
                className={cn(
                  'font-medium',
                  contextTokenCount > 0 ? 'text-green-600' : 'text-orange-600'
                )}
              >
                {contextTokenCount} tokens
              </span>
              {contextTokenCount === 0 && selectedCount > 0 && (
                <span className="ml-1 text-orange-600">
                  (content loading...)
                </span>
              )}
            </span>
            <Button
              onClick={clearAllContext}
              variant="ghost"
              size="sm"
              className="h-6 px-2 text-xs"
            >
              Clear all
            </Button>
          </div>
        )}
      </div>

      {/* Modal for selection */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>Select Context Items</DialogTitle>
          </DialogHeader>

          <div className="flex-1 space-y-4 overflow-hidden">
            {/* Search and controls */}
            <div className="space-y-3">
              {/* Search */}
              <div className="relative">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search research content..."
                  value={searchTerm}
                  onChange={e => setSearchTerm(e.target.value)}
                  className="w-full pl-9 pr-3 py-2 text-sm border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* Quick actions */}
              <div className="flex items-center justify-between">
                <div className="flex space-x-2">
                  <Button
                    onClick={selectAllContext}
                    variant="outline"
                    size="sm"
                  >
                    <FiCheck className="w-3 h-3 mr-1" />
                    Select All
                  </Button>
                  <Button onClick={clearAllContext} variant="outline" size="sm">
                    <FiX className="w-3 h-3 mr-1" />
                    Clear All
                  </Button>
                </div>
                <span className="text-sm text-gray-500">
                  {selectedCount} of {totalCount} selected
                </span>
              </div>
            </div>

            {/* Tree view */}
            <div className="flex-1 border rounded-md p-4 overflow-y-auto min-h-[300px] max-h-[400px]">
              <RecursiveTreeView
                node={frontendTreeStructure}
                nodeContent={nodeContent}
                expandedNodes={expandedNodes}
                setExpandedNodes={setExpandedNodes}
                selectedItems={settings.context}
                onNodeToggle={handleNodeToggle}
                searchTerm={searchTerm}
                isContextLoading={isContextLoading}
              />
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-2 pt-4 border-t">
              <Button variant="outline" onClick={() => setIsModalOpen(false)}>
                Cancel
              </Button>
              <Button onClick={() => setIsModalOpen(false)}>
                Done ({selectedCount} selected)
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}

// Recursive tree component
type RecursiveTreeViewProps = {
  node: any
  nodeContent: Map<string, any>
  expandedNodes: Set<string>
  setExpandedNodes: (nodes: Set<string>) => void
  selectedItems: ContextItem[]
  onNodeToggle: (nodeId: string) => void
  searchTerm: string
  isContextLoading: (id: string) => boolean
  level?: number
}

const RecursiveTreeView: React.FC<RecursiveTreeViewProps> = ({
  node,
  nodeContent,
  expandedNodes,
  setExpandedNodes,
  selectedItems,
  onNodeToggle,
  searchTerm,
  isContextLoading,
  level = 0,
}) => {
  if (!node) return null

  const hasResearch =
    nodeContent.has(node.id) && nodeContent.get(node.id)?.size > 0
  const isCategory = node.type === 'category'
  const isSelected = selectedItems.some(
    item => item.id === node.id && item.selected
  )
  const isExpanded = expandedNodes.has(node.id)
  const hasChildren = node.children && node.children.length > 0
  const isLoading = isContextLoading(node.id)

  // For categories, check if all children with research are selected
  const getChildrenWithResearch = (node: any): string[] => {
    if (!node.children) return []
    const childrenIds: string[] = []

    const collectChildren = (n: any) => {
      if (nodeContent.has(n.id) && nodeContent.get(n.id)?.size > 0) {
        childrenIds.push(n.id)
      }
      if (n.children) {
        n.children.forEach(collectChildren)
      }
    }

    node.children.forEach(collectChildren)
    return childrenIds
  }

  const childrenWithResearch = isCategory ? getChildrenWithResearch(node) : []
  const isCategoryFullySelected =
    isCategory &&
    childrenWithResearch.length > 0 &&
    childrenWithResearch.every(childId =>
      selectedItems.some(item => item.id === childId && item.selected)
    )
  const isCategoryPartiallySelected =
    isCategory &&
    childrenWithResearch.length > 0 &&
    childrenWithResearch.some(childId =>
      selectedItems.some(item => item.id === childId && item.selected)
    ) &&
    !isCategoryFullySelected

  // Filter based on search
  const matchesSearch =
    !searchTerm ||
    (node.label || '').toLowerCase().includes(searchTerm.toLowerCase())

  // Check if any descendant matches search
  const hasMatchingDescendant = (n: any): boolean => {
    if (!n.children) return false
    return n.children.some(
      (child: any) =>
        (child.label || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        hasMatchingDescendant(child)
    )
  }

  const shouldShow = matchesSearch || hasMatchingDescendant(node)

  if (!shouldShow && searchTerm) return null

  const toggleExpand = () => {
    const newExpanded = new Set(expandedNodes)
    if (isExpanded) {
      newExpanded.delete(node.id)
    } else {
      newExpanded.add(node.id)
    }
    setExpandedNodes(newExpanded)
  }

  const paddingLeft = level * 20

  return (
    <div style={{ paddingLeft }}>
      <div className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded-md">
        {/* Expand/collapse button */}
        {hasChildren && (
          <button
            onClick={toggleExpand}
            className="p-1 hover:bg-gray-200 rounded"
          >
            {isExpanded ? (
              <FiChevronDown className="w-3 h-3 text-gray-600" />
            ) : (
              <FiChevronRight className="w-3 h-3 text-gray-600" />
            )}
          </button>
        )}

        {/* Selection checkbox */}
        {hasResearch || (isCategory && childrenWithResearch.length > 0) ? (
          <Checkbox
            checked={hasResearch ? isSelected : isCategoryFullySelected}
            onCheckedChange={() => onNodeToggle(node.id)}
            className={cn(
              'w-4 h-4',
              isCategory && isCategoryPartiallySelected && 'opacity-50'
            )}
          />
        ) : (
          <div className="w-4 h-4 flex items-center justify-center">
            <div className="w-3 h-3 bg-gray-200 rounded border border-gray-300" />
          </div>
        )}

        {/* Icon */}
        {node.type === 'category' ? (
          <FiFolder className="w-4 h-4 text-blue-500" />
        ) : (
          <FiFile className="w-4 h-4 text-gray-400" />
        )}

        {/* Label */}
        <span
          className={cn(
            'text-sm flex-1',
            hasResearch ? 'text-gray-900' : 'text-gray-500',
            matchesSearch && searchTerm ? 'bg-yellow-100' : ''
          )}
        >
          {node.label || 'Untitled'}
          {isLoading ? (
            <span className="ml-2 text-xs text-blue-600 bg-blue-50 px-1.5 py-0.5 rounded flex items-center space-x-1">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
              <span>loading...</span>
            </span>
          ) : hasResearch ? (
            <span className="ml-2 text-xs text-green-600 bg-green-50 px-1.5 py-0.5 rounded">
              ✓ research
            </span>
          ) : (
            !isCategory && (
              <span className="ml-2 text-xs text-orange-600 bg-orange-50 px-1.5 py-0.5 rounded">
                needs research
              </span>
            )
          )}
        </span>
      </div>

      {/* Children */}
      {isExpanded && hasChildren && (
        <div>
          {node.children.map((child: any) => (
            <RecursiveTreeView
              key={child.id}
              node={child}
              nodeContent={nodeContent}
              expandedNodes={expandedNodes}
              setExpandedNodes={setExpandedNodes}
              selectedItems={selectedItems}
              onNodeToggle={onNodeToggle}
              searchTerm={searchTerm}
              isContextLoading={isContextLoading}
              level={level + 1}
            />
          ))}
        </div>
      )}
    </div>
  )
}

export default React.memo(ContextSelection)
