/**
 * Error Boundary for Chat Components
 *
 * Provides error isolation and fallback UI for chat-related components,
 * following the same pattern as ResearchErrorBoundary in the codebase.
 */

import React, { Component, ReactNode, ErrorInfo } from 'react'
import { Alert<PERSON>riangle, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'

type ChatErrorBoundaryProps = {
  children: ReactNode
  fallback?: ReactNode
  onRetry?: () => void
}

type ChatErrorBoundaryState = {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
}

class ChatErrorBoundary extends Component<
  ChatErrorBoundaryProps,
  ChatErrorBoundaryState
> {
  constructor(props: ChatErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(
    error: Error
  ): Partial<ChatErrorBoundaryState> {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log the error for debugging purposes
    console.error('[Chat] Error boundary caught error:', error, errorInfo)

    // Store error info for potential debugging
    this.setState({ errorInfo })

    // You could also log to an external error reporting service here
    // Example: errorReportingService.captureException(error, { extra: errorInfo })
  }

  handleRetry = () => {
    // Reset error state to retry rendering
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })

    // Call optional retry callback
    if (this.props.onRetry) {
      this.props.onRetry()
    }
  }

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided, otherwise show default error UI
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg text-red-700">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <h3 className="font-medium text-red-800 mb-2">
                Chat temporarily unavailable
              </h3>
              <p className="text-sm text-red-600 mb-3">
                Something went wrong with the chat interface. This is usually
                temporary.
              </p>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={this.handleRetry}
                  className="text-red-700 border-red-300 hover:bg-red-100"
                >
                  <RefreshCw className="h-4 w-4 mr-1" />
                  Try Again
                </Button>
                {process.env.NODE_ENV === 'development' && this.state.error && (
                  <details className="text-xs">
                    <summary className="cursor-pointer text-red-500 hover:text-red-700">
                      Show Error Details
                    </summary>
                    <pre className="mt-2 p-2 bg-red-100 rounded text-red-800 overflow-auto max-h-32">
                      {this.state.error.message}
                      {this.state.error.stack && (
                        <>
                          {'\n\n'}
                          {this.state.error.stack}
                        </>
                      )}
                    </pre>
                  </details>
                )}
              </div>
            </div>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

export default ChatErrorBoundary
