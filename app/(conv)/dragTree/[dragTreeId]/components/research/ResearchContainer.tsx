'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { ResearchEditor, type EditorType } from './ResearchEditor'
import { ResearchHeader } from './ResearchHeader'

export interface ResearchContainerProps {
  nodeId: string
  contentId: string
  fullTitle: string
  variant?: 'inline' | 'tab' | 'modal' | 'compact'
  editorType?: EditorType
  className?: string
  showHeader?: boolean
  showNavigation?: boolean
  showSearchResults?: boolean
  showStatusFooter?: boolean
  placeholder?: string
  onContentChange?: (content: string) => void
}

/**
 * Complete research container component that combines header and editor
 * Provides different layout variants for different use cases
 * Future-proof for supporting different editor types (tiptap, etc.)
 */
const ResearchContainer: React.FC<ResearchContainerProps> = ({
  nodeId,
  contentId,
  fullTitle,
  variant = 'inline',
  editorType = 'textarea',
  className,
  showHeader = true,
  showNavigation = true,
  showSearchResults = true,
  showStatusFooter = true,
  placeholder,
  onContentChange,
}) => {
  // Get variant-specific configurations
  const getVariantConfig = () => {
    switch (variant) {
      case 'tab':
        return {
          containerClass: 'h-full w-full',
          wrapperClass:
            'h-full w-full bg-white rounded-lg border border-slate-200 p-4 flex flex-col',
          headerVariant: 'tab' as const,
          editorVariant: 'fullscreen' as const,
          editorContainerClass: 'flex-1 min-h-0',
        }
      case 'modal':
        return {
          containerClass: 'w-full h-full',
          wrapperClass:
            'w-full h-full bg-white rounded-lg border border-slate-200 p-6 flex flex-col',
          headerVariant: 'default' as const,
          editorVariant: 'fullscreen' as const,
          editorContainerClass: 'flex-1 mt-4',
        }
      case 'compact':
        return {
          containerClass: 'w-full',
          wrapperClass: 'w-full',
          headerVariant: 'compact' as const,
          editorVariant: 'compact' as const,
          editorContainerClass: 'mt-2',
        }
      default: // inline
        return {
          containerClass: 'w-full',
          wrapperClass: 'w-full',
          headerVariant: 'default' as const,
          editorVariant: 'default' as const,
          editorContainerClass: 'mt-3',
        }
    }
  }

  const config = getVariantConfig()

  return (
    <div className={cn(config.containerClass, className)}>
      <div className={config.wrapperClass}>
        {showHeader && (
          <ResearchHeader
            nodeId={nodeId}
            contentId={contentId}
            fullTitle={fullTitle}
            variant={config.headerVariant}
            showNavigation={showNavigation}
            showSearchResults={showSearchResults}
          />
        )}

        <div className={config.editorContainerClass}>
          <ResearchEditor
            nodeId={nodeId}
            contentId={contentId}
            variant={config.editorVariant}
            editorType={editorType}
            showStatusFooter={showStatusFooter}
            placeholder={placeholder}
            onContentChange={onContentChange}
          />
        </div>
      </div>
    </div>
  )
}

// Memoize to prevent unnecessary re-renders when props haven't changed
const MemoizedResearchContainer = React.memo(ResearchContainer)
export { MemoizedResearchContainer as ResearchContainer }
export default MemoizedResearchContainer
