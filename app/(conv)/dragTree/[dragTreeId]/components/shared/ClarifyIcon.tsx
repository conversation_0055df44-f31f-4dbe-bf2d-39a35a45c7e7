import React from 'react'

// Shared ClarifyIcon component used in research buttons
export const ClarifyIcon: React.FC = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="w-4 h-4"
  >
    {/* Radiating lines pattern from Clarify app icon */}
    <g transform="translate(12,12)">
      <path
        d="M0,-8 L0,-6"
        stroke="#D4824A"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M5.66,-5.66 L4.24,-4.24"
        stroke="#D4824A"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M8,0 L6,0"
        stroke="#D4824A"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M5.66,5.66 L4.24,4.24"
        stroke="#D4824A"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M0,8 L0,6"
        stroke="#D4824A"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M-5.66,5.66 L-4.24,4.24"
        stroke="#D4824A"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M-8,0 L-6,0"
        stroke="#D4824A"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M-5.66,-5.66 L-4.24,-4.24"
        stroke="#D4824A"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M4.24,-7.07 L3.54,-5.66"
        stroke="#D4824A"
        strokeWidth="1.2"
        strokeLinecap="round"
      />
      <path
        d="M7.07,4.24 L5.66,3.54"
        stroke="#D4824A"
        strokeWidth="1.2"
        strokeLinecap="round"
      />
      <path
        d="M-4.24,7.07 L-3.54,5.66"
        stroke="#D4824A"
        strokeWidth="1.2"
        strokeLinecap="round"
      />
      <path
        d="M-7.07,-4.24 L-5.66,-3.54"
        stroke="#D4824A"
        strokeWidth="1.2"
        strokeLinecap="round"
      />
    </g>
    <circle cx="12" cy="12" r="3" fill="#D4824A" />
  </svg>
)
