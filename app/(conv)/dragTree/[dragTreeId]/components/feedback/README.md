# Enhanced Feedback Widget Component

A floating feedback collection widget for the drag tree page that provides an engaging, streamlined user experience for collecting user feedback with collapsible sections and engagement level selection.

## Features

- **Animated Floating Button**: Small, Next.js-style button with gradient animations and flowing effects
- **Collapsible Modal Interface**: Space-efficient feedback collection with expandable sections
- **Streamlined Feedback Types**: Two focused categories (Bug Reports & Suggestions)
- **Star Rating System**: Reuses existing rating questions from conversation feedback
- **Engagement Level Selection**: Three engaging follow-up options with distinct visual treatments
- **Progressive Disclosure**: Sections collapse automatically when completed but remain editable
- **Development Only**: Feature flag restricts visibility to development environment
- **Responsive Design**: Mobile-friendly with proper breakpoints
- **Accessibility**: Full ARIA support, keyboard navigation, and screen reader compatibility

## Component Structure

```
feedback/
├── FeedbackWidget.tsx    # Main widget component
├── types.ts             # TypeScript type definitions
├── index.ts             # Export barrel
└── README.md           # This documentation
```

## Usage

```tsx
import { FeedbackWidget } from '@/app/(conv)/dragTree/[dragTreeId]/components/feedback'

// Add to any page component
;<FeedbackWidget dragTreeId={dragTreeId} />
```

## Enhanced Feedback Flow

1. **Initial State**: Small animated floating button in bottom-right corner with gradient effects
2. **Modal Open**: Click button to open streamlined feedback collection modal
3. **Type Selection**: Choose from two focused feedback types:
   - 🐛 Report a bug
   - 💡 Give feedback/suggestions
   - _Auto-collapses after selection but remains clickable to change_
4. **Survey Completion**: Rate experience on three dimensions:
   - Overall Flow; Smoothness of Journey
   - Relevance and Clarity of Questions
   - Task Load Assessment
   - _Auto-collapses after completion but remains clickable to review_
5. **Engagement Level**: Choose follow-up preference with engaging options:
   - 🚀 **"YES! Let's connect!"** (Large, green gradient) - Human chat insights
   - 💬 **"I'm open to it"** (Medium, blue gradient) - User interviews/surveys
   - 👂 **"Just listening"** (Small, gray gradient) - No follow-up needed
6. **Detailed Feedback**: Textarea becomes available after all previous steps
7. **Submission**: Submit feedback with loading states and success confirmation

## Integration Points

### Existing Systems

- **Rating System**: Reuses `questionTextMap` from existing feedback components
- **UI Components**: Leverages shadcn/ui Dialog, Button, Textarea components
- **Analytics**: Integrates with Mixpanel for tracking user interactions
- **Notifications**: Uses react-hot-toast for user feedback

### Database Schema

- Generic table `user_feedback` with fields: `id`, `user_id`, `feedback_type`, `entity_type`, `entity_id`, `can_contact`, `feedback_text`, `metadata`, `created_at`, `updated_at`.
- `feedback_type` enum includes `DRAGTREE_FLOATING_BUTTON`.
- `entity_type`/`entity_id` enables polymorphic linkage (e.g., `DRAGTREE` + dragTreeId).
- `metadata` captures extensible structured info: `feedback_reason`, `survey_scores`, `engagement_level`, etc.

## Styling & Design

- **Position**: `fixed bottom-4 right-4` (smaller, Next.js-style positioning)
- **Z-Index**: `z-50` to appear above other content
- **Colors**: Gradient theme (`from-blue-500 via-purple-500 to-blue-600`)
- **Animations**:
  - Pulse animation on idle
  - Flowing shimmer effect on hover
  - Scale transforms (110% on hover, 95% on active)
  - Smooth 300ms transitions
- **Responsive**: Consistent 40px size across all devices
- **Visual Effects**: Gradient backgrounds, shadow variations, and selection indicators

## Accessibility Features

- **ARIA Labels**: Comprehensive labeling for screen readers
- **Keyboard Navigation**: Full keyboard support for all interactions
- **Focus Management**: Proper focus indicators and ring styles
- **Semantic HTML**: Uses fieldset, legend, and proper form structure
- **Screen Reader**: Hidden decorative elements and descriptive text

## Feature Flag

```tsx
const isDevelopment = process.env.NODE_ENV === 'development'

if (!isDevelopment) {
  return null
}
```

Widget only appears in development environment to prevent production visibility until we finish backend rollout.

## Testing

- **Unit Tests**: Basic component rendering and accessibility validation
- **Test Coverage**: Focuses on core functionality and environment-based visibility
- **Mock Dependencies**: Mocks external libraries (mixpanel, react-rating, toast)

## Enhanced Validation Logic

The submit button is only enabled when ALL steps are complete:

1. ✅ Feedback type selected (bug or suggestion)
2. ✅ Survey completed (all 3 star ratings provided)
3. ✅ Engagement level selected (connect, open, or listening)
4. ✅ Detailed feedback provided (non-empty textarea)

## Collapsible Sections

- **Auto-collapse**: Sections automatically collapse when completed
- **Click-to-expand**: Users can click collapsed sections to review/edit
- **Visual indicators**: Checkmarks and status messages show completion
- **Space efficiency**: Reduces visual clutter while maintaining accessibility

## Engagement Level Options

1. **🚀 "YES! Let's connect!"** (Large, green gradient)
   - Most prominent option for highly engaged users
   - Indicates willingness for human chat and deeper insights

2. **💬 "I'm open to it"** (Medium, blue gradient)
   - Moderate engagement for interviews and surveys
   - Balanced option for users open to participation

3. **👂 "Just listening"** (Small, gray gradient)
   - Minimal engagement for feedback-only users
   - Respectful option for users who prefer no follow-up

## Future Enhancements

1. **Backend Integration**: Implemented via server actions `createUserFeedback` and `getLatestUserFeedbackForEntity`.
2. **Follow-up Automation**: Different workflows based on engagement level selection
3. **File Attachments**: Allow users to attach screenshots for bug reports
4. **Email Integration**: Automatic follow-up based on engagement preferences
5. **Analytics Dashboard**: Admin interface with engagement level insights

## Technical Notes

- **Environment Detection**: Uses `process.env.NODE_ENV` for feature flagging
- **State Management**: Local component state with proper cleanup and collapsible sections
- **Error Handling**: Comprehensive error states with user-friendly messages
- **Performance**: Minimal re-renders with optimized state updates and useEffect hooks
- **Type Safety**: Full TypeScript coverage with strict type definitions
- **Animation Performance**: CSS transforms and gradients for smooth interactions
