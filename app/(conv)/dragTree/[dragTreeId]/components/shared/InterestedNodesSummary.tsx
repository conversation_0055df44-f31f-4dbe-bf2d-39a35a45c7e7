// Not in use, but could be useful for future
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { Card } from '@/components/ui/card'
import React from 'react'

const InterestedNodesSummary: React.FC = () => {
  // Optimized: Use individual selector to prevent unnecessary re-renders
  const getInterestedNodes = useDragTreeStore(state => state.getInterestedNodes)
  const interestedNodes = getInterestedNodes()

  if (interestedNodes.length === 0) {
    return null
  }

  return (
    <Card className="p-4 bg-gradient-to-r from-emerald-50 to-green-50 border-emerald-200">
      <h3 className="text-lg font-semibold text-emerald-800 mb-3">
        ✨ Questions You&apos;ve Researched ({interestedNodes.length})
      </h3>
      <div className="space-y-2">
        {interestedNodes.map(node => (
          <div
            key={node.id}
            className="flex items-center gap-2 p-2 bg-white rounded-lg border border-emerald-100"
          >
            <span className="text-xs font-mono text-slate-400 bg-slate-100 px-2 py-1 rounded">
              {node.id}
            </span>
            <span className="text-sm text-slate-700 flex-1">{node.label}</span>
            <div className="flex items-center gap-1">
              <span className="text-emerald-600 text-xs">✨</span>
              <span className="text-xs text-emerald-600 font-medium">
                Researched
              </span>
            </div>
          </div>
        ))}
      </div>
      <p className="text-xs text-emerald-600 mt-3">
        These are questions you&apos;ve shown interest in by using the research
        button. This helps track what topics you&apos;re actively exploring.
      </p>
    </Card>
  )
}

export default InterestedNodesSummary
