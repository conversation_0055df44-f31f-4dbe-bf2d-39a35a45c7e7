-- CreateEnum
CREATE TYPE "FeedbackType" AS ENUM ('ISSUETREE');

-- CreateTable
CREATE TABLE "feedback" (
    "id" TEXT NOT NULL,
    "creator_id" TEXT NOT NULL,
    "conversation_id" TEXT NOT NULL,
    "type" "FeedbackType" NOT NULL DEFAULT 'ISSUETREE',
    "can_contact" BOOLEAN NOT NULL DEFAULT false,
    "data" JSONB NOT NULL DEFAULT '{}',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "feedback_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "feedback_id_idx" ON "feedback" USING HASH ("id");

-- CreateIndex
CREATE INDEX "feedback_creator_id_idx" ON "feedback" USING HASH ("creator_id");

-- CreateIndex
CREATE INDEX "feedback_conversation_id_idx" ON "feedback" USING HASH ("conversation_id");

-- CreateIndex
CREATE INDEX "feedback_created_at_idx" ON "feedback"("created_at");

-- AddForeignKey
ALTER TABLE "feedback" ADD CONSTRAINT "feedback_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "conversations"("id") ON DELETE CASCADE ON UPDATE CASCADE;
