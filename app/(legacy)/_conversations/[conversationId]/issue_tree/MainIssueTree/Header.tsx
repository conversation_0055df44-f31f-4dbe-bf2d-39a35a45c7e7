import React from 'react'
import {
  issueTreeMode,
  issueTreeStoreStatus,
  IssueTreeStoreType,
} from '@/app/stores/_legacy/issuetree_store'
import { Badge } from '@/components/ui/badge'
import { FaListUl } from 'react-icons/fa'
import { TbBinaryTree } from 'react-icons/tb'
import { Hi<PERSON>ursorClick } from 'react-icons/hi'
import { BsThreeDotsVertical } from 'react-icons/bs'
import mixpanel from '@/app/libs/mixpanel'
import { Button } from '@/components/ui/button'
import useNotebookStore from '@/app/stores/_legacy/notebook_store'

export const Header: React.FC<{
  issueTreeStore: IssueTreeStoreType
  openSheet: () => void
}> = ({ issueTreeStore, openSheet }) => {
  // Get data from issueTreeStore
  const rootNode = issueTreeStore.nodes.find(node => node.id === 'L1:1')
  const label = rootNode ? rootNode.data.label : ''
  const unresolvedNodesCount = issueTreeStore.unresolvedNodesCount
  const leafNodeCount = issueTreeStore.leafNodeCount
  const issueTreeStorestatus = issueTreeStore.status
  const notebookStore = useNotebookStore()

  // Handle mode change (Tree/List)
  const handleModeChange = () => {
    const isTreeMode = issueTreeStore.mode === issueTreeMode.Tree
    const mixpanelEvent = isTreeMode ? 'toggle_list_mode' : 'toggle_tree_mode'
    mixpanel.track(mixpanelEvent, { location: 'issue_tree_header' })
    isTreeMode ? issueTreeStore.setListMode() : issueTreeStore.setTreeMode()
  }

  const handleOpenNotebookDialog = () => {
    mixpanel.track('click_open_notebooks', { location: 'issue_tree_header' })
    notebookStore.setShowDialog(true)
  }

  return (
    <header className="sticky top-[var(--conversation-menu-height)] right-0 left-[var(--sidebar-width)] z-[5] bg-white shadow-md transition-all duration-300 ease-in-out">
      <div className="px-4 sm:px-6 lg:px-8 py-3 flex flex-col sm:flex-row justify-between items-start sm:items-center">
        <div className="flex flex-col w-full sm:w-1/2">
          <h1 className="text-xl font-bold truncate mb-2 text-gray-800">
            {label}
          </h1>
          <div className="flex items-center space-x-3 text-xs mb-2 sm:mb-0">
            <span className="px-2 py-1 bg-red-100 rounded text-red-700 font-semibold">
              Needs Review: {unresolvedNodesCount}
            </span>
            <span className="px-2 py-1 bg-green-100 rounded text-green-700 font-semibold">
              Reviewed: {leafNodeCount - unresolvedNodesCount}
            </span>
          </div>
        </div>

        <div className="flex items-center space-x-3 mt-2 sm:mt-0">
          {/* Mobile view */}
          <div className="sm:hidden flex space-x-3">
            <Badge
              className="rounded-full p-2 text-gray-600 bg-gray-100 hover:bg-gray-200 transition-colors duration-200 ease-in-out"
              onClick={handleModeChange}
            >
              {issueTreeStore.mode === issueTreeMode.Tree ? (
                <FaListUl size={18} />
              ) : (
                <TbBinaryTree size={18} />
              )}
            </Badge>
          </div>

          {/* Desktop view */}
          <div className="hidden sm:flex space-x-3">
            <Badge
              className="rounded-full px-3 py-1.5 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 hover:font-bold transition-all duration-200 ease-in-out transform hover:scale-105"
              onClick={handleModeChange}
            >
              {issueTreeStore.mode === issueTreeMode.Tree ? (
                <>
                  <FaListUl className="mr-2 inline" size={16} />
                  <span>List Mode</span>
                </>
              ) : (
                <>
                  <TbBinaryTree className="mr-2 inline" size={16} />
                  <span>Tree Mode</span>
                </>
              )}
            </Badge>
            <Button
              variant="outline"
              className="rounded-full px-3 py-1.5 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 hover:font-bold transition-all duration-200 ease-in-out transform hover:scale-105"
              onClick={handleOpenNotebookDialog}
            >
              <HiCursorClick
                size={18}
                className={'animate-pulse text-blue-500'}
              />
              Open Notebooks
            </Button>
          </div>

          <span
            className={`rounded px-2 py-1 text-sm font-medium ${
              issueTreeStorestatus === issueTreeStoreStatus.Saving
                ? 'bg-yellow-100 text-yellow-700'
                : 'bg-green-100 text-green-700'
            }`}
          >
            {issueTreeStorestatus === issueTreeStoreStatus.Saving
              ? 'Saving...'
              : issueTreeStorestatus}
          </span>
          <Badge
            onClick={openSheet}
            className="rounded-full p-2 text-gray-600 bg-gray-100 hover:bg-gray-200 transition-colors duration-200 ease-in-out"
          >
            <BsThreeDotsVertical size={18} />
          </Badge>
        </div>
      </div>
    </header>
  )
}
