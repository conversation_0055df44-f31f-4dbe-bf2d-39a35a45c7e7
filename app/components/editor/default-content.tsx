// Just show some HTML for the time being

const DEFAULT_EDITOR_CONTENT = {
  type: "doc",
  content: [
    {
      type: "heading",
      attrs: {
        level: 3,
      },
      content: [
        {
          type: "text",
          marks: [
            {
              type: "bold",
            },
          ],
          text: "If you see this, there are some issues with the report generation",
        },
      ],
    },
    {
      type: "paragraph",
      content: [
        {
          type: "text",
          text: "Sorry, it is on us, not your issue",
        },
      ],
    },
    {
      type: "paragraph",
      content: [
        {
          type: "text",
          text: "Please either",
        },
      ],
    },
    {
      type: "orderedList",
      attrs: {
        tight: true,
        start: 1,
      },
      content: [
        {
          type: "listItem",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text: "Retry the generation",
                },
              ],
            },
          ],
        },
        {
          type: "listItem",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text: "Contact us, we can investigate that and help you out of this",
                },
              ],
            },
            {
              type: "bulletList",
              attrs: {
                tight: true,
              },
              content: [
                {
                  type: "listItem",
                  content: [
                    {
                      type: "paragraph",
                      content: [
                        {
                          type: "text",
                          marks: [
                            {
                              type: "link",
                              attrs: {
                                href: "mailto:<EMAIL>",
                                target: "_blank",
                                class:
                                  "text-stone-400 underline underline-offset-[3px] hover:text-stone-600 transition-colors cursor-pointer",
                              },
                            },
                          ],
                          text: "<EMAIL>",
                        },
                      ],
                    },
                  ],
                },
                {
                  type: "listItem",
                  content: [
                    {
                      type: "paragraph",
                      content: [
                        {
                          type: "text",
                          text: "please attach your current route from the browser",
                        },
                      ],
                    },
                  ],
                },
                {
                  type: "listItem",
                  content: [
                    {
                      type: "paragraph",
                      content: [
                        {
                          type: "text",
                          text: "eg: http://",
                        },
                        {
                          type: "text",
                          marks: [
                            {
                              type: "link",
                              attrs: {
                                href: "http://localhost:3000/conversations/clk7vb0vs00010kko8smb5teq/result",
                                target: "_blank",
                                class:
                                  "text-stone-400 underline underline-offset-[3px] hover:text-stone-600 transition-colors cursor-pointer",
                              },
                            },
                            {
                              type: "bold",
                            },
                          ],
                          text: "sth1",
                        },
                        {
                          type: "text",
                          marks: [
                            {
                              type: "link",
                              attrs: {
                                href: "http://localhost:3000/conversations/clk7vb0vs00010kko8smb5teq/result",
                                target: "_blank",
                                class:
                                  "text-stone-400 underline underline-offset-[3px] hover:text-stone-600 transition-colors cursor-pointer",
                              },
                            },
                          ],
                          text: "/conversations/",
                        },
                        {
                          type: "text",
                          marks: [
                            {
                              type: "link",
                              attrs: {
                                href: "http://localhost:3000/conversations/clk7vb0vs00010kko8smb5teq/result",
                                target: "_blank",
                                class:
                                  "text-stone-400 underline underline-offset-[3px] hover:text-stone-600 transition-colors cursor-pointer",
                              },
                            },
                            {
                              type: "bold",
                            },
                          ],
                          text: "sth2",
                        },
                        {
                          type: "text",
                          marks: [
                            {
                              type: "link",
                              attrs: {
                                href: "http://localhost:3000/conversations/clk7vb0vs00010kko8smb5teq/result",
                                target: "_blank",
                                class:
                                  "text-stone-400 underline underline-offset-[3px] hover:text-stone-600 transition-colors cursor-pointer",
                              },
                            },
                          ],
                          text: "/result",
                        },
                      ],
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    },
    {
      type: "paragraph",
    },
  ],
};

export default DEFAULT_EDITOR_CONTENT;
