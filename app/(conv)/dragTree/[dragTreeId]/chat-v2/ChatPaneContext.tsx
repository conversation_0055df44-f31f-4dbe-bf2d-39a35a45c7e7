'use client'

import React, {
  createContext,
  useContext,
  useReducer,
  useEffect,
  useCallback,
  useRef,
} from 'react'
import type { UIMessage } from 'ai'
import { generateAiConversationId } from '@/lib/id'
import { useTabStore } from '../stores/useTabStore'
import { useChatStreamBridge } from './hooks/useChatStreamBridge'
import { useConversationHistory } from './hooks/useConversationHistory'
import { useAssetStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useAssetStore' // Absolute import per preference
// import { ENABLE_CHAT_DEBUG_LOGGING } from '@/app/configs/feature-flags'

// Types
export type ChatPaneStatus =
  | 'idle' // Initial state, no conversation ID
  | 'creating' // Generating conversation ID
  | 'loading-history' // Loading existing messages
  | 'ready' // Ready to send/receive messages
  | 'streaming' // Currently streaming a response
  | 'error' // Error state

export type ChatPaneState = {
  conversationId?: string
  status: ChatPaneStatus
  messages: UIMessage[]
  input: string
  isStreaming: boolean
  error?: string
}

export type ChatPaneActions = {
  setInput: (value: string) => void
  submit: () => void
  refresh: () => Promise<void>
}

// Action types for reducer
type ChatPaneAction =
  | { type: 'RESOLVE_ID'; conversationId: string }
  | { type: 'LOAD_HISTORY_START' }
  | { type: 'LOAD_HISTORY_SUCCESS'; messages: UIMessage[] }
  | { type: 'LOAD_HISTORY_ERROR'; error: string }
  | { type: 'SET_INPUT'; input: string }
  | { type: 'STREAM_START' }
  | { type: 'STREAM_FINISH' }
  | { type: 'UPDATE_MESSAGES'; messages: UIMessage[] }
  | { type: 'ERROR'; error: string }
  | { type: 'RESET_ERROR' }

// Reducer
function chatPaneReducer(
  state: ChatPaneState,
  action: ChatPaneAction
): ChatPaneState {
  console.log('🔄 [ChatPaneReducer] Action:', action.type, {
    fromStatus: state.status,
    conversationId: state.conversationId,
    messagesCount: state.messages.length,
  })

  switch (action.type) {
    case 'RESOLVE_ID':
      return {
        ...state,
        conversationId: action.conversationId,
        status: 'loading-history',
        error: undefined,
      }

    case 'LOAD_HISTORY_START':
      return {
        ...state,
        status: 'loading-history',
        error: undefined,
      }

    case 'LOAD_HISTORY_SUCCESS':
      // Do not overwrite non-empty messages with an empty array from a late history success.
      // Prefer the existing messages if the incoming payload is empty.
      const nextMessages =
        Array.isArray(action.messages) && action.messages.length > 0
          ? action.messages
          : state.messages
      const successState: ChatPaneState = {
        ...state,
        status: 'ready' as ChatPaneStatus,
        messages: nextMessages,
        error: undefined,
      }
      console.log('✅ [ChatPaneReducer] LOAD_HISTORY_SUCCESS result:', {
        status: successState.status,
        messagesCount: successState.messages.length,
        conversationId: successState.conversationId,
      })
      return successState

    case 'LOAD_HISTORY_ERROR':
      return {
        ...state,
        status: 'error',
        error: action.error,
      }

    case 'SET_INPUT':
      return {
        ...state,
        input: action.input,
      }

    case 'STREAM_START':
      return {
        ...state,
        status: 'streaming',
        isStreaming: true,
        error: undefined,
      }

    case 'STREAM_FINISH':
      return {
        ...state,
        status: 'ready',
        isStreaming: false,
      }

    case 'UPDATE_MESSAGES':
      return {
        ...state,
        messages: action.messages,
      }

    case 'ERROR':
      return {
        ...state,
        status: 'error',
        error: action.error,
        isStreaming: false,
      }

    case 'RESET_ERROR':
      return {
        ...state,
        error: undefined,
        status: state.conversationId ? 'ready' : 'idle',
      }

    default:
      return state
  }
}

// Context
const ChatPaneContext = createContext<{
  state: ChatPaneState
  actions: ChatPaneActions
} | null>(null)

// Provider props
export type ChatPaneProviderProps = {
  tabId: string
  dragTreeId: string
  apiEndpoint: string
  model: string
  contextIds: string[]
  getContext: () => string
  initialConversationId?: string
  children: React.ReactNode
}

// Provider component
export function ChatPaneProvider({
  tabId,
  dragTreeId: _dragTreeId,
  apiEndpoint,
  model,
  contextIds,
  getContext,
  initialConversationId,
  children,
}: ChatPaneProviderProps) {
  const { updateTabAiPaneData } = useTabStore()

  // Initialize state
  const [state, dispatch] = useReducer(chatPaneReducer, {
    conversationId: initialConversationId,
    status: initialConversationId ? 'loading-history' : 'idle',
    messages: [],
    input: '',
    isStreaming: false,
  })

  // History hook - only mount when we have a conversation ID
  const historyHook = useConversationHistory({
    conversationId: state.conversationId,
    enabled: Boolean(state.conversationId),
  })

  // Track whether we've already shown the initial loading overlay for this conversation
  const hasShownInitialLoadingRef = useRef<boolean>(false)

  // Reset per-conversation
  useEffect(() => {
    hasShownInitialLoadingRef.current = false
  }, [state.conversationId])

  // Stream bridge hook - only mount when we have a conversation ID
  // Key by conversationId to reset SDK state when conversation changes
  const streamHook = useChatStreamBridge({
    conversationId: state.conversationId,
    apiEndpoint,
    model,
    contextIds,
    getContext,
    enabled: Boolean(state.conversationId),
  })

  // Handle history loading with defensive state transitions
  useEffect(() => {
    if (!state.conversationId) return

    console.log('📊 [ChatPaneContext] History loading effect:', {
      conversationId: state.conversationId,
      historyIsLoading: historyHook.isLoading,
      historyError: historyHook.error,
      historyMessagesLength: historyHook.messages?.length ?? null,
      currentStatus: state.status,
      currentMessagesLength: state.messages.length,
    })

    // Enter loading state on first mount only if we have no messages to render
    if (historyHook.isLoading) {
      if (
        state.status !== 'loading-history' &&
        state.messages.length === 0 &&
        !hasShownInitialLoadingRef.current
      ) {
        console.log('📊 [ChatPaneContext] Dispatching LOAD_HISTORY_START')
        dispatch({ type: 'LOAD_HISTORY_START' })
        hasShownInitialLoadingRef.current = true
      }
      return
    }

    // When loading finishes, transition to ready only when messages array is available
    if (!historyHook.isLoading) {
      if (historyHook.error) {
        if (state.status !== 'error') {
          console.log(
            '📊 [ChatPaneContext] Dispatching LOAD_HISTORY_ERROR:',
            historyHook.error
          )
          dispatch({ type: 'LOAD_HISTORY_ERROR', error: historyHook.error })
        }
      } else if (
        state.status !== 'ready' &&
        Array.isArray(historyHook.messages)
      ) {
        const messages = historyHook.messages
        console.log('📊 [ChatPaneContext] Dispatching LOAD_HISTORY_SUCCESS:', {
          messagesCount: messages.length,
          fromStatus: state.status,
        })
        dispatch({ type: 'LOAD_HISTORY_SUCCESS', messages })
        hasShownInitialLoadingRef.current = true
      }
    }
  }, [
    historyHook.isLoading,
    historyHook.error,
    historyHook.messages,
    state.conversationId,
    state.status,
    state.messages.length,
  ])

  // Removed timeout safety-net to avoid racing READY with empty messages.

  // Fallback: if we have history messages but status didn't flip, force ready
  useEffect(() => {
    if (!state.conversationId) return
    const hasHistory =
      Array.isArray(historyHook.messages) && historyHook.messages.length > 0
    if (
      hasHistory &&
      state.status !== 'ready' &&
      state.status !== 'streaming'
    ) {
      dispatch({
        type: 'LOAD_HISTORY_SUCCESS',
        messages: historyHook.messages!,
      })
    }
  }, [state.conversationId, state.status, historyHook.messages])

  // Handle streaming state
  useEffect(() => {
    if (streamHook.isStreaming && state.status !== 'streaming') {
      dispatch({ type: 'STREAM_START' })
    } else if (!streamHook.isStreaming && state.status === 'streaming') {
      dispatch({ type: 'STREAM_FINISH' })
    }
  }, [streamHook.isStreaming, state.status])

  // Handle streaming errors
  useEffect(() => {
    if (streamHook.error) {
      dispatch({ type: 'ERROR', error: streamHook.error.message })
    }
  }, [streamHook.error])

  // Combine history and streaming messages
  useEffect(() => {
    const historyMessages = historyHook.messages || []
    const streamingMessages = streamHook.messages || []
    const combined = [...historyMessages, ...streamingMessages]

    // Unconditionally update to reflect latest history/streaming state.
    // This avoids stale state if equality checks miss subtle changes.
    console.log('🧩 [ChatPane] Combining messages', {
      history: historyMessages.length,
      streaming: streamingMessages.length,
      combined: combined.length,
    })
    dispatch({ type: 'UPDATE_MESSAGES', messages: combined })
  }, [historyHook.messages, streamHook.messages])

  // Track pending message for auto-send after ID resolution
  const pendingMessageRef = useRef<string>('')

  // Actions
  const actions: ChatPaneActions = {
    setInput: useCallback((value: string) => {
      dispatch({ type: 'SET_INPUT', input: value })
    }, []),

    submit: useCallback(() => {
      if (!state.input.trim()) return

      // If no conversation ID, generate one and store the message for later
      if (!state.conversationId) {
        const newId = generateAiConversationId()
        pendingMessageRef.current = state.input

        dispatch({ type: 'RESOLVE_ID', conversationId: newId })
        dispatch({ type: 'SET_INPUT', input: '' })

        // Update tab store with new conversation ID
        updateTabAiPaneData(tabId, {
          conversationId: newId,
          model,
          contextIds,
        })

        // Immediately create a chat asset so it appears in the sidebar (reduces lag)
        try {
          const addAsset = useAssetStore.getState().addAsset
          addAsset({
            id: newId,
            title: 'Untitled',
            content: '',
            type: 'chat',
            model,
            prompt: '',
            contextIds,
            dragTreeId: _dragTreeId,
          })
        } catch (e) {
          console.warn('[ChatPane] Failed to add chat asset immediately:', e)
        }

        return
      }

      // Send message through stream hook
      if (streamHook.sendMessage) {
        streamHook.sendMessage({ text: state.input })
        dispatch({ type: 'SET_INPUT', input: '' })
      }
    }, [
      state.input,
      state.conversationId,
      streamHook.sendMessage,
      updateTabAiPaneData,
      tabId,
      model,
      contextIds,
    ]),

    refresh: useCallback(async () => {
      if (historyHook.refresh) {
        await historyHook.refresh()
      }
    }, [historyHook.refresh]),
  }

  // Auto-send pending message after ID resolution and history loading
  useEffect(() => {
    if (
      state.conversationId &&
      state.status === 'ready' &&
      pendingMessageRef.current &&
      streamHook.sendMessage
    ) {
      const messageToSend = pendingMessageRef.current
      pendingMessageRef.current = ''
      streamHook.sendMessage({ text: messageToSend })
    }
  }, [state.conversationId, state.status, streamHook.sendMessage])

  // Refresh history only after a streaming turn finishes
  // This avoids re-entering 'loading-history' on initial mount or unrelated 'ready' transitions
  const wasStreamingRef = useRef<boolean>(false)
  useEffect(() => {
    // Track streaming -> ready transition
    if (streamHook.isStreaming) {
      wasStreamingRef.current = true
      return
    }
    if (wasStreamingRef.current && historyHook.refresh) {
      const timer = setTimeout(() => {
        historyHook.refresh?.()
      }, 200)
      wasStreamingRef.current = false
      return () => clearTimeout(timer)
    }
  }, [streamHook.isStreaming, historyHook.refresh])

  // Debug logging - enabled for debugging the loading issue
  useEffect(() => {
    console.log('🎯 [ChatPane] State update:', {
      status: state.status,
      conversationId: state.conversationId,
      messages: state.messages.length,
      historyMessages: historyHook.messages?.length ?? null,
      streamingMessages: streamHook.messages?.length ?? null,
      isStreaming: streamHook.isStreaming,
      historyIsLoading: historyHook.isLoading,
      historyError: historyHook.error,
    })
  }, [
    state.status,
    state.conversationId,
    state.messages.length,
    historyHook.messages,
    streamHook.messages,
    streamHook.isStreaming,
    historyHook.isLoading,
    historyHook.error,
  ])

  return (
    <ChatPaneContext.Provider value={{ state, actions }}>
      {/* Key by conversationId to reset stream bridge when conversation changes */}
      <div key={state.conversationId || 'no-conversation'}>{children}</div>
    </ChatPaneContext.Provider>
  )
}

// Hooks for consuming the context
export function useChatPaneState(): ChatPaneState {
  const context = useContext(ChatPaneContext)
  if (!context) {
    throw new Error('useChatPaneState must be used within a ChatPaneProvider')
  }
  return context.state
}

export function useChatPaneActions(): ChatPaneActions {
  const context = useContext(ChatPaneContext)
  if (!context) {
    throw new Error('useChatPaneActions must be used within a ChatPaneProvider')
  }
  return context.actions
}

export function useChatPane(): ChatPaneState & ChatPaneActions {
  const state = useChatPaneState()
  const actions = useChatPaneActions()
  return { ...state, ...actions }
}
