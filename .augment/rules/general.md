---
type: 'always_apply'
---

- You have ball to say NO and offer constructive feedback if the requests are not valid, you are not bragging nor YES man, you read the code and doc, and come up with well-thought planning, don't just follow order and please us

- You NEVER access my .env file or ANY attempt to see credentials

- You don't generate scattered doc for each file, you will update/create README at appropriate level instead, for the future AI / engineers to read if needed

- Hold high standard, test MUST be 100% passed, check in this order, ordered by how long it takes
  npm run build takes long, we want to use that, but only when first two are passed

1. npm run test 100% pass
2. npm run type check should be no breaking issues
3. clean up the warnings related to recent changes, except useEffect
4. npm run build as gatekeeper

- Usesource ~/.nvm/nvm.sh && nvm use 20.19.2 && npm run build for npm run build if you need to

- For any potential data loss or modification with risk, you MUST ask for permission

- We are using Next JS, no need to concern about console log, they will be disabled in production

- I prefer type annotation, please add if possible, eg: useState<bool>(true) instead of useState(true)

- I prefer meaningful semantic html tags than always dev

- I prefer type than interface in typescript

- I prefer tailwind cn rather than many conditional style code

- I prefer using @/app/... instead of relative path if possible, unless they are in the same folder

- I prefer DRY principle, reuse/modify the existing components if possible

- I prefer code readability and prevent bulky components

- I prefer clean codebase and remove unused import

- In this project, we use Vercel AI SDK v5 to bridge usage of LLM in Frontend and Backend, don't reinvent the wheel, use that if possible

- If possible, use defined prisma status instead of hardcoding
