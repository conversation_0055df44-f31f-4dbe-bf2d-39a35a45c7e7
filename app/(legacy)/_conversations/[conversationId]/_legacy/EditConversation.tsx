import { HiEllipsisHorizontal } from 'react-icons/hi2'
import { useState, useEffect } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'

interface EditConversationProps {
  initialTitle: string
  onSave: (title: string) => void
}

const EditConversation: React.FC<EditConversationProps> = ({
  initialTitle,
  onSave,
}) => {
  const [title, setTitle] = useState(initialTitle)

  useEffect(() => {
    setTitle(initialTitle)
  }, [initialTitle])

  const onClick = () => {
    onSave(title)
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <HiEllipsisHorizontal
          size={32}
          className="text-sky-500 cursor-pointer hover:text-sky-600 transition"
          title="Edit"
        />
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit conversation</DialogTitle>
          <DialogDescription>
            Make changes to your conversation here. Click &quot;Save
            changes&quot; when you are done.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">
              Title
            </Label>
            <Input
              id="name"
              value={title}
              className="col-span-3"
              onChange={e => setTitle(e.target.value)}
            />
          </div>
        </div>
        <DialogFooter>
          <Button type="submit" onClick={onClick}>
            Save changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default EditConversation
