/*
  Warnings:

  - The values [COMPLETED] on the enum `DragTreeNodeContentStatus` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "DragTreeNodeContentStatus_new" AS ENUM ('INITIALIZED', 'PROCESSING', 'ACTIVE', 'INACTIVE');
ALTER TABLE "drag_tree_node_content" ALTER COLUMN "status" DROP DEFAULT;
ALTER TABLE "drag_tree_node_content" ALTER COLUMN "status" TYPE "DragTreeNodeContentStatus_new" USING ("status"::text::"DragTreeNodeContentStatus_new");
ALTER TYPE "DragTreeNodeContentStatus" RENAME TO "DragTreeNodeContentStatus_old";
ALTER TYPE "DragTreeNodeContentStatus_new" RENAME TO "DragTreeNodeContentStatus";
DROP TYPE "DragTreeNodeContentStatus_old";
ALTER TABLE "drag_tree_node_content" ALTER COLUMN "status" SET DEFAULT 'INITIALIZED';
COMMIT;
