# TipTap Editor Components

This directory contains the enhanced TipTap editor components with comprehensive functionality including tables, mermaid diagrams, and **git diff-like suggestions**.

## 🆕 Key Features

### Git Diff-like Suggestions

- **Visual diff display**: Shows old text (red, strikethrough) and new text (green)
- **Individual accept/reject**: Each suggestion has ✓/✗ buttons
- **Bulk operations**: Accept or reject all suggestions at once
- **Multiple occurrences**: Handles multiple instances of the same text
- **Markdown export**: Exports original text (not suggestions) when copying
- **API integration**: Supports fetching suggestions from external APIs

### Table Support

- Interactive table creation and editing
- Contextual controls (blue arrows add, red buttons delete)
- Markdown table parsing and export
- Notion-style table interface

### Mermaid Diagrams

- Interactive mermaid diagram editor
- Live preview and editing
- Proper markdown export with \`\`\`mermaid wrapper
- Click-to-edit functionality

## 📁 Directory Structure

```
app/components/editor/
├── README.md                    # This file
├── TiptapQuickResearchEditor.tsx # Main editor component
├── BaseTiptapEditor.tsx         # Base editor implementation
├── components/
│   └── SuggestionNodeView.tsx   # Suggestion UI component
├── extensions/
│   ├── index.tsx                # Extension registry
│   ├── suggestion-extension.tsx # Suggestion node extension
│   ├── mermaid-extension-v2.tsx # Mermaid diagram extension
│   └── styles.scss              # Editor styles
├── utils/
│   └── suggestion-utils.ts      # Suggestion utility functions
└── plugins/                     # Additional editor plugins
```

## 🔧 Components

### TiptapQuickResearchEditor

Main editor component with all features enabled.

**Props:**

- `content: string` - Initial editor content
- `onContentChange: (content: string) => void` - Content change handler
- `onEditorReady?: (editor: Editor) => void` - Editor ready callback
- `placeholder?: string` - Placeholder text
- `className?: string` - CSS classes
- `autoFocus?: boolean` - Auto-focus on load

**Usage:**

```tsx
<TiptapQuickResearchEditor
  content={content}
  onContentChange={setContent}
  onEditorReady={handleEditorReady}
  placeholder="Start typing here..."
  className="min-h-[400px]"
/>
```

### SuggestionNodeView

React component that renders suggestion nodes with diff-like UI.

**Features:**

- Red strikethrough text for old content
- Green text for new content
- Accept/reject buttons with loading states
- Proper ProseMirror position handling

## 🛠 Extensions

### SuggestionExtension

Custom TipTap node extension for handling suggestions.

**Attributes:**

- `oldText: string` - Original text
- `newText: string` - Suggested replacement
- `id: string` - Unique suggestion identifier

**HTML Attributes:**

- `data-old` - Original text
- `data-new` - New text
- `data-id` - Suggestion ID

### Other Extensions

- **Mermaid Extension**: Interactive diagram editing
- **Table Extensions**: Enhanced table functionality
- **Slash Commands**: Quick insertion commands

## 🔧 Utility Functions

### Suggestion Utils (`utils/suggestion-utils.ts`)

**Core Functions:**

```typescript
// Create suggestions from text replacements
createSuggestionsFromText(editor: Editor, suggestions: SuggestionBatch): boolean

// Get all suggestions in document
getAllSuggestions(editor: Editor): SuggestionData[]

// Accept all suggestions
acceptAllSuggestions(editor: Editor): void

// Reject all suggestions
rejectAllSuggestions(editor: Editor): void

// API integration
applySuggestionsFromAPI(editor: Editor, content: string, apiEndpoint: string): Promise<boolean>
```

**Types:**

```typescript
type SuggestionData = {
  oldText: string
  newText: string
  id?: string
}

type SuggestionBatch = SuggestionData[]
```

## 🎯 Usage Examples

### Basic Suggestion Creation

```typescript
const suggestions: SuggestionBatch = [
  { oldText: 'Hello', newText: 'Hi' },
  { oldText: 'World', newText: 'Universe' },
]

createSuggestionsFromText(editor, suggestions)
```

### API Integration

```typescript
const success = await applySuggestionsFromAPI(
  editor,
  documentContent,
  'https://api.example.com/suggestions'
)
```

### Bulk Operations

```typescript
// Accept all suggestions
acceptAllSuggestions(editor)

// Reject all suggestions
rejectAllSuggestions(editor)

// Get suggestion count
const count = getAllSuggestions(editor).length
```

## 🧪 Testing

Comprehensive test suite available at `__tests__/components/editor/suggestion-utils.test.ts`

**Test Coverage:**

- ID generation and uniqueness
- Multiple occurrence handling
- Accept/reject functionality
- API integration
- Error handling
- Environment-specific behavior

**Run Tests:**

```bash
npm test -- __tests__/components/editor/suggestion-utils.test.ts
```

## 🎨 Styling

### CSS Classes

- `.suggestion-node` - Main suggestion container
- `.old-text` - Old text styling (red, strikethrough)
- `.new-text` - New text styling (green)
- `.accept-btn` - Accept button styling
- `.reject-btn` - Reject button styling

### Customization

Styles are defined in `extensions/styles.scss` and can be customized via Tailwind classes.

## 🔍 Debugging

### Console Logging

Development environment includes detailed logging:

- Suggestion creation counts
- Accept/reject operations
- API call results
- Error messages

### Browser Tools

- Suggestions have `data-suggestion-id` attributes for inspection
- Console logs show "🎯 [SuggestionUtils]" prefix for debugging

## 🚀 Demo

See the full functionality in action at `/citation-demo` page, which includes:

- Interactive suggestion creation
- Multiple occurrence testing
- API simulation
- Bulk operations
- Real-time suggestion counting

## 📝 Implementation Notes

### Position Handling

- Uses ProseMirror's position system for accurate text replacement
- Processes suggestions in reverse order to maintain correct positions
- Handles multiple occurrences within the same text node

### Performance

- Efficient bulk operations using two-pass approach
- Minimal DOM manipulation for smooth user experience
- Proper cleanup of processed suggestions

### Accessibility

- Button tooltips for accept/reject actions
- Semantic HTML structure
- Keyboard navigation support

## 🔄 Future Enhancements

Potential improvements for the suggestion system:

- Suggestion comments and annotations
- Collaborative suggestion workflows
- Suggestion history and undo
- Advanced filtering and search
- Custom suggestion types and categories

---

For implementation details and examples, see the demo at `/citation-demo` or examine the test suite for comprehensive usage patterns.
