// import { NextResponse } from "next/server";
// import prisma from "@/app/libs/prismadb";

// // Define the GET request handler function
// export async function POST(request: Request) {
//   try {
//     // Parse the request body as JSON
//     const body = await request.json();

//     // Extract the necessary fields from the request body
//     const { currentUser, conversationId } = body;
//     console.log("message/system/route.ts", body);

//     // Check if the user is authorized
//     if (!currentUser?.id || !currentUser?.email) {
//       return new NextResponse("Unauthorized", { status: 401 });
//     }

//     // Create a new message in the database using Prisma
//     const messages = await prisma.message.findMany({
//       where: {
//         conversation_id: conversationId,
//         // TODO: We don't have delete functionality yet, but we may
//         // we want to just hide the message instead of deleting it
//         is_hidden: false,
//         // We don't want to leak the system prompt to the user
//         creator_id: "system",
//       },
//       orderBy: {
//         created_at: "asc",
//       },
//     });

//     // find will get the first element that matches the condition
//     const systemMessage = messages.find(
//       (message) => message.creator_id === "system"
//     );

//     // Return the newly created message as a JSON response
//     return NextResponse.json(systemMessage);
//   } catch (error) {
//     console.error(error);

//     // If an error occurs, return a 500 status code with an "Error" message
//     return new NextResponse("Error", { status: 500 });
//   }
// }
