import * as fs from 'fs'
import * as path from 'path'
import { parse } from '@typescript-eslint/typescript-estree'
import { TSESTree } from '@typescript-eslint/typescript-estree'
import { EVENT_DEFINITIONS, type EventName } from '@/app/configs/logging-config'

/**
 * AST-based logging validation test
 *
 * This test parses TypeScript files and validates that:
 * 1. Events are actually called via logEventWithContext()
 * 2. Events are not commented out
 * 3. Events have proper function call structure
 */

function listAppFiles(root: string): string[] {
  const results: string[] = []
  const exts = new Set(['.ts', '.tsx'])
  const skipDirs = new Set([
    'node_modules',
    '.next',
    '.git',
    'dist',
    'build',
    'utils',
    'configs',
    '__tests__',
  ])

  const walk = (dir: string) => {
    let entries: fs.Dirent[]
    try {
      entries = fs.readdirSync(dir, { withFileTypes: true })
    } catch {
      return
    }
    for (const entry of entries) {
      const full = path.join(dir, entry.name)
      if (entry.isDirectory()) {
        if (skipDirs.has(entry.name)) continue
        walk(full)
      } else if (entry.isFile()) {
        const ext = path.extname(entry.name)
        if (exts.has(ext)) results.push(full)
      }
    }
  }

  walk(path.join(root, 'app'))
  return results
}

function findLogEventCalls(
  ast: TSESTree.Program,
  fileContent: string
): Array<{
  eventName: string
  line: number
  column: number
}> {
  const calls: Array<{ eventName: string; line: number; column: number }> = []
  const allEvents = Object.keys(EVENT_DEFINITIONS) as EventName[]

  function visit(node: TSESTree.Node) {
    if (
      node.type === 'CallExpression' &&
      node.callee.type === 'Identifier' &&
      node.callee.name === 'logEventWithContext'
    ) {
      // First argument should be the event name
      const firstArg = node.arguments[0]

      if (
        firstArg &&
        firstArg.type === 'Literal' &&
        typeof firstArg.value === 'string'
      ) {
        // Direct string literal
        calls.push({
          eventName: firstArg.value,
          line: firstArg.loc?.start.line || 0,
          column: firstArg.loc?.start.column || 0,
        })
      } else if (firstArg && firstArg.type === 'Identifier') {
        // Variable reference - need to trace back to find the value
        // For now, let's use a simpler approach: check if any event names appear in the file
        // and if logEventWithContext is called, assume they're connected
        const hasLogCall = fileContent.includes('logEventWithContext')
        if (hasLogCall) {
          for (const eventName of allEvents) {
            if (
              fileContent.includes(`'${eventName}'`) ||
              fileContent.includes(`"${eventName}"`)
            ) {
              calls.push({
                eventName,
                line: firstArg.loc?.start.line || 0,
                column: firstArg.loc?.start.column || 0,
              })
            }
          }
        }
      }
    }

    // Recursively visit child nodes
    for (const key in node) {
      const child = (node as any)[key]
      if (Array.isArray(child)) {
        child.forEach(item => {
          if (item && typeof item === 'object' && item.type) {
            visit(item)
          }
        })
      } else if (child && typeof child === 'object' && child.type) {
        visit(child)
      }
    }
  }

  visit(ast)
  return calls
}

describe('Logging events AST validation', () => {
  it('every defined event has proper logEventWithContext() calls (AST-based)', () => {
    const repoRoot = path.resolve(__dirname, '../..')
    const files = listAppFiles(repoRoot)

    // Exclude files that define events to avoid false positives
    const excluded = new Set([
      path.join(repoRoot, 'app/configs/logging-config.ts'),
      path.join(repoRoot, 'app/libs/logging.ts'),
      path.join(repoRoot, 'app/libs/logging-validation.ts'),
    ])

    const scanFiles = files.filter(f => !excluded.has(f))
    const allEvents = Object.keys(EVENT_DEFINITIONS) as EventName[]
    const foundEvents = new Set<string>()
    const eventLocations: Record<
      string,
      Array<{ file: string; line: number }>
    > = {}

    // Parse each file and look for logEventWithContext calls
    for (const file of scanFiles) {
      try {
        const content = fs.readFileSync(file, 'utf-8')

        // Skip files that don't contain logEventWithContext
        if (!content.includes('logEventWithContext')) continue

        const ast = parse(content, {
          loc: true,
          range: true,
          jsx: true,
        })

        const calls = findLogEventCalls(ast, content)

        for (const call of calls) {
          foundEvents.add(call.eventName)
          if (!eventLocations[call.eventName]) {
            eventLocations[call.eventName] = []
          }
          eventLocations[call.eventName].push({
            file: path.relative(repoRoot, file),
            line: call.line,
          })
        }
      } catch (error) {
        // Skip files that can't be parsed (might be malformed during development)
        console.warn(
          `Could not parse ${file}:`,
          error instanceof Error ? error.message : error
        )
      }
    }

    const missing = allEvents.filter(event => !foundEvents.has(event))

    if (missing.length > 0) {
      const details = missing.map(event => `- ${event}`).join('\n')
      throw new Error(
        `Events not found in logEventWithContext() calls:\n${details}\n\n` +
          `Found events:\n${Array.from(foundEvents)
            .map(e => `- ${e} (${eventLocations[e]?.length || 0} calls)`)
            .join('\n')}`
      )
    }

    // Log success info
    console.log(
      `✅ AST validation passed: ${foundEvents.size}/${allEvents.length} events found`
    )
    for (const event of allEvents) {
      const locations = eventLocations[event] || []
      console.log(`  - ${event}: ${locations.length} call(s)`)
    }

    expect(foundEvents.size).toBe(allEvents.length)
  })
})
