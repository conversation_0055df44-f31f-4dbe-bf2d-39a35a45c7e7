import { useReducer, useCallback, useRef, useEffect } from 'react'
import { TreeNode } from '@/app/types'
import { useNavigationStore } from '@/app/stores/navigation_store'
import { useUIStore } from '@/app/stores/ui_store'

// --- State and Actions for Reducer ---

type InteractionState = {
  isClicked: boolean
  isHovered: boolean
  isExpanding: boolean
  isPersistent: boolean
  lastExpansionTime: number
  lastClickTime: number
}

type Action =
  | { type: 'MOUSE_ENTER' }
  | { type: 'MOUSE_LEAVE' }
  | { type: 'NODE_CLICK_START' }
  | { type: 'NODE_CLICK_END' }
  | { type: 'EXPANSION_START'; time: number }
  | { type: 'EXPANSION_END' }
  | { type: 'SET_PERSISTENT'; persistent: boolean }

const initialState: InteractionState = {
  isClicked: false,
  isHovered: false,
  isExpanding: false,
  isPersistent: false,
  lastExpansionTime: 0,
  lastClickTime: 0,
}

const interactionReducer = (
  state: InteractionState,
  action: Action
): InteractionState => {
  switch (action.type) {
    case 'MOUSE_ENTER':
      return { ...state, isHovered: true }
    case 'MOUSE_LEAVE':
      return { ...state, isHovered: false }
    case 'NODE_CLICK_START':
      return { ...state, isClicked: true, lastClickTime: Date.now() }
    case 'NODE_CLICK_END':
      return { ...state, isClicked: false }
    case 'EXPANSION_START':
      return {
        ...state,
        isExpanding: true,
        lastExpansionTime: action.time,
        isPersistent: true,
      }
    case 'EXPANSION_END':
      return { ...state, isExpanding: false }
    case 'SET_PERSISTENT':
      return { ...state, isPersistent: action.persistent }
    default:
      return state
  }
}

// --- The Hook ---

export const useNodeInteraction = (
  node: TreeNode,
  isLoading: boolean,
  currentCollapsedNodes: Set<string>,
  updateCollapsedNodes: (nodes: Set<string>) => void
) => {
  const [state, dispatch] = useReducer(interactionReducer, initialState)
  const isHoverCollapseLocked = useUIStore(state => state.isHoverCollapseLocked)

  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const { navigateToNode } = useNavigationStore()
  const isCollapsed = currentCollapsedNodes.has(node.id)

  // When a node is collapsed externally (e.g. by a parent), it should lose its persistence.
  useEffect(() => {
    if (isCollapsed) {
      dispatch({ type: 'SET_PERSISTENT', persistent: false })
    }
  }, [isCollapsed])

  // Clean up timeouts on unmount
  useEffect(() => {
    return () => {
      const hoverTimeout = hoverTimeoutRef.current
      const hideTimeout = hideTimeoutRef.current
      if (hoverTimeout) clearTimeout(hoverTimeout)
      if (hideTimeout) clearTimeout(hideTimeout)
    }
  }, [])

  const handleMouseEnter = useCallback(() => {
    if (hideTimeoutRef.current) clearTimeout(hideTimeoutRef.current)
    dispatch({ type: 'MOUSE_ENTER' })

    // Removed hover-to-expand behavior - now only click expands
    // The hover state is maintained for visual feedback only
  }, [
    node,
    isCollapsed,
    isLoading,
    currentCollapsedNodes,
    updateCollapsedNodes,
  ])

  const handleMouseLeave = useCallback(() => {
    dispatch({ type: 'MOUSE_LEAVE' })
    if (hoverTimeoutRef.current) clearTimeout(hoverTimeoutRef.current)

    // Removed hover-to-collapse behavior - now only click collapses
    // The hover state is maintained for visual feedback only
  }, [
    node,
    isCollapsed,
    state.isPersistent,
    isLoading,
    currentCollapsedNodes,
    updateCollapsedNodes,
    isHoverCollapseLocked,
  ])

  const handleToggleCollapse = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation()
      const newCollapsedNodes = new Set(currentCollapsedNodes)
      if (isCollapsed) {
        newCollapsedNodes.delete(node.id)
        dispatch({ type: 'SET_PERSISTENT', persistent: true })
      } else {
        newCollapsedNodes.add(node.id)
        dispatch({ type: 'SET_PERSISTENT', persistent: false })
      }
      updateCollapsedNodes(newCollapsedNodes)
    },
    [currentCollapsedNodes, isCollapsed, node.id, updateCollapsedNodes]
  )

  const handleNodeClick = useCallback(
    (e: React.MouseEvent) => {
      if ((e.target as HTMLElement).closest('button') || isLoading) return
      const currentTime = Date.now()
      if (currentTime - state.lastClickTime < 300) return

      dispatch({ type: 'NODE_CLICK_START' })
      setTimeout(() => dispatch({ type: 'NODE_CLICK_END' }), 150)

      navigateToNode(node.id)

      if (node.type === 'category' && node.children.length > 0 && isCollapsed) {
        dispatch({ type: 'EXPANSION_START', time: Date.now() })

        const newCollapsedNodes = new Set(currentCollapsedNodes)
        newCollapsedNodes.delete(node.id)
        updateCollapsedNodes(newCollapsedNodes)

        setTimeout(() => dispatch({ type: 'EXPANSION_END' }), 500)
      }
    },
    [
      isLoading,
      node,
      navigateToNode,
      currentCollapsedNodes,
      isCollapsed,
      updateCollapsedNodes,
      state.lastClickTime,
    ]
  )

  return {
    ...state,
    handleNodeClick,
    handleToggleCollapse,
    handleMouseEnter,
    handleMouseLeave,
  }
}
