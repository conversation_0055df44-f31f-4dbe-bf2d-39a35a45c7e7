import { useState, useEffect, useCallback, useRef } from 'react'
import { getDragTree } from '@/app/server-actions/drag-tree'
import { checkTutorialStatusFromMetadata } from '@/app/(conv)/dragTree/[dragTreeId]/utils/tutorial-utils'
import { DragTreeStatus } from '@prisma/client'
import { useDragTreeStore } from '@/app/stores/dragtree_store'

export function useDragTreeLoader(
  dragTreeId: string | null,
  userId: string | undefined,
  initialTreeData?: any // Pre-fetched tree data from RSC
) {
  const {
    setFrontendTreeStructure,
    setScreeningQuestion,
    setDragTreeId,
    setDragTreeTitle,
    resetDragTreeData,
  } = useDragTreeStore()

  const hasInitialized = useRef<boolean>(false)
  const currentRequestId = useRef<string>('')

  const prevDragTreeId = useRef<string | null>(null)
  useEffect(() => {
    if (dragTreeId && dragTreeId !== prevDragTreeId.current) {
      console.log(
        '🔄 [useDragTreeLoader] Route changed, resetting store immediately to prevent mixed state'
      )
      resetDragTreeData()
      prevDragTreeId.current = dragTreeId
    }
  }, [dragTreeId, resetDragTreeData])

  const [isDragTreeLoading, setIsDragTreeLoading] =
    useState<boolean>(!initialTreeData) // Skip loading if we have initial data
  const [isGenerating, setIsGenerating] = useState<boolean>(false)
  const [errorMessage, setErrorMessage] = useState<string>('')

  // Tutorial state from user metadata (optimized - no separate API call)
  const [tutorialState, setTutorialState] = useState<{
    isCompleted: boolean
    isSkipped: boolean
    shouldShow: boolean
    checked: boolean
  }>({
    isCompleted: false,
    isSkipped: false,
    shouldShow: false,
    checked: false,
  })

  // Process initial tree data if provided (RSC pre-fetch optimization)
  const processTreeData = useCallback(
    (dragTree: any, userMetadata?: any) => {
      console.log(
        '✅ [useDragTreeLoader] Processing tree data with status:',
        dragTree.status
      )

      // Process user metadata for tutorial state (optimized approach)
      if (userMetadata) {
        console.log(
          '🔍 [useDragTreeLoader] User metadata received:',
          userMetadata
        )
        const tutorialStatus = checkTutorialStatusFromMetadata(userMetadata)
        if (tutorialStatus.success && tutorialStatus.data) {
          const { isCompleted, isSkipped, shouldShow } = tutorialStatus.data
          setTutorialState({
            isCompleted,
            isSkipped,
            shouldShow,
            checked: true,
          })
          console.log('✅ [useDragTreeLoader] Tutorial state:', {
            isCompleted,
            isSkipped,
            shouldShow,
          })
        }
      } else {
        console.log('⚠️ [useDragTreeLoader] No user metadata received')
        // If no metadata, assume tutorial should be shown
        setTutorialState({
          isCompleted: false,
          isSkipped: false,
          shouldShow: true,
          checked: true,
        })
      }

      setScreeningQuestion(
        dragTree.user_prompt || 'Clarification question not available.'
      )

      if (
        dragTree.status === DragTreeStatus.INITIALIZED ||
        dragTree.status === DragTreeStatus.GENERATING
      ) {
        setIsGenerating(true)
      } else if (dragTree.status === DragTreeStatus.ACTIVE) {
        setDragTreeId(dragTreeId!, userId!)
        setFrontendTreeStructure(dragTree)
        setDragTreeTitle(dragTree.title || null)
        setIsGenerating(false)
      } else {
        setErrorMessage(`Unhandled drag tree status: ${dragTree.status}`)
      }
    },
    [
      dragTreeId,
      userId,
      setDragTreeId,
      setFrontendTreeStructure,
      setScreeningQuestion,
      setDragTreeTitle,
    ]
  )

  const loadDragTree = useCallback(async () => {
    if (!dragTreeId || !userId) {
      setIsDragTreeLoading(false)
      if (!dragTreeId) {
        setErrorMessage(
          'No dragTreeId provided. Please start from the screening page.'
        )
      }
      return
    }

    const requestId = `${dragTreeId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    currentRequestId.current = requestId

    console.log(
      '🌳 [useDragTreeLoader] Loading drag tree:',
      dragTreeId,
      'with requestId:',
      requestId
    )
    setIsDragTreeLoading(true)
    setErrorMessage('')

    try {
      const result = await getDragTree(dragTreeId)

      if (currentRequestId.current !== requestId) {
        console.log(
          '🚫 [useDragTreeLoader] Ignoring stale response for:',
          dragTreeId,
          'requestId:',
          requestId
        )
        return
      }

      if (result.success && result.data) {
        processTreeData(result.data, result.userMetadata)
      } else {
        throw new Error(result.error || 'Failed to fetch drag tree.')
      }
    } catch (error) {
      if (currentRequestId.current !== requestId) {
        console.log(
          '🚫 [useDragTreeLoader] Ignoring stale error response for:',
          dragTreeId,
          'requestId:',
          requestId
        )
        return
      }

      console.error('❌ [useDragTreeLoader] Error loading drag tree:', error)
      setErrorMessage(
        error instanceof Error ? error.message : 'An unknown error occurred'
      )
    } finally {
      if (currentRequestId.current === requestId) {
        setIsDragTreeLoading(false)
      }
    }
  }, [dragTreeId, userId, processTreeData])

  // Initialize with pre-fetched data if available
  useEffect(() => {
    if (initialTreeData && dragTreeId && userId && !hasInitialized.current) {
      console.log(
        '⚡ [useDragTreeLoader] Using pre-fetched tree data (RSC optimization)'
      )
      hasInitialized.current = true
      processTreeData(initialTreeData, initialTreeData.userMetadata)
      setIsDragTreeLoading(false)
      return
    }

    if (userId && dragTreeId && !hasInitialized.current) {
      hasInitialized.current = true
      loadDragTree()
    }
  }, [userId, dragTreeId, initialTreeData, processTreeData, loadDragTree])

  return {
    isDragTreeLoading,
    isGenerating,
    errorMessage,
    tutorialState, // Return tutorial state (optimized)
    clearError: () => setErrorMessage(''),
    reloadDragTree: loadDragTree,
  }
}
