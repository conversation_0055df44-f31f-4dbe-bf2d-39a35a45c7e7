/**
 * Node Interaction State Management Hook
 *
 * Separates node interaction logic from UI components.
 * Handles:
 * - Hover state management
 * - Click state tracking
 * - Expansion logic (hover vs persistent)
 * - UI lock mechanisms
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { useUIStore } from '@/app/stores/ui_store'
import { TreeNode } from '@/app/types'

export type NodeInteractionState = {
  // Interaction state
  isHovered: boolean
  isClicked: boolean
  isExpanding: boolean
  isPersistent: boolean
  lastExpansionTime: number

  // Actions
  handleMouseEnter: () => void
  handleMouseLeave: () => void
  handleClick: (e: React.MouseEvent) => void
  handleToggleCollapse: (e: React.MouseEvent) => void
  setIsHovered: (hovered: boolean) => void
  setIsClicked: (clicked: boolean) => void
}

export type UseNodeInteractionsProps = {
  node: TreeNode
  collapsedNodes: Set<string>
  setCollapsedNodes: (nodes: Set<string>) => void
  onNodeClick?: (nodeId: string) => void
  expandDelayMs?: number
  collapseDelayMs?: number
}

/**
 * Hook to manage node interaction state separated from UI rendering
 */
export const useNodeInteractions = ({
  node,
  collapsedNodes,
  setCollapsedNodes,
  onNodeClick,
  expandDelayMs = 500,
  collapseDelayMs = 300,
}: UseNodeInteractionsProps): NodeInteractionState => {
  const { isHoverCollapseLocked } = useUIStore()

  // Interaction state
  const [isHovered, setIsHovered] = useState<boolean>(false)
  const [isClicked, setIsClicked] = useState<boolean>(false)
  const [isExpanding, setIsExpanding] = useState<boolean>(false)
  const [isPersistent, setIsPersistent] = useState<boolean>(
    !collapsedNodes.has(node.id)
  )
  const [lastExpansionTime, setLastExpansionTime] = useState<number>(0)

  // Timers for hover-based expansion/collapse
  const expandTimer = useRef<NodeJS.Timeout | null>(null)
  const collapseTimer = useRef<NodeJS.Timeout | null>(null)

  // Check if node has direct question children (for hover-expand logic)
  const hasDirectQuestionChildren =
    node.type === 'category' &&
    node.children.some(child => child.type === 'question')

  // Clear timers on unmount
  useEffect(() => {
    return () => {
      if (expandTimer.current) clearTimeout(expandTimer.current)
      if (collapseTimer.current) clearTimeout(collapseTimer.current)
    }
  }, [])

  // Update persistent state when collapsedNodes changes
  useEffect(() => {
    setIsPersistent(!collapsedNodes.has(node.id))
  }, [collapsedNodes, node.id])

  const clearTimers = useCallback(() => {
    if (expandTimer.current) {
      clearTimeout(expandTimer.current)
      expandTimer.current = null
    }
    if (collapseTimer.current) {
      clearTimeout(collapseTimer.current)
      collapseTimer.current = null
    }
  }, [])

  const handleMouseEnter = useCallback(() => {
    setIsHovered(true)
    clearTimers()

    // Only auto-expand categories with direct question children
    if (hasDirectQuestionChildren && collapsedNodes.has(node.id)) {
      setIsExpanding(true)
      expandTimer.current = setTimeout(() => {
        const newCollapsed = new Set(collapsedNodes)
        newCollapsed.delete(node.id)
        setCollapsedNodes(newCollapsed)
        setLastExpansionTime(Date.now())
        setIsExpanding(false)
      }, expandDelayMs)
    }
  }, [
    hasDirectQuestionChildren,
    collapsedNodes,
    node.id,
    setCollapsedNodes,
    expandDelayMs,
    clearTimers,
  ])

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false)
    clearTimers()

    // Don't collapse if:
    // 1. UI is locked (dropdown open, etc.)
    // 2. Expansion is persistent (clicked)
    // 3. Node doesn't have direct question children
    if (isHoverCollapseLocked || isPersistent || !hasDirectQuestionChildren) {
      return
    }

    // Only collapse if this was a hover expansion (not persistent)
    const timeSinceExpansion = Date.now() - lastExpansionTime
    const wasRecentHoverExpansion = timeSinceExpansion < 2000 // 2 second window

    if (wasRecentHoverExpansion && !collapsedNodes.has(node.id)) {
      collapseTimer.current = setTimeout(() => {
        const newCollapsed = new Set(collapsedNodes)
        newCollapsed.add(node.id)
        setCollapsedNodes(newCollapsed)
      }, collapseDelayMs)
    }
  }, [
    isHoverCollapseLocked,
    isPersistent,
    hasDirectQuestionChildren,
    lastExpansionTime,
    collapsedNodes,
    node.id,
    setCollapsedNodes,
    collapseDelayMs,
    clearTimers,
  ])

  const handleClick = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault()
      e.stopPropagation()

      setIsClicked(true)
      setTimeout(() => setIsClicked(false), 150) // Brief click feedback

      // If collapsed, expand persistently and navigate
      if (collapsedNodes.has(node.id)) {
        const newCollapsed = new Set(collapsedNodes)
        newCollapsed.delete(node.id)
        setCollapsedNodes(newCollapsed)
        setIsPersistent(true)
      }

      // Trigger navigation if callback provided
      onNodeClick?.(node.id)
    },
    [collapsedNodes, node.id, setCollapsedNodes, onNodeClick]
  )

  const handleToggleCollapse = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault()
      e.stopPropagation()

      clearTimers()

      const newCollapsed = new Set(collapsedNodes)
      const wasCollapsed = collapsedNodes.has(node.id)

      if (wasCollapsed) {
        newCollapsed.delete(node.id)
        setIsPersistent(true) // Mark as persistent when manually expanded
      } else {
        newCollapsed.add(node.id)
        setIsPersistent(false)
      }

      setCollapsedNodes(newCollapsed)
    },
    [collapsedNodes, node.id, setCollapsedNodes, clearTimers]
  )

  return {
    // State
    isHovered,
    isClicked,
    isExpanding,
    isPersistent,
    lastExpansionTime,

    // Actions
    handleMouseEnter,
    handleMouseLeave,
    handleClick,
    handleToggleCollapse,
    setIsHovered,
    setIsClicked,
  }
}
