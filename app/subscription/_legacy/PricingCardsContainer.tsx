import React from 'react'
import { PricingCard } from './PricingCard'

type PricingCardsContainerProps = {
  cardData: Array<{
    image: string
    title: string
    price_id: string
    price: string
    features: string[]
    show_button: boolean
  }>
  isSubscribed: boolean
  onSubscribe: (priceId: string) => void
}

export const PricingCardsContainer: React.FC<PricingCardsContainerProps> = ({
  cardData,
  isSubscribed,
  onSubscribe,
}) => (
  <div className="flex-grow flex items-center justify-center px-4">
    <div className="w-full max-w-5xl flex flex-wrap justify-center gap-4">
      {cardData.map((card, index) => (
        <div key={index} className="w-full sm:w-[calc(50%-1rem)] max-w-md flex">
          <div className="flex-1 flex">
            <PricingCard
              image={card.image}
              title={card.title}
              price={card.price}
              features={card.features}
              showButton={card.show_button}
              isSubscribed={isSubscribed}
              priceId={card.price_id}
              onSubscribe={onSubscribe}
            />
          </div>
        </div>
      ))}
    </div>
  </div>
)
