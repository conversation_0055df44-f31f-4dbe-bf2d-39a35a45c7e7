// // This API route is for generating analysis after they have completed a conversation

// import { NextResponse } from "next/server";
// import { createDraftType } from "@/app/types";
// import {
//   DraftStatus,
//   DraftType,
//   OpenAIUsageType,
//   UserStatus,
//   ConversationStatus,
//   IssueTreeStatus,
// } from "@prisma/client";
// import { Configuration, OpenAIApi } from "openai-edge";
// import { OpenAIStream, StreamingTextResponse } from "ai";
// import { logAPIUsage } from "@/app/api/utils";

// // IMPORTANT! Set the runtime to edge
// // ref: https://community.openai.com/t/api-504s-in-production-vercel-only/28795
// // Originally we tried to use vanilla OpenAI API, it works well in local but not working with Vercel
// // It is because Vercel serverless function for hobby plan only last for 10 seconds
// // While the API call to OpenAI takes longer than that [eg: one local run takes around 18 seconds]
// // So we need to switch to edge function since it can last longer
// export const runtime = "edge";

// export async function POST(request: Request) {
//   try {
//     const body = await request.json();
//     const { conversationId, issueTreeId, currentUser, summaryText } = body;
//     console.log("api/issuetree/final_doc:", body);

//     if (!currentUser?.id || !currentUser?.email) {
//       return new NextResponse("Unauthorized", { status: 400 });
//     }

//     // Block them from accessing API if they are not active
//     if (currentUser?.status !== UserStatus.ACTIVE) {
//       return new NextResponse("Unauthorized", { status: 401 });
//     }

//     const baseUrl =
//       process.env.NODE_ENV === "development"
//         ? "http://localhost:3000"
//         : process.env.NEXT_PUBLIC_SERVER_URL;

//     const configuration = new Configuration({
//       apiKey: process.env.OPENAI_API_KEY,
//     });
//     const openai = new OpenAIApi(configuration);

//     // Create the doc when API is completed
//     const createDraft = async (data: createDraftType) => {
//       try {
//         const response = await fetch(`${baseUrl}/api/drafts`, {
//           method: "POST",
//           headers: {
//             "Content-Type": "application/json",
//           },
//           body: JSON.stringify({
//             status: data.status,
//             type: data.type,
//             content: data.content,
//             // content is editable by user, original content should be immutable
//             // For potential change understanding
//             original_content: data.original_content,
//             currentUser: data.currentUser,
//             conversation_id: data.conversation_id,
//             prompt_id: data.prompt_id,
//             exact_prompt: data.exact_prompt,
//           }),
//         });

//         if (!response.ok) {
//           throw new Error("Server response: " + response.status);
//         }
//         await response.json();
//       } catch (error) {
//         console.error("Error in createDraft:", error);
//       }
//     };

//     // Update the conversation status
//     const markConversationStatus = async (
//       conversationId: string,
//       currentUser: any,
//       status: ConversationStatus
//     ) => {
//       console.log(
//         "markConversationStatus",
//         conversationId,
//         currentUser,
//         status
//       );
//       try {
//         const response = await fetch(
//           `${baseUrl}/api/conversations/${conversationId}`,
//           {
//             method: "POST",
//             headers: {
//               "Content-Type": "application/json",
//             },
//             body: JSON.stringify({
//               conversationId: conversationId,
//               currentUser: currentUser,
//               conversation_status: status,
//             }),
//           }
//         );

//         if (!response.ok) {
//           throw new Error("Server response: " + response.status);
//         }
//         await response.json();
//       } catch (error) {
//         console.error("Error in markConversationStatus:", error);
//       }
//     };

//     // Update the conversation status to COMPLETED in DB
//     const markIssueTreeStatus = async (
//       issueTreeId: string,
//       currentUser: any,
//       status: IssueTreeStatus
//     ) => {
//       try {
//         const response = await fetch(`${baseUrl}/api/issuetree/update`, {
//           method: "POST",
//           headers: {
//             "Content-Type": "application/json",
//           },
//           body: JSON.stringify({
//             issueTreeId: issueTreeId,
//             currentUser: currentUser,
//             status: status,
//           }),
//         });

//         if (!response.ok) {
//           throw new Error("Server response: " + response.status);
//         }
//         await response.json();
//       } catch (error) {
//         console.error("Error in markIssueTreeStatusCompleted:", error);
//       }
//     };

//     // Get the prompt from DB
//     const getPrompt = async (currentUser: any, id: string) => {
//       try {
//         const response = await fetch(`${baseUrl}/api/prompt`, {
//           method: "POST",
//           headers: {
//             "Content-Type": "application/json",
//           },
//           body: JSON.stringify({
//             currentUser: currentUser,
//             id: id,
//           }),
//         });

//         if (!response.ok) {
//           throw new Error("Server response: " + response.status);
//         }
//         const docPrompt = await response.json();
//         return docPrompt;
//       } catch (error) {
//         console.error("Error in getPrompt:", error);
//       }
//     };

//     // TODO: hardcode for the time being
//     const prompt_id = "doc_system_design_prompt";

//     const docPrompt = await getPrompt(currentUser, prompt_id);

//     // Construct the system message
//     const formattedSystemMessage = `
// ${docPrompt?.content}\n\n
// Context below: {${summaryText}}\n\n
// The context above is for your design reference, **you MUST strictly follow our example format and order above with this context to output and do not miss any details. Do not skip any section in the example**\n\n
// Key sections include, DO NOT MISS ANY: Requirement Summary, Terminology, Functional Requirements, Back-of-the-envelope Calculations, Architecture, Core Algorithms and Business Logic\n\n
// For # Core Algorithms and Business Logic, common practices part needs to follow the instruction to elaborate, **give full output even if they are similar to the example**\n\n
// Also, make sure the diagram plots come with \`\`\`mermaid\n\n
// Your next line starts with # Requirement Summary, after that, do the rest until # Conclusion
// `;
//     console.log("formattedSystemMessage:", formattedSystemMessage);

//     const model_name = "gpt-4o-mini";
//     console.log("api/issuetree/final_doc | model_name:", model_name);

//     const temperature = 0.5;

//     // Change it to feedback status, so even FE reload, it will show the feedback page
//     await markConversationStatus(
//       conversationId,
//       currentUser,
//       ConversationStatus.FEEDBACK
//     );

//     const response = await openai.createChatCompletion({
//       model: model_name,
//       stream: true,
//       messages: [{ role: "system", content: formattedSystemMessage }],
//       // Lower the temperature, hope the model align with our example more
//       temperature: temperature,
//     });

//     console.log("api/issuetree/final_doc | config:", model_name, temperature);

//     // Convert the response into a friendly text-stream
//     const stream = OpenAIStream(response, {
//       // Save the response
//       async onCompletion(completion) {
//         console.log(
//           "api/issuetree/final_doc | OpenAIStream:onCompletion",
//           completion
//         );

//         try {
//           await Promise.all([
//             logAPIUsage({
//               open_ai_usage_type: OpenAIUsageType.DRAFTED_DOCUMENT,
//               model_name: model_name,
//               input_text: JSON.stringify({
//                 role: "system",
//                 content: formattedSystemMessage,
//               }),
//               output_text: completion,
//               currentUser: currentUser,
//               conversationId: conversationId,
//             }),
//             createDraft({
//               status: DraftStatus.ACTIVE,
//               type: DraftType.BASE,
//               content: completion,
//               original_content: completion,
//               currentUser: currentUser,
//               conversation_id: conversationId,
//               prompt_id: prompt_id,
//               exact_prompt: formattedSystemMessage,
//             }),
//             markConversationStatus(
//               conversationId,
//               currentUser,
//               ConversationStatus.COMPLETED
//             ),
//             markIssueTreeStatus(
//               issueTreeId,
//               currentUser,
//               IssueTreeStatus.COMPLETED
//             ),
//           ]);
//           console.log(
//             "api/issuetree/final_doc: All tasks have been completed successfully"
//           );
//         } catch (error) {
//           // Change it back to ACTIVE to prevent stucking in FEEDBACK state forever
//           await markConversationStatus(
//             conversationId,
//             currentUser,
//             ConversationStatus.ACTIVE
//           );
//           console.error(
//             "api/issuetree/final_doc: An error occurred during the tasks:",
//             error
//           );
//         }
//       },
//     });

//     return new StreamingTextResponse(stream);
//     // return new NextResponse(formattedSystemMessage, { status: 200 });
//   } catch (error) {
//     console.error(error, "ERROR_MESSAGES");
//     return new NextResponse("Internal Error", { status: 500 });
//   }
// }
