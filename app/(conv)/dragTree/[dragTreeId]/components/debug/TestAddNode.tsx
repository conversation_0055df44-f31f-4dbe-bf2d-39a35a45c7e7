'use client'

import React from 'react'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { Button } from '@/components/ui/button'
import {
  TreeNodeType,
  DEFAULT_NODE_LABELS,
} from '@/app/(conv)/dragTree/[dragTreeId]/constants/node-types'

const TestAddNode: React.FC = () => {
  const frontendTreeStructure = useDragTreeStore(
    state => state.frontendTreeStructure
  )
  const addNode = useDragTreeStore(state => state.addNode)
  const pendingDatabaseOperations = useDragTreeStore(
    state => state.pendingDatabaseOperations
  )

  const handleAddCategory = () => {
    if (frontendTreeStructure) {
      addNode(frontendTreeStructure.id, TreeNodeType.CATEGORY)
    }
  }

  const handleAddQuestion = () => {
    if (frontendTreeStructure) {
      addNode(frontendTreeStructure.id, TreeNodeType.QUESTION)
    }
  }

  return (
    <div className="p-4 border rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold mb-4">Test Add Node</h3>

      <div className="space-y-2 mb-4">
        <Button onClick={handleAddCategory} className="mr-2">
          Add {DEFAULT_NODE_LABELS.NEW_CATEGORY}
        </Button>
        <Button onClick={handleAddQuestion}>
          Add {DEFAULT_NODE_LABELS.NEW_QUESTION}
        </Button>
      </div>

      <div className="text-sm text-gray-600">
        <div>
          <strong>Root Node ID:</strong> {frontendTreeStructure?.id || 'None'}
        </div>
        <div>
          <strong>Pending Operations:</strong>{' '}
          {pendingDatabaseOperations.size > 0
            ? Array.from(pendingDatabaseOperations).join(', ')
            : 'None'}
        </div>
        <div>
          <strong>Tree Children Count:</strong>{' '}
          {frontendTreeStructure?.children?.length || 0}
        </div>
      </div>
    </div>
  )
}

export default TestAddNode
