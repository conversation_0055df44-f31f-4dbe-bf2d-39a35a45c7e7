# DragTree Validation Test Suite

This directory contains comprehensive unit tests for the critical tree structure validation logic used in the DragTree system, particularly the Prisma middleware that ensures data consistency.

## Test Files Overview

### 🌳 `tree-validation.test.ts`

**Core validation utility functions**

Tests the pure utility functions that form the foundation of tree validation:

- `extractIdsFromTreeStructure()` - Extracts all node IDs from tree_structure JSON
- `validateTreeAlignmentRaw()` - Validates tree-node alignment

**Key Test Scenarios:**

- ✅ Simple and complex tree hierarchies
- ✅ Circular reference prevention
- ✅ Invalid input handling (null, undefined, malformed)
- ✅ Empty trees and single-node trees
- ✅ Size mismatches between tree structure and active nodes
- ✅ Integration simulation of middleware logic

### 🔧 `prisma-middleware.test.ts`

**Prisma middleware validation logic**

Tests the complete middleware validation workflow that runs on every DragTree/DragTreeNode mutation:

- ✅ Middleware trigger conditions (model filtering, action filtering)
- ✅ Tree ID resolution from different operation types
- ✅ Database transaction simulation
- ✅ Validation pass/fail scenarios
- ✅ Error message verification

**Critical Validation Cases:**

- **PASS**: Tree structure matches active nodes exactly
- **PASS**: tree_structure is null/undefined (bypass validation)
- **FAIL**: Active nodes contain orphaned IDs not in tree
- **FAIL**: Tree structure references non-existent active nodes
- **FAIL**: Size mismatches trigger sync errors

## What the Validation System Protects Against

### 1. **Orphaned Nodes**

Prevents nodes that exist in the database but aren't referenced in the tree structure:

```typescript
// ❌ This would be caught and prevented
activeNodes: ['root', 'child1', 'orphaned_node']
tree_structure: { root_id: 'root', hierarchy: { root: ['child1'] } }
```

### 2. **Dangling References**

Prevents tree structures that reference non-existent or inactive nodes:

```typescript
// ❌ This would be caught and prevented
activeNodes: ['root', 'child1']
tree_structure: { root_id: 'root', hierarchy: { root: ['child1', 'missing_node'] } }
```

### 3. **Circular References**

The ID extraction algorithm prevents infinite loops during tree traversal:

```typescript
// ✅ Handled gracefully - only visits each node once
hierarchy: { nodeA: ['nodeB'], nodeB: ['nodeA'] }
```

### 4. **Race Conditions**

Middleware runs in database transactions to prevent race conditions during concurrent mutations.

## Middleware Integration Points

The validation runs automatically on these Prisma operations:

- `DragTree`: create, update, delete operations
- `DragTreeNode`: create, update, delete operations

### Tree ID Resolution Logic:

1. **DragTree ops**: Extract from `args.where.id`
2. **DragTreeNode ops**: Extract from `args.data.drag_tree_id` or `args.where.drag_tree_id`
3. **Fallback**: Query existing node to find `drag_tree_id`

### Validation Workflow:

1. Check if operation should trigger validation
2. Resolve tree ID from operation parameters
3. Run database transaction to fetch tree structure + active nodes
4. Compare IDs using validation algorithms
5. Throw error if misalignment detected

## Error Messages

When validation fails, the middleware throws:

```
'Drag tree hierarchy and active node set are out of sync – mutation cancelled.'
```

This prevents data corruption and maintains referential integrity.

## Test Coverage

- **Tree Structure Parsing**: 8 test cases covering all edge cases
- **Alignment Validation**: 7 test cases covering pass/fail scenarios
- **Middleware Logic**: 13 test cases covering complete workflow
- **Integration**: 2 test cases simulating real-world scenarios

**Total: 30 comprehensive test cases** ensuring the validation system works correctly.

## Running the Tests

```bash
# Run all validation tests
npm run test -- __tests__/dragTree/utils/tree-validation.test.ts
npm run test -- __tests__/dragTree/utils/prisma-middleware.test.ts

# Run all dragTree utils tests
npm run test -- __tests__/dragTree/utils/
```

## Implementation Notes

- Tests use isolated functions to avoid next-auth import issues
- Prisma client is fully mocked to simulate database operations
- Test scenarios cover both happy path and error conditions
- Integration tests simulate the actual middleware workflow
- All tests are deterministic and run independently

This test suite ensures the critical data integrity validation system works correctly and prevents data corruption in production.
