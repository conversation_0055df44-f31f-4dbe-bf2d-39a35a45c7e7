import React from 'react'
import { GripVertical } from 'lucide-react'
import { NodeCardProps } from '@/app/(conv)/dragTree/[dragTreeId]/types'
import { cn } from '@/lib/utils'

const NodeCard: React.FC<NodeCardProps> = ({
  node: _node,
  children,
  isOpacityEnabled = false,
  isDraggable = true,
  dragAttributes,
  dragListeners,
  isDragging = false,
}) => {
  return (
    <div
      className={cn(
        'group flex items-start gap-1 rounded-lg bg-white/80 backdrop-blur-sm shadow-sm transition-all duration-200',
        isOpacityEnabled && 'opacity-50',
        {
          'shadow-lg scale-[1.02] bg-white': isDragging,
          'hover:bg-slate-50': !isDragging,
        }
      )}
    >
      {isDraggable && (
        <div
          {...dragAttributes}
          {...dragListeners}
          className="p-2 sm:p-1 self-stretch flex items-center justify-center text-slate-300 hover:text-slate-500 hover:bg-slate-100 rounded-md cursor-grab active:cursor-grabbing transition-colors min-w-[44px] sm:min-w-[32px] touch-manipulation"
          title="Drag to reorder"
        >
          <GripVertical className="h-5 w-5 sm:h-4 sm:w-4" />
        </div>
      )}
      <div className="flex-1 p-2 sm:p-1 pr-3 sm:pr-2">{children}</div>
    </div>
  )
}

// Memoize to prevent unnecessary re-renders when props haven't changed
export default React.memo(NodeCard)
