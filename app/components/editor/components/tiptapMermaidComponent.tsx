'use client'

import { Node<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NodeViewProps } from '@tiptap/react'
import React, { useState, useEffect, useRef } from 'react'
import Mermaid<PERSON><PERSON><PERSON><PERSON><PERSON>, {
  Mermaid<PERSON>hartRendererHandle,
} from '@/app/components/MermaidChartRenderer'
import { Button } from '@/components/ui/button'
import { toast } from 'react-hot-toast'
import { chatgptSiteUrl } from '@/app/configs'
import {
  ZoomIn,
  ZoomOut,
  RefreshC<PERSON>,
  Eye,
  EyeOff,
  Copy,
  ExternalLink,
  MessageCircle,
  Download,
} from 'lucide-react'

const MERMAID_DEFAULT_CODE = 'graph LR\n  MERMAID --> DEFAULT'

type TiptapMermaidComponentProps = NodeViewProps

// Very hacky helper function to extract text content from a node
// TODO: Find a better way to do this
// Basically I observed mermaid code exists under node.content initially, but attrs.code is used for ongoing editing
// So it checks node.attrs.code first, then node.content
function getMermaidCode(node: NodeViewProps['node']) {
  if (node && node.type.name === 'mermaidComponent') {
    // When the code is modified, it is under the node.attrs.code
    if (node.attrs.code !== null) {
      return node.attrs.code
    }
    // When the code is first rendered, they are not under the node.attrs.code
    // To be more specific, this is the moment when notebook.content is EMPTY in DB
    // At that moment, it parses the generation output and needs to get the code from node.content
    // After the first save [notebook.content is no longer empty], the code will be under node.attrs.code
    // Because of cleanJSON function in NotebookEditor.tsx, it moves the code from node.content to node.attrs.code
    // For ongoing editing, everything happens in node.attrs.codes
    // This is hacky, ideally when the code is first generated, it is under node.attrs.code
    // and we don't need to check both node.attrs.code and node.content
    if (node.content && node.content.size > 0) {
      // console.log(
      //   "Debug node.content",
      //   node.content.firstChild?.content.firstChild?.text
      // );
      return node.content.firstChild?.content.firstChild?.text || ''
    }
  }

  return null
}

export default function TiptapMermaidComponent({
  node,
  updateAttributes,
  selected,
}: TiptapMermaidComponentProps) {
  const parsedChartCode = getMermaidCode(node) || MERMAID_DEFAULT_CODE

  const [chartCode, setChartCode] = useState<string>(parsedChartCode)
  const [mermaidErrorMsg, setMermaidErrorMsg] = useState<string | null>(null)
  const [isContentVisible, setContentVisible] = useState<boolean>(false)
  const [zoomLevel, setZoomLevel] = useState<number>(1)

  const buttonRef = useRef<HTMLButtonElement>(null)
  const componentRef = useRef<HTMLDivElement>(null)
  const mermaidRef = useRef<MermaidChartRendererHandle>(null)

  useEffect(() => {
    if (mermaidErrorMsg) {
      setContentVisible(true)
    }
  }, [mermaidErrorMsg])

  const handleZoom = (action: 'in' | 'out' | 'reset') => {
    switch (action) {
      case 'in':
        setZoomLevel(prevZoom => Math.min(prevZoom + 0.1, 3))
        break
      case 'out':
        setZoomLevel(prevZoom => Math.max(prevZoom - 0.1, 0.5))
        break
      case 'reset':
        setZoomLevel(1)
        break
    }
  }

  const toggleContent = () => setContentVisible(prev => !prev)

  const copyToClipboard = (prefix: string = '') => {
    const clipboardMessage = prefix ? `${prefix}\n${chartCode}` : chartCode
    navigator.clipboard
      .writeText(clipboardMessage)
      .then(() => toast.success('Mermaid chart code is copied'))
      .catch(err => console.error('Could not copy text: ', err))
  }

  const openExternalLink = (url: string, prefix: string = '') => {
    copyToClipboard(prefix)
    setTimeout(() => window.open(url, '_blank'), 2000)
  }

  const handleExportSvg = () => {
    if (mermaidRef.current) {
      mermaidRef.current.exportSvg()
    } else {
      toast.error('Unable to export SVG.')
    }
  }

  const handleChartCodeChange = (
    event: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const newCode = event.target.value
    setChartCode(newCode)
    updateAttributes({ code: newCode })
    setMermaidErrorMsg(null) // Clear any previous error messages
  }

  return (
    <NodeViewWrapper
      className={`mermaid-component p-4 rounded-lg shadow-md w-full max-w-5xl mx-0 mb-4 ${
        selected ? 'bg-zinc-400 border-2 border-red-500' : 'bg-zinc-200'
      }`}
      ref={componentRef}
      tabIndex={0}
    >
      <div className="flex justify-between items-center mb-4">
        <div className="flex space-x-2">
          {[
            { action: 'in', icon: ZoomIn, title: 'Zoom In' },
            { action: 'out', icon: ZoomOut, title: 'Zoom Out' },
            { action: 'reset', icon: RefreshCw, title: 'Reset Zoom' },
          ].map(({ action, icon: Icon, title }) => (
            <Button
              key={action}
              onClick={() => handleZoom(action as 'in' | 'out' | 'reset')} // Wrapped in arrow function
              variant="outline"
              size="icon"
              title={title}
              className="bg-blue-100 hover:bg-blue-200"
              ref={action === 'reset' ? buttonRef : undefined}
            >
              <Icon className="h-4 w-4" />
            </Button>
          ))}
        </div>
        <div className="flex space-x-2">
          {[
            {
              onClick: () => copyToClipboard(), // Wrapped in arrow function
              icon: Copy,
              text: 'Copy',
              bgColor: 'bg-purple-100 hover:bg-purple-200',
            },
            {
              onClick: () => openExternalLink('https://mermaid.live/'), // Wrapped in arrow function
              icon: ExternalLink,
              text: 'Open in Mermaid Editor',
              bgColor: 'bg-yellow-100 hover:bg-yellow-200',
            },
            {
              onClick: () =>
                openExternalLink(
                  chatgptSiteUrl,
                  `Debug this:\n\n${mermaidErrorMsg}\n\n`
                ), // Wrapped in arrow function with parameters
              icon: MessageCircle,
              text: 'Debug with ChatGPT',
              bgColor: 'bg-green-100 hover:bg-green-200',
            },
            {
              onClick: toggleContent, // toggleContent matches MouseEventHandler signature
              icon: isContentVisible ? EyeOff : Eye,
              text: isContentVisible ? 'Hide Code' : 'Show Code',
              bgColor: 'bg-gray-100 hover:bg-gray-200',
            },
            {
              onClick: handleExportSvg, // handleExportSvg matches MouseEventHandler signature
              icon: Download,
              text: 'Export SVG',
              bgColor: 'bg-indigo-100 hover:bg-indigo-200',
            },
          ].map(({ onClick, icon: Icon, text, bgColor }, index) => (
            <Button
              key={index}
              onClick={onClick}
              variant="outline"
              size="icon"
              className={`${bgColor} group relative`}
              title={text}
              aria-label={text} // Added for accessibility
            >
              <Icon className="h-4 w-4" />
              <span className="absolute bottom-full left-1/2 transform -translate-x-1/2 bg-gray-600 text-zinc-100 text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                {text}
              </span>
            </Button>
          ))}
        </div>
      </div>

      <div
        className="bg-gray-50 p-4 rounded-lg mb-4"
        style={{
          transform: `scale(${zoomLevel})`,
          transformOrigin: 'top left',
        }}
      >
        {chartCode && !mermaidErrorMsg && (
          <MermaidChartRenderer
            ref={mermaidRef}
            chart={chartCode}
            onError={setMermaidErrorMsg}
          />
        )}
        {mermaidErrorMsg && (
          <div className="text-red-500 bg-red-100 p-4 rounded-lg">
            {mermaidErrorMsg}
          </div>
        )}
      </div>

      {isContentVisible && (
        <div className="my-4 bg-gray-100 border rounded-lg p-4 shadow-inner text-sm h-[20rem] overflow-y-auto font-mono relative">
          <textarea
            className="w-full h-full resize-none p-2 outline-none font-mono bg-transparent"
            value={chartCode}
            onChange={handleChartCodeChange}
            spellCheck={false}
            data-gramm="false"
          />
        </div>
      )}
    </NodeViewWrapper>
  )
}
