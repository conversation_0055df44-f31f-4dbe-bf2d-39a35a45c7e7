/**
 * Modern Pricing Configuration
 * Replaces Stripe metadata dependencies with controlled pricing data
 */

export type PricingTier = 'FREE' | 'PRO'

export type PricingFeature = {
  name: string
  included: boolean
  description?: string
}

export type PricingPlan = {
  id: PricingTier
  name: string
  description: string
  price: {
    monthly: number
    yearly?: number
    currency: string
  }
  features: PricingFeature[]
  popular?: boolean
  ctaText: string
  ctaVariant: 'default' | 'outline'
}

export const PRICING_CONFIG: Record<PricingTier, PricingPlan> = {
  FREE: {
    id: 'FREE',
    name: 'Free Plan',
    description: 'Perfect for getting started with basic features',
    price: {
      monthly: 0,
      currency: 'USD',
    },
    features: [
      { name: 'Up to 5 active drag trees', included: true },
      { name: 'Basic drag tree functionality', included: true },
      { name: 'Basic AI chat capabilities', included: true },
      { name: 'Search and filtering', included: true },
      { name: 'Community support', included: true },
      { name: 'Quick research', included: false, description: 'Pro feature' },
      {
        name: 'Advanced AI features',
        included: false,
        description: 'Pro feature',
      },
      { name: 'Priority support', included: false, description: 'Pro feature' },
      {
        name: 'Advanced analytics',
        included: false,
        description: 'Pro feature',
      },
    ],
    ctaText: 'Current Plan',
    ctaVariant: 'outline',
  },
  PRO: {
    id: 'PRO',
    name: 'Pro Plan',
    description: 'Unlock the full potential with advanced features',
    price: {
      monthly: 42,
      yearly: 420, // 2 months free (10 months * $42)
      currency: 'USD',
    },
    features: [
      { name: 'Unlimited active drag trees', included: true },
      { name: 'Advanced drag tree functionality', included: true },
      { name: 'Quick research capabilities', included: true },
      { name: 'Advanced AI chat & generation', included: true },
      { name: 'Priority support', included: true },
      { name: 'Advanced analytics & insights', included: true },
      { name: 'Export & sharing capabilities', included: true },
      { name: 'Custom integrations', included: true },
      { name: 'Early access to new features', included: true },
    ],
    popular: true,
    ctaText: 'Upgrade to Pro',
    ctaVariant: 'default',
  },
}

export const getPricingPlan = (tier: PricingTier): PricingPlan => {
  return PRICING_CONFIG[tier]
}

export const getAllPricingPlans = (): PricingPlan[] => {
  return Object.values(PRICING_CONFIG)
}

export const formatPrice = (
  amount: number,
  currency: string = 'USD'
): string => {
  if (amount === 0) return 'Free'

  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount)
}
