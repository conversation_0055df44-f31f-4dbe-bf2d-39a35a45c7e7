/**
 * Prisma Middleware Validation Tests
 * Testing the tree/node alignment validation logic in Prisma middleware
 */

// Mock Prisma client
const mockPrismaClient = {
  dragTree: {
    findUnique: jest.fn(),
  },
  dragTreeNode: {
    findUnique: jest.fn(),
    findMany: jest.fn(),
  },
  $transaction: jest.fn(),
}

// Recreate the middleware logic for testing
const middlewareExtractIds = (treeStructure: any): Set<string> => {
  if (!treeStructure || typeof treeStructure !== 'object') return new Set()
  const { root_id, hierarchy } = treeStructure as {
    root_id: string
    hierarchy: Record<string, string[]>
  }
  if (!root_id || !hierarchy) return new Set()
  const ids = new Set<string>()
  const stack: string[] = [root_id]
  while (stack.length) {
    const current = stack.pop() as string
    if (!ids.has(current)) {
      ids.add(current)
      const children = hierarchy[current] || []
      children.forEach(child => stack.push(child))
    }
  }
  return ids
}

const mutatingActions = new Set([
  'create',
  'createMany',
  'update',
  'updateMany',
  'delete',
  'deleteMany',
])

// Simulate the middleware validation logic
const simulateMiddlewareValidation = async (
  params: {
    model: string
    action: string
    args?: any
  },
  mockClient: any
): Promise<{ isValid: boolean; error?: string }> => {
  // Skip non-mutating operations
  if (
    !(params.model === 'DragTree' || params.model === 'DragTreeNode') ||
    !mutatingActions.has(params.action)
  ) {
    return { isValid: true }
  }

  let treeId: string | undefined
  if (params.model === 'DragTree') {
    treeId = params.args?.where?.id as string | undefined
  } else if (params.model === 'DragTreeNode') {
    treeId = (params.args?.data?.drag_tree_id ||
      params.args?.where?.drag_tree_id) as string | undefined
    if (!treeId && params.args?.where?.id) {
      const node = await mockClient.dragTreeNode.findUnique({
        where: { id: params.args.where.id as string },
        select: { drag_tree_id: true },
      })
      treeId = node?.drag_tree_id
    }
  }

  if (!treeId) return { isValid: true }

  const isValid = await mockClient.$transaction(async (tx: any) => {
    const [tree, activeNodes] = await Promise.all([
      tx.dragTree.findUnique({
        where: { id: treeId },
        select: { tree_structure: true },
      }),
      tx.dragTreeNode.findMany({
        where: { drag_tree_id: treeId, status: 'ACTIVE' },
        select: { id: true },
      }),
    ])
    if (!tree || !tree.tree_structure) return true
    const idsFromTree = middlewareExtractIds(tree.tree_structure)
    const activeIdSet = new Set<string>(activeNodes.map((n: any) => n.id))
    if (idsFromTree.size !== activeIdSet.size) return false
    for (const id of Array.from(idsFromTree)) {
      if (!activeIdSet.has(id)) return false
    }
    return true
  })

  if (!isValid) {
    return {
      isValid: false,
      error:
        'Drag tree hierarchy and active node set are out of sync – mutation cancelled.',
    }
  }

  return { isValid: true }
}

describe('Prisma Middleware Tree Validation', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Middleware Trigger Conditions', () => {
    it('should skip validation for non-DragTree models', async () => {
      const params = {
        model: 'User',
        action: 'update',
        args: { where: { id: 'user123' } },
      }

      const result = await simulateMiddlewareValidation(
        params,
        mockPrismaClient
      )
      expect(result.isValid).toBe(true)
      expect(mockPrismaClient.dragTree.findUnique).not.toHaveBeenCalled()
    })

    it('should skip validation for read-only operations', async () => {
      const params = {
        model: 'DragTree',
        action: 'findUnique',
        args: { where: { id: 'tree123' } },
      }

      const result = await simulateMiddlewareValidation(
        params,
        mockPrismaClient
      )
      expect(result.isValid).toBe(true)
      expect(mockPrismaClient.dragTree.findUnique).not.toHaveBeenCalled()
    })

    it('should trigger validation for DragTree mutations', async () => {
      const treeStructure = {
        root_id: 'root',
        hierarchy: { root: [] },
      }

      // Mock successful transaction
      mockPrismaClient.$transaction.mockImplementation(async callback => {
        const tx = {
          dragTree: {
            findUnique: jest.fn().mockResolvedValue({
              tree_structure: treeStructure,
            }),
          },
          dragTreeNode: {
            findMany: jest.fn().mockResolvedValue([{ id: 'root' }]),
          },
        }
        return await callback(tx)
      })

      const params = {
        model: 'DragTree',
        action: 'update',
        args: { where: { id: 'tree123' } },
      }

      const result = await simulateMiddlewareValidation(
        params,
        mockPrismaClient
      )
      expect(result.isValid).toBe(true)
      expect(mockPrismaClient.$transaction).toHaveBeenCalled()
    })
  })

  describe('Tree ID Resolution', () => {
    it('should extract treeId from DragTree operations', async () => {
      mockPrismaClient.$transaction.mockImplementation(async callback => {
        const tx = {
          dragTree: {
            findUnique: jest.fn().mockResolvedValue({ tree_structure: null }),
          },
          dragTreeNode: {
            findMany: jest.fn().mockResolvedValue([]),
          },
        }
        return await callback(tx)
      })

      const params = {
        model: 'DragTree',
        action: 'update',
        args: { where: { id: 'tree123' } },
      }

      await simulateMiddlewareValidation(params, mockPrismaClient)

      expect(mockPrismaClient.$transaction).toHaveBeenCalled()
    })

    it('should extract treeId from DragTreeNode data', async () => {
      mockPrismaClient.$transaction.mockImplementation(async callback => {
        const tx = {
          dragTree: {
            findUnique: jest.fn().mockResolvedValue({ tree_structure: null }),
          },
          dragTreeNode: {
            findMany: jest.fn().mockResolvedValue([]),
          },
        }
        return await callback(tx)
      })

      const params = {
        model: 'DragTreeNode',
        action: 'create',
        args: { data: { drag_tree_id: 'tree123' } },
      }

      await simulateMiddlewareValidation(params, mockPrismaClient)

      expect(mockPrismaClient.$transaction).toHaveBeenCalled()
    })

    it('should lookup treeId from existing node when not in args', async () => {
      // First call to find the node
      mockPrismaClient.dragTreeNode.findUnique.mockResolvedValue({
        drag_tree_id: 'tree123',
      })

      // Transaction call
      mockPrismaClient.$transaction.mockImplementation(async callback => {
        const tx = {
          dragTree: {
            findUnique: jest.fn().mockResolvedValue({ tree_structure: null }),
          },
          dragTreeNode: {
            findMany: jest.fn().mockResolvedValue([]),
          },
        }
        return await callback(tx)
      })

      const params = {
        model: 'DragTreeNode',
        action: 'update',
        args: { where: { id: 'node456' } },
      }

      await simulateMiddlewareValidation(params, mockPrismaClient)

      expect(mockPrismaClient.dragTreeNode.findUnique).toHaveBeenCalledWith({
        where: { id: 'node456' },
        select: { drag_tree_id: true },
      })
      expect(mockPrismaClient.$transaction).toHaveBeenCalled()
    })
  })

  describe('Validation Logic', () => {
    it('should pass validation when tree_structure is null/undefined', async () => {
      mockPrismaClient.$transaction.mockImplementation(async callback => {
        const tx = {
          dragTree: {
            findUnique: jest.fn().mockResolvedValue({ tree_structure: null }),
          },
          dragTreeNode: {
            findMany: jest
              .fn()
              .mockResolvedValue([{ id: 'node1' }, { id: 'node2' }]),
          },
        }
        return await callback(tx)
      })

      const params = {
        model: 'DragTree',
        action: 'update',
        args: { where: { id: 'tree123' } },
      }

      const result = await simulateMiddlewareValidation(
        params,
        mockPrismaClient
      )
      expect(result.isValid).toBe(true)
    })

    it('should pass validation when IDs perfectly align', async () => {
      const treeStructure = {
        root_id: 'root',
        hierarchy: {
          root: ['child1', 'child2'],
          child1: [],
          child2: [],
        },
      }

      mockPrismaClient.$transaction.mockImplementation(async callback => {
        const tx = {
          dragTree: {
            findUnique: jest
              .fn()
              .mockResolvedValue({ tree_structure: treeStructure }),
          },
          dragTreeNode: {
            findMany: jest
              .fn()
              .mockResolvedValue([
                { id: 'root' },
                { id: 'child1' },
                { id: 'child2' },
              ]),
          },
        }
        return await callback(tx)
      })

      const params = {
        model: 'DragTree',
        action: 'update',
        args: { where: { id: 'tree123' } },
      }

      const result = await simulateMiddlewareValidation(
        params,
        mockPrismaClient
      )
      expect(result.isValid).toBe(true)
    })

    it('should fail validation when active nodes are missing from tree', async () => {
      const treeStructure = {
        root_id: 'root',
        hierarchy: {
          root: ['child1'],
          child1: [],
        },
      }

      mockPrismaClient.$transaction.mockImplementation(async callback => {
        const tx = {
          dragTree: {
            findUnique: jest
              .fn()
              .mockResolvedValue({ tree_structure: treeStructure }),
          },
          dragTreeNode: {
            findMany: jest.fn().mockResolvedValue([
              { id: 'root' },
              { id: 'child1' },
              { id: 'orphaned_node' }, // Not in tree_structure
            ]),
          },
        }
        return await callback(tx)
      })

      const params = {
        model: 'DragTree',
        action: 'update',
        args: { where: { id: 'tree123' } },
      }

      const result = await simulateMiddlewareValidation(
        params,
        mockPrismaClient
      )
      expect(result.isValid).toBe(false)
      expect(result.error).toBe(
        'Drag tree hierarchy and active node set are out of sync – mutation cancelled.'
      )
    })

    it('should fail validation when tree references missing active nodes', async () => {
      const treeStructure = {
        root_id: 'root',
        hierarchy: {
          root: ['child1', 'child2'],
          child1: [],
          child2: [],
        },
      }

      mockPrismaClient.$transaction.mockImplementation(async callback => {
        const tx = {
          dragTree: {
            findUnique: jest
              .fn()
              .mockResolvedValue({ tree_structure: treeStructure }),
          },
          dragTreeNode: {
            findMany: jest.fn().mockResolvedValue([
              { id: 'root' },
              { id: 'child1' },
              // child2 is missing from active nodes
            ]),
          },
        }
        return await callback(tx)
      })

      const params = {
        model: 'DragTree',
        action: 'update',
        args: { where: { id: 'tree123' } },
      }

      const result = await simulateMiddlewareValidation(
        params,
        mockPrismaClient
      )
      expect(result.isValid).toBe(false)
      expect(result.error).toBe(
        'Drag tree hierarchy and active node set are out of sync – mutation cancelled.'
      )
    })
  })

  describe('Edge Cases', () => {
    it('should handle empty tree structures', async () => {
      const treeStructure = {
        root_id: 'root',
        hierarchy: { root: [] },
      }

      mockPrismaClient.$transaction.mockImplementation(async callback => {
        const tx = {
          dragTree: {
            findUnique: jest
              .fn()
              .mockResolvedValue({ tree_structure: treeStructure }),
          },
          dragTreeNode: {
            findMany: jest.fn().mockResolvedValue([{ id: 'root' }]),
          },
        }
        return await callback(tx)
      })

      const params = {
        model: 'DragTree',
        action: 'update',
        args: { where: { id: 'tree123' } },
      }

      const result = await simulateMiddlewareValidation(
        params,
        mockPrismaClient
      )
      expect(result.isValid).toBe(true)
    })

    it('should handle operations without treeId', async () => {
      const params = {
        model: 'DragTreeNode',
        action: 'create',
        args: { data: {} }, // No drag_tree_id
      }

      const result = await simulateMiddlewareValidation(
        params,
        mockPrismaClient
      )
      expect(result.isValid).toBe(true)
      expect(mockPrismaClient.$transaction).not.toHaveBeenCalled()
    })

    it('should handle complex hierarchies correctly', async () => {
      const treeStructure = {
        root_id: 'cat_main',
        hierarchy: {
          cat_main: ['que_q1', 'cat_sub1'],
          que_q1: ['cat_sub2'],
          cat_sub1: ['que_q2'],
          cat_sub2: ['que_q3'],
          que_q2: [],
          que_q3: [],
        },
      }

      mockPrismaClient.$transaction.mockImplementation(async callback => {
        const tx = {
          dragTree: {
            findUnique: jest
              .fn()
              .mockResolvedValue({ tree_structure: treeStructure }),
          },
          dragTreeNode: {
            findMany: jest
              .fn()
              .mockResolvedValue([
                { id: 'cat_main' },
                { id: 'que_q1' },
                { id: 'cat_sub1' },
                { id: 'cat_sub2' },
                { id: 'que_q2' },
                { id: 'que_q3' },
              ]),
          },
        }
        return await callback(tx)
      })

      const params = {
        model: 'DragTree',
        action: 'update',
        args: { where: { id: 'tree123' } },
      }

      const result = await simulateMiddlewareValidation(
        params,
        mockPrismaClient
      )
      expect(result.isValid).toBe(true)
    })
  })
})
