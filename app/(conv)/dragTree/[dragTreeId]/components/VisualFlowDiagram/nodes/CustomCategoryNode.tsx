import React from 'react'
import { NodeProps, <PERSON>le, Position, useReactFlow } from 'reactflow'
import { useNavigationStore } from '@/app/stores/navigation_store'
import { useSelectionStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useSelectionStore'
import { DEFAULT_NODE_LABELS } from '@/app/(conv)/dragTree/[dragTreeId]/constants/node-types'
import { cn } from '@/lib/utils'
import { useNodeSelection } from '@/app/(conv)/dragTree/[dragTreeId]/hooks/useNodeSelection'
import { useDragTreeStore } from '@/app/stores/dragtree_store'

/**
 * Custom Category Node component for React Flow
 * Displays category nodes with modern styling and targeting effects
 */
const handleStyle = {
  width: 10,
  height: 10,
  background: '#a8b3cf',
  border: '2px solid white',
}

const CustomCategoryNode: React.FC<NodeProps> = ({ data, id }) => {
  const { targetNodeId, navigateToTreeNodeFromReactFlow } = useNavigationStore()
  const { fitView } = useReactFlow()
  const isTargeted = targetNodeId === id

  // Check if this node is selected
  const selectedNodeIds = useSelectionStore(state => state.selectedNodeIds)
  const isSelected = selectedNodeIds.has(id)

  // Check if this is a new node that hasn't been renamed
  const isNewNode =
    data.label === DEFAULT_NODE_LABELS.NEW_CATEGORY ||
    data.label === DEFAULT_NODE_LABELS.NEW_QUESTION

  const isSelectionMode = useSelectionStore(state => state.isSelectionMode)
  const setSelectedNodes = useSelectionStore(state => state.setSelectedNodes)
  const { getDescendantNodeIds } = useNodeSelection()

  // Handle node click to navigate to outline and focus in React Flow
  const handleNodeClick = (_e: React.MouseEvent) => {
    console.log(`🖱️ React Flow Category Node clicked: ${id}`)

    // If we are in selection mode, treat this as a selection action instead of navigation only
    if (isSelectionMode) {
      // Fetch the freshest data directly from the store (avoids stale closure issues)
      const {
        nodeContent: latestNodeContent,
        researchedNodeIds: latestResearchedIds,
      } = useDragTreeStore.getState()

      const isResearchedNow = (nodeId: string): boolean => {
        const hasContent =
          latestNodeContent.has(nodeId) &&
          latestNodeContent.get(nodeId)!.size > 0
        const isMarked = latestResearchedIds.has(nodeId)
        return hasContent || isMarked
      }

      // Build cumulative selection sets so we don't overwrite prior rectangle selections
      const currentSelected = new Set(
        Array.from(useSelectionStore.getState().selectedNodeIds)
      )
      const currentNonResearched = new Set(
        Array.from(useSelectionStore.getState().nonResearchedNodeIds)
      )

      // Helper to add an id to the appropriate set
      const addToSets = (nodeId: string) => {
        if (isResearchedNow(nodeId)) {
          currentSelected.add(nodeId)
        } else {
          currentNonResearched.add(nodeId)
        }
      }

      // Category node itself (always non-researched per business rule)
      currentNonResearched.add(id)

      // Add all researched descendants
      const descendants = getDescendantNodeIds(id)
      descendants.forEach(addToSets)

      // Persist back to store
      setSelectedNodes(
        Array.from(currentSelected),
        Array.from(currentNonResearched)
      )

      // Early return – don't trigger navigation when selecting
      return
    }

    // -------------------------------------------
    // Normal navigation behaviour when NOT selecting
    // -------------------------------------------

    // Focus on the clicked node in React Flow
    // Use same zoom parameters as outline-to-React Flow navigation for consistency
    fitView({
      nodes: [{ id }],
      duration: 300,
      padding: 0.6, // Increased from 0.3 to show more surrounding context
      maxZoom: 0.4, // Reduced from 1.2 to match outline-to-React Flow zoom level
    })

    // Navigate to tree outline
    navigateToTreeNodeFromReactFlow(id)
  }

  return (
    <div className="relative">
      {/* Modern Category Card - PERFORMANCE OPTIMIZED */}
      <div
        className={cn(
          'px-6 py-5 rounded-2xl border-2 shadow-lg relative overflow-visible transition-all duration-300 min-w-[280px] w-fit max-w-[450px] cursor-pointer hover:scale-105 hover:shadow-xl',
          {
            // Selected nodes - bright blue border and glow
            'ring-4 ring-blue-400 ring-opacity-75 border-blue-500 shadow-blue-300/50':
              isSelected,
            'bg-gradient-to-br from-rose-100 via-pink-50 to-orange-100 border-rose-300 shadow-rose-200/60 hover:shadow-rose-300/80':
              isNewNode && !isSelected,
            'bg-gradient-to-br from-indigo-100 via-purple-100 to-pink-100 border-indigo-400 ring-2 ring-indigo-300 hover:from-indigo-200 hover:via-purple-200 hover:to-pink-200':
              !isNewNode && isTargeted && !isSelected,
            'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 border-blue-300 shadow-blue-200/50 hover:border-blue-400 hover:shadow-blue-300/70 hover:from-blue-100 hover:via-indigo-100 hover:to-purple-100':
              !isNewNode && !isTargeted && !isSelected,
          }
        )}
        onClick={handleNodeClick}
      >
        {/* Handles for all four positions */}
        <Handle
          type="source"
          position={Position.Top}
          id="top"
          style={handleStyle}
        />
        <Handle
          type="source"
          position={Position.Right}
          id="right"
          style={handleStyle}
        />
        <Handle
          type="source"
          position={Position.Bottom}
          id="bottom"
          style={handleStyle}
        />
        <Handle
          type="source"
          position={Position.Left}
          id="left"
          style={handleStyle}
        />
        <Handle
          type="target"
          position={Position.Top}
          id="top"
          style={handleStyle}
        />
        <Handle
          type="target"
          position={Position.Right}
          id="right"
          style={handleStyle}
        />
        <Handle
          type="target"
          position={Position.Bottom}
          id="bottom"
          style={handleStyle}
        />
        <Handle
          type="target"
          position={Position.Left}
          id="left"
          style={handleStyle}
        />

        {/* Node Content - CATEGORY STYLE OPTIMIZED */}
        <div className="flex flex-col gap-3 items-center text-center">
          <span className="text-xs font-mono text-slate-500 bg-slate-50 px-3 py-1.5 rounded-full border border-slate-300 font-semibold">
            {id}
          </span>
          <span
            className={cn(
              'font-black text-lg leading-snug transition-colors duration-200 tracking-wide break-words hyphens-auto text-center',
              {
                'text-rose-700': isNewNode,
                'text-indigo-700': !isNewNode && isTargeted,
                'text-slate-800': !isNewNode && !isTargeted,
              }
            )}
          >
            {data.label}
          </span>
        </div>
      </div>

      {/* Subtle overflow indicator - makes it look intentional */}
      {data.label && data.label.length > 20 && (
        <div className="absolute -top-1 -right-1 w-2 h-2 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-full shadow-sm animate-pulse"></div>
      )}
    </div>
  )
}

// Memoize to prevent unnecessary re-renders when data hasn't changed
export default React.memo(CustomCategoryNode)
