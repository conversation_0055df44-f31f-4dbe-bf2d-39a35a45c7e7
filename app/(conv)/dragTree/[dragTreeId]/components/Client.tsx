'use client'

/**
 * Main Client Component for DragTree System
 *
 * This is the primary orchestrator component that manages the entire dragTree interface.
 * It handles different application states and coordinates between:
 * - Initial tree loading (useDragTreeLoader)
 * - AI generation streaming (useCompletion)
 * - Sub-tree node generation (useNodeGeneration)
 * - State management (useDragTreeStore)
 *
 * State Flow:
 * 1. LOADING: Initial page load, fetching tree data
 * 2. GENERATING: Initial AI tree generation with streaming UI
 * 3. ERROR: Any errors during loading/generation
 * 4. MAIN_CONTENT: Normal interactive tree interface
 *
 * Key Integration Points:
 * - Session management for authentication
 * - Store integration for tree state
 * - API streaming for AI generation
 * - Error handling and recovery
 */

import React, { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { useSearchParams } from 'next/navigation'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { useDragTreeLoader } from '@/app/(conv)/dragTree/[dragTreeId]/hooks/useDragTreeLoader'
import {
  markTutorialCompleted,
  markTutorialSkipped,
} from '@/app/(legacy)/_server-actions/user'
import {
  ErrorState,
  LoadingState,
  MainContent,
} from '@/app/(conv)/dragTree/[dragTreeId]/components/states'
import FuturisticLoadingDemo from './FuturisticLoadingDemo'
// import { useNodeGeneration } from '@/app/hooks/useNodeGeneration'
import { useChat } from '@ai-sdk/react'
import { DefaultChatTransport } from 'ai'
import type { Msg } from '@/app/types/ai-sdk5'
import { extractTextContent } from '@/app/types/ai-sdk5'
import type { AIGenerationMeta } from '@/app/types/ai-generation'
import { useAssetStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useAssetStore'
import { useTabStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useTabStore'

type DragTreeClientProps = {
  dragTreeId?: string
  initialTreeData?: any // Pre-fetched tree data from RSC
  initialGenerations?: AIGenerationMeta[]
}

export default function DragTreeClient({
  dragTreeId: propDragTreeId,
  initialTreeData,
  initialGenerations = [],
}: DragTreeClientProps) {
  const { data: session } = useSession()
  const searchParams = useSearchParams()
  // Optimized: Use individual selector to prevent unnecessary re-renders
  const frontendTreeStructure = useDragTreeStore(
    state => state.frontendTreeStructure
  )

  const [showSyncTest, setShowSyncTest] = useState<boolean>(false)

  // Local state to immediately hide tutorial after user actions
  const [tutorialHidden, setTutorialHidden] = useState<boolean>(false)

  // Manual override to show tutorial even if completed
  const [tutorialManuallyShown, setTutorialManuallyShown] =
    useState<boolean>(false)

  // Prefill asset store with server-prefetched generations for instant UI
  const bulkLoadAssets = useAssetStore(state => state.bulkLoadAssets)
  const addAsset = useAssetStore(state => state.addAsset)
  const assets = useAssetStore(state => state.assets)

  // Tab validation after assets are loaded
  const validateAndCleanupTabs = useTabStore(
    state => state.validateAndCleanupTabs
  )

  const dragTreeId = propDragTreeId || searchParams.get('dragTreeId')

  // Loader that fetches tree structure, tutorial state, etc.
  const {
    isDragTreeLoading,
    isGenerating,
    errorMessage,
    tutorialState, // Optimized tutorial state from single query
    clearError,
    reloadDragTree,
  } = useDragTreeLoader(dragTreeId, session?.user?.id, initialTreeData)

  /**
   * Prefetch chat conversation metadata (ai_conversation table) so they show up
   * in the Asset sidebar the moment the page renders. We only need the basic
   * metadata – the Chat tab will lazy-load messages when the user opens it.
   */
  useEffect(() => {
    async function loadChatAssets() {
      if (!dragTreeId) return

      try {
        const url = new URL('/api/aipane/conversations', window.location.origin)
        url.searchParams.set('contextEntityType', 'drag_tree')
        url.searchParams.set('contextEntityId', dragTreeId)

        const res = await fetch(url.toString())
        if (!res.ok) {
          console.warn('[Client] Failed to fetch conversations:', res.status)
          return
        }

        const data = await res.json()
        const conversations: Array<{
          id: string
          title: string | null
          createdAt: string
          updatedAt: string
        }> = data.conversations || []

        if (conversations.length === 0) return

        const existingAssets = useAssetStore.getState().assets.map(a => a.id)

        // Parallel loading: Create all asset objects first, then add them in parallel
        const chatAssets = conversations
          .filter(conv => !existingAssets.includes(conv.id))
          .map(conv => ({
            id: conv.id,
            title: conv.title || 'Untitled', // Remove prefix, use backend title or simple fallback
            content: '', // Chat assets rely on messages array
            type: 'chat',
            model: 'gpt-4.1', // Unknown – refined when opened
            prompt: '',
            contextIds: [],
            dragTreeId: dragTreeId,
            persistedInDb: true,
            isContentLoaded: true,
            createdAt: new Date(conv.createdAt),
            updatedAt: new Date(conv.updatedAt),
            viewed: true, // Historical chats are treated as read
          }))

        // Add all chat assets in parallel (much faster than sequential forEach)
        await Promise.all(chatAssets.map(asset => addAsset(asset)))

        console.log(
          `⚡ [Client] Loaded ${chatAssets.length} chat assets in parallel`
        )
      } catch (err) {
        console.error('[Client] Error loading chat assets:', err)
      }
    }

    loadChatAssets()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dragTreeId])

  // Load generation assets in parallel with chat assets (triggered by same dragTreeId dependency)
  useEffect(() => {
    if (initialGenerations.length > 0 && dragTreeId) {
      bulkLoadAssets(initialGenerations, dragTreeId)
      console.log(
        '🚀 [Client] Generation assets loading in parallel with chat assets'
      )
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dragTreeId]) // Changed from [] to [dragTreeId] to run in parallel with chat loading

  // Track if initial assets have been loaded to prevent premature validation
  const [initialAssetsLoaded, setInitialAssetsLoaded] = useState(false)

  // Mark initial assets as loaded after server data is processed
  useEffect(() => {
    if (
      initialGenerations.length > 0 &&
      assets.length > 0 &&
      !initialAssetsLoaded
    ) {
      // Wait a bit to ensure bulkLoadAssets has completed
      const timeoutId = setTimeout(() => {
        setInitialAssetsLoaded(true)
        console.log('✅ [Client] Initial assets loaded, validation enabled')
      }, 200)
      return () => clearTimeout(timeoutId)
    } else if (initialGenerations.length === 0 && !initialAssetsLoaded) {
      // No initial generations, enable validation immediately
      setInitialAssetsLoaded(true)
    }
  }, [assets, initialGenerations, initialAssetsLoaded])

  // Validate tabs after assets are loaded (with debounce to prevent excessive validation)
  useEffect(() => {
    if (assets.length > 0 && dragTreeId && initialAssetsLoaded) {
      const timeoutId = setTimeout(() => {
        // Filter assets for current drag tree
        const currentTreeAssets = assets.filter(
          asset =>
            asset.dragTreeId === dragTreeId || asset.dragTreeId === 'current'
        )

        // Only validate if we have assets to validate against
        if (currentTreeAssets.length > 0) {
          console.log(
            `🔍 [Client] Validating tabs against ${currentTreeAssets.length} available assets`
          )
          validateAndCleanupTabs(currentTreeAssets)
        }
      }, 100) // Small debounce to batch rapid asset updates

      return () => clearTimeout(timeoutId)
    }
  }, [assets, dragTreeId, validateAndCleanupTabs, initialAssetsLoaded])

  // ------------------------------------------------------------
  // 🔥 API Priming
  // Fire a background request on first mount to warm up the
  // serverless function behind `/api/dragtree/content`. This
  // dramatically reduces the perceived latency of the first
  // QuickResearchPreviewButton hover/click by mitigating cold
  // starts. We purposely ignore errors and the response body – the
  // sole purpose is to trigger the route.
  // ------------------------------------------------------------
  useEffect(() => {
    // Guard against running in RSC environments
    if (typeof window === 'undefined') return

    // `prime=true` signals a warm-up request – the route will
    // respond with a 400 status because `contentId` is missing, but
    // that is sufficient to compile & warm the Lambda/Edge worker.
    fetch('/api/dragtree/content?prime=true').catch(() => {
      // Silently ignore – warm-up only
    })
  }, [])

  const onToggleSyncTest = useCallback(() => {
    setShowSyncTest(prev => !prev)
  }, [])

  // Track which tree the global store currently represents so we can avoid showing
  // a stale tree briefly during route transitions. The store is global, so on a
  // fast route change it can still contain the *previous* tree for a split
  // second before our loader finishes resetting & loading the new one. By
  // verifying the id matches we render a loading state instead of the stale
  // content, eliminating the noticeable "new → old → new" flicker the user
  // reported.
  const storeDragTreeId = useDragTreeStore(state => state.dragTreeId)

  // The tree is considered ready for display only when:
  // 1. The loader is *not* indicating a loading state AND
  // 2. The global store already holds the structure for *this* dragTreeId.
  const isTreeReadyForDisplay =
    !isDragTreeLoading &&
    frontendTreeStructure !== null &&
    storeDragTreeId === dragTreeId

  // Reset tutorial and generation states when dragTreeId changes to prevent state leakage
  useEffect(() => {
    console.log(
      '🔄 [Client] Route changed, resetting tutorial and generation states to prevent mixed state'
    )
    setTutorialHidden(false)
    setTutorialManuallyShown(false)
    setHasRequestedGeneration(false)
  }, [dragTreeId])

  // Hook for sub-tree generations (expand nodes, generate similar questions/categories)
  // This is separate from initial tree generation
  // const { loadingState, handlers } = useNodeGeneration({
  //   frontendTreeStructure,
  //   onLoadingChange: () => {},
  //   onGeneratingNodeChange: () => {},
  // })

  // AI SDK 5 streaming hook for initial tree generation
  // Uses /api/dragtree/generate_questions for streaming markdown response
  // Using 200ms throttle to reduce UI re-renders during streaming
  const chat = useChat<Msg>({
    transport: new DefaultChatTransport({
      api: '/api/dragtree/generate_questions',
    }),
    experimental_throttle: 200, // 200ms throttle for stable updates
  })

  // Create compatibility layer for useCompletion interface
  const completion =
    chat.messages.length > 0
      ? extractTextContent(chat.messages[chat.messages.length - 1])
      : ''
  const isStreaming = chat.status === 'streaming' || chat.status === 'submitted'
  const complete = useCallback(
    (prompt: string, options?: any) => {
      chat.sendMessage({ text: prompt }, options)
    },
    [chat]
  )

  // Track whether the initial generation request has been sent to avoid duplicate calls
  const [hasRequestedGeneration, setHasRequestedGeneration] = useState(false)

  // Effect to automatically start the generation stream
  useEffect(() => {
    // Only start if the loader detects we are in a generating state,
    // and we haven't already started the stream.
    if (
      isGenerating &&
      dragTreeId &&
      !isStreaming &&
      completion === '' &&
      !hasRequestedGeneration
    ) {
      console.log(
        '🎬 [DragTreeClient] Initial generation needed, starting stream...'
      )
      complete('', { body: { dragTreeId } })
      setHasRequestedGeneration(true)
    }
  }, [
    isGenerating,
    dragTreeId,
    isStreaming,
    completion,
    hasRequestedGeneration,
    complete,
  ])

  // Reset the request flag once generation completes or generation state resets
  useEffect(() => {
    if (!isGenerating) {
      setHasRequestedGeneration(false)
    }
  }, [isGenerating])

  // Handle tutorial completion - mark as completed in DB
  const handleTutorialComplete = useCallback(async () => {
    // Immediately hide tutorial for instant feedback
    setTutorialHidden(true)
    setTutorialManuallyShown(false) // Reset manual override

    if (!session?.user?.id) {
      console.warn('No user session, cannot mark tutorial as completed')
      return
    }

    try {
      const result = await markTutorialCompleted(session.user.id)
      if (result.success) {
        console.log('✅ Tutorial completed - will not show again')
      } else {
        console.error('Failed to mark tutorial as completed:', result.error)
      }
    } catch (error) {
      console.error('Error marking tutorial as completed:', error)
    }
  }, [session?.user?.id])

  // Handle tutorial skip - mark as skipped (distinct from completion)
  const handleTutorialSkip = useCallback(async () => {
    console.log('⏭️ Tutorial skipped - marking as skipped')
    // Immediately hide tutorial for instant feedback
    setTutorialHidden(true)
    setTutorialManuallyShown(false) // Reset manual override

    if (!session?.user?.id) {
      console.warn('No user session, cannot mark tutorial as skipped')
      return
    }

    try {
      const result = await markTutorialSkipped(session.user.id)
      if (result.success) {
        console.log('✅ Tutorial skipped - will not show again')
      } else {
        console.error('Failed to mark tutorial as skipped:', result.error)
      }
    } catch (error) {
      console.error('Error marking tutorial as skipped:', error)
    }
  }, [session?.user?.id])

  // Handle manual tutorial show (from header settings)
  // const handleShowTutorial = useCallback(() => {
  //   console.log('🎯 Tutorial manually shown by user')
  //   setTutorialHidden(false)
  //   setTutorialManuallyShown(true) // Override completion status
  // }, [])

  // Tutorial should show if:
  // 1. Tutorial state is checked (data loaded)
  // 2. Should show from metadata OR manually overridden
  // 3. dragTree is ready (has content and not loading)
  // 4. Not manually hidden by user in current session
  const shouldShowTutorial = Boolean(
    tutorialState.checked &&
      (tutorialState.shouldShow || tutorialManuallyShown) && // Allow manual override
      frontendTreeStructure &&
      !isDragTreeLoading &&
      !tutorialHidden // Hide if user manually closed it this session
  )

  // Debug function - attach to window for manual debugging
  useEffect(() => {
    if (typeof window !== 'undefined') {
      ;(window as any).debugTutorial = {
        checkStatus: () => {
          console.log('🔍 Tutorial Debug Status:', {
            tutorialState,
            shouldShowTutorial,
            session: session?.user?.id,
            tutorialHidden,
            tutorialManuallyShown,
          })
        },
        forceShow: () => {
          console.log('🔧 Forcing tutorial to show')
          setTutorialHidden(false)
          setTutorialManuallyShown(true)
        },
        forceComplete: async () => {
          console.log('🔧 Forcing tutorial completion')
          if (session?.user?.id) {
            await handleTutorialComplete()
          } else {
            console.error('No user session for force complete')
          }
        },
        resetState: () => {
          console.log('🔧 Resetting tutorial state')
          setTutorialHidden(false)
          setTutorialManuallyShown(false)
        },
      }
    }

    // Cleanup: Remove debug functions on unmount to prevent memory leaks
    return () => {
      if (typeof window !== 'undefined') {
        delete (window as any).debugTutorial
      }
    }
  }, [
    tutorialState,
    shouldShowTutorial,
    session,
    tutorialHidden,
    tutorialManuallyShown,
    handleTutorialComplete,
  ])

  console.log('🎯 [DragTreeClient] Tutorial logic:', {
    checked: tutorialState.checked,
    shouldShowFromDB: tutorialState.shouldShow,
    manuallyShown: tutorialManuallyShown,
    hasContent: !!frontendTreeStructure,
    isLoading: isDragTreeLoading,
    hidden: tutorialHidden,
    finalDecision: shouldShowTutorial,
    tutorialStateRaw: tutorialState,
    userId: session?.user?.id,
    sessionExists: !!session,
  })

  // Show error state if there's an error
  if (errorMessage) {
    return (
      <ErrorState
        session={session}
        errorMessage={errorMessage}
        showSyncTest={showSyncTest}
        onToggleSyncTest={onToggleSyncTest}
        onClearError={clearError}
      />
    )
  }

  // If the loader says we're generating, show the futuristic loading UI.
  if (isGenerating) {
    return (
      <FuturisticLoadingDemo
        completion={completion}
        isComplete={!isStreaming && completion.length > 0}
        onGenerationComplete={reloadDragTree}
      />
    )
  }

  // Show the standard loading UI for any other loading states (e.g., page refresh)
  if (isDragTreeLoading) {
    return (
      <LoadingState
        session={session}
        showSyncTest={showSyncTest}
        onToggleSyncTest={onToggleSyncTest}
        dragTreeId={dragTreeId}
      />
    )
  }

  // Show main content when everything is loaded and not generating
  if (isTreeReadyForDisplay) {
    return (
      <MainContent
        session={session}
        showSyncTest={showSyncTest}
        onToggleSyncTest={onToggleSyncTest}
        dragTreeId={dragTreeId}
        isDragTreeLoading={isDragTreeLoading}
        showTutorial={shouldShowTutorial}
        onTutorialComplete={handleTutorialComplete}
        onTutorialSkip={handleTutorialSkip}
      />
    )
  }

  // If the specific tree isn't ready yet, keep the user in the familiar loading
  // UI instead of flashing the previous tree.
  return (
    <LoadingState
      session={session}
      showSyncTest={showSyncTest}
      onToggleSyncTest={onToggleSyncTest}
      dragTreeId={dragTreeId}
    />
  )
}
