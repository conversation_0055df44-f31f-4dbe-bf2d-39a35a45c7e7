import { TreeNode } from '@/app/types'
import { DEFAULT_NODE_LABELS } from '@/app/(conv)/dragTree/[dragTreeId]/constants/node-types'

export const getNodeClasses = (
  node: TreeNode,
  isHovered: boolean,
  isClicked: boolean,
  isCurrentNodeLoading: boolean,
  isLoading: boolean,
  treeTargetNodeId?: string | null
): string => {
  let baseClasses =
    'group p-3 rounded-xl relative transition-all duration-300 cursor-pointer select-none'

  const isTreeTargeted = treeTargetNodeId === node.id
  let bgClasses = ''
  let borderClasses = ''
  let shadowClasses = ''

  // Loading state has highest priority
  if (isCurrentNodeLoading) {
    bgClasses =
      'bg-gradient-to-br from-amber-100 via-yellow-100 to-orange-100 animate-pulse'
    borderClasses = 'border-2 border-amber-400 ring-2 ring-amber-300/50'
    shadowClasses = 'shadow-xl shadow-amber-200/60'
  } else if (isLoading) {
    // Global loading state - make node appear disabled
    bgClasses =
      'bg-gradient-to-br from-slate-100 via-gray-100 to-zinc-100 opacity-60'
    borderClasses = 'border-2 border-slate-300'
    shadowClasses = 'shadow-sm'
    baseClasses =
      'p-3 rounded-xl relative transition-all duration-300 cursor-not-allowed select-none'
  } else if (isTreeTargeted) {
    // Special highlighting for tree-targeted nodes
    bgClasses = 'bg-gradient-to-br from-emerald-100 via-green-100 to-teal-100'
    borderClasses = 'border-2 border-emerald-400 ring-2 ring-emerald-300/50'
    shadowClasses = 'shadow-xl shadow-emerald-200/60'
  } else if (
    node.label === DEFAULT_NODE_LABELS.NEW_CATEGORY ||
    node.label === DEFAULT_NODE_LABELS.NEW_QUESTION
  ) {
    // Modern gradient for new items
    bgClasses = isHovered
      ? 'bg-gradient-to-br from-rose-200 via-pink-100 to-orange-200'
      : 'bg-gradient-to-br from-rose-100 via-pink-50 to-orange-100'
    borderClasses = isHovered
      ? 'border-2 border-rose-300'
      : 'border-2 border-rose-200'
    shadowClasses = isHovered
      ? 'shadow-xl shadow-rose-200/60'
      : 'shadow-lg shadow-rose-100/50'
  } else if (node.type === 'question') {
    // Modern blue gradient for questions
    bgClasses = isHovered
      ? 'bg-gradient-to-br from-blue-100 via-indigo-100 to-purple-100'
      : 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50'
    borderClasses = isHovered
      ? 'border-2 border-blue-300/80'
      : 'border-2 border-blue-200/60'
    shadowClasses = isHovered
      ? 'shadow-xl shadow-blue-200/60'
      : 'shadow-lg shadow-blue-100/40'
  } else {
    // Modern neutral gradient for categories
    bgClasses = isHovered
      ? 'bg-gradient-to-br from-slate-100 via-gray-100 to-zinc-100'
      : 'bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50'
    borderClasses = isHovered
      ? 'border-2 border-slate-300/80'
      : 'border-2 border-slate-200/60'
    shadowClasses = isHovered
      ? 'shadow-xl shadow-slate-200/60'
      : 'shadow-lg shadow-slate-100/40'
  }

  const interactiveClasses = isClicked
    ? 'ring-4 ring-indigo-300/50 shadow-2xl transform scale-[0.98]'
    : isHovered
      ? 'shadow-2xl transform scale-[1.02] border-opacity-100'
      : shadowClasses

  return `${baseClasses} ${bgClasses} ${borderClasses} ${interactiveClasses}`
}
