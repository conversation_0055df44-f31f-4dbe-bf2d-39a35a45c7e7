'use client'
import { useState, useEffect } from 'react'

export const useVisualFlowSetup = () => {
  const [layoutMode, setLayoutMode] = useState<'linear' | 'radial'>('linear')
  const [isInitialLoading, setInitialLoading] = useState(true)

  // Brief loading state for initial ReactFlow setup
  useEffect(() => {
    const timeout = setTimeout(() => setInitialLoading(false), 100) // Quick setup time
    return () => clearTimeout(timeout)
  }, [])

  // Reset layout mode on unmount to prevent state persistence between routes
  useEffect(() => {
    return () => {
      setLayoutMode('linear') // Reset to default
    }
  }, [])

  return {
    isInitialLoading,
    layoutMode,
    setLayoutMode,
  }
}
