'use client'

import { useEffect } from 'react'
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  EditorContent as TiptapEditor<PERSON>ontent,
  useEditor,
} from '@tiptap/react'
import { cn } from '@/lib/utils'
import { marked } from 'marked'
import { generateJSON } from '@tiptap/html'
import DOMPurify from 'isomorphic-dompurify'

// Import minimal extensions for read-only display
import StarterKit from '@tiptap/starter-kit'
import Table from '@tiptap/extension-table'
import TableRow from '@tiptap/extension-table-row'
import TableHeader from '@tiptap/extension-table-header'
import TableCell from '@tiptap/extension-table-cell'
import TaskList from '@tiptap/extension-task-list'
import TaskItem from '@tiptap/extension-task-item'
import CodeBlockLowlight from '@tiptap/extension-code-block-lowlight'
import { lowlight } from 'lowlight'
import css from 'highlight.js/lib/languages/css'
import js from 'highlight.js/lib/languages/javascript'
import ts from 'highlight.js/lib/languages/typescript'
import html from 'highlight.js/lib/languages/xml'
import python from 'highlight.js/lib/languages/python'

// Import highlight.js code block theme for consistent syntax colors
import 'highlight.js/styles/atom-one-dark.css'

// Register languages for syntax highlighting
lowlight.registerLanguage('css', css)
lowlight.registerLanguage('javascript', js)
lowlight.registerLanguage('typescript', ts)
lowlight.registerLanguage('html', html)
lowlight.registerLanguage('python', python)

// Minimal extensions for read-only display
const ReadOnlyExtensions = [
  StarterKit.configure({
    heading: {
      levels: [1, 2, 3, 4, 5, 6],
    },
    codeBlock: false, // We'll use CodeBlockLowlight instead
    bulletList: {
      keepMarks: true,
      keepAttributes: false,
    },
    orderedList: {
      keepMarks: true,
      keepAttributes: false,
    },
  }),
  Table.configure({
    resizable: false,
  }),
  TableRow,
  TableHeader,
  TableCell,
  TaskList,
  TaskItem.configure({
    nested: true,
  }),
  CodeBlockLowlight.configure({
    lowlight,
  }),
]

// Content detection utilities
const isJSONContent = (content: string): boolean => {
  if (typeof content !== 'string') return false
  try {
    const parsed = JSON.parse(content)
    return parsed && typeof parsed === 'object' && parsed.type === 'doc'
  } catch {
    return false
  }
}

// Convert markdown to TipTap JSON
const convertContentToJSON = (content: string): JSONContent | string => {
  if (!content) return ''

  if (isJSONContent(content)) {
    try {
      return JSON.parse(content) as JSONContent
    } catch {
      return content
    }
  }

  // Convert markdown → HTML → Tiptap JSON
  try {
    const rawHTML = marked.parse(content) as string
    const html = DOMPurify.sanitize(rawHTML)
    const json = generateJSON(html, ReadOnlyExtensions)
    return json as JSONContent
  } catch {
    return content
  }
}

export { convertContentToJSON }

type ReadOnlyTiptapEditorProps = {
  content: string
  className?: string
}

export const ReadOnlyTiptapEditor: React.FC<ReadOnlyTiptapEditorProps> = ({
  content,
  className,
}) => {
  const editor = useEditor({
    extensions: ReadOnlyExtensions,
    content: convertContentToJSON(content),
    editable: false,
    editorProps: {
      attributes: {
        class: 'prose prose-sm max-w-none focus:outline-none',
      },
    },
  })

  // Update content when it changes
  useEffect(() => {
    if (editor && content !== editor.getHTML()) {
      const jsonContent = convertContentToJSON(content)
      editor.commands.setContent(jsonContent, false)
    }
  }, [content, editor])

  return (
    <div className={cn('read-only-tiptap', className)}>
      <TiptapEditorContent editor={editor} />
    </div>
  )
}

export default ReadOnlyTiptapEditor
