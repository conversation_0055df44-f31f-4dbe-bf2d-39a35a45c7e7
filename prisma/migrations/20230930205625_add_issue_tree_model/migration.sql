-- CreateEnum
CREATE TYPE "IssueTreeStatus" AS ENUM ('INITIALIZED', 'ACTIVE', 'COMPLETED');

-- AlterEnum
ALTER TYPE "ConversationType" ADD VALUE 'ISSUE_TREE';

-- CreateTable
CREATE TABLE "issue_trees" (
    "id" TEXT NOT NULL,
    "creator_id" TEXT NOT NULL,
    "conversation_id" TEXT NOT NULL,
    "nodes" JSONB NOT NULL DEFAULT '{}',
    "edges" JSONB NOT NULL DEFAULT '{}',
    "raw_markdown" TEXT DEFAULT '',
    "prompt" TEXT DEFAULT '',
    "summary_context" TEXT DEFAULT '',
    "status" "IssueTreeStatus" NOT NULL DEFAULT 'INITIALIZED',
    "config" JSONB NOT NULL DEFAULT '{}',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "issue_trees_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "issue_trees_id_idx" ON "issue_trees" USING HASH ("id");

-- CreateIndex
CREATE INDEX "issue_trees_creator_id_idx" ON "issue_trees" USING HASH ("creator_id");

-- CreateIndex
CREATE INDEX "issue_trees_conversation_id_idx" ON "issue_trees" USING HASH ("conversation_id");

-- CreateIndex
CREATE INDEX "issue_trees_created_at_idx" ON "issue_trees"("created_at");

-- AddForeignKey
ALTER TABLE "issue_trees" ADD CONSTRAINT "issue_trees_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "conversations"("id") ON DELETE CASCADE ON UPDATE CASCADE;
