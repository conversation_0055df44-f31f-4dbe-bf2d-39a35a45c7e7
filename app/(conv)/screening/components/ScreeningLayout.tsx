import React from 'react'
import CenteredInitialView from '@/app/(conv)/screening/components/CenteredInitialView'
import ExpandedAnalysisView from '@/app/(conv)/screening/components/ExpandedAnalysisView'
import type { UseScreeningActionsReturn } from '@/app/(conv)/screening/hooks/useScreeningActions'
import { cn } from '@/lib/utils'

type ScreeningLayoutProps = {
  screeningHook: UseScreeningActionsReturn
}

/**
 * Main layout component that conditionally renders different view states
 * Handles the transition between centered initial view and expanded analysis view
 */
const ScreeningLayout: React.FC<ScreeningLayoutProps> = ({ screeningHook }) => {
  // Determine if content should be centered (no results to show)
  const shouldCenterContent =
    !screeningHook.screenObject && screeningHook.rephrased.length === 0

  // Common props for both views
  const commonProps = {
    description: screeningHook.description,
    setDescription: screeningHook.setDescription,
    isLoading: screeningHook.isProcessing, // Use combined processing state
    showExamples: screeningHook.rephrased.length === 0,
    selectedLanguage: screeningHook.selectedLanguage,
    canStartClarification: screeningHook.canStartClarification, // Workflow state
    hasUserMadeSelection: screeningHook.hasUserMadeSelection, // Selection state
    showUseMyInputButton: screeningHook.showUseMyInputButton, // Show "My Original Input" button
    onParaphraseAndAnalyze: screeningHook.handleParaphraseAndAnalyze, // Step 1
    onStartClarification: screeningHook.handleStartClarification, // Step 2
    onSuggestionClick: screeningHook.handleSuggestionSelection, // Updated to use new handler
    onUseMyInput: screeningHook.handleUseMyInput, // New handler
    onLanguageChange: screeningHook.setSelectedLanguage,
  }

  return (
    <main className="flex flex-col h-full w-full p-4 gap-4">
      <div className="flex-1 overflow-hidden">
        <div
          className={cn('h-full', {
            'flex items-center justify-center': shouldCenterContent,
            'max-w-7xl mx-auto': !shouldCenterContent,
          })}
        >
          {shouldCenterContent ? (
            <CenteredInitialView {...commonProps} />
          ) : (
            <ExpandedAnalysisView
              {...commonProps}
              rephrasedDescriptions={screeningHook.rephrased}
              screenObject={screeningHook.screenObject}
              selectedSuggestion={screeningHook.selectedSuggestion}
            />
          )}
        </div>
      </div>
    </main>
  )
}

export default ScreeningLayout
