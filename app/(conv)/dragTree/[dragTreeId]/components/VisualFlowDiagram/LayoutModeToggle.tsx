'use client'
import React from 'react'
import { Button } from '@/components/ui/button'
import { LayoutGrid, Target } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useSession } from 'next-auth/react'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { logEventWithContext } from '@/app/libs/logging'

type LayoutModeToggleProps = {
  layoutMode: 'linear' | 'radial'
  setLayoutMode: (mode: 'linear' | 'radial') => void
}

export const LayoutModeToggle: React.FC<LayoutModeToggleProps> = React.memo(
  function LayoutModeToggle({ layoutMode, setLayoutMode }) {
    const { data: session } = useSession()
    const dragTreeId = useDragTreeStore(state => state.dragTreeId)

    const handleLayoutModeChange = (mode: 'linear' | 'radial') => {
      // Log the layout mode change event
      const eventName =
        mode === 'linear'
          ? 'click_reactflow_hierarchical'
          : 'click_reactflow_circular'

      logEventWithContext(
        eventName,
        session?.user?.id,
        dragTreeId || undefined,
        {
          previous_mode: layoutMode,
          new_mode: mode,
        }
      )

      setLayoutMode(mode)
    }

    return (
      <div className="absolute top-4 left-4 z-10">
        <div className="flex items-center bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-lg p-1 shadow-sm">
          <Button
            variant={layoutMode === 'linear' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => handleLayoutModeChange('linear')}
            className={cn(
              'h-8 px-3 text-xs font-medium transition-all duration-200',
              layoutMode === 'linear'
                ? 'bg-blue-500 text-white shadow-sm'
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100/50'
            )}
          >
            <LayoutGrid className="w-3 h-3 mr-1.5" />
            Hierarchical
          </Button>
          <Button
            variant={layoutMode === 'radial' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => handleLayoutModeChange('radial')}
            className={cn(
              'h-8 px-3 text-xs font-medium transition-all duration-200',
              layoutMode === 'radial'
                ? 'bg-purple-500 text-white shadow-sm'
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100/50'
            )}
          >
            <Target className="w-3 h-3 mr-1.5" />
            Circular
          </Button>
        </div>
      </div>
    )
  }
)
