# Feature Flags System

## Overview

The feature flags system allows for controlled rollout of new features and A/B testing. Feature flags are implemented as compile-time constants (not environment variables) for better performance and type safety.

## Chat System Feature Flags

### `ENABLE_CHAT_V2` (Default: `true`)

Controls which chat system is used throughout the application:

- **When `true`**: Uses ChatTabContentV2 with improved API architecture
- **When `false`**: Uses ChatTabContent (v1) with legacy architecture

**Chat v2 Benefits:**

- API handles message history retrieval internally
- Reduced payload size for long conversations
- Better separation of concerns between frontend and backend
- Proper system message handling for context
- AI SDK v5 native support with tool call display

### `ENABLE_AI_PANE_CHAT_V2` (Default: `true`)

Controls which chat system is used when starting new AI Pane chats. Requires `ENABLE_CHAT_V2` to be `true`.

## Usage

```typescript
import {
  ENABLE_CHAT_V2,
  getChatSystemVersion,
} from '@/app/configs/feature-flags'

// Check if Chat v2 is enabled
if (ENABLE_CHAT_V2) {
  // Use v2 features
}

// Get current version string
const version = getChatSystemVersion() // Returns 'v1' or 'v2'
```

## Architecture Improvements in Chat v2

### Message Flow Comparison

**v1 Architecture (Frontend-Heavy):**

1. Frontend loads full conversation history from API
2. Frontend passes ALL messages to API on each request
3. API processes entire conversation context every time
4. API only persists new assistant response

**v2 Architecture (API-Heavy):**

1. Frontend sends only new user message + conversation ID
2. API retrieves conversation history internally from database
3. API reconstructs full context server-side
4. Context included only on first message or when changed
5. System messages hidden from UI display

### API Request Format

**Chat v1:**

```typescript
POST /api/aipane/chat
{
  "messages": [msg1, msg2, msg3, msg4, newUserMessage], // ALL messages
  "conversationId": "thread_123",
  "context": contextContent(), // Context on every request
}
```

**Chat v2:**

```typescript
// First message (includes context)
POST /api/aipane/chat-v2
{
  "message": "What's the weather in San Francisco?",
  "conversationId": "thread_abc123",
  "context": "User is asking about weather. Provide current conditions.",
  "model": "gpt-4.1"
}

// Subsequent messages (no context needed)
POST /api/aipane/chat-v2
{
  "message": "What about tomorrow's forecast?",
  "conversationId": "thread_abc123",
  "model": "gpt-4.1"
}
```

### Benefits

- **Reduced Network Traffic**: Only new messages sent, not entire conversation
- **Better Performance**: Shorter request payloads for long conversations
- **Improved Separation**: Frontend focuses on UI, backend handles conversation logic
- **Enhanced Security**: Sensitive context handling server-side
- **Scalability**: More efficient for high-volume usage

## Testing

Feature flags are validated at startup and tested in the test suite. To switch between systems:

1. Change the flag value in `feature-flags.ts`
2. Restart the development server
3. Test both systems to ensure compatibility

## Implementation Details

The system uses a wrapper component (`ChatTabContentWrapper`) to dynamically load the appropriate chat component based on feature flags, ensuring type safety and proper component loading.

### Files Modified

- `app/configs/feature-flags.ts` - Feature flag definitions
- `lib/asset-types/chat.ts` - Asset registry with feature flag integration
- `lib/asset-types/ChatTabContentWrapper.tsx` - Dynamic component wrapper
- `app/api/aipane/chat-v2/route.ts` - Improved API with internal history management
- `app/(conv)/dragTree/[dragTreeId]/hooks/useAiConversationV2.ts` - Updated hook for new API
- `app/(conv)/dragTree/[dragTreeId]/components/tabs/ChatTabContentV2.tsx` - Updated component

### Testing

All tests pass with both v1 and v2 systems. The build system validates TypeScript compatibility and the test suite ensures functionality across both implementations.
