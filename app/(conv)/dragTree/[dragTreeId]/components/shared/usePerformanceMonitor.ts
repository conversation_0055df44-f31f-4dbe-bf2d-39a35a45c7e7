import { useEffect, useRef } from "react";

type PerformanceMonitorProps = {
  name: string;
  enabled?: boolean;
};

/**
 * Lightweight performance monitoring hook for debugging render performance
 */
export const usePerformanceMonitor = ({
  name,
  enabled = process.env.NODE_ENV === "development",
}: PerformanceMonitorProps) => {
  const renderCount = useRef(0);
  const lastRenderTime = useRef(performance.now());

  useEffect(() => {
    if (!enabled) return;

    renderCount.current += 1;
    const currentTime = performance.now();
    const timeSinceLastRender = currentTime - lastRenderTime.current;

    // Only log if render time is concerning (> 16ms for 60fps)
    if (timeSinceLastRender > 16 || renderCount.current % 10 === 0) {
      console.log(
        `⚡ [Performance] ${name}: Render #${
          renderCount.current
        }, Time: ${timeSinceLastRender.toFixed(2)}ms`
      );
    }

    lastRenderTime.current = currentTime;
  });

  return { renderCount: renderCount.current };
};

export default usePerformanceMonitor;
