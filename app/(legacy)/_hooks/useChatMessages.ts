import { useMemo } from 'react'
import { useChat } from '@ai-sdk/react'
import { DefaultChatTransport } from 'ai'
import { extractTextContent } from '@/app/types/ai-sdk5'
import type { Msg } from '@/app/types/ai-sdk5'

type UseChatMessagesProps = {
  api: string
  body: any
}

type UseChatMessagesReturn = {
  lastMessage: string | undefined
  append: (message: { content: string; role: string }) => void
  isLoading: boolean
}

export const useChatMessages = ({
  api,
  body,
}: UseChatMessagesProps): UseChatMessagesReturn => {
  const { messages, sendMessage, status } = useChat<Msg>({
    transport: new DefaultChatTransport({
      api,
      body,
    }),
  })

  // AI SDK 5 compatibility: use sendMessage instead of append
  const append = (message: { content: string; role: string }) => {
    if (message.role === 'user') {
      sendMessage({ text: message.content })
    }
  }

  // AI SDK 5 compatibility: extract text content from message parts
  const lastMessage = useMemo(() => {
    const lastMsg = messages[messages.length - 1]
    return lastMsg ? extractTextContent(lastMsg) : undefined
  }, [messages])

  const isLoading = status === 'streaming' || status === 'submitted'

  return { lastMessage, append, isLoading }
}
