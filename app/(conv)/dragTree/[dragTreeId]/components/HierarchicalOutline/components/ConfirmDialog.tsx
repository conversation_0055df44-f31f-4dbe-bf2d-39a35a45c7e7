import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog'
import { ConfirmDialogProps } from '@/app/(conv)/dragTree/[dragTreeId]/types'

const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  isOpen,
  nodeToDelete,
  onConfirm,
  onCancel,
}) => {
  if (!nodeToDelete) return null

  // Count total nodes that will be affected (including all descendants)
  const countTotalNodes = (node: any): number => {
    let count = 1 // Count the node itself
    if (node.children && node.children.length > 0) {
      for (const child of node.children) {
        count += countTotalNodes(child)
      }
    }
    return count
  }

  // Count categories and questions separately
  const countNodesByType = (
    node: any
  ): { categories: number; questions: number } => {
    let categories = node.type === 'category' ? 1 : 0
    let questions = node.type === 'question' ? 1 : 0

    if (node.children && node.children.length > 0) {
      for (const child of node.children) {
        const childCounts = countNodesByType(child)
        categories += childCounts.categories
        questions += childCounts.questions
      }
    }

    return { categories, questions }
  }

  const totalNodesAffected = countTotalNodes(nodeToDelete)
  const { categories: categoryCount, questions: questionCount } =
    countNodesByType(nodeToDelete)
  const childrenCount = nodeToDelete.children?.length || 0
  const hasChildren = childrenCount > 0

  return (
    <Dialog open={isOpen} onOpenChange={onCancel}>
      <DialogContent className="rounded-2xl border-0 shadow-2xl bg-white/95 backdrop-blur-sm">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold bg-gradient-to-r from-slate-700 to-slate-900 bg-clip-text text-transparent">
            ⚠️ Confirm Deletion
          </DialogTitle>
          <DialogDescription>
            This action will permanently remove the {nodeToDelete.type} and
            cannot be undone.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-slate-700 leading-relaxed">
              Are you sure you want to delete the {nodeToDelete.type}{' '}
              <span className="font-semibold text-slate-900">
                &quot;{nodeToDelete.label}&quot;
              </span>
              ?
            </p>

            {hasChildren && (
              <div className="mt-3 space-y-2">
                <p className="text-red-700 font-medium">
                  ⚠️ This will also delete all content underneath:
                </p>
                <ul className="text-sm text-red-600 ml-4 space-y-1">
                  <li>
                    • {childrenCount} direct child
                    {childrenCount !== 1 ? 'ren' : ''}
                  </li>
                  <li>
                    • {totalNodesAffected} total item
                    {totalNodesAffected !== 1 ? 's' : ''}
                    {(categoryCount > 0 || questionCount > 0) && (
                      <span className="text-red-500">
                        {' '}
                        (
                        {categoryCount > 0 &&
                          `${categoryCount} categor${
                            categoryCount !== 1 ? 'ies' : 'y'
                          }`}
                        {categoryCount > 0 && questionCount > 0 && '; '}
                        {questionCount > 0 &&
                          `${questionCount} question${
                            questionCount !== 1 ? 's' : ''
                          }`}
                        )
                      </span>
                    )}
                  </li>
                </ul>
              </div>
            )}

            {!hasChildren && (
              <p className="text-sm text-slate-600 mt-2">
                This {nodeToDelete.type} has no children and can be safely
                removed.
              </p>
            )}
          </div>
        </div>

        <div className="flex justify-end mt-6 gap-3">
          <Button
            variant="ghost"
            onClick={onCancel}
            className="rounded-xl px-6 py-2 font-medium"
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={onConfirm}
            className="rounded-xl px-6 py-2 font-medium bg-gradient-to-r from-rose-500 to-red-600 shadow-lg shadow-rose-200/50"
          >
            {hasChildren
              ? `Delete All (${totalNodesAffected} items)`
              : 'Delete'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}

// Memoize to prevent unnecessary re-renders when props haven't changed
export default React.memo(ConfirmDialog)
