'use client'

import clsx from 'clsx'
import { format } from 'date-fns'
import { User } from '@prisma/client'
import Avatar from '@/app/components/Avatar'
import { UIMessage } from 'ai'
interface MessageBoxProps {
  data: UIMessage
  isCurrentUser?: boolean
  currentUser?: User | null
}

const MessageBox: React.FC<MessageBoxProps> = ({
  data,
  isCurrentUser,
  currentUser,
}) => {
  const container = clsx('flex gap-3 p-4 justify-start')
  const body = clsx('flex flex-col gap-2')
  const message = clsx(
    'text-sm overflow-hidden max-w-xs md:max-w-md lg:max-w-2xl mx-auto stretch whitespace-pre-wrap break-words',
    isCurrentUser ? 'bg-sky-500 text-white' : 'bg-gray-100',
    'rounded-2xl py-2 px-3'
  )

  return (
    <div className={container}>
      <div>
        {isCurrentUser && currentUser && <Avatar user={currentUser} />}
        {!isCurrentUser && <Avatar />}
      </div>
      <div className={body}>
        <div className="flex items-center gap-1">
          <div className="text-xs text-gray-400">
            {/* Pp example: 07/03/2023, 6:15 PM */}
            {(data as any).createdAt &&
              format(new Date((data as any).createdAt), 'Pp')}
          </div>
        </div>
        <div className={message}>
          <div>{(data as any).content}</div>
        </div>
      </div>
    </div>
  )
}

export default MessageBox
