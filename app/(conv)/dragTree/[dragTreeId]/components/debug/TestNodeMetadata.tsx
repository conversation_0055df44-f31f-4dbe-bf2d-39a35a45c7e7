'use client'

import React, { useState } from 'react'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { Button } from '@/components/ui/button'
import { TreeNode } from '@/app/types'
import { TreeNodeType } from '@/app/(conv)/dragTree/[dragTreeId]/constants/node-types'

const TestNodeMetadata: React.FC = () => {
  // Optimized: Use individual selectors to prevent unnecessary re-renders
  const frontendTreeStructure = useDragTreeStore(
    state => state.frontendTreeStructure
  )
  const addNode = useDragTreeStore(state => state.addNode)
  const nodeMap = useDragTreeStore(state => state.nodeMap)
  const [selectedParentId, setSelectedParentId] = useState<string>('')
  const [nodeType, setNodeType] = useState<TreeNodeType>(TreeNodeType.CATEGORY)

  // Get all nodes for parent selection
  const getAllNodes = (node: TreeNode): TreeNode[] => {
    let nodes = [node]
    node.children.forEach(child => {
      nodes = nodes.concat(getAllNodes(child))
    })
    return nodes
  }

  const allNodes = frontendTreeStructure
    ? getAllNodes(frontendTreeStructure)
    : []

  const handleAddNode = () => {
    if (!selectedParentId) return
    addNode(selectedParentId, nodeType)
  }

  // Helper function for node info - currently not used
  // const getNodeInfo = (nodeId: string) => {
  //   const node = nodeMap.get(nodeId)
  //   if (!node) return 'Unknown'
  //   const childCount = node.children.length
  //   return `${node.type}: "${node.label}" (${childCount} children)`
  // }

  // Calculate node level for display
  const calculateNodeLevel = (tree: TreeNode, targetNodeId: string): number => {
    function findNodeLevel(
      node: TreeNode,
      targetId: string,
      currentLevel: number = 0
    ): number | null {
      if (node.id === targetId) {
        return currentLevel
      }

      for (const child of node.children) {
        const foundLevel = findNodeLevel(child, targetId, currentLevel + 1)
        if (foundLevel !== null) {
          return foundLevel
        }
      }

      return null
    }

    const level = findNodeLevel(tree, targetNodeId)
    return level !== null ? level : 0
  }

  const getNodeWithLevel = (nodeId: string) => {
    const node = nodeMap.get(nodeId)
    if (!node || !frontendTreeStructure) return 'Unknown'

    const level = calculateNodeLevel(frontendTreeStructure, nodeId)
    const childCount = node.children.length
    return `Level ${level} - ${node.type}: "${node.label}" (${childCount} children)`
  }

  return (
    <div className="p-4 border rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold mb-4">📋 Test Node Metadata</h3>

      {frontendTreeStructure ? (
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                Select Parent Node:
              </label>
              <select
                value={selectedParentId}
                onChange={e => setSelectedParentId(e.target.value)}
                className="w-full p-2 border rounded-md"
              >
                <option value="">-- Select a parent node --</option>
                {allNodes.map(node => (
                  <option key={node.id} value={node.id}>
                    {getNodeWithLevel(node.id)}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Node Type to Add:
              </label>
              <select
                value={nodeType}
                onChange={e => setNodeType(e.target.value as TreeNodeType)}
                className="w-full p-2 border rounded-md"
              >
                <option value={TreeNodeType.CATEGORY}>Category</option>
                <option value={TreeNodeType.QUESTION}>Question</option>
              </select>
            </div>
          </div>

          <Button
            onClick={handleAddNode}
            disabled={!selectedParentId}
            className="w-full"
          >
            Add {nodeType} to Selected Parent
          </Button>

          <div className="text-sm text-gray-600 space-y-1">
            <div>
              <strong>Total Nodes:</strong> {allNodes.length}
            </div>
            <div>
              <strong>Root Node:</strong> {frontendTreeStructure.label}
            </div>
            <div>
              <strong>Selected Parent:</strong>{' '}
              {selectedParentId ? getNodeWithLevel(selectedParentId) : 'None'}
            </div>
            {selectedParentId && frontendTreeStructure && (
              <div>
                <strong>New Node Level will be:</strong>{' '}
                {calculateNodeLevel(frontendTreeStructure, selectedParentId) +
                  1}
              </div>
            )}
          </div>

          <div className="bg-blue-50 border border-blue-200 p-3 rounded-md">
            <h4 className="font-medium text-blue-800 mb-2">
              📋 Expected Metadata Format:
            </h4>
            <pre className="text-sm text-blue-700 bg-blue-100 p-2 rounded">
              {selectedParentId && frontendTreeStructure
                ? JSON.stringify(
                    {
                      level:
                        calculateNodeLevel(
                          frontendTreeStructure,
                          selectedParentId
                        ) + 1,
                      parentId: selectedParentId,
                      createdAt: new Date().toISOString(),
                      version: 'v1',
                    },
                    null,
                    2
                  )
                : 'Select a parent to see expected metadata'}
            </pre>
          </div>

          <div className="bg-green-50 border border-green-200 p-3 rounded-md">
            <h4 className="font-medium text-green-800 mb-2">
              ✅ Metadata Features:
            </h4>
            <ul className="text-sm text-green-700 space-y-1">
              <li>✅ Level calculated automatically (parent level + 1)</li>
              <li>✅ ParentId included for hierarchy tracking</li>
              <li>✅ CreatedAt timestamp for audit trail</li>
              <li>✅ Version field for future compatibility</li>
              <li>
                ✅ Categories auto-create child questions with proper metadata
              </li>
            </ul>
          </div>
        </div>
      ) : (
        <p className="text-gray-500">No tree data available</p>
      )}
    </div>
  )
}

export default TestNodeMetadata
