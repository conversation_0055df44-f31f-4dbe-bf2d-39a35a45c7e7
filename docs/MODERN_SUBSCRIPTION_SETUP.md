# Modern Subscription System Setup

This document outlines the setup requirements for the modernized subscription system.

## 🏗️ Architecture Overview

The new subscription system has been completely modernized with the following improvements:

### ✅ **Migrated from Legacy (v1) to Modern (v2)**

- **Universal Sidebar**: Uses dragTree sidebar throughout the entire application
- **Session-based User Data**: No more `currentUser` context dependencies
- **Controlled Pricing**: Hardcoded pricing config instead of Stripe metadata
- **Modern Components**: Clean, reusable pricing components
- **Complete Separation**: Zero dependencies on legacy conversation system

### ✅ **Key Components**

1. **ModernSubscriptionPage**: Main subscription page component
2. **ModernPricingCard**: Individual pricing plan cards
3. **ModernSubscriptionHeader**: Dynamic header based on user tier
4. **UniversalDragTreeLayout**: Consistent layout with dragTree sidebar
5. **useModernSubscription**: Session-based subscription hook

## 🔧 Required Environment Variables

Add these to your `.env.local` file:

```bash
# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
```

## 📋 Stripe Setup Checklist

### 1. **Create Stripe Products & Prices**

In your Stripe Dashboard:

1. **Create PRO Product**:
   - Name: "Pro Plan"
   - Description: "Unlock advanced AI features"

2. **Create PRO Price**:
   - Amount: $20.00 USD
   - Billing: Monthly recurring
   - Copy the Price ID to `NEXT_PUBLIC_STRIPE_PRO_PRICE_ID`

### 2. **Configure Webhooks**

1. **Add Webhook Endpoint**: `https://yourdomain.com/api/payments/webhook`

2. **Select Events**:
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `checkout.session.completed`
   - `product.created` / `product.updated`
   - `price.created` / `price.updated`
   - `invoice.payment_failed` (optional: observability)

3. **Copy Webhook Secret** to `STRIPE_WEBHOOK_SECRET`

## 🎨 Pricing Source of Truth

Pricing is driven by Stripe Products/Prices and is mirrored into the DB via webhooks:

- Webhooks listen to `product.*` and `price.*` and upsert into `products` and `prices` tables.
- Frontend fetches active prices from `GET /api/subscription/prices` and never uses hardcoded price IDs.

This eliminates env-based price coupling and keeps the app aligned with Stripe.

## 🔄 Migration Benefits

### **Before (v1 - Legacy)**

- ❌ Dependent on conversation-based user context
- ❌ Stripe metadata parsing for pricing
- ❌ Mixed legacy/modern components
- ❌ Inconsistent sidebar across pages

### **After (v2 - Modern)**

- ✅ Session-based user data
- ✅ Controlled pricing configuration
- ✅ Consistent modern components
- ✅ Universal dragTree sidebar
- ✅ Complete separation from legacy code

## 🔄 Reconciliation & Cron

- Endpoint: `POST /api/subscription/reconcile` with optional `{ "hours": 24 }` to sync users whose subscription data is stale.
- Suggested schedule: Nightly via Vercel Cron or GitHub Actions.

## 🧪 Testing the Modern System

1. **Start Development Server**:

   ```bash
   npm run dev
   ```

2. **Visit Subscription Page**:

   ```
   http://localhost:3000/subscription
   ```

3. **Test Features**:
   - Modern pricing cards display
   - Universal dragTree sidebar
   - Session-based user tier detection
   - Stripe checkout flow (with test cards)

4. **Test User Tiers**:

   ```bash
   # Set user to PRO to see "Current Plan" state
   node scripts/test-subscription.js <EMAIL> PRO

   # Set user to FREE to see "Upgrade" state
   node scripts/test-subscription.js <EMAIL> FREE
   ```

## 🚀 Deployment Notes

1. **Environment Variables**: Ensure all Stripe keys are set in production
2. **Webhook URL**: Update webhook endpoint to production domain
3. **Price IDs**: Use production price IDs in production environment
4. **Testing**: Test complete checkout flow with Stripe test cards

## 📁 File Structure

```
app/subscription/
├── components/
│   ├── ModernPricingConfig.ts      # Pricing configuration
│   ├── ModernPricingCard.tsx       # Individual pricing cards
│   ├── ModernSubscriptionHeader.tsx # Dynamic header
│   ├── ModernSubscriptionPage.tsx   # Main page component
│   └── UniversalDragTreeLayout.tsx  # Universal layout
├── hooks/
│   └── useModernSubscription.ts     # Session-based hook
├── layout.tsx                       # Updated layout
└── page.tsx                         # Updated page
```

## 🎯 Next Steps

The modern subscription system is now ready for production use with:

- ✅ Complete separation from legacy code
- ✅ Modern, maintainable architecture
- ✅ Consistent user experience
- ✅ Controlled pricing configuration
- ✅ Universal dragTree integration

All legacy components have been commented out and preserved for reference.
