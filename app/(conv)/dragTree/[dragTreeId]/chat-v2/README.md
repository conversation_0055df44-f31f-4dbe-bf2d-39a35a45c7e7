# Chat V2 Architecture

This directory contains the refactored Chat V2 implementation that eliminates complex conversation ID flows, reduces coupling, and provides a cleaner provider-based architecture.

## Overview

The new architecture uses a provider pattern with focused hooks to manage conversation state, streaming, and history separately. This eliminates the race conditions and complexity of the previous implementation.

## Key Components

### ChatPaneProvider (`ChatPaneContext.tsx`)

The main provider that manages conversation lifecycle with a state machine approach:

**States:**

- `idle` - Initial state, no conversation ID
- `creating` - Generating conversation ID
- `loading-history` - Loading existing messages
- `ready` - Ready to send/receive messages
- `streaming` - Currently streaming a response
- `error` - Error state

**Props:**

```typescript
type ChatPaneProviderProps = {
  tabId: string
  dragTreeId: string
  apiEndpoint: string
  model: string
  contextIds: string[]
  getContext: () => string
  initialConversationId?: string
  children: React.ReactNode
}
```

### Focused Hooks

#### `useChatStreamBridge` (`hooks/useChatStreamBridge.ts`)

Wraps Vercel AI SDK `useChat` with conversation ID gating:

- Only mounts when `conversationId` is available
- <PERSON><PERSON> request preparation for chat-v2 API
- Manages first-message context inclusion
- Optional throttling for reduced re-renders

#### `useConversationHistory` (`hooks/useConversationHistory.ts`)

Manages conversation history separately from streaming:

- Fetches existing messages from API
- Converts database format to UIMessage format
- Provides refresh functionality
- Proper cleanup and abort handling

### Public API

Components consume the provider through these hooks:

```typescript
// Combined state and actions
function useChatPane(): ChatPaneState & ChatPaneActions

// Separate hooks for specific needs
function useChatPaneState(): ChatPaneState
function useChatPaneActions(): ChatPaneActions
```

**State:**

```typescript
type ChatPaneState = {
  conversationId?: string
  status: ChatPaneStatus
  messages: UIMessage[]
  input: string
  isStreaming: boolean
  error?: string
}
```

**Actions:**

```typescript
type ChatPaneActions = {
  setInput: (value: string) => void
  submit: () => void
  refresh: () => Promise<void>
}
```

## Flow

### First Message Flow

1. User types message and submits
2. If no `conversationId`, provider generates one with `generateAiConversationId()`
3. Provider updates tab store with new ID
4. Provider stores message for auto-send after history loads
5. History hook mounts and loads (empty for new conversations)
6. Stream hook mounts and auto-sends the pending message
7. Server upserts conversation on first POST to chat-v2 API

### Subsequent Messages

1. User types and submits
2. Stream hook sends message immediately
3. On stream finish, history hook refreshes to get persisted messages

### Message State

Messages are combined from two sources:

- **History messages**: Loaded from database, stable
- **Streaming messages**: From AI SDK, temporary during streaming

Combined as: `[...historyMessages, ...streamingMessages]`

## Key Improvements

### Single ID Resolution Path

- No more client pre-creation of conversations
- ID generated only on first submit
- Single tab store update per conversation

### Eliminated Race Conditions

- Conditional mounting of hooks based on conversation ID
- Clear state machine prevents invalid transitions
- Proper cleanup and abort handling

### Reduced Coupling

- Only one tab store interaction (setting conversationId)
- Provider handles all state management internally
- Components only consume through hooks

### Simplified Message Handling

- No more ref-based caching with version counters
- Straightforward array concatenation
- Single refresh point after streaming

## Migration Notes

### From useAiConversationV2

The old hook's surface is replaced by `useChatPane()`:

```typescript
// Old
const {
  messages,
  input,
  handleInputChange,
  handleSubmit,
  isLoading,
  conversation,
  isLoadingConversation,
} = useAiConversationV2(options)

// New
const {
  messages,
  input,
  setInput,
  submit,
  isStreaming,
  status,
  conversationId,
} = useChatPane()
```

### Component Changes

Components now wrap content with the provider:

```tsx
<ChatPaneProvider
  tabId={tab.id}
  dragTreeId={dragTreeId}
  apiEndpoint={getChatApiEndpoint()}
  model={tab.aiPaneData?.model || 'gpt-4.1'}
  contextIds={tab.aiPaneData?.contextIds || []}
  getContext={contextContent}
  initialConversationId={tab.aiPaneData?.conversationId}
>
  <ChatContent />
</ChatPaneProvider>
```

## Server API Contract

The implementation works with existing server APIs:

- **POST /api/aipane/chat-v2**: Streams responses, upserts conversation on first message
- **GET /api/aipane/conversations/[id]**: Loads conversation history
- **POST /api/aipane/conversations**: No longer used for pre-creation

## Testing Considerations

Key scenarios to test:

1. **First message without existing conversation ID**
   - Should generate ID, update tab store, and stream successfully

2. **Loading existing conversation**
   - Should load history first, then be ready for new messages

3. **Stream finish refresh**
   - Should refresh history after streaming completes

4. **Error handling**
   - Should handle network errors gracefully
   - Should not leave hooks in invalid states

5. **Cleanup**
   - Should abort requests on unmount
   - Should not update state after unmount

## Performance Notes

- Optional stream throttling reduces re-renders during streaming
- Conditional hook mounting prevents unnecessary API calls
- Single history refresh per stream instead of continuous polling
- Eliminated redundant conversation creation calls

## Critical Fixes Applied

### Fixed "Loading conversation..." Forever Bug

- **Issue**: Spinner would show forever even when messages were loaded
- **Root Cause**: Loading condition was tied to `messages.length === 0` instead of provider status
- **Fix**: Changed condition to only check `status === 'loading-history'`

### Defensive History Loading

- **Issue**: History loading could hang on abort/strict-mode double mount
- **Root Cause**: Loading state not cleared in finally block on AbortError
- **Fix**: Always clear loading state in finally block, handle 404/429 properly

### Proper Message Conversion

- **Issue**: Database messages not properly converted to UIMessage format
- **Root Cause**: Missing role lowercasing and parts array structure
- **Fix**: Added `toUIMessages()` utility following AI SDK v5 format

### Stream Bridge Reset

- **Issue**: SDK state could leak across conversation changes
- **Root Cause**: `useChat` not reset when conversation ID changes
- **Fix**: Added conversation ID-based keying and proper context memoization

### State Transition Hardening

- **Issue**: Provider could get stuck in loading states
- **Root Cause**: Incomplete state transitions on error/404 cases
- **Fix**: Ensure all history loading paths transition to ready/error state

## Future Enhancements

- Add conversation metadata sync from server to tab store
- Implement conversation title updates
- Add typing indicators
- Consider message pagination for very long conversations
