import { LogAPIUsageDataType } from "@/app/types";

export const logAPIUsage = async (data: LogAPIUsageDataType) => {
  try {
    const baseUrl =
      process.env.NODE_ENV === "development"
        ? "http://localhost:3000"
        : process.env.NEXT_PUBLIC_SERVER_URL;

    const response = await fetch(`${baseUrl}/api/record`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        open_ai_usage_type: data.open_ai_usage_type,
        model_name: data.model_name,
        input_text: data.input_text,
        output_text: data.output_text,
        currentUser: data.currentUser,
        conversationId: data.conversationId,
      }),
    });

    if (!response.ok) {
      throw new Error("Server response: " + response.status);
    }

    const result = await response.json();
    console.log("Log API usage response:", result);
  } catch (error) {
    console.error("Error in logAPIUsage:", error);
  }
};
