import { Button } from '@/components/ui/button'
import {
  SheetDes<PERSON>,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet'
import { Textarea } from '@/components/ui/textarea'
import { Draft } from '@prisma/client'
import { PartialIssueTree } from '.'
import { useConversationStore } from '@/app/stores/_legacy/conversation_store'

interface MetadataTabContentProps {
  displaySummaryContext: string
  draft?: Draft
  handleCopySummaryContext: () => void
  handleCopyQA: () => void
  issueTrees?: PartialIssueTree[] | null
  qaString?: string
}

export const MetadataTabContent = ({
  displaySummaryContext,
  draft,
  handleCopySummaryContext,
  handleCopyQA,
  qaString,
}: MetadataTabContentProps) => {
  const conversationStore = useConversationStore()
  const conversation = conversationStore.conversationList.find(
    conversation => conversation.id === draft?.conversation_id
  )
  const originalAsk =
    (conversation as any)?.config?.prompt?.description ||
    'THIS SHOULD SHOW THE ORIGINAL ASK, if you see this and want to know the original ask, refreshing the page usually works'
  return (
    <div className="flex flex-col h-full">
      <div className="flex-none">
        {/* SheetHeader, Textarea, and SheetDescription here */}
        <SheetHeader className="flex flex-row justify-between items-center py-2">
          <SheetTitle>Original ask</SheetTitle>
        </SheetHeader>
        <Textarea rows={4} placeholder={originalAsk} disabled />
        <SheetHeader className="flex flex-row justify-between items-center py-2">
          <SheetTitle>Summary context for this draft</SheetTitle>
          <Button
            className="bg-purple-100 hover:bg-purple-200 text-gray-500"
            size={'sm'}
            onClick={handleCopySummaryContext}
          >
            Copy
          </Button>
        </SheetHeader>
        <Textarea rows={7} placeholder={displaySummaryContext} disabled />
        <SheetHeader className="flex flex-row justify-between items-center py-2">
          <SheetTitle>The facts</SheetTitle>
          <Button
            className="bg-purple-100 hover:bg-purple-200 text-gray-500"
            size={'sm'}
            onClick={handleCopyQA}
          >
            Copy
          </Button>
        </SheetHeader>
        <Textarea rows={7} placeholder={qaString} disabled />
        <SheetDescription>
          {'Draft id: ' + (draft?.id || 'N/A')}
        </SheetDescription>
        <SheetDescription>
          {'Created at: ' + (draft?.created_at?.toLocaleString() || 'N/A')}
        </SheetDescription>
      </div>
    </div>
  )
}
