import { test as setup, expect } from '@playwright/test'
import { PrismaClient } from '@prisma/client'
import fs from 'fs'

const authFile = 'playwright/.auth/user.json'
const prisma = new PrismaClient()
const testUserEmail = '<EMAIL>'

setup('authenticate', async ({ request }) => {
  // Check if we are in a CI environment
  if (!process.env.CI) {
    console.log('Not in CI, skipping authentication setup.')
    // In local dev, you might rely on an existing session or manual login.
    // For this test, we will proceed to ensure a consistent state.
  }

  // 1. Create the user in the database if they don't exist
  const user = await prisma.user.upsert({
    where: { email: testUserEmail },
    update: {
      status: 'ACTIVE', // Ensure user is active
    },
    create: {
      name: 'Test User',
      email: testUserEmail,
      image: '',
      status: 'ACTIVE', // Set status to ACTIVE for new users
    },
  })

  console.log('User created/updated:', {
    id: user.id,
    email: user.email,
    status: user.status,
  })

  // 2. Programmatically log in using the new credentials provider
  // This is much faster and more reliable than a UI-based login.
  const response = await request.post(
    'http://localhost:3000/api/auth/callback/credentials',
    {
      form: {
        email: testUserEmail,
        // The CSRF token is required for credentials login. We can get it from the sign-in page.
        csrfToken: await getCsrfToken(request),
      },
    }
  )

  // Check if the login was successful
  expect(response.ok()).toBeTruthy()

  // 3. Persist authenticated state returned from the API request context
  const apiStorage = await request.storageState()
  await fs.promises.mkdir('playwright/.auth', { recursive: true })
  await fs.promises.writeFile(authFile, JSON.stringify(apiStorage, null, 2))
  console.log(
    `Authenticated state for ${testUserEmail} saved successfully using API context storage state.`
  )
})

/**
 * Helper function to fetch the CSRF token from the sign-in page.
 * Next-Auth requires this for POST-based sign-ins.
 */
async function getCsrfToken(request: typeof setup.prototype.request) {
  const response = await request.get('http://localhost:3000/api/auth/csrf')
  const json = await response.json()
  if (!json?.csrfToken) {
    throw new Error('CSRF token not found. Is the server running?')
  }
  return json.csrfToken
}
