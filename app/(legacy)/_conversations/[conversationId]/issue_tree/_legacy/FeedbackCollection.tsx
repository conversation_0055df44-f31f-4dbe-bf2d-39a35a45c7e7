// This is old feedback collection code, keep just in case

// import React, { useState, useEffect } from "react";
// import { Rating } from "@smastrom/react-rating";
// import { Textarea } from "@/components/ui/textarea";
// import { Checkbox } from "@/components/ui/checkbox";
// import { Button } from "@/components/ui/button";
// import { useRouter } from "next/navigation";
// import toast from "react-hot-toast";
// import axios from "axios";
// import useIssueTreeStore from "@/app/stores/issuetree";
// import { Badge } from "@/components/ui/badge";
// import mixpanel from "@/app/libs/mixpanel";

// type RatingKeys = "smoothness" | "quality" | "effort";
// type NullableRating = Record<RatingKeys, number | null>;

// const questionTextMap: Record<RatingKeys, string> = {
//   smoothness: "Overall Flow; Smoothness of Journey",
//   quality: "Relevance and Clarity of Questions",
//   effort:
//     "Did we overwhelm you with the task load? [one: overwhelming; five: reasonable]",
// };

// interface FeedbackCollectionProps {
//   conversationId: string;
//   isReportGenerated: boolean;
//   draftText: string;
// }

// const FeedbackCollection: React.FC<FeedbackCollectionProps> = ({
//   conversationId,
//   isReportGenerated,
//   draftText,
// }) => {
//   const router = useRouter();
//   const [ratings, setRatings] = useState<NullableRating>({
//     smoothness: null,
//     quality: null,
//     effort: null,
//   });
//   const [feedback, setFeedback] = useState<string>("");
//   const [canContact, setCanContact] = useState<boolean>(false);
//   const [summaryText, setSummaryText] = useState<string>("");
//   const issueTreeStore = useIssueTreeStore();

//   let isSetSummaryText = false;
//   useEffect(() => {
//     if (!isSetSummaryText) {
//       isSetSummaryText = true;
//       setSummaryText(issueTreeStore.summaryText);
//     }
//   }, [issueTreeStore.summaryText]);

//   const handleRating = (question: RatingKeys, rate: number) => {
//     setRatings((prevRatings) => ({
//       ...prevRatings,
//       [question]: rate,
//     }));
//   };

//   const handleFinishClick = () => {
//     if (isFeedbackComplete) {
//       const outputObject = {
//         ...ratings,
//         canContact: canContact,
//         feedback: feedback,
//         questionTextMap: questionTextMap,
//       };

//       mixpanel.track("finish_feedback_click");

//       axios
//         .post("/api/feedback/create", {
//           outputObject,
//           conversationId,
//           canContact,
//         })
//         .then((response) => {
//           if (!response.data.error) {
//             // Redirect to the same conversationm, but with the new issue tree
//             toast.success("Feedback Done!", {
//               duration: 2000,
//             });
//             router.push(`/conversations/${conversationId}/result`);
//           } else {
//             toast.error(response.data.error);
//           }
//         })
//         .catch((error) => {
//           console.log("Error in API call:", error);
//           toast.error("Something went wrong. Please try again.");
//         });
//     } else {
//       toast.error("Please complete the feedback before proceeding");
//     }
//   };

//   // Check if all ratings are provided
//   const isFeedbackComplete = Object.values(ratings).every(
//     (rate) => rate !== null
//   );

//   return (
//     <div className="flex flex-col self-center max-w-7xl items-center">
//       <h1 className="font-bold text-2xl text-zinc-700 pt-4">
//         Generation takes around 2-3 minutes, great time for feedback
//       </h1>
//       <div className="flex flex-row bg-red-100 self-center p-4 mx-12 justify-between">
//         <IdeaPitch summaryText={summaryText} />
//         <div className="flex flex-col max-w-sm p-4 mx-auto bg-gray-200 rounded-lg shadow-lg flex-grow">
//           <div className="flex flex-col justify-between flex-grow">
//             {" "}
//             {(["smoothness", "quality", "effort"] as RatingKeys[]).map(
//               (question, idx) => (
//                 <div key={idx} className="flex flex-col mb-4">
//                   <label className="mb-2">{questionTextMap[question]}</label>
//                   <Rating
//                     value={ratings[question] ?? 0}
//                     onChange={(rate: number) => handleRating(question, rate)}
//                   />
//                 </div>
//               )
//             )}
//             <Textarea
//               className="w-full h-16 text-sm p-1 rounded-md transition-shadow overflow-auto bg-green-50 border-green-300"
//               placeholder="Anything you want to share"
//               value={feedback}
//               onChange={(e) => setFeedback(e.target.value)}
//             />
//             <div className="flex items-center space-x-2 py-2 flex-shrink-0">
//               <Checkbox
//                 id="terms"
//                 checked={canContact}
//                 onCheckedChange={(e) => {
//                   if (typeof e === "boolean") {
//                     setCanContact(e);
//                   } else if (e === "indeterminate") {
//                     // Handle indeterminate state if necessary
//                     // For example, you can set it to a default value
//                     setCanContact(false);
//                   }
//                 }}
//               />

//               <label
//                 htmlFor="terms"
//                 className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
//               >
//                 Is it okay to contact you for more feedback?
//               </label>
//             </div>
//             {/* show things are streaming when final_doc API is running*/}
//             {!isReportGenerated && draftText.length > 0 && (
//               <>
//                 <h1 className="font-bold text-xs text-red-400 pt-4 text-center">
//                   Please do not refresh the page when text is still generating
//                 </h1>

//                 <Badge className="self-center p-2 text-white rounded m-2 bg-zinc-400 hover:bg-zinc-400 max-w-xl">
//                   {"Text generated so far: " + draftText.length + " characters"}
//                 </Badge>
//               </>
//             )}
//             {/* Only show reroute button after doc is ready */}
//             {isReportGenerated && (
//               <Button
//                 disabled={!isFeedbackComplete}
//                 className={`self-center p-4 text-white rounded m-4 ${
//                   isFeedbackComplete
//                     ? "bg-green-400 hover:bg-green-500"
//                     : "bg-gray-400 cursor-not-allowed"
//                 } max-w-xl`}
//                 onClick={handleFinishClick}
//               >
//                 {isFeedbackComplete
//                   ? "Result is here!"
//                   : "Please complete the feedback before proceeding"}
//               </Button>
//             )}
//           </div>{" "}
//         </div>
//         <AskForSth />
//       </div>
//     </div>
//   );
// };

// interface IdeaPitchProps {
//   summaryText: string;
// }

// const IdeaPitch: React.FC<IdeaPitchProps> = ({ summaryText }) => {
//   const handleCopyText = (text: string) => {
//     navigator.clipboard.writeText(text).then(
//       () => {
//         toast.success("Copied to clipboard!");
//       },
//       (err) => {
//         toast.error("Could not copy text: ", err);
//       }
//     );
//   };

//   const handleCopySummaryContext = () => {
//     handleCopyText(summaryText);
//   };

//   return (
//     <div className="flex flex-col justify-between h-full p-4 text-zinc-700">
//       <div>
//         <h1 className="p-2">
//           Did you know? <strong>Context</strong> is vital to harness the full
//           potential of Large Language Models like ChatGPT.
//         </h1>
//         <h1 className="p-2">
//           While you have cutting-edge tools at your disposal, they aren&apos;t
//           mind readers (
//           <a
//             href="https://ai.meta.com/research/publications/decoding-speech-perception-from-non-invasive-brain-recordings/"
//             target="_blank"
//             rel="noopener noreferrer"
//             className="text-blue-500 hover:underline hover:text-blue-700"
//           >
//             yet
//           </a>
//           ). They require a detailed context to function optimally.
//         </h1>

//         <h1 className="p-2">
//           Crafting a detailed context can be challenging, akin to onboarding a
//           new team member. That&apos;s why we want to streamline the process,
//           both for you and for our day-to-day work.
//         </h1>
//         <h1 className="p-2">
//           With the relevant context, you can effortlessly use other LLM
//           products. Chances are, they&apos;ll perform better with better
//           context!
//         </h1>
//       </div>

//       {summaryText && (
//         <Button
//           className="self-center mt-auto p-4 text-white rounded mx-4 bg-green-400 hover:bg-green-500"
//           onClick={handleCopySummaryContext}
//         >
//           Copy context
//         </Button>
//       )}
//     </div>
//   );
// };

// const AskForSth: React.FC = () => {
//   const baseUrl = process.env.NEXT_PUBLIC_SERVER_URL || "http://localhost:3000";
//   return (
//     <div className="flex flex-col justify-between h-full p-4 text-zinc-700">
//       <div>
//         <h1 className="p-2">
//           Just so you know, our development involves countless weekends. Tuning
//           the prompts can be ultimate nightmare, and API cost is real!
//         </h1>
//         <h1 className="p-2">
//           If you find our product valuable, we&apos;d appreciate your support.
//           🙇
//         </h1>
//         <h1 className="p-2">
//           <ol>
//             <li>1. Money is the best</li>
//             <li>2. Sharing with your network is cool</li>
//             <li>3. Giving us constructive feedback is great</li>
//           </ol>
//         </h1>

//         <h1 className="p-2">
//           We have{" "}
//           <a
//             href={`${baseUrl}/feedback/`}
//             target="_blank"
//             rel="noopener noreferrer"
//             className="text-blue-500 hover:underline hover:text-blue-700"
//           >
//             feature request page
//           </a>{" "}
//           to collect suggestions
//         </h1>
//       </div>
//       <div className="self-center mt-auto p-4">
//         <a
//           href="https://www.buymeacoffee.com/clarifyai"
//           target="_blank"
//           rel="noreferrer"
//           onClick={() =>
//             mixpanel.track("buy_me_a_coffee_click", {
//               location: "feedback_collection",
//             })
//           }
//         >
//           <img
//             src="https://cdn.buymeacoffee.com/buttons/v2/default-blue.png"
//             alt="Buy Me A Coffee"
//             style={{ height: "60px", width: "217px" }}
//           />
//         </a>
//       </div>

//       {/* <Button
//         className="self-center mt-auto p-4 text-white rounded mx-4 bg-green-400 hover:bg-green-500"
//         onClick={() => toast.success("Clicked donation")}
//       >
//         Donate icon
//       </Button> */}
//     </div>
//   );
// };

// export default FeedbackCollection;
