import { isRateLimited, getRetryAfterSeconds } from '@/app/libs/rateLimiter'

describe('rateLimiter utility', () => {
  const key = 'test:user:action'
  const originalNodeEnv = process.env.NODE_ENV

  beforeEach(() => {
    // Clear the rate limiter state between tests
    jest.clearAllMocks()
  })

  afterAll(() => {
    // Restore original NODE_ENV
    Object.defineProperty(process.env, 'NODE_ENV', {
      value: originalNodeEnv,
      writable: true,
    })
  })

  it('allows first call in production', () => {
    const limited = isRateLimited(key, 1000)
    expect(limited).toBe(false)
  })

  it('blocks immediate successive call within window in production', () => {
    // First call to set timestamp
    isRateLimited('test2', 1000)
    const blocked = isRateLimited('test2', 1000)
    expect(blocked).toBe(true)
  })

  it('allows call after window passes', async () => {
    jest.useFakeTimers()
    // First call to set timestamp
    isRateLimited('test3', 1000)
    // Advance time by window duration + 1ms
    jest.advanceTimersByTime(1001)

    const limited = isRateLimited('test3', 1000)
    expect(limited).toBe(false)

    jest.useRealTimers()
  })

  it('returns correct retry-after seconds', () => {
    jest.useFakeTimers()
    // First call to set timestamp
    isRateLimited('test4', 5000)

    // Immediately check retry after - should be close to the window duration
    const retryAfter = getRetryAfterSeconds('test4', 5000)
    expect(retryAfter).toBeGreaterThan(0) // Should be a positive number
    expect(retryAfter).toBeLessThanOrEqual(5) // Should not exceed the window

    // Advance time by 2 seconds
    jest.advanceTimersByTime(2000)
    const retryAfter2 = getRetryAfterSeconds('test4', 5000)
    expect(retryAfter2).toBeLessThan(retryAfter) // Should be less than initial

    jest.useRealTimers()
  })

  it('uses shorter window in development environment', () => {
    // Temporarily set NODE_ENV to development
    Object.defineProperty(process.env, 'NODE_ENV', {
      value: 'development',
      writable: true,
    })

    // First call
    const limited1 = isRateLimited('test5', 5000)
    expect(limited1).toBe(false)

    // Second call should be blocked even with shorter window
    const limited2 = isRateLimited('test5', 5000)
    expect(limited2).toBe(true)

    // Restore production env
    Object.defineProperty(process.env, 'NODE_ENV', {
      value: 'production',
      writable: true,
    })
  })
})
