import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { FeedbackWidget } from '@/app/(conv)/dragTree/[dragTreeId]/components/feedback'

// Mock dependencies
jest.mock('react-hot-toast')
jest.mock('@/app/libs/mixpanel', () => ({
  track: jest.fn(),
}))

// Mock server actions to avoid next-auth ES module issues
jest.mock('@/app/server-actions/user-feedback', () => ({
  getLatestUserFeedbackForEntity: jest.fn(),
  createUserFeedback: jest.fn(),
}))

// Mock @smastrom/react-rating
jest.mock('@smastrom/react-rating', () => ({
  Rating: ({
    value,
    onChange,
  }: {
    value: number
    onChange: (rate: number) => void
  }) => (
    <div data-testid="rating-component">
      {[1, 2, 3, 4, 5].map(star => (
        <button
          key={star}
          onClick={() => onChange(star)}
          data-testid={`star-${star}`}
          aria-label={`Rate ${star} stars`}
        >
          {star <= value ? '★' : '☆'}
        </button>
      ))}
    </div>
  ),
}))

// Mock environment variable
const originalEnv = process.env.NODE_ENV

describe('FeedbackWidget', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Mock NODE_ENV for tests
    Object.defineProperty(process.env, 'NODE_ENV', {
      value: 'development',
      writable: true,
      configurable: true,
    })
  })

  afterEach(() => {
    Object.defineProperty(process.env, 'NODE_ENV', {
      value: originalEnv,
      writable: true,
      configurable: true,
    })
  })

  const defaultProps = {
    dragTreeId: 'test-drag-tree-id',
  }

  it('should render floating button in development mode', () => {
    render(<FeedbackWidget {...defaultProps} />)

    const button = screen.getByRole('button', { name: /open feedback widget/i })
    expect(button).toBeInTheDocument()
    expect(button).toHaveAttribute('title', 'Share your thoughts 💭')
  })

  it('should not render in production mode', () => {
    Object.defineProperty(process.env, 'NODE_ENV', {
      value: 'production',
      writable: true,
      configurable: true,
    })
    render(<FeedbackWidget {...defaultProps} />)

    const button = screen.queryByRole('button', {
      name: /open feedback widget/i,
    })
    expect(button).not.toBeInTheDocument()
  })

  it('should open modal when floating button is clicked', async () => {
    const user = userEvent.setup()
    render(<FeedbackWidget {...defaultProps} />)

    const button = screen.getByRole('button', { name: /open feedback widget/i })
    await user.click(button)

    await waitFor(() => {
      expect(screen.getByText('Share Your Feedback')).toBeInTheDocument()
      expect(
        screen.getByText('What type of feedback would you like to share?')
      ).toBeInTheDocument()
    })
  })

  it('should render component structure correctly', () => {
    render(<FeedbackWidget {...defaultProps} />)

    // Check that the floating button is rendered
    const button = screen.getByRole('button', { name: /open feedback widget/i })
    expect(button).toBeInTheDocument()
    expect(button).toHaveClass('rounded-full')

    // Check that the button is inside a fixed positioned container
    const container = button.parentElement
    expect(container).toHaveClass('fixed')
  })

  it('should have proper accessibility attributes', () => {
    render(<FeedbackWidget {...defaultProps} />)

    const button = screen.getByRole('button', { name: /open feedback widget/i })
    expect(button).toHaveAttribute('aria-label', 'Open feedback widget')
    expect(button).toHaveAttribute('title', 'Share your thoughts 💭')
  })
})
