export const MAX_BATCH_ITEMS = 12 // hidden cap for quick research batch size

// Research options configuration type
export type ResearchOptionConfig = {
  id: string
  label: string
  iconType: 'clarify' | null // Use string identifier instead of JSX
  urlPattern: string
  isExternal: boolean
  successMessage: string
}

// Shared research options configuration - single source of truth
export const RESEARCH_OPTIONS_CONFIG: ResearchOptionConfig[] = [
  {
    id: 'clarify',
    label: 'Quick Research',
    iconType: 'clarify',
    urlPattern: '', // Internal action, no URL
    isExternal: false,
    successMessage: 'Research initiated for: {prompt}',
  },
  {
    id: 'CHATGPT',
    label: 'Open in ChatGPT',
    iconType: null,
    urlPattern: 'https://chatgpt.com/?q={query}',
    isExternal: true,
    successMessage: 'Opening ChatGPT with screening question context',
  },
  {
    id: 'CLAUDE',
    label: 'Open in Claude AI',
    iconType: null,
    urlPattern: 'https://claude.ai/new?q={query}',
    isExternal: true,
    successMessage: 'Opening Claude AI with screening question context',
  },
  {
    id: 'CONSENSUS',
    label: 'Open in Consensus',
    iconType: null,
    urlPattern: 'https://consensus.app/results/?q={query}',
    isExternal: true,
    successMessage: 'Opening Consensus with screening question context',
  },
  {
    id: 'PERPLEXITY',
    label: 'Open in Perplexity AI',
    iconType: null,
    urlPattern: 'https://www.perplexity.ai/?q={query}',
    isExternal: true,
    successMessage: 'Opening Perplexity AI with screening question context',
  },
  {
    id: 'GROK',
    label: 'Open in Grok',
    iconType: null,
    urlPattern: 'https://grok.com/?q={query}',
    isExternal: true,
    successMessage: 'Opening Grok with screening question context',
  },
]
