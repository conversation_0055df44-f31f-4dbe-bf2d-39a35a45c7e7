import React from 'react'
import { cn } from '@/lib/utils'

type TransitionWrapperProps = {
  children: React.ReactNode
  isVisible: boolean
  className?: string
  duration?: 'fast' | 'normal' | 'slow'
  variant?: 'fade' | 'slide-up' | 'slide-down' | 'scale'
}

/**
 * Transition wrapper component for smooth UI state changes
 * Provides consistent animation patterns across the application
 */
export const TransitionWrapper: React.FC<TransitionWrapperProps> = ({
  children,
  isVisible,
  className = '',
  duration = 'normal',
  variant = 'fade',
}) => {
  const durationClasses = {
    fast: 'duration-200',
    normal: 'duration-300',
    slow: 'duration-500',
  }

  const variantClasses = {
    fade: isVisible ? 'animate-in fade-in' : 'animate-out fade-out',
    'slide-up': isVisible
      ? 'animate-in slide-in-from-bottom-4 fade-in'
      : 'animate-out slide-out-to-bottom-4 fade-out',
    'slide-down': isVisible
      ? 'animate-in slide-in-from-top-4 fade-in'
      : 'animate-out slide-out-to-top-4 fade-out',
    scale: isVisible
      ? 'animate-in zoom-in-95 fade-in'
      : 'animate-out zoom-out-95 fade-out',
  }

  if (!isVisible) {
    return null
  }

  return (
    <div
      className={cn(
        variantClasses[variant],
        durationClasses[duration],
        className
      )}
    >
      {children}
    </div>
  )
}

export default TransitionWrapper
