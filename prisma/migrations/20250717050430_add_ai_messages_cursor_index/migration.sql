/*
  Warnings:

  - A unique constraint covering the columns `[message_id,step_order]` on the table `ai_execution_steps` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterEnum
ALTER TYPE "AIUsageType" ADD VALUE 'CHAT';

-- CreateIndex
CREATE INDEX "ai_attachments_file_type_idx" ON "ai_attachments"("file_type");

-- CreateIndex
CREATE INDEX "ai_attachments_message_id_created_at_idx" ON "ai_attachments"("message_id", "created_at");

-- CreateIndex
CREATE INDEX "ai_conversations_user_id_updated_at_idx" ON "ai_conversations"("user_id", "updated_at");

-- CreateIndex
CREATE INDEX "ai_conversations_created_at_idx" ON "ai_conversations"("created_at");

-- CreateIndex
CREATE INDEX "ai_execution_steps_type_created_at_idx" ON "ai_execution_steps"("type", "created_at");

-- CreateIndex
CREATE INDEX "ai_execution_steps_parallel_key_idx" ON "ai_execution_steps"("parallel_key");

-- CreateIndex
CREATE INDEX "ai_execution_steps_parent_step_id_idx" ON "ai_execution_steps"("parent_step_id");

-- CreateIndex
CREATE UNIQUE INDEX "ai_execution_steps_message_id_step_order_key" ON "ai_execution_steps"("message_id", "step_order");

-- CreateIndex
CREATE INDEX "ai_messages_conversation_id_created_at_idx" ON "ai_messages"("conversation_id", "created_at");

-- CreateIndex
CREATE INDEX "ai_messages_role_created_at_idx" ON "ai_messages"("role", "created_at");

-- CreateIndex
CREATE INDEX "ai_messages_created_at_idx" ON "ai_messages"("created_at");
