'use client'

import React, { Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import ScreeningClient from '@/app/(conv)/screening/components/ScreeningClient'
import UTMPersistOnAuth from '@/app/(conv)/screening/components/UTMPersistOnAuth'

// Wrapper component to handle useSearchParams inside Suspense boundary
function ScreeningPageContent(): React.JSX.Element {
  const searchParams = useSearchParams()
  const prefilledQuestion = searchParams.get('question')

  return <ScreeningClient prefilledQuestion={prefilledQuestion} />
}

export default function ScreeningPage(): React.JSX.Element {
  return (
    <div className="w-full h-screen bg-gray-50 overflow-hidden">
      {/* Persist any captured UTM values into DB once authenticated */}
      <UTMPersistOnAuth />
      <Suspense fallback={<div>Loading...</div>}>
        <ScreeningPageContent />
      </Suspense>
    </div>
  )
}
