import { NextRequest, NextResponse } from 'next/server'
import { getNodeContentOnDemand } from '@/app/server-actions/drag-tree/get-tree-structure'
import { isRateLimited, getRetryAfterSeconds } from '@/app/libs/rateLimiter'
import { auth } from '@/auth'

/**
 * GET /api/dragtree/node-content
 *
 * On-demand endpoint for fetching node content when user interacts with specific nodes
 * Implements lazy loading pattern to reduce initial payload size
 *
 * Query Parameters:
 * - nodeId: string (required) - The node ID to fetch content for
 * - includeMetrics: boolean (optional) - Include performance metrics in response
 */
export async function GET(request: NextRequest) {
  const startTime = performance.now()

  try {
    // Validate authentication
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Extract query parameters
    const { searchParams } = new URL(request.url)
    const nodeId = searchParams.get('nodeId')
    const includeMetrics = searchParams.get('includeMetrics') === 'true'

    if (!nodeId) {
      return NextResponse.json(
        { error: 'nodeId parameter is required' },
        { status: 400 }
      )
    }

    // Rate limiting to prevent abuse
    const rateLimitKey = `${session.user.id}:dragtree:node-content:${nodeId}`
    if (isRateLimited(rateLimitKey, 1000)) {
      // 1 second cooldown
      const retryAfter = getRetryAfterSeconds(rateLimitKey, 1000)
      return NextResponse.json(
        { error: 'Too many requests – please wait a moment.' },
        {
          status: 429,
          headers: { 'Retry-After': retryAfter.toString() },
        }
      )
    }

    console.log(
      `🚀 [API] /dragtree/node-content - Fetching content for node: ${nodeId}`
    )

    // Fetch node content on-demand
    const result = await getNodeContentOnDemand(nodeId)
    const totalTime = performance.now() - startTime

    if (!result.success) {
      console.log(`❌ [API] /dragtree/node-content - Failed: ${result.error}`)
      return NextResponse.json(
        { error: result.error },
        { status: result.error === 'Unauthorized' ? 401 : 404 }
      )
    }

    // Prepare response with optional metrics
    const response: any = {
      success: true,
      data: result.data,
    }

    if (includeMetrics) {
      response.metrics = {
        ...result.metrics,
        totalRequestTime: totalTime,
        endpoint: 'node-content',
        timestamp: new Date().toISOString(),
      }
    }

    console.log(`✅ [API] /dragtree/node-content - Success:`, {
      nodeId: nodeId,
      contentItemCount: result.data.content_items.length,
      payloadSize: `${(result.metrics.payloadSize / 1024).toFixed(2)}KB`,
      totalTime: `${totalTime.toFixed(2)}ms`,
    })

    // Add performance headers for monitoring
    const headers: Record<string, string> = {
      'X-Response-Time': `${totalTime.toFixed(2)}ms`,
      'X-Payload-Size': result.metrics.payloadSize.toString(),
      'X-Content-Items': result.data.content_items.length.toString(),
    }

    return NextResponse.json(response, { headers })
  } catch (error) {
    const totalTime = performance.now() - startTime
    console.error('❌ [API] /dragtree/node-content - Unexpected error:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        requestTime: `${totalTime.toFixed(2)}ms`,
      },
      {
        status: 500,
        headers: {
          'X-Response-Time': `${totalTime.toFixed(2)}ms`,
        },
      }
    )
  }
}

/**
 * OPTIONS handler for CORS preflight requests
 */
export async function OPTIONS() {
  return NextResponse.json(
    {},
    {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    }
  )
}
