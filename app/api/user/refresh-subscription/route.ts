import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { clearUserTierCache } from '@/app/libs/model-config'

export const runtime = 'nodejs'
export const dynamic = 'force-dynamic'

/**
 * API endpoint to refresh user subscription data
 * Clears caches and forces fresh data fetch from database
 */
export async function POST(_req: NextRequest) {
  try {
    // Check authentication
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session.user.id

    // Clear the model config cache for this user
    clearUserTierCache(userId)

    console.log(`🔄 [Refresh Subscription] Cleared caches for user: ${userId}`)

    return NextResponse.json({
      success: true,
      message: 'Subscription data refreshed successfully',
      userId: userId,
    })
  } catch (error) {
    console.error('❌ [Refresh Subscription] Error:', error)
    return NextResponse.json(
      { error: 'Failed to refresh subscription data' },
      { status: 500 }
    )
  }
}
