# States Components

## Overview

State management components for the dragTree interface that handle different UI states: loading, error, generating content, and main content display.

## Components

### **LoadingState.tsx**

**Purpose**: Loading indicator for initial dragTree data loading

**Key Features**:

- Animated loading spinner with customizable size (`sm`, `md`, `lg`)
- Optional message display (configurable via `showMessage`)
- Header integration with session and debug controls
- Default message: "Loading..."

**Usage**:

```tsx
import { LoadingState } from '@/app/(conv)/dragTree/[dragTreeId]/components/states'
;<LoadingState
  session={session}
  showSyncTest={showSyncTest}
  onToggleSyncTest={onToggleSyncTest}
  dragTreeId={dragTreeId}
  message="Loading tree..."
  size="md"
  showMessage={true}
/>
```

### **ErrorState.tsx**

**Purpose**: Error display and recovery options for dragTree failures

**Key Features**:

- Error message display with retry functionality
- Header integration with debug controls
- Error clearing mechanism via `onClearError`
- Session-aware error handling

**Usage**:

```tsx
import { ErrorState } from '@/app/(conv)/dragTree/[dragTreeId]/components/states'
;<ErrorState
  session={session}
  errorMessage="Failed to load tree data"
  showSyncTest={showSyncTest}
  onToggleSyncTest={onToggleSyncTest}
  onClearError={clearError}
/>
```

### **GeneratingState.tsx**

**Purpose**: Shows progress during AI content generation

**Key Features**:

- Generation progress display
- Header integration
- Session management
- Debug controls integration

**Usage**:

```tsx
import { GeneratingState } from '@/app/(conv)/dragTree/[dragTreeId]/components/states'
;<GeneratingState
  session={session}
  showSyncTest={showSyncTest}
  onToggleSyncTest={onToggleSyncTest}
  generationProgress="Generating categories..."
/>
```

### **MainContent.tsx**

**Purpose**: Main interactive interface container for dragTree functionality

**Key Features**:

- **Header Integration**: Session management, debug controls, tutorial controls
- **Layout Management**: ResizableLayout with HierarchicalOutline and VisualFlowDiagram
- **Tutorial System**: Optional tutorial overlay with completion/skip handlers
- **Loading States**: Supports both tree loading and node generation loading
- **Responsive Design**: Adaptable layout for different screen sizes

**Props**:

```typescript
type MainContentProps = {
  // Basic state
  session: Session | null
  showSyncTest: boolean
  onToggleSyncTest: () => void
  dragTreeId: string | null

  // Loading states
  isLoading: boolean // Node generation loading
  isDragTreeLoading?: boolean // Tree loading state
  streamingContent: string // AI streaming content (currently unused)
  generatingNodeId: string // Currently generating node (currently unused)

  // Generation handlers (currently unused but kept for future compatibility)
  onGenerateSimilarQuestions: (
    nodeId: string,
    guidance: string
  ) => Promise<void>
  onGenerateSimilarCategories: (
    nodeId: string,
    guidance: string
  ) => Promise<void>

  // Tutorial system
  showTutorial?: boolean
  onToggleTutorial?: () => void
  onTutorialComplete?: () => void
  onTutorialSkip?: () => void
}
```

**Usage**:

```tsx
import { MainContent } from '@/app/(conv)/dragTree/[dragTreeId]/components/states'
;<MainContent
  session={session}
  showSyncTest={showSyncTest}
  onToggleSyncTest={onToggleSyncTest}
  dragTreeId={dragTreeId}
  isLoading={isLoading}
  isDragTreeLoading={isDragTreeLoading}
  streamingContent=""
  generatingNodeId=""
  onGenerateSimilarQuestions={handleGenerateSimilarQuestions}
  onGenerateSimilarCategories={handleGenerateSimilarCategories}
  showTutorial={shouldShowTutorial}
  onToggleTutorial={handleShowTutorial}
  onTutorialComplete={handleTutorialComplete}
  onTutorialSkip={handleTutorialSkip}
/>
```

## Import Path

```tsx
import {
  LoadingState,
  ErrorState,
  GeneratingState,
  MainContent,
} from '@/app/(conv)/dragTree/[dragTreeId]/components/states'
```

## Architecture

### **State Flow in Client.tsx**

```tsx
export default function DragTreeClient({ dragTreeId, initialTreeData }) {
  // State determination logic
  if (errorMessage) return <ErrorState ... />
  if (isGenerating) return <FuturisticLoadingDemo ... />
  if (isDragTreeLoading) return <LoadingState ... />
  if (frontendTreeStructure) return <MainContent ... />

  return null // Fallback
}
```

### **Component Responsibilities**

1. **LoadingState**: Initial data loading feedback
2. **ErrorState**: Error handling and recovery
3. **GeneratingState**: AI generation progress (currently using FuturisticLoadingDemo instead)
4. **MainContent**: Main application interface orchestration

### **Design Patterns**

- **Consistent Props**: All state components receive session and debug props
- **Header Integration**: All components include Header with appropriate state
- **Error Boundaries**: Components handle their own error states
- **Accessibility**: Loading states include appropriate ARIA labels
- **Performance**: Components use React.memo where appropriate

## Future Enhancements

1. **Enhanced Error Recovery**: Automatic retry logic with exponential backoff
2. **Progress Tracking**: More detailed progress indicators for long operations
3. **State Persistence**: Remember user preferences for debug controls
4. **Mobile Optimization**: Touch-friendly interactions for mobile devices
5. **Internationalization**: Multi-language support for state messages
