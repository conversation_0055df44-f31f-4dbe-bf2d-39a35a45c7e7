# Magic Link Authentication Implementation

This document outlines the implementation of magic link authentication using NextAuth v5 and Resend email service, alongside the existing Google OAuth provider.

## Current Status

⚠️ **TEMPORARILY DISABLED** - The magic link functionality is currently commented out in the frontend while domain setup is completed.

The authentication system currently supports:

1. **Google OAuth** - Active social authentication
2. **Magic Link Email** - Implemented but disabled (ready for future activation)

## Technical Implementation

### Dependencies Added

```bash
npm install resend nodemailer @types/nodemailer
```

### NextAuth Configuration

#### Main Auth Configuration (`auth.ts`)

- Added `EmailProvider` from NextAuth v5
- Integrated Resend client for email delivery
- Custom email template with branded design
- Proper error handling for email sending failures

#### Edge-Safe Configuration (`auth-edge.ts`)

- Added Email provider for middleware compatibility
- Simplified configuration without Prisma adapter

### Authentication Form Enhancement

#### New UI Components (`AuthForm.tsx`)

- **Method Selection**: Clean interface showing both Google and Email options
- **Email Input State**: Validation and error handling
- **Sending State**: Loading indicator during email dispatch
- **Confirmation State**: Success message with retry options

#### State Management

- `AuthMethod`: Tracks current authentication method
- `EmailState`: Manages email flow progression
- Email validation with regex pattern
- Comprehensive error handling

### Email Template

Custom HTML email template featuring:

- Branded Clarify AI design
- Clear call-to-action button
- Security messaging
- Responsive layout
- Professional styling

## Environment Variables

Add to your `.env.local`:

```bash
# Resend Configuration
RESEND_API_KEY=your_resend_api_key_here
EMAIL_FROM=<EMAIL>

# Existing NextAuth variables
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_secret_key
```

## User Experience Flow

### Google OAuth Flow

1. User clicks "Continue with Google"
2. Redirected to Google OAuth
3. Returns to `/screening` on success

### Magic Link Flow

1. User clicks "Continue with Email"
2. Enters email address with validation
3. Clicks "Send Magic Link"
4. Receives branded email with secure link
5. Clicks link to authenticate
6. Redirected to `/screening` on success

## Security Features

- Email validation (client and server-side)
- Magic link expiration (24 hours)
- Secure token generation via NextAuth
- CSRF protection
- Rate limiting through existing middleware

## Error Handling

- Invalid email format detection
- Email sending failure recovery
- Network error handling
- User-friendly error messages
- Retry mechanisms

## Testing

All existing tests pass:

- ✅ 650 tests passed
- ✅ TypeScript compilation successful
- ✅ Build process completed
- ✅ No breaking changes to existing functionality

## Database Schema

Uses existing NextAuth tables:

- `users` - User account information
- `accounts` - OAuth provider accounts
- `sessions` - User sessions
- `verification_token` - Magic link tokens

## Production Considerations

1. **Email Deliverability**: Configure Resend domain authentication
2. **Rate Limiting**: Monitor magic link request frequency
3. **Analytics**: Track authentication method usage
4. **Monitoring**: Set up alerts for email delivery failures

## Re-enabling Magic Link Authentication

To re-enable the magic link functionality after domain setup:

1. **Uncomment Frontend Code** in `app/(landing)/components/AuthForm.tsx`:
   - Uncomment import statements for `HiMail` and `Label`
   - Uncomment type definitions for `AuthMethod` and `EmailState`
   - Uncomment state variables and functions
   - Uncomment the email UI components

2. **Configure Domain** in Resend:
   - Set up domain authentication
   - Update `EMAIL_FROM` environment variable

3. **Test Email Delivery**:
   - Verify emails are delivered successfully
   - Test spam folder placement
   - Confirm link functionality

## Future Enhancements

- Email template customization
- Multi-language support
- Enhanced security options
- Analytics dashboard
- A/B testing for authentication flows
