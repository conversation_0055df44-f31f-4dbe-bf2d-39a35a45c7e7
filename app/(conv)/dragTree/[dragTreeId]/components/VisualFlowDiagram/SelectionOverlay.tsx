'use client'
import React, { useCallback, useRef, useState } from 'react'
import { useSelectionStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useSelectionStore'

type SelectionOverlayProps = {
  className?: string
}

/**
 * SelectionOverlay Component
 *
 * Handles mouse interactions for drawing selection rectangles over the React Flow canvas.
 * Only active when selection mode is enabled.
 */
export const SelectionOverlay: React.FC<SelectionOverlayProps> = ({
  className,
}) => {
  const overlayRef = useRef<HTMLDivElement>(null)
  // const { project } = useReactFlow() // Currently unused

  // Track mouse state to differentiate between click and drag
  const [mouseDownPos, setMouseDownPos] = useState<{
    x: number
    y: number
  } | null>(null)
  const [isDragging, setIsDragging] = useState<boolean>(false)
  const dragThreshold = 5 // pixels to move before considering it a drag

  const isSelectionMode = useSelectionStore(state => state.isSelectionMode)
  const isDrawing = useSelectionStore(state => state.isDrawing)
  const currentRectangle = useSelectionStore(state => state.currentRectangle)

  const startDrawing = useSelectionStore(state => state.startDrawing)
  const updateDrawing = useSelectionStore(state => state.updateDrawing)
  const finishDrawing = useSelectionStore(state => state.finishDrawing)
  const cancelDrawing = useSelectionStore(state => state.cancelDrawing)

  // Handle mouse down to potentially start drawing selection rectangle
  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      if (!isSelectionMode || e.button !== 0) return // Only left mouse button

      const rect = overlayRef.current?.getBoundingClientRect()
      if (!rect) return

      const x = e.clientX - rect.left
      const y = e.clientY - rect.top

      // Store mouse down position but don't start drawing yet
      setMouseDownPos({ x, y })
      setIsDragging(false)

      // Don't prevent default here - let React Flow handle the initial click
    },
    [isSelectionMode]
  )

  // Handle mouse move to detect drag and update selection rectangle
  const handleMouseMove = useCallback(
    (e: React.MouseEvent) => {
      if (!isSelectionMode || !mouseDownPos) return

      const rect = overlayRef.current?.getBoundingClientRect()
      if (!rect) return

      const x = e.clientX - rect.left
      const y = e.clientY - rect.top

      // Check if we've moved enough to consider this a drag
      const deltaX = Math.abs(x - mouseDownPos.x)
      const deltaY = Math.abs(y - mouseDownPos.y)
      const hasDraggedEnough = deltaX > dragThreshold || deltaY > dragThreshold

      if (!isDragging && hasDraggedEnough) {
        // Start drawing selection rectangle
        setIsDragging(true)
        startDrawing(mouseDownPos.x, mouseDownPos.y)
        e.preventDefault()
        e.stopPropagation()
      }

      if (isDragging && isDrawing) {
        // Update the selection rectangle
        updateDrawing(x, y)
        e.preventDefault()
      }
    },
    [
      isSelectionMode,
      mouseDownPos,
      isDragging,
      isDrawing,
      dragThreshold,
      startDrawing,
      updateDrawing,
    ]
  )

  // Handle mouse up to finish drawing selection rectangle
  const handleMouseUp = useCallback(
    (e: React.MouseEvent) => {
      if (!isSelectionMode) return

      if (isDragging && isDrawing) {
        // Finish the selection rectangle
        e.preventDefault()
        e.stopPropagation()
        finishDrawing()
      }

      // Reset mouse state
      setMouseDownPos(null)
      setIsDragging(false)
    },
    [isSelectionMode, isDragging, isDrawing, finishDrawing]
  )

  // Handle mouse leave to cancel drawing if mouse leaves the overlay
  const handleMouseLeave = useCallback(() => {
    if (isDragging && isDrawing) {
      cancelDrawing()
    }
    // Reset mouse state
    setMouseDownPos(null)
    setIsDragging(false)
  }, [isDragging, isDrawing, cancelDrawing])

  // Render a selection rectangle
  const renderRectangle = (rect: typeof currentRectangle) => {
    if (!rect) return null

    const left = Math.min(rect.startX, rect.endX)
    const top = Math.min(rect.startY, rect.endY)
    const width = Math.abs(rect.endX - rect.startX)
    const height = Math.abs(rect.endY - rect.startY)

    return (
      <div
        key={rect.id}
        className={`absolute border-2 border-emerald-500 bg-emerald-100/20 pointer-events-none ${
          rect.isActive ? 'border-dashed' : 'border-solid'
        }`}
        style={{
          left: `${left}px`,
          top: `${top}px`,
          width: `${width}px`,
          height: `${height}px`,
        }}
      />
    )
  }

  // Don't render overlay if not in selection mode
  if (!isSelectionMode) {
    return null
  }

  return (
    <div
      ref={overlayRef}
      className={`absolute inset-0 z-10 ${className || ''}`}
      style={{
        cursor: isDragging ? 'crosshair' : 'default',
        // Always receive pointer events but handle them conditionally
        pointerEvents: 'auto',
        // Make overlay transparent so React Flow is visible underneath
        backgroundColor: 'transparent',
      }}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseLeave}
    >
      {/* Only render current drawing rectangle - completed rectangles are cleared automatically */}
      {currentRectangle && renderRectangle(currentRectangle)}
    </div>
  )
}
