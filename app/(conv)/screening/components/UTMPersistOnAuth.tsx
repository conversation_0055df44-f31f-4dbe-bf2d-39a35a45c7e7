'use client'

import { useEffect, useRef } from 'react'
import { useSession } from 'next-auth/react'
import { persistUtmFromCookie } from '@/app/server-actions/utm'

/**
 * Client shim to call a server action as soon as the user is authenticated
 * to persist UTM data from cookie into User.metadata.
 */
export default function UTMPersistOnAuth(): null {
  const { status } = useSession()
  const fired = useRef<boolean>(false)

  useEffect(() => {
    if (status !== 'authenticated' || fired.current) return
    fired.current = true
    void persistUtmFromCookie()
  }, [status])

  return null
}
