'use client'

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { FiZap, FiX } from 'react-icons/fi'
import { cn } from '@/lib/utils'
import { logEventWithContext } from '@/app/libs/logging'
import { SubscriptionTier } from '@prisma/client'
import type { UpgradeBannerProps } from './types'

/**
 * Unified upgrade banner component for larger promotional areas
 * Uses emerald-teal gradient with optional dismissal functionality
 */
export const UpgradeBanner: React.FC<UpgradeBannerProps> = ({
  title = 'Unlock Premium Features',
  message,
  context,
  currentTier,
  onClick,
  className,
  dismissible = false,
  buttonText = 'Upgrade to Pro',
}) => {
  const router = useRouter()
  const { data: session } = useSession()
  const [isDismissed, setIsDismissed] = useState<boolean>(false)

  const handleUpgradeClick = () => {
    // Get current tier for analytics
    const tier =
      currentTier ||
      (((session?.user as any)?.subscription_tier ||
        (session?.user as any)?.subscription?.tier ||
        SubscriptionTier.FREE) as SubscriptionTier)

    // Log analytics event
    logEventWithContext('click_upgrade_banner', session?.user?.id, undefined, {
      context,
      current_tier: tier,
      title,
      message: message.slice(0, 100), // Truncate for analytics
    })

    if (onClick) {
      onClick()
    } else {
      router.push('/subscription')
    }
  }

  const handleDismiss = () => {
    // Log dismissal for analytics
    const tier =
      currentTier ||
      (((session?.user as any)?.subscription_tier ||
        (session?.user as any)?.subscription?.tier ||
        SubscriptionTier.FREE) as SubscriptionTier)

    logEventWithContext(
      'dismiss_upgrade_banner',
      session?.user?.id,
      undefined,
      {
        context,
        current_tier: tier,
        title,
      }
    )

    setIsDismissed(true)
  }

  if (isDismissed) {
    return null
  }

  return (
    <div
      className={cn(
        'relative rounded-xl p-6 bg-gradient-to-br from-emerald-50 to-teal-50 border border-emerald-200',
        className
      )}
    >
      {/* Dismiss button */}
      {dismissible && (
        <button
          onClick={handleDismiss}
          className="absolute top-4 right-4 p-1 text-emerald-600 hover:text-emerald-800 transition-colors"
        >
          <FiX size={16} />
        </button>
      )}

      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
        {/* Content */}
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-emerald-900 mb-2">
            {title}
          </h3>
          <p className="text-emerald-700 text-sm leading-relaxed">{message}</p>
        </div>

        {/* Upgrade button */}
        <button
          onClick={handleUpgradeClick}
          className={cn(
            'inline-flex items-center justify-center px-6 py-3 font-medium rounded-lg transition-all duration-200',
            'bg-gradient-to-r from-emerald-500 to-teal-600 text-white shadow-md',
            'hover:from-emerald-600 hover:to-teal-700 hover:shadow-lg',
            'transform hover:scale-[1.02] active:scale-[0.98]',
            'focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2',
            'whitespace-nowrap'
          )}
        >
          <FiZap size={16} className="mr-2" />
          <span>{buttonText}</span>
        </button>
      </div>
    </div>
  )
}
