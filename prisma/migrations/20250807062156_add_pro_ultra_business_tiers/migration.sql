-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "SubscriptionTier" ADD VALUE 'PRO';
ALTER TYPE "SubscriptionTier" ADD VALUE 'ULTRA';
ALTER TYPE "SubscriptionTier" ADD VALUE 'BUSINESS';

-- AlterTable
ALTER TABLE "users" ADD COLUMN     "subscription_cancel_pending" BOOLEAN NOT NULL DEFAULT false;
