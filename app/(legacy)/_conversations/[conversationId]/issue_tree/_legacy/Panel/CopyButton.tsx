import React from 'react'
import { MdOutlineContentCopy } from 'react-icons/md'
import { Button } from '@/components/ui/button'
import toast from 'react-hot-toast'
import { usePanelStore } from '@/app/stores/_legacy/panel_store'
import mixpanel from '@/app/libs/mixpanel'

type CopyButtonProps = {
  buttonText: string
  promptTitle: string
  url: string
  copiedText: string
}

const CopyButton: React.FC<CopyButtonProps> = ({
  buttonText,
  promptTitle,
  url,
  copiedText,
}) => {
  const { isOpenNewWindow } = usePanelStore()

  const handleCopyPrompt = async (): Promise<void> => {
    try {
      await navigator.clipboard.writeText(copiedText)

      mixpanel.track('use_constructed_prompt', {
        location: 'prompt_panel',
        model: buttonText,
        prompt_title: promptTitle,
      })

      toast.success('Copied to clipboard!')

      if (isOpenNewWindow && url) {
        setTimeout(() => window.open(url, '_blank'), 1500)
      }
    } catch (err) {
      toast.error(`Could not copy text: ${err}`)
    }
  }

  const buttonTitle = url ? `Copy & Open ${url}` : 'Copy'

  return (
    <Button
      className="bg-blue-100 hover:bg-blue-200 rounded-2xl m-2"
      size="sm"
      variant="ghost"
      onClick={handleCopyPrompt}
      title={buttonTitle}
    >
      <MdOutlineContentCopy className="h-4 w-4 mr-2.5" aria-hidden="true" />
      <span>{buttonText}</span>
    </Button>
  )
}

export default CopyButton
