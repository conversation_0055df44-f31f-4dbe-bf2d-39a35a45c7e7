/**
 * ResearchEditor permissions behavior
 * - Only VIEWER and DUMMY should be read-only
 * - FREE and higher tiers should be editable
 * - No localStorage tier override should affect behavior
 */

import React from 'react'
import { render, screen } from '@testing-library/react'
import { SubscriptionTier } from '@prisma/client'

// Mock next-auth useSession per test
jest.mock('next-auth/react', () => ({
  useSession: jest.fn(),
}))
const { useSession } = require('next-auth/react')

// Mock drag tree store minimal API used by ResearchEditor
jest.mock('@/app/stores/dragtree_store', () => ({
  useDragTreeStore: jest.fn(selector => {
    const state = {
      updateNodeContent: jest.fn(),
      fetchNodeContent: jest.fn(),
      isContentFetching: jest.fn(() => false),
      getNodeContent: (_nodeId: string) =>
        new Map([
          [
            'content_1',
            {
              contentText: 'Existing content',
            },
          ],
        ]),
    }
    return selector ? selector(state) : state
  }),
}))

// Mock UI deps
jest.mock('@/components/ui/textarea', () => ({
  Textarea: (props: any) => (
    <textarea data-testid="editor-textarea" {...props} />
  ),
}))

// Import after mocks
import { ResearchEditor } from '@/app/(conv)/dragTree/[dragTreeId]/components/research/ResearchEditor'

describe('ResearchEditor permissions', () => {
  const baseProps = {
    nodeId: 'node_1',
    contentId: 'content_1',
    editorType: 'textarea' as const,
    placeholder: 'Test placeholder',
    showStatusFooter: true,
  }

  const mockSessionTier = (tier: SubscriptionTier) => {
    useSession.mockReturnValue({
      data: { user: { subscription: { tier } } },
    })
  }

  beforeEach(() => {
    jest.clearAllMocks()
    // Provide a localStorage override that should NOT be respected anymore
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn(() => 'DUMMY'),
        setItem: jest.fn(),
        removeItem: jest.fn(),
        clear: jest.fn(),
      },
      writable: true,
    })
  })

  it('VIEWER should be read-only and show read-only placeholder', () => {
    mockSessionTier(SubscriptionTier.VIEWER)
    render(<ResearchEditor {...baseProps} />)
    const textarea = screen.getByTestId(
      'editor-textarea'
    ) as HTMLTextAreaElement
    expect(textarea.readOnly).toBe(true)
    expect(textarea.placeholder.toLowerCase()).toContain('read-only')
  })

  it('DUMMY should be read-only and show read-only placeholder', () => {
    mockSessionTier(SubscriptionTier.DUMMY)
    render(<ResearchEditor {...baseProps} />)
    const textarea = screen.getByTestId(
      'editor-textarea'
    ) as HTMLTextAreaElement
    expect(textarea.readOnly).toBe(true)
    expect(textarea.placeholder.toLowerCase()).toContain('read-only')
  })

  it('FREE should be editable (ignoring any localStorage override)', () => {
    mockSessionTier(SubscriptionTier.FREE)
    render(<ResearchEditor {...baseProps} />)
    const textarea = screen.getByTestId(
      'editor-textarea'
    ) as HTMLTextAreaElement
    expect(textarea.readOnly).toBe(false)
    expect(textarea.placeholder).toBe('Test placeholder')
  })

  it('PRO should be editable', () => {
    mockSessionTier(SubscriptionTier.PRO)
    render(<ResearchEditor {...baseProps} />)
    const textarea = screen.getByTestId(
      'editor-textarea'
    ) as HTMLTextAreaElement
    expect(textarea.readOnly).toBe(false)
  })
})
