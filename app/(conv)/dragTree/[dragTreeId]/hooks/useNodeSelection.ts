'use client'

import { useCallback, useEffect } from 'react'
import { useReactFlow, Node } from 'reactflow'
import { toast } from 'react-hot-toast'
import {
  useSelectionStore,
  SelectionRectangle,
} from '@/app/(conv)/dragTree/[dragTreeId]/stores/useSelectionStore'
import { useDragTreeStore } from '@/app/stores/dragtree_store/store'

/**
 * Hook for handling node selection logic
 *
 * Provides utilities for:
 * - Detecting which nodes intersect with selection rectangles
 * - Automatically including child nodes when category nodes are selected
 * - Filtering to only include researched nodes
 */
export const useNodeSelection = () => {
  const { getNodes, getViewport } = useReactFlow()
  const selectionRectangles = useSelectionStore(
    state => state.selectionRectangles
  )
  const setSelectedNodes = useSelectionStore(state => state.setSelectedNodes)
  const clearRectangles = useSelectionStore(state => state.clearRectangles)

  // Get researched node data from drag tree store
  const researchedNodeIds = useDragTreeStore(state => state.researchedNodeIds)
  const nodeContent = useDragTreeStore(state => state.nodeContent)
  const frontendTreeStructure = useDragTreeStore(
    state => state.frontendTreeStructure
  )

  /**
   * Check if a node has been researched (has content or is marked as researched)
   */
  const isNodeResearched = useCallback(
    (nodeId: string): boolean => {
      const hasContent =
        nodeContent.has(nodeId) && nodeContent.get(nodeId)!.size > 0
      const isMarkedResearched = researchedNodeIds.has(nodeId)
      return hasContent || isMarkedResearched
    },
    [nodeContent, researchedNodeIds]
  )

  /**
   * Get all descendant node IDs for a given node (for category selection)
   */
  const getDescendantNodeIds = useCallback(
    (nodeId: string): string[] => {
      if (!frontendTreeStructure) return []

      const findNode = (node: any, targetId: string): any => {
        if (node.id === targetId) return node
        for (const child of node.children || []) {
          const found = findNode(child, targetId)
          if (found) return found
        }
        return null
      }

      const collectDescendants = (node: any): string[] => {
        const descendants: string[] = []
        for (const child of node.children || []) {
          descendants.push(child.id)
          descendants.push(...collectDescendants(child))
        }
        return descendants
      }

      const targetNode = findNode(frontendTreeStructure, nodeId)
      return targetNode ? collectDescendants(targetNode) : []
    },
    [frontendTreeStructure]
  )

  /**
   * Check if a rectangle intersects with a node's bounding box
   */
  const doesRectangleIntersectNode = useCallback(
    (rectangle: SelectionRectangle, node: Node): boolean => {
      const viewport = getViewport()

      // Convert node position to screen coordinates
      const nodeScreenX = node.position.x * viewport.zoom + viewport.x
      const nodeScreenY = node.position.y * viewport.zoom + viewport.y
      const nodeWidth = (node.data?.width || 300) * viewport.zoom
      const nodeHeight = (node.data?.height || 100) * viewport.zoom

      // Rectangle bounds
      const rectLeft = Math.min(rectangle.startX, rectangle.endX)
      const rectRight = Math.max(rectangle.startX, rectangle.endX)
      const rectTop = Math.min(rectangle.startY, rectangle.endY)
      const rectBottom = Math.max(rectangle.startY, rectangle.endY)

      // Node bounds
      const nodeLeft = nodeScreenX
      const nodeRight = nodeScreenX + nodeWidth
      const nodeTop = nodeScreenY
      const nodeBottom = nodeScreenY + nodeHeight

      // Check for intersection
      return !(
        rectRight < nodeLeft ||
        rectLeft > nodeRight ||
        rectBottom < nodeTop ||
        rectTop > nodeBottom
      )
    },
    [getViewport]
  )

  /**
   * Find all nodes that intersect with selection rectangles
   * Returns both researched and non-researched nodes for feedback
   */
  const getNodesInSelectionRectangles = useCallback((): {
    researched: string[]
    nonResearched: string[]
  } => {
    const nodes = getNodes()
    const researchedNodeIdsSet = new Set<string>()
    const nonResearchedNodeIdsSet = new Set<string>()
    const handledNodes = new Set<string>() // Nodes already processed under a category

    // Track category selections for toast notifications
    const categorySelections: Array<{
      categoryId: string
      researchedCount: number
      nonResearchedCount: number
    }> = []

    // Helper for classifying a nodeId
    const classifyNode = (nodeId: string) => {
      if (handledNodes.has(nodeId)) return // Skip duplicates
      if (isNodeResearched(nodeId)) {
        researchedNodeIdsSet.add(nodeId)
      } else {
        nonResearchedNodeIdsSet.add(nodeId)
      }
      handledNodes.add(nodeId)
    }

    for (const rectangle of selectionRectangles) {
      for (const node of nodes) {
        if (!doesRectangleIntersectNode(rectangle, node)) continue

        // Skip if this node has already been processed in this iteration
        if (handledNodes.has(node.id)) continue

        // --------------------------------------------------
        // 1. Category nodes – always traverse descendants
        // --------------------------------------------------
        if (node.type === 'customCategoryNode') {
          handledNodes.add(node.id) // Category handled – but do not flag as non-researched for individual toasts

          const descendants = getDescendantNodeIds(node.id)
          let researchedDescendants = 0
          let nonResearchedDescendants = 0

          descendants.forEach(descendantId => {
            // Mark descendant as handled to avoid double-processing later
            if (handledNodes.has(descendantId)) return
            if (isNodeResearched(descendantId)) {
              researchedNodeIdsSet.add(descendantId)
              researchedDescendants++
            } else {
              nonResearchedNodeIdsSet.add(descendantId)
              nonResearchedDescendants++
            }
            handledNodes.add(descendantId)
          })

          categorySelections.push({
            categoryId: node.id,
            researchedCount: researchedDescendants,
            nonResearchedCount: nonResearchedDescendants,
          })

          continue // Done with this node; skip further checks
        }

        // --------------------------------------------------
        // 2. Non-category nodes – classify individually
        // --------------------------------------------------
        classifyNode(node.id)
      }
    }

    // Toast notifications for categories
    if (categorySelections.length > 0) {
      // Aggregate counts across all category selections
      let aggResearched = 0
      let aggSkipped = 0
      const categoryIds = new Set<string>()

      categorySelections.forEach(sel => {
        aggResearched += sel.researchedCount
        aggSkipped += sel.nonResearchedCount
        categoryIds.add(sel.categoryId)
      })

      const message = `${aggResearched} node${aggResearched !== 1 ? 's' : ''} selected${
        aggSkipped > 0
          ? `, ${aggSkipped} node${aggSkipped !== 1 ? 's' : ''} skipped (not researched)`
          : ''
      }`

      toast.dismiss('category-selection')
      if (aggSkipped > 0) {
        toast(`📁 Category Selected\n${message}`, {
          id: 'category-selection',
          icon: '⚠️',
          duration: 4000,
        })
      } else {
        toast.success(`📁 Category Selected\n${message}`, {
          id: 'category-selection',
          duration: 4000,
        })
      }
    }

    // Toast for individual non-researched nodes not covered by category selections
    const categoryDescendantSet = new Set<string>()
    categorySelections.forEach(sel => {
      // Add descendants *and* the category itself to exclusion set
      categoryDescendantSet.add(sel.categoryId)
      getDescendantNodeIds(sel.categoryId).forEach(id =>
        categoryDescendantSet.add(id)
      )
    })

    const individualNonResearched = Array.from(nonResearchedNodeIdsSet).filter(
      id => !categoryDescendantSet.has(id)
    )

    if (individualNonResearched.length > 0) {
      const msg = `${individualNonResearched.length} non-researched node${individualNonResearched.length !== 1 ? 's' : ''} selected – these cannot be used for context until researched`
      toast(`⚠️ ${msg}`, { icon: '🔍', duration: 4000 })
    }

    return {
      researched: Array.from(researchedNodeIdsSet),
      nonResearched: Array.from(nonResearchedNodeIdsSet),
    }
  }, [
    getNodes,
    selectionRectangles,
    isNodeResearched,
    doesRectangleIntersectNode,
    getDescendantNodeIds,
  ])

  /**
   * Update selected nodes based on current selection rectangles
   * This now supports cumulative selection by adding to existing selections
   */
  const updateSelectedNodesFromRectangles = useCallback(() => {
    const { researched, nonResearched } = getNodesInSelectionRectangles()

    // If we have rectangles, add the newly selected nodes to existing selection
    if (selectionRectangles.length > 0) {
      // Get current selected nodes
      const currentSelectedNodes = useSelectionStore.getState().selectedNodeIds
      const currentNonResearched =
        useSelectionStore.getState().nonResearchedNodeIds

      const cumulativeSelection = new Set([
        ...Array.from(currentSelectedNodes),
        ...researched,
      ])

      const cumulativeNonResearched = new Set([
        ...Array.from(currentNonResearched),
        ...nonResearched,
      ])

      setSelectedNodes(
        Array.from(cumulativeSelection),
        Array.from(cumulativeNonResearched)
      )

      // Clear rectangles after processing to prevent them from staying visible
      setTimeout(() => {
        clearRectangles()
      }, 100) // Small delay to ensure selection is processed
    }
  }, [
    getNodesInSelectionRectangles,
    setSelectedNodes,
    selectionRectangles,
    clearRectangles,
  ])

  // Update selected nodes whenever selection rectangles change
  useEffect(() => {
    if (selectionRectangles.length > 0) {
      updateSelectedNodesFromRectangles()
    }
  }, [selectionRectangles.length, updateSelectedNodesFromRectangles])

  return {
    isNodeResearched,
    getDescendantNodeIds,
    getNodesInSelectionRectangles,
    updateSelectedNodesFromRectangles,
  }
}
