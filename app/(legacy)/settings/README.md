# Legacy Settings

This folder contains the settings page that uses the legacy sidebar architecture.

## Files Moved Here

- `page.tsx` - Settings page using legacy Sidebar component

## Current Usage

This settings page is still functional but uses legacy architecture:

- Uses `@/app/(legacy)/components/sidebar/Sidebar` for layout
- Imports `@/app/components/settings/SettingsPage` for content
- Requires authentication via `getCurrentUser`

## Modern Alternative

The settings functionality could be modernized by:

1. Creating a new settings page that uses modern layout components
2. Integrating settings into the dragTree header (similar to export functionality)
3. Using modern modal-based settings interface

## Architecture Notes

**Legacy Settings Architecture:**

- Full-page layout with legacy sidebar
- Separate route at `/settings`
- Uses legacy sidebar navigation

**Potential Modern Architecture:**

- Modal-based settings accessible from dragTree header
- Integrated into modern layout system
- No separate route needed

## Cleanup Strategy

This legacy settings page can be removed once:

1. Settings functionality is integrated into modern dragTree interface
2. User preferences are accessible through modern UI components
3. All settings features are migrated to modern architecture
