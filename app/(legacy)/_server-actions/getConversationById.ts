import prisma from '@/app/libs/prismadb'
import { auth } from '@/auth'
import { ConversationLayoutType } from '@/app/types'

const getConversationById = async (
  conversationId: string
): Promise<ConversationLayoutType | null> => {
  const session = await auth()

  if (!session?.user?.id) {
    return null
  }

  try {
    const conversation = await prisma.conversation.findUnique({
      where: {
        id: conversationId,
      },
      select: {
        id: true,
        creator_id: true,
        conversation_status: true,
        conversation_type: true,
        created_at: true,
        updated_at: true,
        is_hidden: true,
        title: true,
        config: true,
        issue_trees: {
          orderBy: {
            updated_at: 'desc',
          },
          take: 1,
          select: {
            updated_at: true,
          },
        },
      },
    })

    return conversation
      ? {
          ...conversation,
          config_prompt_description:
            (typeof conversation.config === 'object' &&
            conversation.config !== null &&
            'prompt' in conversation.config &&
            typeof conversation.config.prompt === 'object'
              ? (conversation.config.prompt as { description?: string })
                  .description
              : null) ?? null,
          issuetree_updated_at:
            conversation.issue_trees[0]?.updated_at || conversation.updated_at,
        }
      : null

    // return conversation;
  } catch (error: any) {
    console.log(error, 'SERVER_ERROR')
    return null
  }
}

export default getConversationById
