/**
 * Tab ID Prefix Constants
 *
 * These prefixes are used to identify different types of tabs and their lifecycle states.
 */

/**
 * Temporary tab prefix for AI generation/chat tabs that are waiting for completion.
 *
 * TEMP tabs are placeholders shown to users during active generation/chat.
 * They have no business meaning and are always safe to remove on browser reload
 * if they have an assetId (meaning generation completed but tab wasn't transformed).
 *
 * Lifecycle:
 * 1. User starts generation → temp-generate-123 (no assetId)
 * 2. Generation completes → asset-generate-uuid (permanent)
 * 3. Browser reload with temp + assetId → Remove (stale placeholder)
 *
 * Note: Only temp- prefix is used. No legacy ai- support needed.
 */
export const TEMP_TAB_PREFIX = 'temp-'

/**
 * Asset tab prefix for permanent tabs that reference completed assets.
 * These tabs survive browser reloads and represent actual persisted content.
 */
export const ASSET_TAB_PREFIX = 'asset-'

/**
 * Core tab types that don't use prefixes (main, research)
 */
export const CORE_TAB_TYPES = ['main', 'research'] as const

/**
 * Helper functions for tab ID operations
 */
export const TabPrefixUtils = {
  /**
   * Check if a tab ID indicates a temporary placeholder tab
   */
  isTempTab: (tabId: string): boolean => {
    return tabId.startsWith(TEMP_TAB_PREFIX)
  },

  /**
   * Check if a tab ID indicates a permanent asset tab
   */
  isAssetTab: (tabId: string): boolean => {
    return tabId.startsWith(ASSET_TAB_PREFIX)
  },

  /**
   * Create a temporary tab ID for a new AI generation/chat
   */
  createTempTabId: (assetType: string): string => {
    return `${TEMP_TAB_PREFIX}${assetType}-${Date.now()}`
  },

  /**
   * Create a permanent asset tab ID
   */
  createAssetTabId: (assetType: string, assetId: string): string => {
    return `${ASSET_TAB_PREFIX}${assetType}-${assetId}`
  },

  /**
   * Extract asset type from tab ID
   */
  extractAssetType: (tabId: string): string | null => {
    if (tabId.startsWith(TEMP_TAB_PREFIX)) {
      const parts = tabId.substring(TEMP_TAB_PREFIX.length).split('-')
      return parts[0] || null
    }
    if (tabId.startsWith(ASSET_TAB_PREFIX)) {
      const parts = tabId.substring(ASSET_TAB_PREFIX.length).split('-')
      return parts[0] || null
    }
    return null
  },
}
