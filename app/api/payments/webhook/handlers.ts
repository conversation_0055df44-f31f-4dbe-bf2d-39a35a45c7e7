import Stripe from 'stripe'
import {
  createProductRecord,
  updateProductRecord,
  createPriceRecord,
  updatePriceRecord,
  manageSubscriptionRecord,
  handleCheckoutSessionCompleted,
  handleSubscriptionDeleted,
  SubscriptionEventType,
} from './utils'
import {
  upsertInvoiceRecord,
  upsertPaymentIntentRecord,
} from '@/app/api/payments/webhook/invoices'

// Main function to handle different webhook events
export async function handleWebhookEvent(event: Stripe.Event) {
  switch (event.type) {
    case 'product.created':
      await createProductRecord(event.data.object as Stripe.Product)
      break
    case 'product.updated':
      await updateProductRecord(event.data.object as Stripe.Product)
      break
    case 'price.created':
      await createPriceRecord(event.data.object as Stripe.Price)
      break
    case 'price.updated':
      await updatePriceRecord(event.data.object as Stripe.Price)
      break
    case 'customer.subscription.created':
      await manageSubscriptionRecord(
        event.data.object as Stripe.Subscription,
        SubscriptionEventType.Created
      )
      break
    case 'customer.subscription.updated':
      await manageSubscriptionRecord(
        event.data.object as Stripe.Subscription,
        SubscriptionEventType.Updated
      )
      break
    case 'customer.subscription.deleted':
      await handleSubscriptionDeleted(event.data.object as Stripe.Subscription)
      break
    case 'invoice.created':
    case 'invoice.finalized':
    case 'invoice.payment_succeeded':
    case 'invoice.voided':
    case 'invoice.marked_uncollectible':
    case 'invoice.updated':
      await upsertInvoiceRecord(event.data.object as Stripe.Invoice)
      break
    case 'payment_intent.created':
    case 'payment_intent.succeeded':
    case 'payment_intent.payment_failed':
    case 'payment_intent.processing':
    case 'payment_intent.canceled':
    case 'payment_intent.requires_action':
    case 'payment_intent.partially_funded':
      await upsertPaymentIntentRecord(event.data.object as Stripe.PaymentIntent)
      break
    case 'invoice.payment_failed': {
      await upsertInvoiceRecord(event.data.object as Stripe.Invoice)
      console.warn(
        `Invoice payment failed for customer ${(event.data.object as any).customer}`
      )
      break
    }
    case 'checkout.session.completed':
      const checkoutSession = event.data.object as Stripe.Checkout.Session
      if (checkoutSession.mode === 'subscription') {
        await handleCheckoutSessionCompleted(checkoutSession)
      }
      break
    default:
      console.warn(`Unhandled relevant event type: ${event.type}`)
  }
}
