import type { TreeNode } from '@/app/types'
import type { FilterMode } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useFilterStore'
import type { NodeContentItem } from '@/app/stores/dragtree_store/store'
import { DragTreeNodeContentStatus } from '@prisma/client'

// Helper to check if a question node is researchable: no ACTIVE or PROCESSING content
export const isResearchable = (
  node: TreeNode,
  getNodeContent: (nodeId: string) => Map<string, NodeContentItem> | undefined
): boolean => {
  if (node.type !== 'question') return false
  const map = getNodeContent(node.id)
  if (!map) return true
  let hasBlocking = false
  map.forEach(item => {
    if (
      item.status === DragTreeNodeContentStatus.ACTIVE ||
      item.status === DragTreeNodeContentStatus.PROCESSING
    ) {
      hasBlocking = true
    }
  })
  return !hasBlocking
}

// A node is researched if it has any ACTIVE content
export const isResearched = (
  node: TreeNode,
  getNodeContent: (nodeId: string) => Map<string, NodeContentItem> | undefined
): boolean => {
  if (node.type !== 'question') return false
  const map = getNodeContent(node.id)
  if (!map) return false
  let foundActive = false
  map.forEach(item => {
    if (item.status === DragTreeNodeContentStatus.ACTIVE) {
      foundActive = true
    }
  })
  return foundActive
}

// Unread researched: researched with at least one ACTIVE content where metadata.isRead !== true
export const isUnreadResearched = (
  node: TreeNode,
  getNodeContent: (nodeId: string) => Map<string, NodeContentItem> | undefined
): boolean => {
  if (node.type !== 'question') return false
  const map = getNodeContent(node.id)
  if (!map) return false
  let hasActive = false
  let hasUnread = false
  map.forEach(item => {
    if (item.status === DragTreeNodeContentStatus.ACTIVE) {
      hasActive = true
      if (item.metadata?.isRead !== true) {
        hasUnread = true
      }
    }
  })
  return hasActive && hasUnread
}

// Prune tree based on filter mode. Only leaf (question) nodes are filtered.
// Category nodes are retained only if they still have visible children after filtering.
export const pruneTreeByFilter = (
  root: TreeNode | null | undefined,
  mode: FilterMode,
  getNodeContent: (nodeId: string) => Map<string, NodeContentItem> | undefined
): TreeNode | null => {
  if (!root || mode === 'none') return root || null

  const clone = (node: TreeNode): TreeNode | null => {
    if (node.type === 'question') {
      // Apply filter predicate to leaf nodes only
      const show =
        mode === 'researchable'
          ? isResearchable(node, getNodeContent)
          : mode === 'researched'
            ? isResearched(node, getNodeContent)
            : mode === 'unread'
              ? isUnreadResearched(node, getNodeContent)
              : true
      return show ? { ...node, children: [] } : null
    }

    // Category: process children and include only if any visible child remains
    const nextChildren: TreeNode[] = []
    node.children.forEach(child => {
      const pruned = clone(child)
      if (pruned) nextChildren.push(pruned)
    })
    if (nextChildren.length === 0) return null
    return { ...node, children: nextChildren }
  }

  // Keep root even if category gets emptied? Root should be kept with zero children
  if (root.type === 'category') {
    const prunedChildren: TreeNode[] = []
    root.children.forEach(child => {
      const pruned = clone(child)
      if (pruned) prunedChildren.push(pruned)
    })
    return { ...root, children: prunedChildren }
  }

  // Root is question
  return clone(root)
}
