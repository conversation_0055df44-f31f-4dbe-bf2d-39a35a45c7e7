// // This API route is for generating analysis after they have completed a conversation

// import { NextResponse } from "next/server";
// import { createDraftType } from "@/app/types";
// import {
//   DraftStatus,
//   DraftType,
//   OpenAIUsageType,
//   UserStatus,
// } from "@prisma/client";
// import { Configuration, OpenAIApi } from "openai-edge";
// import { OpenAIStream, StreamingTextResponse } from "ai";
// import { logAPIUsage } from "@/app/api/utils";

// // IMPORTANT! Set the runtime to edge
// // ref: https://community.openai.com/t/api-504s-in-production-vercel-only/28795
// // Originally we tried to use vanilla OpenAI API, it works well in local but not working with Vercel
// // It is because Vercel serverless function for hobby plan only last for 10 seconds
// // While the API call to OpenAI takes longer than that [eg: one local run takes around 18 seconds]
// // So we need to switch to edge function since it can last longer
// export const runtime = "edge";

// export async function POST(request: Request) {
//   try {
//     const body = await request.json();
//     const { conversation, currentUser, messages, analysisPrompt } = body;
//     console.log("api/email:body email route", body);

//     if (!currentUser?.id || !currentUser?.email) {
//       return new NextResponse("Unauthorized", { status: 400 });
//     }

//     // Block them from accessing API if they are not active
//     if (currentUser?.status !== UserStatus.ACTIVE) {
//       return new NextResponse("Unauthorized", { status: 401 });
//     }

//     const baseUrl =
//       process.env.NODE_ENV === "development"
//         ? "http://localhost:3000"
//         : process.env.NEXT_PUBLIC_SERVER_URL;

//     const configuration = new Configuration({
//       apiKey: process.env.OPENAI_API_KEY,
//     });
//     const openai = new OpenAIApi(configuration);

//     // const logAPIUsage = async (data: LogAPIUsageDataType) => {
//     //   try {
//     //     const response = await fetch(`${baseUrl}/api/record`, {
//     //       method: "POST",
//     //       headers: {
//     //         "Content-Type": "application/json",
//     //       },
//     //       body: JSON.stringify({
//     //         open_ai_usage_type: data.open_ai_usage_type,
//     //         model_name: data.model_name,
//     //         input_text: data.input_text,
//     //         output_text: data.output_text,
//     //         currentUser: currentUser,
//     //         conversationId: conversation.id,
//     //       }),
//     //     });

//     //     if (!response.ok) {
//     //       throw new Error("Server response: " + response.status);
//     //     }

//     //     const result = await response.json();
//     //     console.log("Log API usage response:", result);
//     //   } catch (error) {
//     //     console.error("Error in logAPIUsage:", error);
//     //   }
//     // };

//     const createDraft = async (data: createDraftType) => {
//       try {
//         const response = await fetch(`${baseUrl}/api/drafts`, {
//           method: "POST",
//           headers: {
//             "Content-Type": "application/json",
//           },
//           body: JSON.stringify({
//             status: data.status,
//             type: data.type,
//             // TODO: should be JSON form for TipTap instead of completion
//             content: data.content,
//             // content is mutable by user, original content should be immutable
//             original_content: data.original_content,
//             currentUser: data.currentUser,
//             conversation_id: data.conversation_id,
//             prompt_id: data.prompt_id,
//             exact_prompt: data.exact_prompt,
//           }),
//         });

//         if (!response.ok) {
//           throw new Error("Server response: " + response.status);
//         }
//         const result = await response.json();
//       } catch (error) {
//         console.error("Error in createDraft:", error);
//       }
//     };

//     const UserAndAssistantMessages = messages.map(
//       (message: { role: string; content: string }) => ({
//         role: message.role,
//         content: message.content,
//       })
//     );

//     const formattedMessages = [
//       // System prompt, the content in prompts table
//       { role: "system", content: analysisPrompt },
//       // conversations
//       ...UserAndAssistantMessages,
//       // remind LLM it should start with the defined format
//       {
//         role: "assistant",
//         content:
//           "Review user responses carefully then based on that to understand the requirements. Then fill ALL sections in the system prompt without stopping after one section",
//       },
//     ];

//     console.log("api/report: formattedMessages:", formattedMessages);

//     const model_name = "gpt-4o-mini";
//     console.log("api/report: model_name:", model_name);
//     const response = await openai.createChatCompletion({
//       model: model_name,
//       stream: true,
//       messages: formattedMessages,
//     });

//     // Convert the response into a friendly text-stream
//     const stream = OpenAIStream(response, {
//       // Save the response
//       async onCompletion(completion) {
//         console.log("api/report: OpenAIStream:onCompletion", completion);

//         try {
//           await Promise.all([
//             logAPIUsage({
//               open_ai_usage_type: OpenAIUsageType.DRAFTED_DOCUMENT,
//               model_name: model_name,
//               input_text: JSON.stringify(formattedMessages),
//               output_text: completion,
//               currentUser: currentUser,
//               conversationId: conversation.id,
//             }),
//             createDraft({
//               status: DraftStatus.ACTIVE,
//               type: DraftType.BASE,
//               content: completion,
//               original_content: completion,
//               currentUser: currentUser,
//               conversation_id: conversation.id,
//               prompt_id: conversation.prompt_id,
//               exact_prompt: analysisPrompt,
//             }),
//           ]);
//           console.log("api/report: All tasks have been completed successfully");
//         } catch (error) {
//           console.error(
//             "api/report: An error occurred during the tasks:",
//             error
//           );
//         }
//       },
//     });

//     return new StreamingTextResponse(stream);
//   } catch (error) {
//     console.error(error, "ERROR_MESSAGES");
//     return new NextResponse("Internal Error", { status: 500 });
//   }
// }
