import {
  reorderNodeInTree as reorderUtil,
  deleteNodeFromTree as deleteUtil,
  addNodeToTree as addUtil,
} from '@/app/stores/dragtree_store/utils/tree-utils'

const makeTree = () => ({
  id: 'root',
  label: 'Root',
  type: 'category',
  isInterestedIn: false,
  children: [
    {
      id: 'a',
      label: 'A',
      type: 'question',
      isInterestedIn: false,
      children: [],
    },
    {
      id: 'b',
      label: 'B',
      type: 'question',
      isInterestedIn: false,
      children: [],
    },
    {
      id: 'c',
      label: 'C',
      type: 'question',
      isInterestedIn: false,
      children: [],
    },
  ],
})

describe('tree-utils basics', () => {
  it('reorderNodeInTree: valid reorder', () => {
    const t = makeTree()
    const out = reorderUtil(t as any, 'root', 0, 2)
    expect(out).not.toBeNull()
    expect(out!.children.map(n => n.id)).toEqual(['b', 'c', 'a'])
    // original not mutated
    expect(t.children.map(n => n.id)).toEqual(['a', 'b', 'c'])
  })

  it('reorderNodeInTree: out-of-bounds rejected', () => {
    const t = makeTree()
    const out = reorderUtil(t as any, 'root', 0, 10)
    expect(out).toBeNull()
  })

  it('deleteNodeFromTree: removes node and preserves siblings', () => {
    const t = makeTree()
    const out = deleteUtil(t as any, 'b')
    expect(out).not.toBeNull()
    expect(out!.children.map(n => n.id)).toEqual(['a', 'c'])
  })

  it('addNodeToTree: appends child under parent', () => {
    const t = makeTree()
    const newChild = {
      id: 'd',
      label: 'D',
      type: 'question',
      children: [],
      isInterestedIn: false,
    }
    const out = addUtil(t as any, 'root', newChild as any)
    expect(out).not.toBeNull()
    expect(out!.children.map(n => n.id)).toEqual(['a', 'b', 'c', 'd'])
  })
})

import {
  calculateNodeLevel,
  buildNodeMap,
  collectInterestedNodes,
  resetAllInterestInTree,
  addNodeToTree,
  deleteNodeFromTree,
  editNodeInTree,
  reorderNodeInTree,
  toggleNodeInterest,
  collectAllNodeIds,
} from '@/app/stores/dragtree_store/utils/tree-utils'
import { TreeNode } from '@/app/types'

// Mock data for testing
const mockTree: TreeNode = {
  id: 'root',
  label: 'Root',
  type: 'category',
  children: [
    {
      id: 'cat1',
      label: 'Category 1',
      type: 'category',
      children: [
        { id: 'que1', label: 'Question 1', type: 'question', children: [] },
      ],
    },
    { id: 'cat2', label: 'Category 2', type: 'category', children: [] },
  ],
}

describe('Tree Utils', () => {
  describe('calculateNodeLevel', () => {
    it('should return the correct level for a node', () => {
      expect(calculateNodeLevel(mockTree, 'root')).toBe(0)
      expect(calculateNodeLevel(mockTree, 'cat1')).toBe(1)
      expect(calculateNodeLevel(mockTree, 'que1')).toBe(2)
    })

    it('should return -1 for a node that does not exist', () => {
      expect(calculateNodeLevel(mockTree, 'nonexistent')).toBe(-1)
    })
  })

  describe('buildNodeMap', () => {
    it('should create a map of all nodes in the tree', () => {
      const nodeMap = buildNodeMap(mockTree)
      expect(nodeMap.size).toBe(4)
      expect(nodeMap.get('root')).toBe(mockTree)
      expect(nodeMap.get('cat1')).toBe(mockTree.children[0])
      expect(nodeMap.get('que1')).toBe(mockTree.children[0].children[0])
    })
  })

  describe('addNodeToTree', () => {
    it('should add a new node to the correct parent', () => {
      const newNode: TreeNode = {
        id: 'newQue',
        label: 'New Question',
        type: 'question',
        children: [],
      }
      const newTree = addNodeToTree(mockTree, 'cat2', newNode)
      const cat2 = newTree!.children.find(c => c.id === 'cat2')
      expect(cat2!.children.length).toBe(1)
      expect(cat2!.children[0].id).toBe('newQue')
    })
  })

  describe('deleteNodeFromTree', () => {
    it('should delete a node from the tree', () => {
      const newTree = deleteNodeFromTree(mockTree, 'cat1')
      expect(newTree!.children.length).toBe(1)
      expect(newTree!.children[0].id).toBe('cat2')
    })
  })

  describe('editNodeInTree', () => {
    it('should edit the label of a node', () => {
      const newTree = editNodeInTree(mockTree, 'que1', 'Updated Question 1')
      const que1 = newTree!.children[0].children[0]
      expect(que1.label).toBe('Updated Question 1')
    })
  })

  describe('reorderNodeInTree', () => {
    it('should reorder nodes within a parent', () => {
      const treeWithMoreChildren: TreeNode = {
        id: 'root',
        label: 'Root',
        type: 'category',
        children: [
          { id: 'child1', label: 'Child 1', type: 'category', children: [] },
          { id: 'child2', label: 'Child 2', type: 'category', children: [] },
          { id: 'child3', label: 'Child 3', type: 'category', children: [] },
        ],
      }

      const newTree = reorderNodeInTree(treeWithMoreChildren, 'root', 0, 2)
      expect(newTree!.children[0].id).toBe('child2')
      expect(newTree!.children[1].id).toBe('child3')
      expect(newTree!.children[2].id).toBe('child1')
    })
  })

  describe('toggleNodeInterest', () => {
    it('should toggle the isInterestedIn flag of a node', () => {
      const newTree = toggleNodeInterest(mockTree, 'que1')
      const que1 = newTree!.children[0].children[0]
      expect(que1.isInterestedIn).toBe(true)

      const toggledBackTree = toggleNodeInterest(newTree!, 'que1')
      const que1ToggledBack = toggledBackTree!.children[0].children[0]
      expect(que1ToggledBack.isInterestedIn).toBe(false)
    })
  })

  describe('collectInterestedNodes', () => {
    it('should collect all nodes with isInterestedIn set to true', () => {
      let interestedTree = toggleNodeInterest(mockTree, 'cat1')
      interestedTree = toggleNodeInterest(interestedTree!, 'que1')

      const interestedNodes = collectInterestedNodes(interestedTree!)
      expect(interestedNodes.length).toBe(2)
      expect(interestedNodes.map(n => n.id)).toContain('cat1')
      expect(interestedNodes.map(n => n.id)).toContain('que1')
    })
  })

  describe('resetAllInterestInTree', () => {
    it('should set isInterestedIn to false for all nodes', () => {
      let interestedTree = toggleNodeInterest(mockTree, 'cat1')
      interestedTree = toggleNodeInterest(interestedTree!, 'que1')

      const resetTree = resetAllInterestInTree(interestedTree!)
      const interestedNodes = collectInterestedNodes(resetTree)
      expect(interestedNodes.length).toBe(0)
    })
  })

  describe('collectAllNodeIds', () => {
    it('should collect all node IDs from the tree', () => {
      const ids = collectAllNodeIds(mockTree)
      expect(ids).toEqual(
        expect.arrayContaining(['root', 'cat1', 'cat2', 'que1'])
      )
      expect(ids.length).toBe(4)
    })
  })
})
