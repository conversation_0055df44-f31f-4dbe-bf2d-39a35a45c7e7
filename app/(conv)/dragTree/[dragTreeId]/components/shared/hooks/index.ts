/**
 * Shared hooks for the dragTree components
 *
 * These hooks separate business logic from UI components,
 * making the codebase more modular and testable.
 */

export { useResearchState } from './useResearchState'
export type { ResearchState, UseResearchStateProps } from './useResearchState'

export { useResearchActions } from './useResearchActions'
export type {
  ResearchActionHandlers,
  UseResearchActionsProps,
} from './useResearchActions'

export { useNodeInteractions } from './useNodeInteractions'
export type {
  NodeInteractionState,
  UseNodeInteractionsProps,
} from './useNodeInteractions'
