import React from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogFooter,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  type CarouselApi,
} from '@/components/ui/carousel'
import {
  X,
  Play,
  Square,
  CheckCircle,
  Heart,
  SkipForward,
  Target,
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useBatchResearchStore } from '@/app/stores/batchResearchStore'
import TreeSelectionPanel from './TreeSelectionPanel'
import type {
  BatchResearchItem,
  CategoryGroup,
  HierarchicalCategory,
} from '@/app/(conv)/dragTree/[dragTreeId]/hooks/useBatchResearch'

type BatchResearchDialogProps = {
  isOpen: boolean
  onClose: () => void
  dragTreeId: string
  items: BatchResearchItem[]
  categoryGroups: CategoryGroup[]
  hierarchicalStructure: HierarchicalCategory[]
  selectedItems: Set<string>
  onToggleItem: (nodeId: string) => void
  onToggleCategorySelection: (categoryId: string) => void
  onStartBatch: () => void
  onCancelBatch: () => void
  isProcessing: boolean
  processedCount: number
  totalCount: number
  currentProcessingId: string | null
  // Coach mode props - FEATURE TEMPORARILY DISABLED
  // Complete tinder-like interface implementation is preserved for potential future use
  isCoachMode: boolean
  currentQuestionIndex: number
  currentQuestion: BatchResearchItem | null
  coachModeQuestions: BatchResearchItem[]
  coachModeTotal: number
  onToggleCoachMode: () => void
  onGoToNextQuestion: () => void
  onGoToPreviousQuestion: () => void
  onSelectCurrentQuestion: () => void
  onSkipCurrentQuestion: () => void
}

const BatchResearchDialog: React.FC<BatchResearchDialogProps> = ({
  isOpen,
  onClose,
  dragTreeId,
  items,
  onStartBatch,
  onCancelBatch,
  isProcessing,
  processedCount,
  totalCount,
  // Coach mode props
  currentQuestionIndex,
  currentQuestion,
  coachModeQuestions: _coachModeQuestions,
  coachModeTotal: _coachModeTotal,
  onToggleCoachMode,
  onGoToNextQuestion,
  onGoToPreviousQuestion,
  onSelectCurrentQuestion,
  onSkipCurrentQuestion,
}) => {
  const { getSelections, isCoachModeOpen, setCoachModeOpen } =
    useBatchResearchStore()
  const persistentSelections = getSelections(dragTreeId)
  const coachModeOpenState = isCoachModeOpen(dragTreeId)

  /*
    COACH MODE IMPLEMENTATION - TEMPORARILY DISABLED

    The following carousel and animation logic implements a complete tinder-like
    interface for question selection, but is currently disabled via UI button removal.

    Features implemented:
    - Carousel-based card swiping with embla-carousel
    - Heart/X button actions with smooth animations
    - Auto-advance to next card after selection/skip
    - Keyboard shortcuts (←/→ for skip/select, ↑/↓ for navigation)
    - Synchronized highlighting with tree view
    - Improved counting logic showing processed vs remaining

    All logic is preserved and functional - only the entry point (toggle button)
    has been disabled in TreeSelectionPanel.tsx
  */

  // Carousel API state for the tinder-like interface
  const [carouselApi, setCarouselApi] = React.useState<CarouselApi>()
  const [isSelectAnimating, setIsSelectAnimating] = React.useState(false)

  // Get progress data for better UX - improved counting logic
  const researchableQuestions = React.useMemo(() => {
    return items.filter(item => !item.hasExistingResearch)
  }, [items])

  const unresearchedQuestions = React.useMemo(() => {
    return researchableQuestions.filter(
      item => !persistentSelections.has(item.nodeId)
    )
  }, [researchableQuestions, persistentSelections])

  const selectedCount = persistentSelections.size
  const totalResearchableCount = researchableQuestions.length
  const unresearchedCount = unresearchedQuestions.length
  const processedCardsCount = totalResearchableCount - unresearchedCount

  const actualProgressRaw =
    totalCount > 0 ? (processedCount / totalCount) * 100 : 0
  // Clamp to 99% if completed to leave headroom for UX
  const actualProgress = processedCount === totalCount ? 99 : actualProgressRaw

  // Sync carousel with current question index
  React.useEffect(() => {
    if (carouselApi && coachModeOpenState) {
      carouselApi.scrollTo(currentQuestionIndex, false)
    }
  }, [carouselApi, currentQuestionIndex, coachModeOpenState])

  // Track carousel state to prevent infinite loops
  const [isCarouselControlled, setIsCarouselControlled] = React.useState(false)

  // Enhanced select function with animation
  const handleSelectWithAnimation = React.useCallback(() => {
    if (!currentQuestion || currentQuestion.hasExistingResearch) return

    setIsSelectAnimating(true)
    onSelectCurrentQuestion()

    // Move to next card after selection
    setTimeout(() => {
      if (carouselApi) {
        carouselApi.scrollNext()
      }
      setIsSelectAnimating(false)
    }, 500)
  }, [currentQuestion, onSelectCurrentQuestion, carouselApi])

  // Enhanced skip function with animation
  const handleSkipWithAnimation = React.useCallback(() => {
    if (!currentQuestion) return

    onSkipCurrentQuestion()

    // Move to next card after skip
    setTimeout(() => {
      if (carouselApi) {
        carouselApi.scrollNext()
      }
    }, 300)
  }, [currentQuestion, onSkipCurrentQuestion, carouselApi])

  // Listen to carousel scroll events to sync navigation
  React.useEffect(() => {
    if (!carouselApi || isCarouselControlled) return

    const onSelect = () => {
      const selectedIndex = carouselApi.selectedScrollSnap()
      if (selectedIndex !== currentQuestionIndex && !isCarouselControlled) {
        // Prevent carousel events from triggering during programmatic navigation
        setIsCarouselControlled(true)

        // Simple sync without complex loops
        if (selectedIndex > currentQuestionIndex) {
          onGoToNextQuestion()
        } else if (selectedIndex < currentQuestionIndex) {
          onGoToPreviousQuestion()
        }

        // Reset the flag after a brief delay
        setTimeout(() => setIsCarouselControlled(false), 100)
      }
    }

    carouselApi.on('select', onSelect)
    return () => {
      carouselApi.off('select', onSelect)
    }
  }, [
    carouselApi,
    currentQuestionIndex,
    isCarouselControlled,
    onGoToNextQuestion,
    onGoToPreviousQuestion,
  ])

  // Enhanced keyboard navigation for coach mode
  React.useEffect(() => {
    if (!coachModeOpenState || isProcessing) return

    const handleKeyDown = (event: KeyboardEvent) => {
      // Prevent default only for our specific keys
      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault()
          handleSkipWithAnimation()
          break
        case 'ArrowRight':
          event.preventDefault()
          handleSelectWithAnimation()
          break
        case 'ArrowUp':
          event.preventDefault()
          onGoToPreviousQuestion()
          break
        case 'ArrowDown':
          event.preventDefault()
          onGoToNextQuestion()
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [
    coachModeOpenState,
    isProcessing,
    handleSkipWithAnimation,
    handleSelectWithAnimation,
    onGoToPreviousQuestion,
    onGoToNextQuestion,
  ])

  // Smoothed progress for nicer UX
  const [animatedProgress, setAnimatedProgress] = React.useState(0)

  React.useEffect(() => {
    if (!isProcessing) {
      setAnimatedProgress(0)
      return
    }

    const interval = setInterval(() => {
      setAnimatedProgress(prev => {
        const target = actualProgress
        if (prev < target) {
          // Move 10-30% towards the target for smoother behaviour
          const step = Math.max(
            1,
            (target - prev) * (0.1 + Math.random() * 0.2)
          )
          return Math.min(prev + step, target)
        }
        return prev
      })
    }, 400)

    return () => clearInterval(interval)
  }, [actualProgress, isProcessing])

  const handleCoachModeToggle = () => {
    setCoachModeOpen(dragTreeId, !coachModeOpenState)
    onToggleCoachMode()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className={cn(
          'h-[85vh] max-h-[85vh] flex flex-col transition-all duration-300',
          coachModeOpenState ? 'max-w-[95vw]' : 'max-w-4xl'
        )}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
              <Play className="w-4 h-4 text-blue-600" />
            </div>
            Batch Quick Research
            {isProcessing && (
              <span className="text-sm font-normal text-blue-600">
                ({processedCount}/{totalCount} completed)
              </span>
            )}
            {/* Enhanced progress indicator for coach mode */}
            {coachModeOpenState && !isProcessing && (
              <div className="ml-auto flex items-center gap-4 text-sm">
                <div className="bg-blue-50 px-3 py-1 rounded-full border border-blue-200">
                  <span className="font-medium text-blue-700">
                    {selectedCount}
                  </span>
                  <span className="mx-1 text-blue-600">selected</span>
                </div>
                <div className="bg-gray-50 px-3 py-1 rounded-full border border-gray-200">
                  <span className="font-medium text-gray-700">
                    {processedCardsCount}
                  </span>
                  <span className="mx-1 text-gray-600">of</span>
                  <span className="font-medium text-gray-700">
                    {totalResearchableCount}
                  </span>
                  <span className="ml-1 text-gray-600">processed</span>
                </div>
                {unresearchedCount > 0 && (
                  <div className="bg-orange-50 px-3 py-1 rounded-full border border-orange-200">
                    <span className="font-medium text-orange-700">
                      {unresearchedCount}
                    </span>
                    <span className="ml-1 text-orange-600">to go</span>
                  </div>
                )}
              </div>
            )}
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 flex min-h-0">
          {/* Progress bar overlay (only show when processing) */}
          {isProcessing && (
            <div className="absolute inset-0 bg-white/95 z-10 flex flex-col justify-center items-center">
              <div className="w-full max-w-md space-y-4">
                <div className="flex justify-between text-sm text-gray-600">
                  <span>
                    {processedCount === 0
                      ? 'Starting research requests...'
                      : processedCount === totalCount
                        ? 'All requests queued! Finishing up...'
                        : 'Queuing research requests...'}
                  </span>
                  <span>
                    {processedCount}/{totalCount}
                  </span>
                </div>
                <Progress value={animatedProgress} className="w-full" />
              </div>
            </div>
          )}

          {/* Tree Selection Panel */}
          <TreeSelectionPanel
            dragTreeId={dragTreeId}
            items={items}
            isCoachModeOpen={coachModeOpenState}
            currentQuestionId={currentQuestion?.nodeId}
            className={cn(
              'h-full min-h-0 transition-all duration-300',
              coachModeOpenState ? 'w-1/2' : 'w-full'
            )}
          />

          {/* Enhanced Coach Mode Panel with Carousel - FEATURE TEMPORARILY DISABLED
              This entire coach mode panel implements a tinder-like card interface but is
              currently inaccessible since the toggle button has been disabled. The implementation
              remains complete and functional for potential future re-enablement. */}
          {coachModeOpenState && (
            <div className="w-1/2 border-l border-gray-200 bg-gray-50 flex flex-col">
              {/* Enhanced Coach Mode Header with Progress */}
              <div className="p-4 border-b bg-white">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold flex items-center gap-2">
                      <Target className="w-5 h-5 text-blue-600" />
                      Coach Mode
                    </h3>
                    <p className="text-sm text-gray-600">
                      Swipe through questions, tinder-style
                    </p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCoachModeToggle}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>

                {/* Progress Statistics */}
                <div className="mt-3 flex items-center justify-center gap-6 text-sm">
                  <div className="flex items-center gap-2">
                    <Heart className="w-4 h-4 text-green-500 fill-current" />
                    <span className="text-green-600 font-medium">
                      {selectedCount} interested
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <SkipForward className="w-4 h-4 text-gray-500" />
                    <span className="text-gray-600">
                      {processedCardsCount - selectedCount} skipped
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 bg-orange-400 rounded-full"></div>
                    <span className="text-orange-600 font-medium">
                      {unresearchedCount} remaining
                    </span>
                  </div>
                </div>
              </div>

              {/* Carousel Content */}
              <div className="flex-1 flex items-center justify-center p-6">
                {unresearchedQuestions.length > 0 ? (
                  <div className="w-full max-w-md">
                    <Carousel
                      setApi={setCarouselApi}
                      className="w-full"
                      opts={{
                        align: 'start',
                        loop: false,
                        startIndex: currentQuestionIndex,
                      }}
                    >
                      <CarouselContent>
                        {unresearchedQuestions.map((question, index) => (
                          <CarouselItem key={question.nodeId}>
                            <div className="p-1">
                              <Card
                                className={cn(
                                  'border-2 shadow-xl transition-all duration-300 transform hover:scale-[1.02]',
                                  persistentSelections.has(question.nodeId)
                                    ? 'border-green-300 bg-green-50 shadow-green-200'
                                    : 'border-blue-200 bg-white shadow-blue-200',
                                  index === currentQuestionIndex &&
                                    isSelectAnimating &&
                                    'animate-pulse scale-105'
                                )}
                              >
                                <CardHeader className="pb-4">
                                  <div className="text-xs text-gray-500 mb-2">
                                    {question.categoryPath.length > 0
                                      ? question.categoryPath.join(' › ')
                                      : 'Root Questions'}
                                  </div>
                                  <CardTitle className="text-lg leading-relaxed min-h-[4rem]">
                                    {question.questionText}
                                  </CardTitle>
                                  {persistentSelections.has(
                                    question.nodeId
                                  ) && (
                                    <div className="flex items-center gap-2 text-sm text-green-600 font-medium animate-in fade-in-0 duration-300">
                                      <Heart className="w-4 h-4 fill-current" />
                                      <span>Selected for research</span>
                                    </div>
                                  )}
                                </CardHeader>

                                <CardContent className="pt-0">
                                  {/* Tinder-style Action Buttons */}
                                  <div className="flex gap-4 justify-center">
                                    <Button
                                      variant="outline"
                                      size="lg"
                                      onClick={handleSkipWithAnimation}
                                      className={cn(
                                        'flex-1 flex items-center justify-center gap-2 h-14 font-medium transition-all duration-200',
                                        'border-2 border-red-200 text-red-700 hover:bg-red-50 hover:border-red-300 hover:scale-105',
                                        'focus:ring-2 focus:ring-red-200 focus:ring-offset-2 active:scale-95'
                                      )}
                                    >
                                      <X className="w-5 h-5" />
                                      <span>Skip</span>
                                    </Button>

                                    <Button
                                      size="lg"
                                      onClick={handleSelectWithAnimation}
                                      className={cn(
                                        'flex-1 flex items-center justify-center gap-2 h-14 font-medium transition-all duration-200',
                                        persistentSelections.has(
                                          question.nodeId
                                        )
                                          ? 'bg-green-600 hover:bg-green-700 text-white border-green-600 shadow-lg scale-105'
                                          : 'bg-blue-600 hover:bg-blue-700 text-white',
                                        'hover:scale-105 active:scale-95 focus:ring-2 focus:ring-blue-200 focus:ring-offset-2'
                                      )}
                                    >
                                      <Heart
                                        className={cn(
                                          'w-5 h-5 transition-all duration-200',
                                          persistentSelections.has(
                                            question.nodeId
                                          )
                                            ? 'fill-current'
                                            : ''
                                        )}
                                      />
                                      <span>
                                        {persistentSelections.has(
                                          question.nodeId
                                        )
                                          ? 'Selected'
                                          : 'Interested'}
                                      </span>
                                    </Button>
                                  </div>

                                  {/* Progress indicator */}
                                  <div className="mt-4 text-center text-sm text-gray-500">
                                    <span className="font-medium text-blue-600">
                                      {index + 1}
                                    </span>
                                    <span className="mx-1">of</span>
                                    <span className="font-medium">
                                      {unresearchedQuestions.length}
                                    </span>
                                    <span className="ml-1">questions</span>
                                  </div>
                                </CardContent>
                              </Card>
                            </div>
                          </CarouselItem>
                        ))}
                      </CarouselContent>

                      {/* Carousel Navigation */}
                      <div className="flex items-center justify-center gap-4 mt-4">
                        <CarouselPrevious className="relative translate-x-0 translate-y-0" />
                        <div className="text-xs text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                          Swipe or use arrows
                        </div>
                        <CarouselNext className="relative translate-x-0 translate-y-0" />
                      </div>

                      {/* Keyboard hints */}
                      <div className="flex items-center justify-center gap-4 mt-3 text-xs text-gray-400">
                        <div className="flex items-center gap-1">
                          <kbd className="px-2 py-1 bg-gray-100 rounded">←</kbd>
                          <span>skip</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <kbd className="px-2 py-1 bg-gray-100 rounded">→</kbd>
                          <span>select</span>
                        </div>
                      </div>
                    </Carousel>
                  </div>
                ) : (
                  <div className="text-center py-12 text-gray-500 space-y-4">
                    <div className="w-16 h-16 mx-auto bg-green-100 rounded-full flex items-center justify-center">
                      <CheckCircle className="w-8 h-8 text-green-500" />
                    </div>
                    <div>
                      <p className="text-lg font-medium text-gray-700">
                        All questions reviewed!
                      </p>
                      <p className="text-sm text-gray-500">
                        {selectedCount > 0
                          ? `You've selected ${selectedCount} questions for research.`
                          : 'No questions selected for research.'}
                      </p>
                      {selectedCount > 0 && (
                        <p className="text-sm text-blue-600 mt-2 font-medium">
                          Ready to start research when you&apos;re ready!
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="flex gap-2">
          {isProcessing ? (
            <>
              <Button variant="outline" onClick={onCancelBatch}>
                <Square className="w-4 h-4 mr-2" />
                Cancel
              </Button>
              <Button disabled>
                <div className="w-4 h-4 mr-2 animate-spin border-2 border-white border-t-transparent rounded-full" />
                Processing...
              </Button>
            </>
          ) : (
            <>
              <Button
                onClick={onStartBatch}
                disabled={persistentSelections.size === 0}
                className={cn(
                  'transition-all duration-200 hover:scale-105 active:scale-95',
                  persistentSelections.size > 0
                    ? 'bg-green-600 hover:bg-green-700 shadow-md hover:shadow-lg'
                    : 'bg-gray-300'
                )}
              >
                <Play className="w-4 h-4 mr-2" />
                Start Research ({persistentSelections.size} questions)
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

// Memoize to prevent unnecessary re-renders when props haven't changed
export default React.memo(BatchResearchDialog)
