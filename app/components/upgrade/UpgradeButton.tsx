'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { FiZap, FiLoader } from 'react-icons/fi'
import { cn } from '@/lib/utils'
import { logEventWithContext } from '@/app/libs/logging'
import { SubscriptionTier } from '@prisma/client'
import type { UpgradeButtonProps } from './types'

/**
 * Unified upgrade button component with consistent emerald-teal styling
 * Replaces inconsistent upgrade buttons throughout the application
 */
export const UpgradeButton: React.FC<UpgradeButtonProps> = ({
  text = 'Upgrade to Pro',
  size = 'md',
  variant = 'primary',
  icon: IconComponent = FiZap,
  context,
  currentTier,
  onClick,
  className,
  disabled = false,
  loading = false,
  fullWidth = false,
}) => {
  const router = useRouter()
  const { data: session } = useSession()

  const handleClick = () => {
    // Get current tier for analytics
    const tier =
      currentTier ||
      (((session?.user as any)?.subscription_tier ||
        (session?.user as any)?.subscription?.tier ||
        SubscriptionTier.FREE) as SubscriptionTier)

    // Log analytics event
    logEventWithContext('click_upgrade_button', session?.user?.id, undefined, {
      context,
      current_tier: tier,
      button_text: text,
    })

    if (onClick) {
      onClick()
    } else {
      router.push('/subscription')
    }
  }

  // Size classes
  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-3 text-sm',
    lg: 'px-6 py-4 text-base',
  }

  // Icon sizes
  const iconSizes = {
    sm: 14,
    md: 16,
    lg: 18,
  }

  // Base classes for emerald-teal theme
  const baseClasses = cn(
    'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200',
    'focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2',
    fullWidth && 'w-full',
    sizeClasses[size],
    className
  )

  // Variant-specific classes
  const variantClasses = {
    primary: cn(
      'bg-gradient-to-r from-emerald-500 to-teal-600 text-white shadow-md',
      'hover:from-emerald-600 hover:to-teal-700 hover:shadow-lg',
      'transform hover:scale-[1.02] active:scale-[0.98]',
      disabled &&
        'opacity-50 cursor-not-allowed hover:scale-100 hover:shadow-md'
    ),
    outline: cn(
      'border-2 border-emerald-500 text-emerald-600 bg-white',
      'hover:bg-emerald-50 hover:border-emerald-600',
      'transform hover:scale-[1.02] active:scale-[0.98]',
      disabled && 'opacity-50 cursor-not-allowed hover:scale-100 hover:bg-white'
    ),
    ghost: cn(
      'text-emerald-600 bg-transparent',
      'hover:bg-emerald-50 hover:text-emerald-700',
      'transform hover:scale-[1.02] active:scale-[0.98]',
      disabled &&
        'opacity-50 cursor-not-allowed hover:scale-100 hover:bg-transparent'
    ),
  }

  return (
    <button
      onClick={handleClick}
      disabled={disabled || loading}
      className={cn(baseClasses, variantClasses[variant])}
    >
      {loading ? (
        <FiLoader size={iconSizes[size]} className="mr-2 animate-spin" />
      ) : (
        <IconComponent size={iconSizes[size]} className="mr-2" />
      )}
      <span>{loading ? 'Loading...' : text}</span>
    </button>
  )
}
