# Testing Setup Guide

This project includes comprehensive testing setup with unit tests, e2e tests, and pre-commit hooks to ensure code quality and functionality.

## 🧪 Testing Stack

### Unit Testing

- **Jest**: Test runner and assertion library
- **React Testing Library**: Component testing utilities
- **@testing-library/jest-dom**: Custom Jest matchers for DOM testing

### E2E Testing

- **Playwright**: Modern e2e testing framework
- Cross-browser testing (Chromium, Firefox, WebKit)
- Mobile and desktop viewport testing

### Code Quality

- **ESLint**: Code linting and formatting
- **Prettier**: Code formatting
- **Husky**: Git hooks for pre-commit checks
- **lint-staged**: Run checks only on staged files

## 📝 Available Scripts

### Unit Tests

```bash
# Run all unit tests
npm run test

# Run tests in watch mode (great for development)
npm run test:watch

# Run tests with coverage report
npm run test:coverage
```

### E2E Tests

```bash
# Run all e2e tests (headless)
npm run test:e2e

# Run e2e tests with UI (interactive mode)
npm run test:e2e:ui

# Debug e2e tests (step-by-step debugging)
npm run test:e2e:debug

# Run all tests (unit + e2e)
npm run test:all
```

## 📁 Test Structure

```
next13-clarify/
├── __tests__/                    # Unit tests
│   └── components/
│       ├── Button.test.tsx       # Example component test
│       └── LoadingSkeleton.test.tsx
├── e2e/                          # E2E tests
│   └── example.spec.ts           # Example e2e test
├── jest.config.js                # Jest configuration
├── jest.setup.js                 # Jest test environment setup
├── playwright.config.ts          # Playwright configuration
└── .husky/
    └── pre-commit                # Pre-commit hook
```

## ✍️ Writing Tests

### Unit Tests Example

```typescript
import { render, screen, fireEvent } from '@testing-library/react'
import Button from '@/app/components/Button'

describe('Button Component', () => {
  it('renders button with children', () => {
    render(<Button>Test Button</Button>)

    const button = screen.getByRole('button', { name: /test button/i })
    expect(button).toBeInTheDocument()
  })

  it('calls onClick when clicked', () => {
    const handleClick = jest.fn()
    render(<Button onClick={handleClick}>Clickable Button</Button>)

    const button = screen.getByRole('button')
    fireEvent.click(button)

    expect(handleClick).toHaveBeenCalledTimes(1)
  })
})
```

### E2E Tests Example

```typescript
import { test, expect } from '@playwright/test'

test('homepage loads successfully', async ({ page }) => {
  await page.goto('/')

  await expect(page).toHaveTitle(/Clarify/)
  await expect(page.locator('body')).toBeVisible()
})
```

## 🔧 Configuration Files

### Jest Configuration (`jest.config.js`)

- Configured for Next.js with TypeScript support
- Module path mapping for `@/` imports
- Coverage collection from app and components directories
- Excludes e2e tests from unit test runs

### Playwright Configuration (`playwright.config.ts`)

- Multi-browser testing setup
- Mobile and desktop viewport testing
- Automatic dev server startup for testing
- HTML reports for test results

## 🚀 Pre-commit Hooks

The pre-commit hook automatically runs:

1. **ESLint** - Fixes linting issues on staged files
2. **Jest** - Runs tests related to staged files
3. **Prettier** - Formats staged files

This ensures that only quality, tested code gets committed to the repository.

### Bypassing Pre-commit Hooks

If you need to bypass the pre-commit hooks (not recommended):

```bash
git commit --no-verify -m "commit message"
```

## 🎯 Testing Best Practices

### Unit Tests

- Test component behavior, not implementation details
- Use meaningful test descriptions
- Test user interactions and edge cases
- Mock external dependencies
- Aim for good test coverage but focus on critical paths

### E2E Tests

- Test complete user workflows
- Test critical business functionality
- Keep tests independent and isolated
- Use data-testid for stable element selection
- Test across different browsers and viewports

### General

- Write tests before or alongside feature development
- Keep tests simple and focused
- Use descriptive test names
- Group related tests with `describe` blocks
- Clean up after tests (automatic with React Testing Library)

## 🔍 Debugging Tests

### Unit Tests

```bash
# Debug specific test file
npm run test Button.test.tsx

# Debug with verbose output
npm run test -- --verbose

# Debug with watch mode
npm run test:watch
```

### E2E Tests

```bash
# Run in headed mode to see browser
npx playwright test --headed

# Debug specific test
npx playwright test example.spec.ts --debug

# Open test results in browser
npx playwright show-report
```

## 📊 Coverage Reports

Run `npm run test:coverage` to generate coverage reports. The report will show:

- Line coverage
- Function coverage
- Branch coverage
- Statement coverage

Coverage reports are generated in the `coverage/` directory.

## 🚀 Continuous Integration

This setup is ready for CI/CD environments:

- Tests run in headless mode by default
- Playwright includes retry logic for flaky tests
- Coverage reports can be uploaded to services like Codecov
- Pre-commit hooks ensure consistent code quality

## 🆘 Troubleshooting

### Common Issues

1. **Tests timing out**: Increase timeout in Jest/Playwright config
2. **Module resolution errors**: Check path mappings in `jest.config.js`
3. **Playwright installation issues**: Run `npx playwright install`
4. **Pre-commit hooks not running**: Ensure git hooks are executable

### Getting Help

- Jest documentation: https://jestjs.io/docs/getting-started
- React Testing Library: https://testing-library.com/docs/react-testing-library/intro/
- Playwright documentation: https://playwright.dev/docs/intro
