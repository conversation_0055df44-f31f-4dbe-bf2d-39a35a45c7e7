import { NextResponse } from 'next/server'
import prisma from '@/app/libs/prismadb'
const { encode } = require('@nem035/gpt-3-encoder')

// Define the GET request handler function
export async function POST(request: Request) {
  try {
    // Parse the request body as JSON
    const body = await request.json()

    // Extract the necessary fields from the request body
    const {
      open_ai_usage_type,
      model_name,
      input_text,
      output_text,
      currentUser,
      conversationId,
      //   leave some flexibility for the future
      //   config,
    } = body

    // Quick approximation of the tokens, subject to the text format [eg: list of objects may consume more]
    const calculateTokens = (text: string) => {
      return encode(text).length
    }

    // Create a new message in the database using Prisma
    const newUsage = await prisma.openAIUsage.create({
      data: {
        open_ai_usage_type: open_ai_usage_type,
        model_name: model_name,
        input_text: input_text,
        output_text: output_text,
        // Just the estimate, since some inputs could be result from JSON.stringify
        input_token_est: calculateTokens(input_text),
        output_token_est: calculateTokens(output_text),
        user_id: currentUser.id,
        conversation_id: conversationId,
        // config: {},
      },
    })

    // Return the newly created message as a JSON response
    return NextResponse.json(newUsage)
  } catch (error) {
    console.error(error)

    // If an error occurs, return a 500 status code with an "Error" message
    return new NextResponse('Error', { status: 500 })
  }
}
