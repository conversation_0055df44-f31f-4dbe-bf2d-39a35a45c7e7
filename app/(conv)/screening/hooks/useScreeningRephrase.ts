import { useCallback, useEffect } from 'react'
import { experimental_useObject as useObject } from '@ai-sdk/react'
import { useSession } from 'next-auth/react'
import { toast } from 'react-hot-toast'
import type { SupportedLanguageCode } from '@/app/(conv)/screening/constants/languages'
import { isDevelopmentOrCi } from '@/lib/environment'
import { z } from 'zod'

// Define schema for the rephrase response - matches API schema
const rephraseSchema = z.object({
  rephrases: z
    .array(z.string())
    .describe('Array of rephrased problem statements'),
})

type UseScreeningRephraseProps = {
  description: string
  selectedLanguage: SupportedLanguageCode
  onRephrasedUpdate: (rephrased: string[]) => void
  useSimulator?: boolean // New prop to enable simulator mode
}

type UseScreeningRephraseReturn = {
  isStreaming: boolean
  startRephrase: () => void
}

/**
 * Custom hook for handling real-time screening rephrase streaming
 * Manages the problem statement generation with structured object streaming
 * Supports both real API and simulator modes for testing
 */
export const useScreeningRephrase = ({
  description,
  selectedLanguage,
  onRephrasedUpdate,
  useSimulator = false,
}: UseScreeningRephraseProps): UseScreeningRephraseReturn => {
  const { data: session } = useSession()

  // Choose API endpoint based on simulator mode
  const apiEndpoint = useSimulator
    ? '/api/screening/rephrase-simulator'
    : '/api/screening/rephrase'

  // Use experimental_useObject for structured streaming
  const { object, isLoading, submit } = useObject({
    api: apiEndpoint,
    schema: rephraseSchema,
  })

  const isStreaming = isLoading

  // Process streaming object updates in real-time
  useEffect(() => {
    if (object?.rephrases && Array.isArray(object.rephrases)) {
      // Filter out any empty strings and update with current rephrases
      const validRephrases = object.rephrases.filter(
        (rephrase): rephrase is string =>
          typeof rephrase === 'string' && rephrase.trim().length > 0
      )
      if (validRephrases.length > 0) {
        onRephrasedUpdate(validRephrases)
      }
    }
  }, [object, onRephrasedUpdate])

  // Handle completion toast
  useEffect(() => {
    if (!isStreaming && object?.rephrases && object.rephrases.length > 0) {
      if (isDevelopmentOrCi()) {
        const mode = useSimulator ? 'Simulator' : 'Real API'
        toast.success(`Problem statements generated! (${mode})`)
      }
    }
  }, [isStreaming, object?.rephrases, useSimulator])

  const startRephrase = useCallback(() => {
    // In simulator mode, we don't need authentication
    if (!useSimulator && !session?.user?.id) {
      toast.error('Please log in to use the real API.')
      return
    }

    // Submit with the required data
    submit({
      userId: session?.user?.id ?? '',
      description,
      preferredLanguage: selectedLanguage,
    })
  }, [session?.user?.id, submit, useSimulator, description, selectedLanguage])

  return {
    isStreaming,
    startRephrase,
  }
}
