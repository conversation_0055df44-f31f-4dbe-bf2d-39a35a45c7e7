import type { Editor } from '@tiptap/react'

/**
 * Helper function to fill empty table cell paragraphs with non-breaking spaces
 * This prevents visual alignment issues and click targeting problems in tables
 */
export const fillEmptyTableCells = (
  editor: Editor,
  delay: number = 50
): void => {
  setTimeout(() => {
    const { state } = editor

    // Collect the positions of *empty* paragraphs that live inside a table cell/header.
    const emptyParagraphPositions: number[] = []

    state.doc.descendants((node, pos) => {
      if (node.type.name !== 'paragraph' || node.content.size !== 0) return

      // Check if this paragraph sits inside a table cell / header.
      const $pos = state.doc.resolve(pos)
      for (let depth = $pos.depth; depth > 0; depth--) {
        const ancestorName = $pos.node(depth).type.name
        if (ancestorName === 'tableCell' || ancestorName === 'tableHeader') {
          emptyParagraphPositions.push(pos)
          break
        }
      }
    })

    if (!emptyParagraphPositions.length) return

    // Insert spaces starting from the *end* of the document so earlier inserts
    // don't invalidate the stored positions of later paragraphs.
    let tr = state.tr
    const spaceText = state.schema.text('\u00A0')

    emptyParagraphPositions
      .sort((a, b) => b - a) // descending order
      .forEach(position => {
        tr = tr.insert(position + 1, spaceText)
      })

    if (tr.docChanged) {
      editor.view.dispatch(tr)
    }
  }, delay)
}

/**
 * Helper function to create a table with pre-populated non-breaking spaces
 */
export const createTableWithContent = (editor: Editor): void => {
  editor
    .chain()
    .focus()
    .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
    .run()

  fillEmptyTableCells(editor, 50)
}
