import { useEffect, useCallback } from 'react'
import { experimental_useObject as useObject } from '@ai-sdk/react'
import { toast } from 'react-hot-toast'
import { useSession } from 'next-auth/react'
import { screenSchema } from '@/app/api/screening/utils'
import type { ScreenObjectType } from '@/app/(conv)/screening/constants/mockData'
import { isDevelopmentOrCi, isDevelopment } from '@/lib/environment'
import { ScreeningDiagnoseRequestType } from '@/app/types/api'

type UseScreeningDiagnosisProps = {
  onScreenObjectUpdate: (screenObject: ScreenObjectType | null) => void
  useSimulator?: boolean // New prop to enable simulator mode
}

type UseScreeningDiagnosisReturn = {
  isStreaming: boolean
  submit: (data: ScreeningDiagnoseRequestType) => void
}

/**
 * Custom hook for handling real-time screening diagnosis streaming
 * Manages the problem analysis with structured object streaming
 * Supports both real API and simulator modes for testing
 */
export const useScreeningDiagnosis = ({
  onScreenObjectUpdate,
  useSimulator = false,
}: UseScreeningDiagnosisProps): UseScreeningDiagnosisReturn => {
  const { data: session } = useSession()

  // Choose API endpoint based on simulator mode
  const apiEndpoint = useSimulator
    ? '/api/screening/diagnose-simulator'
    : '/api/screening/diagnose'

  // Create completely stable hook configuration to prevent AI SDK from re-initializing
  const {
    object: screenObjectResult,
    submit,
    isLoading,
  } = useObject({
    api: apiEndpoint,
    schema: screenSchema,
    // Don't pass any callbacks - handle via useEffect instead
  })

  // Process streaming object updates in real-time
  useEffect(() => {
    if (!screenObjectResult) return

    // Update the parent with current object state as it streams
    onScreenObjectUpdate(screenObjectResult as ScreenObjectType)
  }, [JSON.stringify(screenObjectResult), onScreenObjectUpdate]) // Depend on the stringified object to prevent excessive re-renders for the same content

  // Handle completion toast separately to avoid re-triggering on every object update
  useEffect(() => {
    if (screenObjectResult && !isLoading) {
      if (isDevelopmentOrCi()) {
        const mode = useSimulator ? 'Simulator' : 'Real API'
        toast.success(`Analysis completed! (${mode})`)
      }
    }
  }, [!!screenObjectResult, isLoading, useSimulator]) // Only trigger on completion

  // Create stable submit function that includes authentication check
  const submitWithAuth = useCallback(
    (data: ScreeningDiagnoseRequestType) => {
      // Only log in development, not during tests
      if (isDevelopment()) {
        console.log('🔬 [SCREENING DIAGNOSIS HOOK] Submit called with:', {
          userId: data.userId,
          description: data.description?.slice(0, 100) + '...',
          preferredLanguage: data.preferredLanguage,
          apiEndpoint,
          useSimulator,
        })
      }

      // For real API, we still need authentication via session (server-side)
      // For simulator, no auth needed
      if (!useSimulator && !session?.user?.id) {
        if (isDevelopment()) {
          console.log(
            '❌ [SCREENING DIAGNOSIS HOOK] Not authenticated for real API'
          )
        }
        toast.error('Please log in to use the real API.')
        return
      }

      if (isDevelopment()) {
        console.log(
          '✅ [SCREENING DIAGNOSIS HOOK] Calling submit() with validated data'
        )
      }
      submit({
        userId: data.userId,
        description: data.description,
        preferredLanguage: data.preferredLanguage,
      })
    },
    [submit, session?.user?.id, useSimulator, apiEndpoint]
  )

  return {
    isStreaming: isLoading,
    submit: submitWithAuth,
  }
}
