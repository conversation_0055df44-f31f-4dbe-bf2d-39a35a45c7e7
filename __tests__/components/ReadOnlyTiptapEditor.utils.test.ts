// Mock lowlight to avoid registerLanguage errors in test environment
jest.mock('lowlight', () => ({ lowlight: { registerLanguage: jest.fn() } }))
jest.mock('@tiptap/extension-code-block-lowlight', () => ({
  __esModule: true,
  default: { configure: jest.fn(() => ({})) },
}))

import { convertContentToJSON } from '@/app/components/editor/ReadOnlyTiptapEditor'

describe('convertContentToJSON utility', () => {
  it('detects and parses valid Tiptap JSON string', () => {
    const jsonString = JSON.stringify({ type: 'doc', content: [] })
    const result = convertContentToJSON(jsonString as string)
    expect(typeof result).toBe('object')
    if (typeof result === 'object' && result !== null) {
      expect((result as any).type).toBe('doc')
    }
  })

  it('converts markdown into a Tiptap JSON object', () => {
    const markdown = '## Subheading'
    const result = convertContentToJSON(markdown)
    expect(typeof result).toBe('object')
    if (typeof result === 'object' && result !== null) {
      expect((result as any).type).toBe('doc')
    }
  })

  it('handles malformed JSON string gracefully', () => {
    const malformed = '{"type":"doc",'
    let error = null
    try {
      convertContentToJSON(malformed)
    } catch (e) {
      error = e
    }
    expect(error).toBeNull()
  })
})
