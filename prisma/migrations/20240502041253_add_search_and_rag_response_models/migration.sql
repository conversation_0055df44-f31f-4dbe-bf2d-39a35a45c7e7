-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "OpenAIUsageType" ADD VALUE 'RAG_QUERY_REPHRASER';
ALTER TYPE "OpenAIUsageType" ADD VALUE 'RAG_GENERATE_RESPONSE';

-- CreateTable
CREATE TABLE "searches" (
    "id" TEXT NOT NULL,
    "creator_id" TEXT NOT NULL,
    "conversation_id" TEXT NOT NULL,
    "issue_tree_id" TEXT NOT NULL,
    "selected_node_id" TEXT NOT NULL,
    "search_query" TEXT NOT NULL,
    "search_result" TEXT NOT NULL,
    "search_engine" TEXT NOT NULL DEFAULT 'BRAVE_SEARCH',
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "type" TEXT NOT NULL DEFAULT 'RAG_SEARCH',
    "config" JSONB NOT NULL DEFAULT '{}',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "searches_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "rag_responses" (
    "id" TEXT NOT NULL,
    "creator_id" TEXT NOT NULL,
    "conversation_id" TEXT NOT NULL,
    "issue_tree_id" TEXT NOT NULL,
    "selected_node_id" TEXT NOT NULL,
    "generation_input" TEXT NOT NULL,
    "generation_output" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "type" TEXT NOT NULL DEFAULT 'RAG_SEARCH',
    "config" JSONB NOT NULL DEFAULT '{}',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "rag_responses_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "searches_id_idx" ON "searches" USING HASH ("id");

-- CreateIndex
CREATE INDEX "searches_creator_id_idx" ON "searches" USING HASH ("creator_id");

-- CreateIndex
CREATE INDEX "searches_conversation_id_idx" ON "searches" USING HASH ("conversation_id");

-- CreateIndex
CREATE INDEX "searches_issue_tree_id_idx" ON "searches" USING HASH ("issue_tree_id");

-- CreateIndex
CREATE INDEX "searches_created_at_idx" ON "searches"("created_at");

-- CreateIndex
CREATE INDEX "rag_responses_id_idx" ON "rag_responses" USING HASH ("id");

-- CreateIndex
CREATE INDEX "rag_responses_creator_id_idx" ON "rag_responses" USING HASH ("creator_id");

-- CreateIndex
CREATE INDEX "rag_responses_conversation_id_idx" ON "rag_responses" USING HASH ("conversation_id");

-- CreateIndex
CREATE INDEX "rag_responses_issue_tree_id_idx" ON "rag_responses" USING HASH ("issue_tree_id");

-- CreateIndex
CREATE INDEX "rag_responses_created_at_idx" ON "rag_responses"("created_at");

-- AddForeignKey
ALTER TABLE "searches" ADD CONSTRAINT "searches_issue_tree_id_fkey" FOREIGN KEY ("issue_tree_id") REFERENCES "issue_trees"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "searches" ADD CONSTRAINT "searches_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "conversations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "rag_responses" ADD CONSTRAINT "rag_responses_issue_tree_id_fkey" FOREIGN KEY ("issue_tree_id") REFERENCES "issue_trees"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "rag_responses" ADD CONSTRAINT "rag_responses_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "conversations"("id") ON DELETE CASCADE ON UPDATE CASCADE;
