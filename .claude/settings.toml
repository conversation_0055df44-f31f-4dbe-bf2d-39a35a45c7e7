[[hooks]]
# <PERSON><PERSON> once <PERSON> has finished its tool actions and is about to send the final reply.
event = "Stop"

# Run synchronously so that any failures are surfaced back to <PERSON> for fixing.
run_in_background = false

# Your CI-style safety net: fail fast if either step exits non-zero.
command = """
  echo '🔍  Running unit tests…'           && \
  npm run test                             && \
  echo '🏗️   Building production bundle…'  && \
  npm run build
"""