// Types for AI Pane Chat v2 API - Improved Architecture

/**
 * Chat v2 request payload - improved architecture
 * Frontend sends only new user message, API handles history retrieval
 */
export type ChatV2Request = {
  /** New user message content */
  message: string
  /** Conversation ID for persistence (must start with 'thread_') */
  conversationId: string
  /** Context content (only included on first message or when context changes) */
  context?: string
  /** Model to use (defaults to gpt-4.1) */
  model?: string
  /** Context document IDs to include */
  contextIds?: string[]
  /** Drag tree ID for categorization */
  dragTreeId?: string
}

/**
 * Chat v2 response - streams UIMessage format with proper tool call support
 * API handles message history internally and returns streaming response
 */
export type ChatV2Response = {
  /** Streaming response in AI SDK v5 UIMessageStreamResponse format */
  stream: ReadableStream
  /** Response headers for streaming */
  headers: {
    'Content-Type': 'text/event-stream'
    'Cache-Control': 'no-cache'
    Connection: 'keep-alive'
  }
}

/**
 * Internal message format used by the API
 */
export type InternalMessage = {
  role: 'system' | 'user' | 'assistant'
  content: string
}

/**
 * Database message format
 */
export type DatabaseMessage = {
  role: string
  content: string
  createdAt: Date
}
