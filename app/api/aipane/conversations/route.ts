import { NextResponse } from 'next/server'
import { auth } from '@/auth'
import { createAiConversation } from '@/app/server-actions/ai-chat'
import { standardErrors } from '../shared/errors'
import { getConversationsByContext } from '@/app/server-actions/ai-chat'
import prisma from '@/app/libs/prismadb'
import { SubscriptionTier } from '@prisma/client'
import { getTierPermissions } from '@/app/configs/tier-permissions'

// Force dynamic to prevent static optimization
export const dynamic = 'force-dynamic'
export const maxDuration = 30

export async function POST(req: Request) {
  try {
    const session = await auth()
    if (!session?.user?.id) return standardErrors.unauthorized()

    const body = await req.json().catch(() => ({}))
    const {
      conversationId: requestedConversationId,
      contextEntityType,
      contextEntityId,
      title = 'AI Chat Session',
      // Optional: prepare context message server-side for stability
      context,
      contextIds: providedContextIds,
      // New: initial user message to generate title up front
      initialUserMessage,
    } = body || {}

    // Enforce per-tier AI Pane chat conversation count per drag tree
    try {
      const userTier = await prisma.user.findUnique({
        where: { id: session.user.id },
        select: { subscription_tier: true },
      })
      const tier = userTier?.subscription_tier || SubscriptionTier.FREE
      const { aiPaneChatPerTree } = getTierPermissions(tier)

      if (contextEntityType === 'drag_tree' && contextEntityId) {
        const convCount = await prisma.aiConversation.count({
          where: {
            userId: session.user.id,
            contextEntityType: 'drag_tree',
            contextEntityId,
          },
        })
        if (convCount >= aiPaneChatPerTree) {
          return standardErrors.forbidden(
            tier === SubscriptionTier.FREE
              ? 'Free plan limit reached: You can start up to 3 AI chats per drag tree. Upgrade to PRO to unlock more.'
              : 'You have reached the chat limit for this drag tree.'
          )
        }
      }
    } catch (e) {
      console.warn('Failed to enforce conversation limit', e)
    }

    const result = await createAiConversation(
      {
        userId: session.user.id,
        title,
        contextEntityType,
        contextEntityId,
        // Persist selected context IDs so the UI can read them from metadata
        metadata: {
          contextIds: Array.isArray((body as any)?.contextIds)
            ? (body as any).contextIds
            : [],
        },
      },
      requestedConversationId // optional exact id provided by client
    )

    if (!result.success)
      return standardErrors.internalError(result.error ?? 'create failed')

    const conversationId = result.data!.conversationId

    // Optionally create a SYSTEM context message for first-turn stability
    try {
      // Build context text: prefer direct context; else construct from contextIds
      let contextText: string | undefined
      if (typeof context === 'string' && context.trim().length > 0) {
        contextText = context.trim()
      } else {
        // Determine final contextIds (prefer provided, fallback to metadata)
        let finalContextIds: string[] = Array.isArray(providedContextIds)
          ? providedContextIds
          : []
        if (
          finalContextIds.length === 0 &&
          Array.isArray((body as any)?.contextIds)
        ) {
          finalContextIds = (body as any).contextIds
        }
        if (finalContextIds.length > 0) {
          // Fetch node labels and latest content snippet
          const nodes = await prisma.dragTreeNode.findMany({
            where: { id: { in: finalContextIds } },
            select: { id: true, label: true },
          })
          const contentByNodeId: Record<string, string> = {}
          await Promise.all(
            finalContextIds.map(async nodeId => {
              try {
                const c = await prisma.dragTreeNodeContent.findFirst({
                  where: { drag_tree_node_id: nodeId, status: 'ACTIVE' },
                  select: { content_text: true, updated_at: true },
                  orderBy: { updated_at: 'desc' },
                })
                if (c?.content_text) {
                  const txt = c.content_text.trim()
                  contentByNodeId[nodeId] = txt
                }
              } catch {}
            })
          )

          if (nodes.length > 0) {
            const lines: string[] = []
            lines.push("Selected context items from user's workspace:")
            nodes.forEach((n, idx) => {
              const snippet = contentByNodeId[n.id]
              if (snippet) {
                lines.push(`${idx + 1}. ${n.label}`)
                lines.push('')
                lines.push(snippet)
                lines.push('')
              } else {
                lines.push(`${idx + 1}. ${n.label}`)
              }
            })
            contextText = lines.join('\n')
          }
        }
      }

      if (contextText && contextText.trim().length > 0) {
        await prisma.aiMessage.create({
          data: {
            conversationId,
            role: 'SYSTEM',
            content: `CONTEXT:\n${contextText}`,
            ui_message: {
              id: `sys_${Date.now()}`,
              role: 'system',
              parts: [{ type: 'text', text: `CONTEXT:\n${contextText}` }],
            },
          },
        })
      }
    } catch (e) {
      console.warn('[Conversations POST] Failed to create context message:', e)
    }

    // Generate title upfront from the initial user message (fast heuristic)
    let finalTitle = title
    const generateTitleFromText = (text: string): string => {
      const t = (text || '').trim().replace(/\s+/g, ' ')
      if (!t) return 'Untitled chat'
      const firstLine = t.split('\n')[0]
      const max = 50
      if (firstLine.length <= max) return firstLine
      const idx = firstLine.lastIndexOf(' ', max)
      return (
        (idx > 20 ? firstLine.slice(0, idx) : firstLine.slice(0, max)) + '...'
      )
    }

    try {
      if (typeof initialUserMessage === 'string' && initialUserMessage.trim()) {
        finalTitle = generateTitleFromText(initialUserMessage)
        await prisma.aiConversation.update({
          where: { id: conversationId },
          data: { title: finalTitle },
        })
      }
    } catch (e) {
      console.warn('[Conversations POST] Failed to set upfront title:', e)
    }

    return NextResponse.json({ conversationId, title: finalTitle })
  } catch (err) {
    console.error('Conversation create error', err)
    return standardErrors.internalError()
  }
}

/**
 * GET /api/aipane/conversations?contextEntityType=drag_tree&contextEntityId=tree_123
 * Returns array of conversation metadata (id, title, createdAt, updatedAt)
 */
export async function GET(req: Request) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
    }

    const url = new URL(req.url)
    const contextEntityType = url.searchParams.get('contextEntityType')
    const contextEntityId = url.searchParams.get('contextEntityId')

    if (!contextEntityType || !contextEntityId) {
      return NextResponse.json(
        { message: 'Missing contextEntityType or contextEntityId' },
        { status: 400 }
      )
    }

    const result = await getConversationsByContext(
      contextEntityType,
      contextEntityId
    )

    if (!result.success) {
      return NextResponse.json({ message: result.error }, { status: 500 })
    }

    return NextResponse.json({ conversations: result.data })
  } catch (error) {
    console.error('List conversations error:', error)
    return NextResponse.json(
      { message: 'Error listing conversations' },
      { status: 500 }
    )
  }
}
