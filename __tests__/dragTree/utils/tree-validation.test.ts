/**
 * Tree validation utilities test
 * Testing pure utility functions for tree structure validation
 * These are the core functions used in Prisma middleware
 */

// Extract the functions we want to test directly to avoid next-auth import issues
const extractIdsFromTreeStructure = (treeStructure: any): Set<string> => {
  if (!treeStructure || typeof treeStructure !== 'object') return new Set()

  const { root_id, hierarchy } = treeStructure as {
    root_id: string
    hierarchy: Record<string, string[]>
  }

  if (!root_id || !hierarchy) return new Set()

  const ids = new Set<string>()
  const stack: string[] = [root_id]

  while (stack.length) {
    const current = stack.pop() as string
    if (!ids.has(current)) {
      ids.add(current)
      const children = hierarchy[current] || []
      children.forEach(child => stack.push(child))
    }
  }

  return ids
}

const validateTreeAlignmentRaw = (
  treeStructure: any,
  activeIds: Set<string>
): boolean => {
  const idsFromTree = extractIdsFromTreeStructure(treeStructure)

  if (idsFromTree.size !== activeIds.size) return false
  for (const id of Array.from(idsFromTree)) {
    if (!activeIds.has(id)) return false
  }
  return true
}

describe('Tree Validation Utilities (Core Logic)', () => {
  describe('extractIdsFromTreeStructure', () => {
    it('extracts all IDs from a simple tree structure', () => {
      const treeStructure = {
        root_id: 'root',
        hierarchy: {
          root: ['child1', 'child2'],
          child1: ['grandchild1'],
          child2: [],
          grandchild1: [],
        },
      }

      const result = extractIdsFromTreeStructure(treeStructure)

      expect(result).toEqual(
        new Set(['root', 'child1', 'child2', 'grandchild1'])
      )
      expect(result.size).toBe(4)
    })

    it('handles deep nested hierarchies correctly', () => {
      const treeStructure = {
        root_id: 'level0',
        hierarchy: {
          level0: ['level1a', 'level1b'],
          level1a: ['level2a'],
          level1b: ['level2b'],
          level2a: ['level3'],
          level2b: [],
          level3: [],
        },
      }

      const result = extractIdsFromTreeStructure(treeStructure)

      expect(result).toEqual(
        new Set([
          'level0',
          'level1a',
          'level1b',
          'level2a',
          'level2b',
          'level3',
        ])
      )
    })

    it('handles single node tree (root only)', () => {
      const treeStructure = {
        root_id: 'solo',
        hierarchy: {
          solo: [],
        },
      }

      const result = extractIdsFromTreeStructure(treeStructure)

      expect(result).toEqual(new Set(['solo']))
      expect(result.size).toBe(1)
    })

    it('prevents infinite loops with circular references', () => {
      const treeStructure = {
        root_id: 'nodeA',
        hierarchy: {
          nodeA: ['nodeB'],
          nodeB: ['nodeA'], // circular reference
        },
      }

      const result = extractIdsFromTreeStructure(treeStructure)

      // Should only visit each node once
      expect(result).toEqual(new Set(['nodeA', 'nodeB']))
      expect(result.size).toBe(2)
    })

    it('returns empty set for invalid input types', () => {
      expect(extractIdsFromTreeStructure(null).size).toBe(0)
      expect(extractIdsFromTreeStructure(undefined).size).toBe(0)
      expect(extractIdsFromTreeStructure('invalid').size).toBe(0)
      expect(extractIdsFromTreeStructure(123).size).toBe(0)
    })

    it('returns empty set when root_id is missing', () => {
      const treeStructure = {
        hierarchy: { a: ['b'] },
      }

      const result = extractIdsFromTreeStructure(treeStructure)
      expect(result.size).toBe(0)
    })

    it('returns empty set when hierarchy is missing', () => {
      const treeStructure = {
        root_id: 'root',
      }

      const result = extractIdsFromTreeStructure(treeStructure)
      expect(result.size).toBe(0)
    })

    it('handles missing children arrays gracefully', () => {
      const treeStructure = {
        root_id: 'root',
        hierarchy: {
          root: ['child1'],
          // child1 not defined in hierarchy - should be treated as empty array
        },
      }

      const result = extractIdsFromTreeStructure(treeStructure)
      expect(result).toEqual(new Set(['root', 'child1']))
    })
  })

  describe('validateTreeAlignmentRaw', () => {
    it('validates TRUE when tree IDs exactly match active IDs', () => {
      const treeStructure = {
        root_id: 'root',
        hierarchy: {
          root: ['a', 'b'],
          a: ['c'],
          b: [],
          c: [],
        },
      }
      const activeIds = new Set(['root', 'a', 'b', 'c'])

      expect(validateTreeAlignmentRaw(treeStructure, activeIds)).toBe(true)
    })

    it('validates FALSE when active IDs are missing from tree', () => {
      const treeStructure = {
        root_id: 'root',
        hierarchy: {
          root: ['child1', 'child2'],
          child1: [],
          child2: [],
        },
      }
      const activeIds = new Set(['root', 'child1']) // missing child2

      expect(validateTreeAlignmentRaw(treeStructure, activeIds)).toBe(false)
    })

    it('validates FALSE when active IDs contain extra nodes not in tree', () => {
      const treeStructure = {
        root_id: 'root',
        hierarchy: {
          root: [],
        },
      }
      const activeIds = new Set(['root', 'extraNode'])

      expect(validateTreeAlignmentRaw(treeStructure, activeIds)).toBe(false)
    })

    it('validates TRUE for empty tree and empty active set', () => {
      const treeStructure = undefined
      const activeIds = new Set<string>()

      expect(validateTreeAlignmentRaw(treeStructure, activeIds)).toBe(true)
    })

    it('validates FALSE when sizes differ (more in tree than active)', () => {
      const treeStructure = {
        root_id: 'root',
        hierarchy: {
          root: ['child'],
          child: [],
        },
      }
      const activeIds = new Set(['root']) // missing child

      expect(validateTreeAlignmentRaw(treeStructure, activeIds)).toBe(false)
    })

    it('validates FALSE when sizes differ (more active than in tree)', () => {
      const treeStructure = {
        root_id: 'root',
        hierarchy: {
          root: [],
        },
      }
      const activeIds = new Set(['root', 'extra1', 'extra2'])

      expect(validateTreeAlignmentRaw(treeStructure, activeIds)).toBe(false)
    })

    it('handles complex tree structures correctly', () => {
      const treeStructure = {
        root_id: 'cat_financial',
        hierarchy: {
          cat_financial: ['que_budget', 'que_savings'],
          que_budget: ['cat_expenses'],
          que_savings: [],
          cat_expenses: ['que_housing', 'que_food'],
          que_housing: [],
          que_food: [],
        },
      }
      const activeIds = new Set([
        'cat_financial',
        'que_budget',
        'que_savings',
        'cat_expenses',
        'que_housing',
        'que_food',
      ])

      expect(validateTreeAlignmentRaw(treeStructure, activeIds)).toBe(true)
    })
  })

  describe('Integration Tests - Prisma Middleware Logic', () => {
    it('mimics middleware validation for valid tree-node alignment', () => {
      // Simulate what the Prisma middleware does
      const treeStructure = {
        root_id: 'root',
        hierarchy: {
          root: ['child1', 'child2'],
          child1: [],
          child2: [],
        },
      }

      // Simulate active nodes from database
      const mockActiveNodes = [
        { id: 'root' },
        { id: 'child1' },
        { id: 'child2' },
      ]

      const activeIdSet = new Set(mockActiveNodes.map(n => n.id))
      const isValid = validateTreeAlignmentRaw(treeStructure, activeIdSet)

      expect(isValid).toBe(true)
      // In real middleware, if isValid is false, it would throw an error
    })

    it('detects misalignment that would trigger middleware error', () => {
      const treeStructure = {
        root_id: 'root',
        hierarchy: {
          root: ['child1', 'child2'],
          child1: [],
          child2: [],
        },
      }

      // Simulate orphaned node in database (not in tree_structure)
      const mockActiveNodes = [
        { id: 'root' },
        { id: 'child1' },
        { id: 'child2' },
        { id: 'orphaned_node' }, // This would cause validation failure
      ]

      const activeIdSet = new Set(mockActiveNodes.map(n => n.id))
      const isValid = validateTreeAlignmentRaw(treeStructure, activeIdSet)

      expect(isValid).toBe(false)
      // In real middleware, this would throw: 'Drag tree hierarchy and active node set are out of sync'
    })
  })
})
