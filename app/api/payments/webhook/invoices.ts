import Stripe from 'stripe'
import prisma from '@/app/libs/prismadb'
import { stripe } from '@/lib/stripe/config'

function toDate(ts?: number | null): Date | null {
  if (!ts) return null
  const d = new Date(0)
  d.setSeconds(ts)
  return d
}

export async function upsertInvoiceRecord(invoice: Stripe.Invoice) {
  try {
    const priceId = invoice.lines?.data?.[0]?.price?.id
    const productId =
      (invoice.lines?.data?.[0]?.price?.product as string) || undefined
    const subscriptionId = (invoice.subscription as string) || undefined

    // Best-effort match to user by customer_id
    const user = invoice.customer
      ? await prisma.user.findFirst({
          where: { subscription_customer_id: invoice.customer as string },
          select: { id: true },
        })
      : null

    await (prisma as any).invoice.upsert({
      where: { id: invoice.id },
      update: {
        customer_id: (invoice.customer as string) ?? '',
        user_id: user?.id,
        subscription_id: subscriptionId,
        product_id: productId,
        price_id: priceId,
        status: invoice.status ?? 'unknown',
        currency: invoice.currency ?? 'usd',
        number: invoice.number ?? null,
        hosted_invoice_url: invoice.hosted_invoice_url ?? null,
        invoice_pdf: invoice.invoice_pdf ?? null,
        subtotal: (invoice.subtotal as number | null) ?? null,
        total: (invoice.total as number) ?? 0,
        amount_paid: (invoice.amount_paid as number | null) ?? null,
        amount_due: (invoice.amount_due as number | null) ?? null,
        attempted: invoice.attempted ?? null,
        paid: invoice.paid ?? null,
        period_start: toDate(invoice.period_start ?? null),
        period_end: toDate(invoice.period_end ?? null),
        due_date: toDate(invoice.due_date ?? null),
        metadata: invoice.metadata ? JSON.stringify(invoice.metadata) : null,
      },
      create: {
        id: invoice.id,
        customer_id: (invoice.customer as string) ?? '',
        user_id: user?.id ?? null,
        subscription_id: subscriptionId ?? null,
        product_id: productId ?? null,
        price_id: priceId ?? null,
        status: invoice.status ?? 'unknown',
        currency: invoice.currency ?? 'usd',
        number: invoice.number ?? null,
        hosted_invoice_url: invoice.hosted_invoice_url ?? null,
        invoice_pdf: invoice.invoice_pdf ?? null,
        subtotal: (invoice.subtotal as number | null) ?? null,
        total: (invoice.total as number) ?? 0,
        amount_paid: (invoice.amount_paid as number | null) ?? null,
        amount_due: (invoice.amount_due as number | null) ?? null,
        attempted: invoice.attempted ?? null,
        paid: invoice.paid ?? null,
        period_start: toDate(invoice.period_start ?? null),
        period_end: toDate(invoice.period_end ?? null),
        due_date: toDate(invoice.due_date ?? null),
        metadata: invoice.metadata ? JSON.stringify(invoice.metadata) : null,
      },
    })
  } catch (error) {
    console.error('Failed to upsert invoice:', error)
    throw error
  }
}

export async function upsertPaymentIntentRecord(pi: Stripe.PaymentIntent) {
  try {
    // Best-effort link to user by customer_id if present
    const user = pi.customer
      ? await prisma.user.findFirst({
          where: { subscription_customer_id: pi.customer as string },
          select: { id: true },
        })
      : null

    // Retrieve receipt_url from latest_charge if available
    let receiptUrl: string | null = null
    if (typeof pi.latest_charge === 'string' && pi.latest_charge) {
      try {
        const charge = await stripe.charges.retrieve(pi.latest_charge)
        receiptUrl = (charge as any)?.receipt_url ?? null
      } catch (err) {
        console.warn('Could not retrieve charge for receipt_url:', err)
      }
    }

    await (prisma as any).paymentIntent.upsert({
      where: { id: pi.id },
      update: {
        customer_id: (pi.customer as string) ?? null,
        user_id: user?.id ?? null,
        amount: (pi.amount as number) ?? 0,
        currency: pi.currency ?? 'usd',
        status: pi.status,
        payment_method: (pi.payment_method as string) ?? null,
        latest_charge: (pi.latest_charge as string) ?? null,
        receipt_url: receiptUrl,
        description: pi.description ?? null,
        metadata: pi.metadata ? JSON.stringify(pi.metadata) : null,
        stripe_created_at: toDate(pi.created),
      },
      create: {
        id: pi.id,
        customer_id: (pi.customer as string) ?? null,
        user_id: user?.id ?? null,
        amount: (pi.amount as number) ?? 0,
        currency: pi.currency ?? 'usd',
        status: pi.status,
        payment_method: (pi.payment_method as string) ?? null,
        latest_charge: (pi.latest_charge as string) ?? null,
        receipt_url: receiptUrl,
        description: pi.description ?? null,
        metadata: pi.metadata ? JSON.stringify(pi.metadata) : null,
        stripe_created_at: toDate(pi.created),
      },
    })
  } catch (error) {
    console.error('Failed to upsert PaymentIntent:', error)
    throw error
  }
}
