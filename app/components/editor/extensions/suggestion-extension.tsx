'use client'

import { Node, mergeAttributes } from '@tiptap/core'
import { ReactNodeViewRenderer } from '@tiptap/react'
import SuggestionNodeView from '@/app/components/editor/components/SuggestionNodeView'

const SuggestionExtension = Node.create({
  name: 'suggestion',

  group: 'inline',
  inline: true,
  atom: true,
  selectable: false,

  addAttributes() {
    return {
      oldText: {
        default: '',
        parseHTML: element => element.getAttribute('data-old') || '',
        renderHTML: attributes => {
          if (!attributes.oldText) return {}
          return { 'data-old': attributes.oldText }
        },
      },
      newText: {
        default: '',
        parseHTML: element => element.getAttribute('data-new') || '',
        renderHTML: attributes => {
          if (!attributes.newText) return {}
          return { 'data-new': attributes.newText }
        },
      },
      id: {
        default: '',
        parseHTML: element => element.getAttribute('data-id') || '',
        renderHTML: attributes => {
          if (!attributes.id) return {}
          return { 'data-id': attributes.id }
        },
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: "span[data-type='suggestion']",
        getAttrs: element => ({
          oldText: (element as HTMLElement).getAttribute('data-old') || '',
          newText: (element as HTMLElement).getAttribute('data-new') || '',
          id: (element as HTMLElement).getAttribute('data-id') || '',
        }),
      },
    ]
  },

  renderHTML({ node, HTMLAttributes }) {
    return [
      'span',
      mergeAttributes(
        {
          'data-type': 'suggestion',
          'data-old': node.attrs.oldText,
          'data-new': node.attrs.newText,
          'data-id': node.attrs.id,
        },
        HTMLAttributes
      ),
      0,
    ]
  },

  addNodeView() {
    return ReactNodeViewRenderer(SuggestionNodeView)
  },

  addStorage() {
    return {
      markdown: {
        // When exporting to markdown, use the old text (original content)
        serialize: (state: any, node: any) => {
          state.text(node.attrs.oldText || '', false)
        },
      },
    }
  },
})

export default SuggestionExtension
