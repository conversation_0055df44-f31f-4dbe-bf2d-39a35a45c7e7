import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog'
import { toast } from 'react-hot-toast'
import { GuidanceDialogProps } from '@/app/(conv)/dragTree/[dragTreeId]/types'

const GuidanceDialog: React.FC<GuidanceDialogProps> = ({
  isOpen,
  guidanceType,
  node,
  onGenerate,
  onCancel,
  isExpanding,
}) => {
  const [guidanceText, setGuidanceText] = useState<string>('')

  const handleGenerate = async (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    console.log('Generate button clicked', {
      guidanceType,
      guidanceText,
    })

    // Store values before clearing state
    const currentGuidanceText = guidanceText

    // Close dialog immediately to prevent interaction issues
    onCancel()
    setGuidanceText('')

    // Small delay to ensure dialog closes and focus is properly managed
    setTimeout(async () => {
      try {
        console.log('Calling onGenerate...')
        await onGenerate(currentGuidanceText)
        console.log('Handler completed successfully')
      } catch (error) {
        console.error('Error in generate button handler:', error)
        toast.error('Failed to generate content')
      }
    }, 150)
  }

  const handleCancel = () => {
    console.log('Cancel button clicked')
    setGuidanceText('')
    onCancel()
  }

  const handleOpenChange = (open: boolean) => {
    console.log('Dialog onOpenChange:', open, 'expanding:', isExpanding)

    // Prevent dialog closure during expansion operations
    if (!open && isExpanding) {
      console.warn('⚠️ Preventing dialog closure during expansion')
      return
    }

    if (!open) {
      handleCancel()
    }
  }

  const isQuestion = node?.type === 'question'
  const targetType = guidanceType === 'questions' ? 'questions' : 'categories'

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>
            Generate {targetType === 'questions' ? 'Questions' : 'Categories'}
          </DialogTitle>
          <DialogDescription>
            {isQuestion
              ? `Generate ${targetType} related to the question: "${node?.label}"`
              : `Generate ${targetType} under the category: "${node?.label}"`}
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <Textarea
            placeholder={`Optional: Provide specific guidance for generating ${targetType}...`}
            value={guidanceText}
            onChange={e => setGuidanceText(e.target.value)}
            className="min-h-[80px]"
          />
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleGenerate} disabled={isExpanding}>
            {isExpanding ? 'Generating...' : `Generate ${targetType}`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

// Memoize to prevent unnecessary re-renders when props haven't changed
export default React.memo(GuidanceDialog)
