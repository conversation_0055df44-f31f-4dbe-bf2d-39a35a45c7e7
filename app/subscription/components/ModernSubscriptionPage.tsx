'use client'

import React from 'react'
import { ModernSubscriptionHeader } from './ModernSubscriptionHeader'
import { ModernPricingCard } from './ModernPricingCard'
import { BillingToggle } from './BillingToggle'
import { ContactInfo } from './ContactInfo'
import { getAllPricingPlans, PricingTier } from './ModernPricingConfig'
import { useModernSubscription } from '../hooks/useModernSubscription'
import { SubscriptionTier } from '@prisma/client'

export const ModernSubscriptionPage: React.FC = () => {
  const { session, currentTier, isPaidUser, isLoading, handleStripeCheckout } =
    useModernSubscription()

  const [isYearly, setIsYearly] = React.useState<boolean>(false)
  const pricingPlans = getAllPricingPlans()

  const isCurrentPlan = (planTier: PricingTier): boolean => {
    if (planTier === 'FREE') {
      return currentTier === SubscriptionTier.FREE
    }
    if (planTier === 'PRO') {
      return isPaidUser // PRO, GUEST, ULTRA, BUSINESS all count as "PRO" plan
    }
    return false
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      {/* Header Section */}
      <ModernSubscriptionHeader
        userTier={currentTier}
        userName={session?.user?.name || undefined}
      />

      {/* Pricing Cards Section */}
      <div className="max-w-4xl mx-auto px-4 pb-16">
        {/* Billing Toggle */}
        <BillingToggle isYearly={isYearly} onToggle={setIsYearly} />

        <div className="grid md:grid-cols-2 gap-8 max-w-3xl mx-auto">
          {pricingPlans.map(plan => (
            <ModernPricingCard
              key={plan.id}
              plan={plan}
              isCurrentPlan={isCurrentPlan(plan.id)}
              onSubscribe={handleStripeCheckout}
              isLoading={isLoading}
              isYearly={isYearly}
            />
          ))}
        </div>
      </div>

      {/* Contact Info */}
      <ContactInfo />
    </div>
  )
}
