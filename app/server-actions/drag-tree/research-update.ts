'use server'

import { auth } from '@/auth'
import prismadb from '@/app/libs/prismadb'
import { revalidatePath } from 'next/cache'
import type { Prisma } from '@prisma/client'

/**
 * Direct server action for updating research content
 * Validates user ownership and updates content_text
 */
export async function updateDragTreeNodeContent(
  contentId: string,
  contentText: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const session = await auth()

    if (!session?.user?.id) {
      return { success: false, error: 'Unauthorized' }
    }

    // Get the content with nested relations to check ownership
    const existingContent = await prismadb.dragTreeNodeContent.findUnique({
      where: { id: contentId },
      include: {
        drag_tree_node: {
          include: {
            drag_tree: true,
          },
        },
      },
    })

    if (!existingContent) {
      return { success: false, error: 'Content not found' }
    }

    // Validate user ownership
    if (existingContent.drag_tree_node.drag_tree.user_id !== session.user.id) {
      return { success: false, error: 'Unauthorized' }
    }

    // Update the content
    await prismadb.dragTreeNodeContent.update({
      where: { id: contentId },
      data: { content_text: contentText },
    })

    // Revalidate the specific drag tree page
    revalidatePath(`/dragTree/${existingContent.drag_tree_node.drag_tree.id}`)

    return { success: true }
  } catch (error) {
    console.error('Error updating research content:', error)
    return { success: false, error: 'Failed to update content' }
  }
}

/**
 * Server action for marking research content as read
 * Safely updates content_metadata to add isRead: true while preserving existing metadata
 */
export async function markContentAsRead(
  contentId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const session = await auth()

    if (!session?.user?.id) {
      return { success: false, error: 'Unauthorized' }
    }

    // Get the content with minimal data needed for ownership check and metadata update
    const existingContent = await prismadb.dragTreeNodeContent.findUnique({
      where: { id: contentId },
      select: {
        id: true,
        content_metadata: true,
        drag_tree_node: {
          select: {
            drag_tree: {
              select: {
                id: true,
                user_id: true,
              },
            },
          },
        },
      },
    })

    if (!existingContent) {
      return { success: false, error: 'Content not found' }
    }

    // Validate user ownership
    if (existingContent.drag_tree_node.drag_tree.user_id !== session.user.id) {
      return { success: false, error: 'Unauthorized' }
    }

    // Safely merge the isRead flag with existing metadata
    const currentMetadata = (
      typeof existingContent.content_metadata === 'object' &&
      existingContent.content_metadata !== null
        ? (existingContent.content_metadata as Prisma.JsonObject)
        : {}
    ) as Prisma.JsonObject

    const updatedMetadata: Prisma.JsonObject = {
      ...currentMetadata,
      isRead: true,
    }

    // Update the content metadata
    await prismadb.dragTreeNodeContent.update({
      where: { id: contentId },
      data: { content_metadata: updatedMetadata },
    })

    console.log(`✅ [markContentAsRead] Marked content ${contentId} as read`)
    return { success: true }
  } catch (error) {
    console.error('Error marking content as read:', error)
    return { success: false, error: 'Failed to mark content as read' }
  }
}
