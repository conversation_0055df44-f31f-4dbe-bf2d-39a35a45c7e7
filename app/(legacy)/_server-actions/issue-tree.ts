'use server'

import prisma from '@/app/libs/prismadb'
import { IssueTreeStatus } from '@prisma/client'
import { JsonValue } from '@prisma/client/runtime/library'

export type fetchedIssueTreeType = {
  id: string
  conversation_id: string
  status: IssueTreeStatus
  raw_markdown: string | null
  nodes: string | null
  edges: string | null
  summary_context: string | null
  config: JsonValue
}

export const fetchIssueTree = async (
  conversationId: string
): Promise<fetchedIssueTreeType | null> => {
  const issueTree = await prisma.issueTree.findFirst({
    where: {
      conversation_id: conversationId,
    },
    select: {
      id: true,
      conversation_id: true,
      status: true,
      raw_markdown: true,
      nodes: true,
      edges: true,
      summary_context: true,
      config: true,
    },
    orderBy: {
      created_at: 'desc',
    },
  })
  return issueTree
}

export const checkIssueTreeIsActive = async (
  conversationId: string
): Promise<boolean> => {
  const issueTree = await prisma.issueTree.findFirst({
    where: { conversation_id: conversationId },
    select: { status: true },
    orderBy: { created_at: 'asc' },
  })

  return !!issueTree && issueTree.status === IssueTreeStatus.ACTIVE
}

export const setIssueTreeStatus = async (
  conversationId: string,
  status: IssueTreeStatus
): Promise<void> => {
  const issueTree = await prisma.issueTree.findFirst({
    where: { conversation_id: conversationId },
    select: { id: true },
    orderBy: { created_at: 'asc' },
  })

  if (!issueTree) return

  await prisma.issueTree.update({
    where: { id: issueTree.id },
    data: { status },
  })
}
