'use client'

import { create } from 'zustand'

// Types for selection functionality
export type SelectionRectangle = {
  id: string
  startX: number
  startY: number
  endX: number
  endY: number
  isActive: boolean // Whether this rectangle is currently being drawn
}

export type SelectionState = {
  // Core selection mode state
  isSelectionMode: boolean
  selectedNodeIds: Set<string>
  nonResearchedNodeIds: Set<string> // Track non-researched nodes for feedback
  selectionRectangles: SelectionRectangle[]

  // Current drawing state
  isDrawing: boolean
  currentRectangle: SelectionRectangle | null

  // Context selection modal state
  showContextSelectionModal: boolean

  // Actions
  toggleSelectionMode: () => void
  setSelectionMode: (enabled: boolean) => void

  // Node selection actions
  addSelectedNode: (nodeId: string) => void
  removeSelectedNode: (nodeId: string) => void
  removeNonResearchedNode: (nodeId: string) => void
  toggleSelectedNode: (nodeId: string) => void
  setSelectedNodes: (nodeIds: string[], nonResearchedIds?: string[]) => void
  clearSelectedNodes: () => void

  // Rectangle drawing actions
  startDrawing: (x: number, y: number) => void
  updateDrawing: (x: number, y: number) => void
  finishDrawing: () => void
  cancelDrawing: () => void
  clearRectangles: () => void

  // Utility actions
  clearAll: () => void
  getSelectedCount: () => number

  // Context selection modal actions
  setShowContextSelectionModal: (show: boolean) => void
}

export const useSelectionStore = create<SelectionState>((set, get) => ({
  // Initial state
  isSelectionMode: false,
  selectedNodeIds: new Set<string>(),
  nonResearchedNodeIds: new Set<string>(),
  selectionRectangles: [],
  isDrawing: false,
  currentRectangle: null,
  showContextSelectionModal: false,

  // Selection mode actions
  toggleSelectionMode: () => {
    const { isSelectionMode } = get()
    const newMode = !isSelectionMode

    set({ isSelectionMode: newMode })

    // Clear all selections when exiting selection mode
    if (!newMode) {
      get().clearAll()
    }
  },

  setSelectionMode: (enabled: boolean) => {
    set({ isSelectionMode: enabled })

    // Clear all selections when exiting selection mode
    if (!enabled) {
      get().clearAll()
    }
  },

  // Node selection actions
  addSelectedNode: (nodeId: string) => {
    set(state => ({
      selectedNodeIds: new Set([...Array.from(state.selectedNodeIds), nodeId]),
    }))
  },

  removeSelectedNode: (nodeId: string) => {
    set(state => {
      const newSet = new Set(state.selectedNodeIds)
      newSet.delete(nodeId)
      return { selectedNodeIds: newSet }
    })
  },

  removeNonResearchedNode: (nodeId: string) => {
    set(state => {
      const newSet = new Set(state.nonResearchedNodeIds)
      newSet.delete(nodeId)
      return { nonResearchedNodeIds: newSet }
    })
  },

  toggleSelectedNode: (nodeId: string) => {
    const { selectedNodeIds } = get()
    if (selectedNodeIds.has(nodeId)) {
      get().removeSelectedNode(nodeId)
    } else {
      get().addSelectedNode(nodeId)
    }
  },

  setSelectedNodes: (nodeIds: string[], nonResearchedIds: string[] = []) => {
    set({
      selectedNodeIds: new Set(nodeIds),
      nonResearchedNodeIds: new Set(nonResearchedIds),
    })
  },

  clearSelectedNodes: () => {
    set({
      selectedNodeIds: new Set<string>(),
      nonResearchedNodeIds: new Set<string>(),
    })
  },

  // Rectangle drawing actions
  startDrawing: (x: number, y: number) => {
    const rectangleId = `rect_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const newRectangle: SelectionRectangle = {
      id: rectangleId,
      startX: x,
      startY: y,
      endX: x,
      endY: y,
      isActive: true,
    }

    set({
      isDrawing: true,
      currentRectangle: newRectangle,
    })
  },

  updateDrawing: (x: number, y: number) => {
    const { currentRectangle } = get()
    if (!currentRectangle) return

    set({
      currentRectangle: {
        ...currentRectangle,
        endX: x,
        endY: y,
      },
    })
  },

  finishDrawing: () => {
    const { currentRectangle, selectionRectangles } = get()
    if (!currentRectangle) return

    const finishedRectangle: SelectionRectangle = {
      ...currentRectangle,
      isActive: false,
    }

    set({
      isDrawing: false,
      currentRectangle: null,
      selectionRectangles: [...selectionRectangles, finishedRectangle],
    })
  },

  cancelDrawing: () => {
    set({
      isDrawing: false,
      currentRectangle: null,
    })
  },

  clearRectangles: () => {
    set({
      selectionRectangles: [],
      currentRectangle: null,
      isDrawing: false,
    })
  },

  // Utility actions
  clearAll: () => {
    set({
      selectedNodeIds: new Set<string>(),
      nonResearchedNodeIds: new Set<string>(),
      selectionRectangles: [],
      currentRectangle: null,
      isDrawing: false,
    })
  },

  getSelectedCount: () => {
    return get().selectedNodeIds.size
  },

  // Context selection modal actions
  setShowContextSelectionModal: (show: boolean) => {
    set({ showContextSelectionModal: show })
  },
}))
