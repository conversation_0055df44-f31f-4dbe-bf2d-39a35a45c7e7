// Helper utilities for working with DragTree NodeContentItem structures
// The goal is to reliably extract plain-text (Markdown) from the many shapes
// a NodeContentItem may take.  Historically, research content might be stored
// in different places depending on the version of the pipeline:
// 1. contentText (preferred going forward)
// 2. metadata.summary or metadata.content
// 3. metadata.searchResults – Brave/Serp API results
// 4. messages – the first assistant message often contains the response
// 5. Tiptap JSON stored in any of the above
//
// This helper consolidates those cases so the rest of the app has ONE place to
// ask for the best available plain-text representation.

import { JSONContent } from '@tiptap/core'
import { convertTiptapJsonToMarkdown } from '@/app/components/editor/utils'

/**
 * Attempt to convert an arbitrary value into Markdown text.
 */
function toMarkdownMaybe(value: unknown): string {
  if (!value) return ''

  // Tiptap JSON (object with { type: 'doc' })
  if (typeof value === 'object' && (value as any).type === 'doc') {
    try {
      return convertTiptapJsonToMarkdown(value as J<PERSON><PERSON>ontent)
    } catch {
      return ''
    }
  }

  // JSON string that might be Tiptap JSON
  if (typeof value === 'string') {
    const trimmed = value.trim()
    if (trimmed.startsWith('{') && trimmed.endsWith('}')) {
      try {
        const parsed = JSON.parse(trimmed)
        if (parsed && parsed.type === 'doc') {
          return convertTiptapJsonToMarkdown(parsed as JSONContent)
        }
      } catch {
        // fall-through – treat as plain string
      }
    }
    return trimmed
  }

  return ''
}

/**
 * Extract best-effort markdown text from a NodeContentItem-like object.
 * The function is tolerant to missing fields so it can be used broadly.
 */
export const extractNodeContentText = (item: any): string => {
  if (!item || typeof item !== 'object') return ''

  // Preferred: explicit contentText field
  let text = toMarkdownMaybe((item as any).contentText)
  if (text) return text

  // Search common metadata fields
  const meta = (item as any).metadata || {}
  if (meta.summary) {
    text = toMarkdownMaybe(meta.summary)
    if (text) return text
  }
  if (meta.content) {
    text = toMarkdownMaybe(meta.content)
    if (text) return text
  }
  if (meta.searchResults) {
    // searchResults can be string or array
    if (typeof meta.searchResults === 'string') {
      text = meta.searchResults
    } else if (Array.isArray(meta.searchResults)) {
      text = meta.searchResults
        .map((r: any) => r.snippet || r.description || r.content || '')
        .filter(Boolean)
        .join('\n')
    }
    if (text) return text
  }

  // Fallback: first assistant message content
  if (Array.isArray(item.messages) && item.messages.length > 0) {
    const assistant = item.messages.find(
      (m: any) => m.role === 'assistant' && typeof m.content === 'string'
    )
    if (assistant?.content) {
      text = assistant.content
      if (text) return text
    }
  }

  return ''
}
