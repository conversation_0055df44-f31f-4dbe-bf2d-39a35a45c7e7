// Polyfills for browsers that miss certain modern Web APIs (e.g. Safari < 15.4)

/**
 * Safari < 15.4 does not implement globalThis.structuredClone.
 * For our usage (deep-cloning plain objects / arrays), a
 * JSON-based fallback is sufficient.
 *
 * NOTE: This fallback will ignore non-serializable values
 * (functions, Map, Set, Date, RegExp, etc.). Our TreeNode
 * objects only contain primitives, so this is safe.
 */
if (typeof globalThis.structuredClone === 'undefined') {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  globalThis.structuredClone = (value: any) => JSON.parse(JSON.stringify(value))
}

export {} // keep this a module
