'use client'

import { signIn, useSession } from 'next-auth/react'
import { useEffect, useState } from 'react'
import { FcGoogle } from 'react-icons/fc'
// TODO: Re-enable email authentication after domain setup
// import { HiMail } from 'react-icons/hi'
import { useRouter } from 'next/navigation'
import { toast } from 'react-hot-toast'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
// TODO: Re-enable email authentication after domain setup
// import { Label } from '@/components/ui/label'
import { isLocalOrDevEnv } from '@/lib/utils'
import LoadingPage from '@/app/components/LoadingPage'
// import { useSessionRefresh } from '@/app/hooks/useSessionRefresh'

const LOCAL_ACCESS_CODE = '+852'

// TODO: Re-enable email authentication after domain setup
// type AuthMethod = 'select' | 'google' | 'email'
// type EmailState = 'input' | 'sending' | 'sent'

const AuthForm = () => {
  const session = useSession()
  const router = useRouter()
  // const { refreshSessionSilently } = useSessionRefresh()
  const [accessCode, setAccessCode] = useState<string>('')
  const [isAccessGranted, setIsAccessGranted] =
    useState<boolean>(!isLocalOrDevEnv())
  const [isLoading, setIsLoading] = useState<boolean>(false)
  // TODO: Re-enable email authentication after domain setup
  // const [authMethod, setAuthMethod] = useState<AuthMethod>('select')
  // const [email, setEmail] = useState<string>('')
  // const [emailState, setEmailState] = useState<EmailState>('input')
  // const [emailError, setEmailError] = useState<string>('')

  useEffect(() => {
    if (session?.status === 'loading') {
      setIsLoading(true)
    } else if (session?.status === 'authenticated') {
      router.push('/screening')
    } else {
      setIsLoading(false)
    }
  }, [session?.status, router])

  const socialAction = async (action: string) => {
    setIsLoading(true)
    try {
      // Let NextAuth handle the redirect to screening after successful sign-in
      await signIn(action, { callbackUrl: '/screening' })
    } catch (error) {
      console.error('Error during sign-in:', error)
      toast.error('Sign-in failed. Please try again.')
      setIsLoading(false)
    }
  }

  // TODO: Re-enable email authentication after domain setup
  /*
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const handleEmailSignIn = async () => {
    if (!email.trim()) {
      setEmailError('Email is required')
      return
    }

    if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address')
      return
    }

    setEmailError('')
    setEmailState('sending')
    setIsLoading(true)

    try {
      const result = await signIn('email', {
        email: email.trim(),
        callbackUrl: '/screening',
        redirect: false,
      })

      if (result?.error) {
        throw new Error(result.error)
      }

      setEmailState('sent')
      toast.success('Magic link sent! Check your email.')
    } catch (error) {
      console.error('Error sending magic link:', error)
      toast.error('Failed to send magic link. Please try again.')
      setEmailState('input')
    } finally {
      setIsLoading(false)
    }
  }

  const resetEmailForm = () => {
    setEmail('')
    setEmailError('')
    setEmailState('input')
    setAuthMethod('select')
  }
  */

  const handleAccessCodeSubmit = () => {
    if (accessCode === LOCAL_ACCESS_CODE) {
      setIsAccessGranted(true)
      toast.success('Access granted!')
    } else {
      toast.error('Invalid access code!')
    }
  }

  if (isLoading) {
    return <LoadingPage />
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className="bg-white text-gray-900 hover:bg-gray-200 hover:text-black transition-colors duration-300"
        >
          Try It Now
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px] bg-gray-800 text-white">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-center">
            Welcome to Clarify AI
          </DialogTitle>
        </DialogHeader>
        <div className="mt-8">
          <div className="bg-gray-700 shadow px-4 py-8 sm:rounded-lg sm:px-10">
            <div className="md:hidden mb-6">
              <h2 className="text-red-400 font-bold mb-2">IMPORTANT:</h2>
              <h2 className="text-lg mb-2">
                <strong>Laptop</strong> for the best experience!
              </h2>
              <h2 className="text-lg">Mobile is simply too small :-(</h2>
            </div>
            <div className="mt-6">
              {isLocalOrDevEnv() && !isAccessGranted ? (
                <div>
                  <Input
                    type="text"
                    placeholder="Dev env, pls enter access code to proceed..."
                    value={accessCode}
                    onChange={e => setAccessCode(e.target.value)}
                    className="mb-2 text-gray-900"
                  />
                  <Button onClick={handleAccessCodeSubmit} className="w-full">
                    Submit
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <Button
                    onClick={() => socialAction('google')}
                    className="w-full bg-white text-gray-900 hover:bg-gray-200 transition-colors duration-300 flex items-center justify-center"
                  >
                    <FcGoogle className="mr-2" />
                    Continue with Google
                  </Button>

                  {/* TODO: Re-enable email authentication after domain setup
                  <div className="relative">
                    <div className="absolute inset-0 flex items-center">
                      <span className="w-full border-t border-gray-600" />
                    </div>
                    <div className="relative flex justify-center text-xs uppercase">
                      <span className="bg-gray-700 px-2 text-gray-400">
                        Or
                      </span>
                    </div>
                  </div>

                  <Button
                    onClick={() => setAuthMethod('email')}
                    variant="outline"
                    className="w-full border-gray-600 text-gray-300 hover:bg-gray-600 hover:text-white hover:border-gray-500 transition-colors duration-300 flex items-center justify-center"
                  >
                    <HiMail className="mr-2" />
                    Continue with Email
                  </Button>
                  */}

                  {/* TODO: Re-enable email authentication after domain setup
                  {authMethod === 'email' && (
                    <div className="space-y-4">
                      {emailState === 'input' && (
                        <>
                          <div className="space-y-2">
                            <Label
                              htmlFor="email"
                              className="text-sm font-medium text-gray-300"
                            >
                              Email address
                            </Label>
                            <Input
                              id="email"
                              type="email"
                              placeholder="Enter your email address"
                              value={email}
                              onChange={e => {
                                setEmail(e.target.value)
                                if (emailError) setEmailError('')
                              }}
                              className="text-gray-900"
                              disabled={isLoading}
                            />
                            {emailError && (
                              <p className="text-sm text-red-400">
                                {emailError}
                              </p>
                            )}
                          </div>

                          <div className="flex space-x-3">
                            <Button
                              onClick={resetEmailForm}
                              variant="outline"
                              className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-600 hover:text-white hover:border-gray-500"
                              disabled={isLoading}
                            >
                              Back
                            </Button>
                            <Button
                              onClick={handleEmailSignIn}
                              className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
                              disabled={isLoading || !email.trim()}
                            >
                              {isLoading ? 'Sending...' : 'Send Magic Link'}
                            </Button>
                          </div>
                        </>
                      )}

                      {emailState === 'sending' && (
                        <div className="text-center py-4">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
                          <p className="text-gray-300">Sending magic link...</p>
                        </div>
                      )}

                      {emailState === 'sent' && (
                        <div className="text-center py-4 space-y-4">
                          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                            <HiMail className="w-8 h-8 text-green-600" />
                          </div>
                          <div>
                            <h3 className="text-lg font-medium text-white mb-2">
                              Check your email
                            </h3>
                            <p className="text-gray-300 text-sm mb-4">
                              We've sent a magic link to{' '}
                              <strong>{email}</strong>
                            </p>
                            <p className="text-gray-400 text-xs">
                              Didn't receive it? Check your spam folder or try
                              again.
                            </p>
                          </div>
                          <Button
                            onClick={resetEmailForm}
                            variant="outline"
                            className="border-gray-600 text-gray-300 hover:bg-gray-600 hover:text-white hover:border-gray-500"
                          >
                            Try Different Email
                          </Button>
                        </div>
                      )}
                    </div>
                  )}
                  */}
                </div>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default AuthForm
