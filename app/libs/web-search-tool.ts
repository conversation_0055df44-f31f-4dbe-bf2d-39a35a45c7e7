/**
 * Reusable web search tool for AI model integration
 * Uses Brave Search API with Vercel AI SDK v5 tool() pattern
 * Can be used across different chat implementations (chat-demo, AI pane chat, etc.)
 */

import { tool } from 'ai'
import { z } from 'zod'
import {
  braveWebSearch,
  formatSearchResultsForAI,
  createSearchMetadata,
  type SearchMetadata,
} from '@/app/api/dragtree/shared/brave-search'

// Global rate limiter for Brave Search API
// Limit concurrent requests to stay well under 50 QPS limit
let braveLimit: any = null

// Lazy initialization to avoid issues with module loading
async function getBraveLimit() {
  if (!braveLimit) {
    try {
      // Dynamic import for p-limit (ES modules)
      const { default: pLimit } = await import('p-limit')
      braveLimit = pLimit(20) // Max 20 concurrent Brave API calls
      console.log(
        '🚦 [Web Search Tool] Brave Search rate limiter initialized (max 20 concurrent)'
      )
    } catch (_error) {
      console.warn(
        '⚠️ [Web Search Tool] p-limit not available, using unbounded requests'
      )
      // Fallback: no limiting
      braveLimit = (fn: any) => fn()
    }
  }
  return braveLimit
}

// Progress tracking for search operations
export type SearchProgressCallback = (status: {
  type: 'searching' | 'completed' | 'error'
  query?: string
  resultCount?: number
  error?: string
}) => void

/**
 * Creates a web search tool using Brave Search API
 * @param metadataCollector - Optional array to collect search metadata for frontend display
 * @param progressCallback - Optional callback for search progress updates
 * @returns AI SDK v5 compatible tool for web search
 */
export function createWebSearchTool(
  metadataCollector?: SearchMetadata[],
  progressCallback?: SearchProgressCallback
) {
  return tool({
    description: `Search the web for current, relevant information. Use this tool when you need:
- Recent developments or current events
- Specific facts, statistics, or data
- Technical information or specifications
- Expert opinions or authoritative sources
- Market research or industry insights

Guidelines for effective searches:
- Use specific, focused queries
- Include relevant keywords and terms
- Search for authoritative sources
- Verify information across multiple results`,
    inputSchema: z.object({
      query: z
        .string()
        .describe(
          'The search query to find relevant information. Be specific and include key terms.'
        ),
      count: z
        .number()
        .min(1)
        .max(20)
        .default(10)
        .describe('Number of search results to retrieve (1-20, default: 10)'),
    }),
    execute: async ({ query, count = 10 }, { toolCallId }) => {
      const limit = await getBraveLimit()

      try {
        console.log(
          `🔧 [Web Search Tool] Executing search: "${query}" (Tool Call ID: ${toolCallId})`
        )

        // Notify frontend about search start
        progressCallback?.({
          type: 'searching',
          query,
        })

        // Use rate limiter to control concurrent Brave API calls
        const searchResults = await limit(() => braveWebSearch(query, count))

        // Collect metadata if collector is provided
        if (metadataCollector && searchResults.length > 0) {
          const searchMetadata = createSearchMetadata(query, searchResults)
          metadataCollector.push(...searchMetadata)
          console.log(
            `📊 [Web Search Tool] Collected ${searchMetadata.length} search result(s) for metadata`
          )
        }

        // Notify frontend about search completion
        progressCallback?.({
          type: 'completed',
          query,
          resultCount: searchResults.length,
        })

        // Return formatted results for AI consumption
        return formatSearchResultsForAI(searchResults)
      } catch (error) {
        console.error('💥 [Web Search Tool] Error:', error)

        // Notify frontend about search error
        progressCallback?.({
          type: 'error',
          query,
          error: error instanceof Error ? error.message : 'Unknown error',
        })

        return `Error performing web search: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    },
  })
}

/**
 * Simple web search tool factory for basic use cases
 * No metadata collection or progress callbacks
 */
export function createSimpleWebSearchTool() {
  return createWebSearchTool()
}

/**
 * Web search tool factory with metadata collection
 * Useful for implementations that need to display search sources
 */
export function createWebSearchToolWithMetadata(
  metadataCollector: SearchMetadata[]
) {
  return createWebSearchTool(metadataCollector)
}

/**
 * Web search tool factory with progress tracking
 * Useful for implementations that need real-time search status updates
 */
export function createWebSearchToolWithProgress(
  progressCallback: SearchProgressCallback
) {
  return createWebSearchTool(undefined, progressCallback)
}

/**
 * Full-featured web search tool factory
 * Includes both metadata collection and progress tracking
 */
export function createFullWebSearchTool(
  metadataCollector: SearchMetadata[],
  progressCallback: SearchProgressCallback
) {
  return createWebSearchTool(metadataCollector, progressCallback)
}
