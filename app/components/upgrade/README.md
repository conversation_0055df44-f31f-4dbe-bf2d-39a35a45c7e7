# Unified Upgrade Component System

This directory contains the unified upgrade/upselling component system that provides consistent upgrade experiences across the application.

## 🎯 Purpose

Replaces inconsistent upgrade implementations throughout the app with a cohesive, friendly upgrade experience that feels encouraging rather than restrictive.

## 📦 Components

### 1. `UpgradeButton`

**Primary upgrade button for main CTAs**

- Used in: Sidebar, ProfileDialog, main upgrade flows
- Features: Emerald-teal gradient, hover effects, loading states
- Sizes: `sm`, `md`, `lg`
- Variants: `primary`, `outline`, `ghost`

```tsx
<UpgradeButton context="sidebar" currentTier={tier} fullWidth size="md" />
```

### 2. `UpgradeHint`

**Inline upgrade hints for feature restrictions**

- Used in: Export restrictions, quota warnings, feature gates
- Features: Friendly emerald-teal styling (replaces red warnings)
- Compact design with optional inline button

```tsx
<UpgradeHint
  message="Export is a paid feature. Upgrade to unlock."
  context="export"
  currentTier={tier}
/>
```

### 3. `UpgradeBanner`

**Large promotional upgrade areas**

- Used in: Dashboard promotions, feature announcements
- Features: Full banner with title, description, dismissible option
- Gradient background with prominent CTA button

```tsx
<UpgradeBanner
  title="Unlock Premium Features"
  message="Get unlimited drag trees, exports, and more."
  context="banner"
  dismissible
/>
```

## 🎨 Design System

### Color Scheme

- **Primary**: Emerald-teal gradient (`from-emerald-500 to-teal-600`)
- **Hover**: Darker gradient (`from-emerald-600 to-teal-700`)
- **Background**: Light emerald tints (`emerald-50`, `emerald-100`)
- **Text**: Emerald variants (`emerald-700`, `emerald-800`)

### Interactive Effects

- **Hover scale**: `hover:scale-[1.02]`
- **Active scale**: `active:scale-[0.98]`
- **Shadow elevation**: `hover:shadow-lg`
- **Smooth transitions**: `transition-all duration-200`

## 📊 Analytics Integration

All components automatically track user interactions:

### Events Tracked

- `click_upgrade_button` - Main upgrade button clicks
- `click_upgrade_hint` - Feature restriction hint clicks
- `click_upgrade_banner` - Banner upgrade clicks
- `dismiss_upgrade_banner` - Banner dismissals

### Context Tracking

Each component requires a `context` prop for analytics:

- `sidebar` - Sidebar upgrade button
- `profile` - Profile dialog upgrade
- `export` - Export feature restrictions
- `quota` - Usage limit warnings
- `screening` - Screening flow restrictions
- `feature-gate` - General feature restrictions
- `banner` - Promotional banners

## 🔄 Migration Guide

### Before (Inconsistent)

```tsx
// Red warning style (negative)
<UpgradeNotice message="Feature restricted" />

// Purple-blue gradient
<Button className="bg-gradient-to-r from-purple-600 to-blue-600">
  Upgrade
</Button>

// Custom emerald implementation
<button className="bg-gradient-to-r from-emerald-500 to-teal-600">
  Upgrade to Pro
</button>
```

### After (Unified)

```tsx
// Friendly emerald-teal hint
<UpgradeHint message="Feature restricted" context="export" />

// Consistent emerald-teal button
<UpgradeButton context="profile" fullWidth />

// Same emerald-teal styling everywhere
<UpgradeButton context="sidebar" size="md" />
```

## ✅ Benefits

1. **Consistent Visual Design**: All upgrade touchpoints use emerald-teal theme
2. **Friendly UX**: Replaces negative red warnings with encouraging styling
3. **Analytics Tracking**: Comprehensive conversion funnel tracking
4. **Type Safety**: Full TypeScript support with proper event typing
5. **Maintainability**: Single source of truth for upgrade styling
6. **Flexibility**: Customizable while maintaining consistency

## 🧪 Testing

All components include:

- Unit tests for different props and states
- Analytics event validation
- Accessibility compliance
- Visual regression testing

## 📈 Conversion Optimization

The unified system enables:

- A/B testing different upgrade messages
- Conversion funnel analysis by context
- User journey optimization
- Consistent upgrade experience across all touchpoints
