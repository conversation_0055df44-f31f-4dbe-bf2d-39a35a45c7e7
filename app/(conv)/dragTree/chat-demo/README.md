# Enhanced AI SDK Elements Chat Demo

A standalone ChatGPT-style chat interface built using AI SDK Elements best practices, enhanced with initialization flow, conversation persistence, and real web search. This implementation serves as a realistic foundation that mirrors the AI pane chat experience and demonstrates key patterns used throughout the application.

## Features

- **Initialization Flow**: Start Chat button with preset prompt ("What is the Tesla stock price today?")
- **Conversation Persistence**: Client sends only latest message, server reconstructs full conversation
- **Real Web Search**: Brave Search API integration with reusable utility (`app/libs/web-search-tool.ts`)
- **ChatGPT-style Interface**: Clean, modern chat UI with user/assistant message bubbles
- **AI SDK Elements Integration**: Uses official AI SDK Elements components for optimal performance
- **Tool Calling Support**: Real weather, math, and web search tools with proper visualization
- **Streaming Support**: Real-time message streaming with step limiting (`stopWhen: stepCountIs(10)`)
- **Error Handling**: Comprehensive error handling and user feedback
- **Responsive Design**: Works across desktop and mobile devices

## Architecture

### Components Used

- **Conversation Components**:
  - `Conversation`: Main chat container with auto-scrolling
  - `ConversationContent`: Content wrapper for messages
  - `ConversationScrollButton`: Auto-scroll to bottom functionality

- **Message Components**:
  - `Message`: Individual message wrapper with role-based styling
  - `MessageContent`: Message content container
  - `Response`: Markdown rendering for message text

- **Input Components**:
  - `PromptInput`: Input container with form handling
  - `PromptInputTextarea`: Text input with proper sizing
  - `PromptInputSubmit`: Submit button with status indicators

### API Integration

- **Routes**:
  - `/api/chat-demo-persistent`: Main route with conversation persistence (use this)
  - `/api/chat-demo`: Deprecated (kept temporarily for compatibility)
- **Model**: Azure OpenAI (`gpt-4.1-mini`) via Vercel AI SDK
- **Tools**: Real Weather, Math Calculator, Brave Web Search
- **Streaming**: Full streaming support with tool execution and step limiting
- **Persistence**: In-memory chat store (`app/libs/chat-demo-store.ts`) for demo

### Key Implementation Details

1. **Message Structure**: Uses AI SDK's native message parts structure for proper tool call handling
2. **Tool Visualization**: Simple, clean tool call and result display with color-coded sections
3. **Error Handling**: Graceful error display with user-friendly messages
4. **Loading States**: Proper loading indicators during streaming
5. **Auto-scroll**: Automatic scroll to bottom with manual scroll button

## Usage

1. Navigate to `/dragTree/chat-demo`
2. Click the "Start Chat" button to begin with preset prompt
3. Continue the conversation to see persistence in action
4. Try tool-enabled queries:
   - "What's the weather in San Francisco?"
   - "Calculate 15 \* 23 + 7"
   - "Search for information about Next.js"
   - "What's the latest news about AI?"

## Development Notes

- **No Dependencies**: Completely independent from existing chat implementations
- **Clean Code**: Minimal, focused implementation without over-engineering
- **AI SDK Best Practices**: Follows official AI SDK Elements patterns
- **Type Safety**: Full TypeScript support with proper type annotations
- **Performance**: Optimized for smooth streaming and minimal re-renders

### Environment Variables

To enable Brave Search integration, set the following in your `.env.local`:

```
BRAVE_SEARCH_API_KEY=your_brave_search_api_key
```

Without this key, the web search tool will gracefully return no results and the model will answer without external sources.

## Future Enhancements

- Integration with proper AI SDK Elements `Tool` component (currently using simplified display)
- `Reasoning` component integration for GPT-5 reasoning display
- Enhanced tool call visualization with collapsible sections
- Message persistence and conversation history
- User authentication and personalization

## Files

- `page.tsx`: Main chat interface component with initialization flow
- `../../../api/chat-demo-persistent/route.ts`: Enhanced API route with persistence
- `../../../api/chat-demo/route.ts`: Original API route (fallback)
- `../../../libs/chat-demo-store.ts`: Simple conversation storage
- `../../../libs/web-search-tool.ts`: Reusable web search utility
- `README.md`: This documentation

## Key Patterns for AI Pane Chat Refactoring

This implementation demonstrates patterns that can be applied to simplify the AI pane chat:

1. **Initialization with Context**: How to start conversations with preset prompts
2. **Efficient Message Handling**: Send only latest message, rebuild on server
3. **Real Tool Integration**: Actual web search instead of mock data
4. **Reliable Streaming**: Proven patterns for stable AI interactions
5. **Error Recovery**: Graceful handling of edge cases and failures

This enhanced implementation serves as a working reference for building production-quality chat interfaces using AI SDK Elements while maintaining clean, maintainable code and demonstrating the key patterns used by AI pane chat.
