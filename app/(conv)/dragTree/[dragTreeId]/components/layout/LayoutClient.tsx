'use client'

import React, { useState } from 'react'
import { useSession } from 'next-auth/react'
import Sidebar from '@/app/(conv)/dragTree/[dragTreeId]/components/layout/Sidebar'
import Header from '@/app/(conv)/dragTree/[dragTreeId]/components/layout/Header'
import LoadingPage from '@/app/components/LoadingPage'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { cn } from '@/lib/utils'

type DragTreeLayoutClientProps = {
  children: React.ReactNode
  className?: string
}

const DragTreeLayoutClient: React.FC<DragTreeLayoutClientProps> = ({
  children,
  className,
}) => {
  const { data: session, status } = useSession()
  const [isSidebarOpen, setIsSidebarOpen] = useState<boolean>(false)

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen)
  }

  const closeSidebar = () => {
    setIsSidebarOpen(false)
  }

  // Show loading while session is being fetched
  if (status === 'loading') {
    return <LoadingPage />
  }

  // Only logged-in users can access this page
  if (status === 'unauthenticated' || !session?.user) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">
            Access Denied
          </h1>
          <p className="text-gray-600">
            Please log in to access this test page.
          </p>
        </div>
      </div>
    )
  }

  return (
    <TooltipProvider>
      <div className={cn('h-screen flex flex-col bg-gray-50', className)}>
        {/* Header */}
        <Header session={session} />

        {/* Main content area with sidebar */}
        <div className="flex-1 flex overflow-hidden">
          {/* Sidebar */}
          <Sidebar
            isOpen={isSidebarOpen}
            onClose={closeSidebar}
            session={session}
          />

          {/* Content */}
          <main className="flex-1 overflow-hidden">{children}</main>
        </div>

        {/* Subtle 1px gray bar with expand slider when sidebar is closed */}
        {!isSidebarOpen && (
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="fixed inset-y-0 left-0 z-[55]">
                <div
                  onClick={toggleSidebar}
                  className="w-2 h-full bg-gray-600 hover:bg-gray-700 cursor-pointer group transition-colors"
                >
                  <div className="absolute left-2 top-1/2 transform -translate-y-1/2 opacity-70 group-hover:opacity-100 transition-opacity">
                    <div className="w-6 h-12 bg-gray-600 rounded-r-xl flex items-center justify-center shadow-lg">
                      <div className="w-1 h-6 bg-white rounded"></div>
                    </div>
                  </div>
                </div>
              </div>
            </TooltipTrigger>
            <TooltipContent side="right">
              <p>Click to open sidebar</p>
            </TooltipContent>
          </Tooltip>
        )}

        {/* Overlay for mobile */}
        {isSidebarOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
            onClick={closeSidebar}
          />
        )}
      </div>
    </TooltipProvider>
  )
}

export default React.memo(DragTreeLayoutClient)
