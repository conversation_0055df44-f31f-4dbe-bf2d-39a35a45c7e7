import React, { useState, useCallback } from 'react'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { cn } from '@/lib/utils'

type DescriptionInputProps = {
  description: string
  setDescription: (value: string) => void
  isLoading: boolean
  showExamples?: boolean
  wasPrefilled?: boolean
  setWasPrefilled?: (value: boolean) => void
  hasUserMadeSelection?: boolean
}

const DescriptionInput: React.FC<DescriptionInputProps> = ({
  description,
  setDescription,
  isLoading,
  showExamples = true,
  // wasPrefilled: unused
  // setWasPrefilled: unused
  // hasUserMadeSelection: unused
}) => {
  const [isFocused, setIsFocused] = useState<boolean>(false)
  const charCount = description.length
  const minChars = 10
  const maxChars = 2000

  const handleDescriptionChange = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const value = e.target.value
      setDescription(value)
    },
    [setDescription]
  )

  const handleFocus = useCallback(() => {
    setIsFocused(true)
  }, [])

  const handleBlur = useCallback(() => {
    setIsFocused(false)
  }, [])

  return (
    <div className="space-y-3">
      <div className="space-y-1">
        <Label
          htmlFor="description"
          className="text-xl font-semibold text-slate-700"
        >
          Describe Your Problem or Idea
        </Label>
        <p className="text-sm text-slate-500">
          Be as specific as possible. Include context, goals, and any
          constraints you&apos;re aware of.
        </p>
      </div>

      <div className="relative">
        <Textarea
          id="description"
          value={description}
          onChange={handleDescriptionChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          disabled={isLoading}
          placeholder="For example: Design a go-to-market strategy for new eco-friendly apparel product in Asia..."
          className={cn(
            'min-h-[80px] resize-none transition-all duration-300 border-slate-300',
            {
              'ring-2 ring-blue-500/20 border-blue-400': isFocused,
              'opacity-50 cursor-not-allowed': isLoading,
            }
          )}
          maxLength={maxChars}
        />

        {/* Character Count */}
        <div className="absolute bottom-2 right-2 flex items-center space-x-2 text-xs">
          {charCount < minChars ? (
            <span className="text-orange-500 font-medium">
              {minChars - charCount} more needed
            </span>
          ) : (
            <span className="text-green-500 font-medium flex items-center">
              <svg
                className="w-3 h-3 mr-1"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                />
              </svg>
              Ready
            </span>
          )}
          <span className="text-slate-400">
            {charCount}/{maxChars}
          </span>
        </div>
      </div>

      {/* Examples - only show when showExamples is true */}
      {showExamples && (
        <div className="bg-slate-50 border border-slate-200 rounded-lg p-3">
          <h4 className="text-xs font-medium text-slate-700 mb-1">Examples:</h4>
          <div className="space-y-1 text-xs text-slate-600">
            <div className="flex items-start space-x-1">
              <span className="text-blue-500 font-medium mt-0.5">•</span>
              <span>
                As a yoga teacher in SF, I want to open a studio to teach yoga
                classes and build a community that promotes wellness and
                mindfulness.
              </span>
            </div>
            <div className="flex items-start space-x-1">
              <span className="text-blue-500 font-medium mt-0.5">•</span>
              <span>
                Research efficient ways to reduce game load time on low-end
                phones for mobile gaming while maintaining high-quality graphics
                and smooth gameplay experience.
              </span>
            </div>
            <div className="flex items-start space-x-1">
              <span className="text-blue-500 font-medium mt-0.5">•</span>
              <span>
                Develop a comprehensive customer retention strategy for a SaaS
                product with high churn rate in the SMB segment, focusing on
                onboarding improvements and feature adoption.
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default DescriptionInput
