// Types for AI Pane Generate API

/**
 * Request type for AI Pane Generate API
 * - prompt: The final prompt string, including user instructions and all selected context content (already combined by the frontend)
 * - model: The model to use (e.g., 'gpt-4.1')
 * - settings: Any additional settings for generation (optional, can be empty object)
 * - dragTreeId: The ID of the drag tree this generation belongs to (required for persistence)
 * - entityType: The entity type for categorization (optional, defaults to 'drag_tree_v1')
 * - promptTemplate: The name of the template selected by the user (optional, for smart title generation)
 */
export type AIPaneGenerateRequest = {
  prompt: string
  model: string
  settings: Record<string, any>
  dragTreeId: string
  entityType?: string
  promptTemplate?: string
}
