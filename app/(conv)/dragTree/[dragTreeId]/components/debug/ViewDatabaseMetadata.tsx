'use client'

import React, { useState } from 'react'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { Button } from '@/components/ui/button'
import { getDragTree } from '@/app/server-actions/drag-tree'

const ViewDatabaseMetadata: React.FC = () => {
  const { dragTreeId } = useDragTreeStore()
  const [databaseData, setDatabaseData] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [selectedNodeId, setSelectedNodeId] = useState<string>('')

  const loadDatabaseData = async () => {
    if (!dragTreeId) return

    setLoading(true)
    try {
      const result = await getDragTree(dragTreeId)
      if (result.success && result.data) {
        setDatabaseData(result.data)
        console.log(
          '📋 [ViewDatabaseMetadata] Loaded database data:',
          result.data
        )
      } else {
        console.error('❌ [ViewDatabaseMetadata] Failed to load:', result.error)
      }
    } catch (error) {
      console.error('❌ [ViewDatabaseMetadata] Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const getNodeMetadata = (nodeId: string) => {
    if (!databaseData?.nodes) return null

    const node = databaseData.nodes.find((n: any) => n.id === nodeId)
    return node?.metadata || null
  }

  const getFormattedMetadata = (nodeId: string) => {
    const metadata = getNodeMetadata(nodeId)
    if (!metadata) return 'No metadata found'

    return JSON.stringify(metadata, null, 2)
  }

  return (
    <div className="p-4 border rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold mb-4">🗄️ View Database Metadata</h3>

      <div className="space-y-4">
        <div className="flex gap-4">
          <Button
            onClick={loadDatabaseData}
            disabled={!dragTreeId || loading}
            className="flex-1"
          >
            {loading ? 'Loading...' : 'Load Database Data'}
          </Button>
        </div>

        {databaseData && (
          <>
            <div className="bg-blue-50 border border-blue-200 p-3 rounded-md">
              <h4 className="font-medium text-blue-800 mb-2">
                📊 Database Overview:
              </h4>
              <div className="text-sm text-blue-700 space-y-1">
                <div>
                  <strong>Tree ID:</strong> {databaseData.id}
                </div>
                <div>
                  <strong>Tree Title:</strong> {databaseData.title}
                </div>
                <div>
                  <strong>Total Nodes:</strong>{' '}
                  {databaseData.nodes?.length || 0}
                </div>
                <div>
                  <strong>Status:</strong> {databaseData.status}
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Select Node to View Metadata:
              </label>
              <select
                value={selectedNodeId}
                onChange={e => setSelectedNodeId(e.target.value)}
                className="w-full p-2 border rounded-md"
              >
                <option value="">-- Select a node --</option>
                {databaseData.nodes?.map((node: any) => (
                  <option key={node.id} value={node.id}>
                    {node.node_type}: &quot;{node.label}&quot; (ID: {node.id})
                  </option>
                ))}
              </select>
            </div>

            {selectedNodeId && (
              <div className="bg-green-50 border border-green-200 p-3 rounded-md">
                <h4 className="font-medium text-green-800 mb-2">
                  📋 Metadata for:{' '}
                  {
                    databaseData.nodes?.find(
                      (n: any) => n.id === selectedNodeId
                    )?.label
                  }
                </h4>
                <pre className="text-sm text-green-700 bg-green-100 p-2 rounded overflow-auto max-h-64">
                  {getFormattedMetadata(selectedNodeId)}
                </pre>
              </div>
            )}

            <div className="bg-yellow-50 border border-yellow-200 p-3 rounded-md">
              <h4 className="font-medium text-yellow-800 mb-2">
                🔍 All Nodes Summary:
              </h4>
              <div className="text-sm text-yellow-700 space-y-1 max-h-64 overflow-auto">
                {databaseData.nodes?.map((node: any) => {
                  const metadata = node.metadata || {}
                  return (
                    <div key={node.id} className="p-2 bg-yellow-100 rounded">
                      <div>
                        <strong>{node.node_type}:</strong> &quot;{node.label}
                        &quot;
                      </div>
                      <div className="text-xs">
                        <strong>ID:</strong> {node.id} |<strong> Level:</strong>{' '}
                        {metadata.level || 'N/A'} |<strong> Parent:</strong>{' '}
                        {metadata.parentId || 'N/A'} |<strong> Status:</strong>{' '}
                        {node.status}
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          </>
        )}

        {!databaseData && !loading && (
          <div className="text-center text-gray-500 py-8">
            <p>Click &quot;Load Database Data&quot; to view metadata</p>
            {!dragTreeId && (
              <p className="text-red-500">No drag tree ID available</p>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

// Memoize to prevent unnecessary re-renders when dragTreeId hasn't changed
export default React.memo(ViewDatabaseMetadata)
