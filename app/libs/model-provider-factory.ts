import { createAzure } from '@ai-sdk/azure'
import { createOpenAI } from '@ai-sdk/openai'
import type { AzureProviderAPI, ModelProvider } from '@/app/configs/llm-models'

/**
 * Multi-Provider Model Factory
 *
 * Creates model instances for different LLM providers (Azure, OpenAI).
 * Lightweight - no caching, no complexity.
 *
 * Environment Variables Required:
 * - Azure Standard: AZURE_RESOURCE_NAME, AZURE_API_KEY
 * - Azure East US 2: AZURE_EASTUS2_RESOURCE_NAME, AZURE_EASTUS2_API_KEY
 * - OpenAI: OPENAI_API_KEY
 */

/**
 * Provider instances - lightweight, no caching needed
 * OpenAI provider is created lazily to allow for better error handling
 */
let _openAIProvider: ReturnType<typeof createOpenAI> | null = null
function getOpenAIProvider() {
  const apiKey = process.env.OPENAI_API_KEY

  if (!apiKey) {
    throw new Error(
      `🚨 Missing OpenAI environment variable. Required: OPENAI_API_KEY`
    )
  }

  if (_openAIProvider) return _openAIProvider
  _openAIProvider = createOpenAI({ apiKey })
  return _openAIProvider
}

/**
 * Creates Azure provider instance
 * @param providerApi - The Azure provider API to use
 * @returns Azure provider instance
 * @throws Error if required environment variables are missing
 */
const _azureProviders = new Map<
  AzureProviderAPI,
  ReturnType<typeof createAzure>
>()
export function getAzureProvider(
  providerApi: AzureProviderAPI = 'azure_standard'
) {
  const cached = _azureProviders.get(providerApi)
  if (cached) return cached
  switch (providerApi) {
    case 'azure_standard':
      const standardResourceName = process.env.AZURE_RESOURCE_NAME
      const standardApiKey = process.env.AZURE_API_KEY

      if (!standardResourceName || !standardApiKey) {
        throw new Error(
          `🚨 Missing Azure Standard environment variables. Required: AZURE_RESOURCE_NAME, AZURE_API_KEY`
        )
      }

      const std = createAzure({
        resourceName: standardResourceName,
        apiKey: standardApiKey,
      })
      _azureProviders.set('azure_standard', std)
      return std

    case 'azure_eastus2':
      const eastus2ResourceName = process.env.AZURE_EASTUS2_RESOURCE_NAME
      const eastus2ApiKey = process.env.AZURE_EASTUS2_API_KEY

      if (!eastus2ResourceName || !eastus2ApiKey) {
        throw new Error(
          `🚨 Missing Azure East US 2 environment variables. Required: AZURE_EASTUS2_RESOURCE_NAME, AZURE_EASTUS2_API_KEY`
        )
      }

      const east = createAzure({
        resourceName: eastus2ResourceName,
        apiKey: eastus2ApiKey,
      })
      _azureProviders.set('azure_eastus2', east)
      return east

    default:
      console.warn(
        `⚠️ Unknown Azure provider API: ${providerApi}. Falling back to standard.`
      )

      const fallbackResourceName = process.env.AZURE_RESOURCE_NAME
      const fallbackApiKey = process.env.AZURE_API_KEY

      if (!fallbackResourceName || !fallbackApiKey) {
        throw new Error(
          `🚨 Missing Azure Standard environment variables for fallback. Required: AZURE_RESOURCE_NAME, AZURE_API_KEY`
        )
      }

      const fb = createAzure({
        resourceName: fallbackResourceName,
        apiKey: fallbackApiKey,
      })
      _azureProviders.set('azure_standard', fb)
      return fb
  }
}

/**
 * Gets the appropriate model instance based on provider and configuration
 * This is the main function you should use - it handles all providers
 * @param modelName - The model name to use
 * @param provider - The model provider ('azure', 'openai')
 * @param providerApi - Additional provider configuration (only used for Azure)
 * @returns Model instance for the specified provider
 */
export function getModelFromProvider(
  modelName: string,
  provider: ModelProvider,
  providerApi?: AzureProviderAPI
) {
  switch (provider) {
    case 'azure':
      const azureProvider = getAzureProvider(providerApi || 'azure_standard')
      return azureProvider(modelName)

    case 'openai':
      const openaiProvider = getOpenAIProvider()
      return openaiProvider(modelName)

    default:
      throw new Error(
        `🚨 Unsupported model provider: ${provider}. Currently supported: azure, openai`
      )
  }
}

/**
 * Gets the appropriate Azure model instance based on model configuration
 * @param modelName - The model name to use
 * @param providerApi - The Azure provider API to use
 * @returns Azure model instance
 */
export function getAzureModel(
  modelName: string,
  providerApi: AzureProviderAPI = 'azure_standard'
) {
  const provider = getAzureProvider(providerApi)
  return provider(modelName)
}
