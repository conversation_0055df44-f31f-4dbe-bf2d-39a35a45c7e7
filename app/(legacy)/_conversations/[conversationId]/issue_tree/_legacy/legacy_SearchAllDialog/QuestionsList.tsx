// import React, { useState } from "react";
// import { Checkbox } from "@/components/ui/checkbox";
// import { Button } from "@/components/ui/button";
// import { toast } from "react-hot-toast";
// import { CheckedState } from "@radix-ui/react-checkbox";
// import { ExtendedNode } from "./SearchAllDialogComponent";

// type QuestionsListType = {
//   questions: ExtendedNode[];
// };

// const QuestionsList: React.FC<QuestionsListType> = ({ questions }) => {
//   // Initialize state with all questions set to false
//   const [selectedQuestions, setSelectedQuestions] = useState<{
//     [key: string]: boolean;
//   }>(
//     questions.reduce((acc, question) => {
//       acc[question.id] = false;
//       return acc;
//     }, {} as { [key: string]: boolean })
//   );

//   const handleCheckboxChange = (
//     questionId: string,
//     isChecked: CheckedState
//   ) => {
//     if (typeof isChecked === "boolean") {
//       toast.success(`Toggled: ${questionId}`);
//       setSelectedQuestions((prev) => ({
//         ...prev,
//         [questionId]: isChecked,
//       }));
//     }
//   };

//   const handleButtonClick = () => {
//     toast.success("Submitting selected questions...");
//     const selected = questions.filter(
//       (question) => selectedQuestions[question.id]
//     );
//     console.log(selected);
//     selected.forEach((question) => {
//       toast.success(`Selected: ${question.id} - ${question.data?.label}`);
//     });
//   };

//   return (
//     <div className="flex-1">
//       {questions.map((question) => (
//         <div key={question.id} className="flex items-center mb-2">
//           <Checkbox
//             id={`checkbox-${question.id}`}
//             checked={selectedQuestions[question.id]}
//             onCheckedChange={(isChecked) =>
//               handleCheckboxChange(question.id, isChecked)
//             }
//           />
//           <p>{question.categories.join("/")}</p>
//           <label htmlFor={`checkbox-${question.id}`} className="ml-2">
//             {question.data?.label}
//           </label>
//         </div>
//       ))}
//       <Button
//         className="mt-4 p-2 bg-blue-500 text-white rounded hover:bg-blue-700"
//         onClick={handleButtonClick}
//       >
//         Submit Selected Questions
//       </Button>
//     </div>
//   );
// };

// export default QuestionsList;
