/**
 * Asset Types Module
 * Central module for asset type definitions and registry management
 */

// Export registry and core types
export * from './registry'

// Export individual asset type definitions
export * from './generate'
export * from './chat'

// Import for registration
import { registerAssetType } from './registry'
import { generateAssetType } from './generate'
import { chatAssetType } from './chat'

/**
 * Initialize built-in asset types
 * This function registers all built-in asset types with the registry
 */
export const initializeBuiltInAssetTypes = (): void => {
  // Register built-in asset types
  registerAssetType(generateAssetType)
  registerAssetType(chatAssetType)

  // Only log in non-test environments to reduce test output verbosity
  if (process.env.NODE_ENV !== 'test') {
    console.log('🎯 Built-in asset types initialized')
  }
}

/**
 * Auto-initialize built-in types when module is imported
 * This ensures the registry is populated as soon as the module loads
 */
initializeBuiltInAssetTypes()

// Re-export commonly used functions for convenience
export {
  registerAssetType,
  getAssetType,
  isValidAssetType,
  isAssetTab,
} from './registry'

export {
  isGenerateTab,
  createGenerateTabId,
  getGenerateTabData,
} from './generate'

export {
  isChatTab,
  createChatTabId,
  getChatTabData,
  generateChatAssetTitle,
} from './chat'
