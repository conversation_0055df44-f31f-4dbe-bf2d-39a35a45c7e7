import { B<PERSON>bleMenu, BubbleMenuProps, Editor } from '@tiptap/react'
import { FC, useState } from 'react'
import {
  Plus,
  Minus,
  Trash2,
  ArrowUp,
  ArrowDown,
  ArrowLeft,
  ArrowRight,
} from 'lucide-react'
import { cn } from '@/lib/utils'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { fillEmptyTableCells } from '../utils/table-helpers'

export type TableBubbleMenuProps = Omit<BubbleMenuProps, 'children'> & {
  editor: Editor
}

export const TableBubbleMenu: FC<TableBubbleMenuProps> = ({
  editor,
  ...props
}) => {
  // Confirmation dialog state for destructive delete actions
  const [confirmOpen, setConfirmOpen] = useState(false)
  const [pendingDelete, setPendingDelete] = useState<{
    label: string
    run: () => void
  } | null>(null)

  const requestDelete = (label: string, run: () => void) => {
    setPendingDelete({ label, run })
    setConfirmOpen(true)
  }

  const confirmDelete = () => {
    pendingDelete?.run()
    setConfirmOpen(false)
    setPendingDelete(null)
  }
  const bubbleMenuProps = {
    ...props,
    editor,
    shouldShow: ({ editor }: { editor: Editor }) => {
      return editor.isActive('table')
    },
    tippyOptions: {
      moveTransition: 'transform 0.15s ease-out',
      appendTo: () => document.body,
      placement: 'top' as const,
      popperOptions: {
        strategy: 'fixed' as const,
        modifiers: [
          {
            name: 'preventOverflow',
            options: {
              boundary: 'viewport',
            },
          },
        ],
      },
    },
  }

  type TableAction = {
    name: string
    icon:
      | typeof Plus
      | typeof Minus
      | typeof Trash2
      | typeof ArrowUp
      | typeof ArrowDown
      | typeof ArrowLeft
      | typeof ArrowRight
    command: () => void
    tooltip: string
    destructive?: boolean
    iconClassName?: string // optional extra classes for icon styling (e.g., rotation)
  }

  const tableActions: TableAction[] = [
    {
      name: 'Add column left',
      icon: ArrowLeft,
      command: () => {
        editor.chain().focus().addColumnBefore().run()
        fillEmptyTableCells(editor)
      },
      tooltip: '← Add column to the left',
    },
    {
      name: 'Add column right',
      icon: ArrowRight,
      command: () => {
        editor.chain().focus().addColumnAfter().run()
        fillEmptyTableCells(editor)
      },
      tooltip: 'Add column to the right →',
    },
    {
      name: 'Add row above',
      icon: ArrowUp,
      command: () => {
        editor.chain().focus().addRowBefore().run()
        fillEmptyTableCells(editor)
      },
      tooltip: '↑ Add row above',
    },
    {
      name: 'Add row below',
      icon: ArrowDown,
      command: () => {
        editor.chain().focus().addRowAfter().run()
        fillEmptyTableCells(editor)
      },
      tooltip: 'Add row below ↓',
    },
    {
      name: 'Delete column',
      icon: Minus,
      iconClassName: 'rotate-90', // visual cue: vertical line
      command: () =>
        requestDelete('column', () =>
          editor.chain().focus().deleteColumn().run()
        ),
      tooltip: 'Delete current column',
      destructive: true,
    },
    {
      name: 'Delete row',
      icon: Minus,
      command: () =>
        requestDelete('row', () => editor.chain().focus().deleteRow().run()),
      tooltip: 'Delete current row',
      destructive: true,
    },
    {
      name: 'Delete table',
      icon: Trash2,
      command: () =>
        requestDelete('table', () =>
          editor.chain().focus().deleteTable().run()
        ),
      tooltip: 'Delete entire table',
      destructive: true,
    },
  ]

  // Group actions for better organization
  const addActions = tableActions.slice(0, 4) // Add column/row actions
  const deleteActions = tableActions.slice(4) // Delete actions

  return (
    <BubbleMenu
      {...bubbleMenuProps}
      className="flex divide-x divide-stone-200 rounded border border-stone-200 bg-white/90 backdrop-blur-md shadow-xl"
    >
      {/* Add actions group */}
      <div className="flex flex-col items-center px-1">
        <div className="flex">
          {addActions.map((action, index) => (
            <button
              key={index}
              onClick={action.command}
              title={action.tooltip}
              className="p-2 text-blue-600 hover:bg-blue-50 active:bg-blue-100 transition-colors"
            >
              <action.icon className="h-4 w-4" />
            </button>
          ))}
        </div>
        <span className="mt-0.5 text-[10px] font-semibold text-stone-500">
          Add
        </span>
      </div>

      {/* Delete actions group */}
      <div className="flex flex-col items-center px-1">
        <div className="flex">
          {deleteActions.map((action, index) => (
            <button
              key={index + addActions.length}
              onClick={action.command}
              title={action.tooltip}
              className="p-2 text-red-600 hover:bg-red-50 active:bg-red-100 transition-colors"
            >
              <action.icon
                className={cn(
                  'h-4 w-4',
                  action.iconClassName,
                  action.destructive ? 'text-red-600' : ''
                )}
              />
            </button>
          ))}
        </div>
        <span className="mt-0.5 text-[10px] font-semibold text-red-600">
          Delete
        </span>
      </div>
      {/* Confirm destructive action */}
      <AlertDialog open={confirmOpen} onOpenChange={setConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete {pendingDelete?.label}?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. Are you sure you want to delete this{' '}
              {pendingDelete?.label}?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setPendingDelete(null)}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete}>
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </BubbleMenu>
  )
}
