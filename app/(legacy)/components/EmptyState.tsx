// Replace with Starter component
// "use client";

// import { Textarea } from "@/components/ui/textarea";

// import { Button } from "@/components/ui/button";

// import { useEffect, useState, useMemo } from "react";
// import { toast } from "react-hot-toast";
// import axios from "axios";
// import { useConversationStore } from "@/app/stores/conversation";
// import { ConversationStatus } from "@prisma/client";
// import { useChat } from "ai/react";
// import { useRouter } from "next/navigation";
// import { maxActiveConversations, minChars } from "@/app/configs";
// import mixpanel from "../libs/mixpanel";

// interface EmptyStateProps {
//   currentUser: any;
// }

// interface UseChatMessagesProps {
//   api: string;
//   body: any;
// }

// interface UseChatMessagesReturn {
//   lastMessage: string | undefined;
//   append: (message: { content: string; role: string }) => void;
//   isLoading: boolean;
// }

// const EmptyState: React.FC<EmptyStateProps> = ({ currentUser }) => {
//   console.log("Empty state currentUser", currentUser);
//   if (currentUser) {
//     console.log("Empty state currentUser.id", currentUser.id);
//     mixpanel.identify(currentUser.id ? currentUser.id : "anonymous");
//   }

//   // Solve hydration issue by rendering only after DOM is loaded
//   // ref: https://stackoverflow.com/a/72318597
//   const [domLoaded, setDomLoaded] = useState(false);
//   const [description, setDescription] = useState("");
//   const [canStartClarify, setCanStartClarify] = useState(false);
//   const [userSubmittedDescription, setUserSubmittedDescription] = useState<
//     string | null
//   >(null);
//   const [lastRephrasedDescription, setLastRephrasedDescription] = useState<
//     string | null
//   >(null);
//   const [userIntention, setUserIntention] = useState<string>("");

//   const conversationStore = useConversationStore();
//   const router = useRouter();

//   useEffect(() => {
//     setDomLoaded(true);
//   }, []);

//   const { lastMessage, append, isLoading } = useChatMessages({
//     api: "/api/conversations/rephrase",
//     body: { currentUser, description },
//   });

//   useEffect(() => {
//     if (lastMessage !== undefined) {
//       setUserIntention(lastMessage);
//     }
//   }, [lastMessage]);

//   const currentActiveConversations = conversationStore.conversationList.filter(
//     (item) => item.conversation_status === ConversationStatus.ACTIVE
//   ).length;

//   const handleSuggestionClick = (index: number, suggestion: string) => {
//     setDescription(suggestion);
//     setCanStartClarify(true);
//     setLastRephrasedDescription(suggestion);
//   };

//   const handleStartRephrase = () => {
//     if (description.length >= minChars) {
//       setUserSubmittedDescription(description);
//       setLastRephrasedDescription(description);
//       mixpanel.track("start_rephrase_click");
//       toast.success("start rephrasing questions");
//       // Kickstart the rephrase process by appending dummy message
//       append({ role: "system", content: "start rephrasing questions" });

//       console.log("userIntention", userIntention);
//     }
//   };

//   const handleStartClarify = async () => {
//     // Simple Frontend stop
//     if (currentActiveConversations >= maxActiveConversations) {
//       toast.error(
//         "You have reached the max ACTIVE conversations, please finsh some first"
//       );
//     } else if (description.trim() === "") {
//       toast.error("Please enter a description to proceed.");
//     } else if (description.length < minChars) {
//       toast.error(`less than ${minChars} chars`);
//     } else {
//       toast.success("Started, few seconds please!", { duration: 10000 });
//       // toast.success(description);
//       mixpanel.track("start_clarify_click");
//       axios
//         .post("/api/conversations", { description })
//         .then((response) => {
//           // Create a copy of the response data
//           let conversationData = { ...response.data };

//           // Convert created_at and updated_at to Date objects from ISO 8601 timestamp strings
//           conversationData.created_at = new Date(response.data.created_at);
//           conversationData.updated_at = new Date(response.data.updated_at);

//           // Add the timestamp modified conversation data to the store
//           conversationStore.addConversation(conversationData);

//           if (!response.data.error) {
//             // Redirect to the conversation page
//             router.push(`/conversations/${response.data.id}`);
//             // toast.success(`Can route to: /conversations/${response.data.id}`);
//           } else {
//             toast.error(response.data.error);
//           }
//         })
//         .catch((error) => {
//           console.log("Error in API call:", error);
//           toast.error("Something went wrong. Please try again.");
//         });
//     }
//   };

//   let suggestions = extractNumberedSuggestions(userIntention);
//   if (userSubmittedDescription) {
//     suggestions = [userSubmittedDescription, ...suggestions];
//   }

//   const tmp = [
//     "test0",
//     ...extractNumberedSuggestions(`
// 1. [UX Design] As a UX Designer, create an intuitive and visually appealing mobile application tailored for the sale of luxury women's clothing, ensuring a seamless shopping experience for users.
// 2. [Market Expansion] Develop a user-centric mobile application catering to the affluent market segment, offering an extensive collection of high-end women's fashion products, thereby enhancing the accessibility of luxury clothing to a wider audience.
// 3. [Platform Differentiation] Designing an app for selling luxury women's clothing presents the challenge of balancing opulent aesthetics with functional usability, requiring careful consideration to avoid overwhelming users with excessive extravagance at the expense of intuitive navigation and purchasing processes.
// 4. [User Story] As a fashion-conscious woman with a penchant for luxury, I want to easily browse and purchase high-end clothing via a user-friendly mobile app, so that I can indulge in premium fashion experiences conveniently from the comfort of my own home, enhancing my sense of style and confidence.
// `),
//   ];
//   return (
//     <>
//       {domLoaded && (
//         <div className="container lg:pl-16 mx-auto p-4 flex flex-col items-center">
//           <h1 className="text-2xl font-semi-bold">
//             {currentActiveConversations >= maxActiveConversations ? (
//               <>
//                 <span className="text-red-500">
//                   You currently have {currentActiveConversations} active
//                   sessions,which is our current maximum limit of{" "}
//                   {maxActiveConversations}
//                 </span>
//                 <br />
//                 <span className="text-red-500">
//                   Please complete some sessions before starting another.
//                 </span>
//               </>
//             ) : (
//               "The first step is to clearly identify what we want to clarify"
//             )}
//           </h1>
//           <div className="w-full flex flex-col items-center mt-2">
//             <Textarea
//               id="name"
//               className="p-2 border rounded text-lg max-w-4xl w-full"
//               // Develop LTV model for... | Launch email campaign for... | Design new service for...
//               placeholder={
//                 "It could be short but ensure your request is clear. Providing more context is always welcomed.\n\n- eg: As a yoga teacher in SF (who/where), I want to open a studio (what) to teach yoga classes (why).\n- eg: Determining the efficient ways to reduce mobile game load time on low end devices\n- eg: Design goto market strategy for new eco-friendly product in Asia for a apparel brand"
//               }
//               disabled={currentActiveConversations >= maxActiveConversations}
//               value={description}
//               onChange={(e) => {
//                 setDescription(e.target.value);
//               }}
//               // Initial request should not be huge, 2000 should be enough
//               maxLength={2000}
//               minLength={10}
//               rows={5}
//             />
//             <div className="text-right mt-2 w-full max-w-4xl px-4">
//               {description.length < minChars
//                 ? `${description.length} / ${minChars}`
//                 : "./."}
//             </div>
//             <div className="flex justify-center w-full max-w-2xl">
//               <div className="flex flex-row max-w-lg justify-between mb-4">
//                 <Button
//                   className={`p-4 text-white rounded mx-4 ${
//                     description.length < minChars ||
//                     description === lastRephrasedDescription // Disable if description is the same as last rephrased
//                       ? "bg-gray-400 hover:bg-gray-500"
//                       : "bg-green-400 hover:bg-green-500"
//                   }`}
//                   onClick={handleStartRephrase}
//                   disabled={
//                     description.length < minChars ||
//                     description === lastRephrasedDescription ||
//                     isLoading
//                   }
//                 >
//                   Rephrase
//                 </Button>
//               </div>
//               <div className="flex space-x-4">
//                 <Button
//                   className={`p-4 text-white rounded mx-4 ${
//                     canStartClarify
//                       ? "bg-blue-400 hover:bg-blue-500"
//                       : "bg-gray-400 hover:bg-gray-500"
//                   }`}
//                   onClick={handleStartClarify}
//                   disabled={!canStartClarify || isLoading}
//                 >
//                   Start Clarify
//                 </Button>
//               </div>
//             </div>
//           </div>
//           <SuggestedRefinedQuestions
//             suggestions={tmp}
//             onSuggestionClick={handleSuggestionClick}
//             isLoading={isLoading}
//           />

//           {/* {userIntention && (
//             <SuggestedRefinedQuestions
//               suggestions={suggestions}
//               onSuggestionClick={handleSuggestionClick}
//               isLoading={isLoading}
//             />
//           )} */}
//         </div>
//       )}
//     </>
//   );
// };

// const extractNumberedSuggestions = (text: string) => {
//   const lines = text.split("\n");
//   const numberedLines = lines.filter((line) => /^\d+\./.test(line.trim()));
//   const suggestions = numberedLines.map((line) =>
//     line.replace(/^\d+\.\s*/, "").trim()
//   );
//   return suggestions;
// };

// interface SuggestedRefinedQuestionsProps {
//   suggestions: string[];
//   onSuggestionClick: (index: number, suggestion: string) => void;
//   isLoading?: boolean;
// }

// // Function to bold the text inside the first pair of square brackets
// const formatSuggestion = (suggestion: string) => {
//   const match = suggestion.match(/\[(.*?)\]/);
//   if (match) {
//     const boldText = match[1];
//     return (
//       <>
//         <strong>{boldText}</strong>
//         {suggestion.replace(match[0], "")}
//       </>
//     );
//   } else {
//     return suggestion;
//   }
// };

// const SuggestedRefinedQuestions: React.FC<SuggestedRefinedQuestionsProps> = ({
//   suggestions,
//   onSuggestionClick,
//   isLoading,
// }) => {
//   const bgColorClasses = [
//     "bg-red-100",
//     "bg-green-100",
//     "bg-blue-100",
//     "bg-yellow-100",
//     "bg-purple-100",
//     "bg-pink-100",
//   ];

//   return (
//     <div className="rounded-lg p-4">
//       <h1 className="text-xl">
//         Clear intention matters; choose the one that best describes your goal.
//       </h1>
//       <h1 className="px-4 text-sm">
//         * You can continue to refine after selection
//       </h1>
//       <div className="grid grid-cols-2 gap-4">
//         {suggestions.map((suggestion, i) => (
//           <div
//             key={i}
//             className={`flex flex-row items-start ${
//               bgColorClasses[i % bgColorClasses.length]
//             } hover:bg-opacity-75 rounded p-3 ${i === 0 ? "col-span-2" : ""}`}
//           >
//             {/* Scrollable container for text */}
//             <div className="flex-1 min-w-0 mr-2">
//               <p className="h-16 overflow-auto leading-tight ">
//                 {formatSuggestion(suggestion)}
//               </p>
//             </div>
//             {/* Button aligned to the right */}
//             <Button
//               className="bg-blue-500 text-white rounded-xl"
//               onClick={() => onSuggestionClick(i, suggestion)}
//               disabled={isLoading}
//             >
//               Use this
//             </Button>
//           </div>
//         ))}
//       </div>
//     </div>
//   );
// };

// const useChatMessages = ({
//   api,
//   body,
// }: UseChatMessagesProps): UseChatMessagesReturn => {
//   const {
//     messages,
//     append: appendRaw,
//     isLoading,
//   } = useChat({
//     api,
//     body,
//   });
//   const append = (message: { content: string; role: string }) => {
//     appendRaw(message as any);
//   };

//   const lastMessage = useMemo(
//     () => messages[messages.length - 1]?.content,
//     [messages]
//   );

//   return { lastMessage, append, isLoading };
// };

// export default EmptyState;
