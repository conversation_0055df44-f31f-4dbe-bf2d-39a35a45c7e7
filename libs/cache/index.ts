/**
 * Generic in-memory cache with TTL support
 * Provides type-safe caching with automatic expiry and memory management
 */

export type C<PERSON><PERSON>ntry<T> = {
  value: T
  expiresAt: number
}

export type CacheOptions = {
  defaultTtlMs?: number
  maxEntries?: number
  maxTtlMs?: number // Hard limit to prevent memory leaks
}

export class Cache<T = any> {
  private cache = new Map<string, CacheEntry<T>>()
  private defaultTtlMs: number
  private maxEntries: number
  private maxTtlMs: number

  constructor(options: CacheOptions = {}) {
    this.defaultTtlMs = options.defaultTtlMs ?? 60 * 60 * 1000 // 1 hour
    this.maxEntries = options.maxEntries ?? 1000
    this.maxTtlMs = options.maxTtlMs ?? 24 * 60 * 60 * 1000 // 24 hours max
  }

  /**
   * Get value from cache, returns null if expired or not found
   */
  get<K extends T>(key: string): K | null {
    const entry = this.cache.get(key)
    if (!entry) return null

    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key)
      return null
    }

    // Touch to extend TTL (sliding window)
    this.touch(entry)
    return entry.value as K
  }

  /**
   * Set value in cache with optional TTL
   */
  set<K extends T>(key: string, value: K, ttlMs?: number): void {
    this.pruneExpired()

    const effectiveTtl = Math.min(ttlMs ?? this.defaultTtlMs, this.maxTtlMs)

    this.cache.set(key, {
      value,
      expiresAt: Date.now() + effectiveTtl,
    })

    // Enforce max entries limit
    if (this.cache.size > this.maxEntries) {
      this.evictOldest()
    }
  }

  /**
   * Delete value from cache
   */
  del(key: string): boolean {
    return this.cache.delete(key)
  }

  /**
   * Check if key exists and is not expired
   */
  has(key: string): boolean {
    return this.get(key) !== null
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear()
  }

  /**
   * Get current cache size
   */
  size(): number {
    this.pruneExpired()
    return this.cache.size
  }

  /**
   * Get all non-expired keys
   */
  keys(): string[] {
    this.pruneExpired()
    return Array.from(this.cache.keys())
  }

  // Private methods

  private touch(entry: CacheEntry<T>): void {
    const newExpiry = Date.now() + this.defaultTtlMs
    // Respect max TTL limit
    entry.expiresAt = Math.min(newExpiry, Date.now() + this.maxTtlMs)
  }

  private pruneExpired(): void {
    const now = Date.now()
    const entries = Array.from(this.cache.entries())
    for (const [key, entry] of entries) {
      if (now > entry.expiresAt) {
        this.cache.delete(key)
      }
    }
  }

  private evictOldest(): void {
    let oldestKey: string | null = null
    let oldestExpiry = Infinity

    const entries = Array.from(this.cache.entries())
    for (const [key, entry] of entries) {
      if (entry.expiresAt < oldestExpiry) {
        oldestExpiry = entry.expiresAt
        oldestKey = key
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey)
    }
  }
}

// Create default export for common use cases
export const createCache = <T = any>(options?: CacheOptions) =>
  new Cache<T>(options)
