import React, { useState } from 'react'
import { ChevronDown, Globe, Info } from 'lucide-react'
import {
  LANGUAGE_OPTIONS,
  type SupportedLanguageCode,
} from '@/app/constants/languages'
import { cn } from '@/lib/utils'

type LanguageSelectorProps = {
  value: SupportedLanguageCode
  onChange: (language: SupportedLanguageCode) => void
  disabled?: boolean
  compact?: boolean
  dropdownDirection?: 'up' | 'down' // New prop for dropdown direction
  showDisclaimer?: boolean // Show language quality disclaimer
}

/**
 * Language Selector Component
 *
 * A modern dropdown component for selecting the preferred language
 * for AI generation. Features smooth animations and hover effects.
 * Supports both upward and downward dropdown expansion.
 */
const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  value,
  onChange,
  disabled = false,
  compact = false,
  dropdownDirection = 'down',
  showDisclaimer = false,
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false)

  const selectedOption =
    LANGUAGE_OPTIONS.find(option => option.value === value) ||
    LANGUAGE_OPTIONS[0]

  const handleSelect = (language: SupportedLanguageCode): void => {
    onChange(language)
    setIsOpen(false)
  }

  const toggleDropdown = (): void => {
    if (!disabled) {
      setIsOpen(!isOpen)
    }
  }

  return (
    <div className="relative inline-block text-left">
      {/* Disclaimer Tooltip */}
      {showDisclaimer && (
        <div className="absolute -top-8 left-0 z-[10000] bg-gray-800 text-white text-xs px-2 py-1 rounded shadow-lg whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
          <Info className="w-3 h-3 inline mr-1" />
          Best effort translation - quality may vary
        </div>
      )}

      {/* Dropdown Button */}
      <button
        type="button"
        onClick={toggleDropdown}
        disabled={disabled}
        className={cn(
          'inline-flex items-center justify-between w-full font-medium bg-white border border-gray-300 rounded-lg shadow-sm transition-all duration-200 ease-in-out group',
          {
            'opacity-50 cursor-not-allowed bg-gray-50': disabled,
            'hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500':
              !disabled,
          },
          {
            'px-2 py-1 text-xs min-w-[120px] h-6': compact,
            'px-4 py-2.5 text-sm min-w-[200px]': !compact,
          }
        )}
      >
        <div className="flex items-center">
          <Globe
            className={cn('text-gray-400', {
              'w-3 h-3 mr-1': compact,
              'w-4 h-4 mr-3': !compact,
            })}
          />
          <span
            className={cn('truncate', {
              'text-xs': compact,
              'text-sm': !compact,
            })}
          >
            {selectedOption.label}
          </span>
        </div>
        <ChevronDown
          className={cn(
            'text-gray-400 transition-transform duration-200',
            {
              'w-3 h-3 ml-1': compact,
              'w-4 h-4 ml-3': !compact,
            },
            {
              'transform rotate-180': isOpen,
            }
          )}
        />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div
          className={cn(
            'absolute z-[9999] w-full bg-white border border-gray-300 rounded-lg shadow-lg',
            {
              'mt-1': dropdownDirection === 'down',
              'mb-1 bottom-full': dropdownDirection === 'up',
            }
          )}
        >
          <div className="py-1">
            {LANGUAGE_OPTIONS.map(option => (
              <button
                key={option.value}
                onClick={() => handleSelect(option.value)}
                className={cn(
                  'flex items-center w-full text-left transition-colors duration-150 ease-in-out',
                  {
                    'bg-blue-50 text-blue-900 font-medium':
                      value === option.value,
                    'text-gray-900 hover:bg-gray-50': value !== option.value,
                  },
                  {
                    'px-2 py-1.5 text-xs': compact,
                    'px-4 py-2.5 text-sm': !compact,
                  }
                )}
              >
                <Globe
                  className={cn('text-gray-400', {
                    'w-3 h-3 mr-1': compact,
                    'w-4 h-4 mr-3': !compact,
                  })}
                />
                <span>{option.label}</span>
                {value === option.value && (
                  <svg
                    className="w-4 h-4 ml-auto text-blue-600"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                )}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Backdrop to close dropdown when clicking outside */}
      {isOpen && (
        <div
          className="fixed inset-0 z-[9998]"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  )
}

export default LanguageSelector
