{"numFailedTestSuites": 1, "numFailedTests": 2, "numPassedTestSuites": 25, "numPassedTests": 315, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 0, "numTodoTests": 0, "numTotalTestSuites": 26, "numTotalTests": 317, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1752865018883, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["ExecutionStepCollector", "step collection methods"], "duration": 3, "failing": false, "failureDetails": [{"matcherResult": {"expected": "high", "message": "expect(received).toBe(expected) // Object.is equality\n\nExpected: \"high\"\nReceived: undefined", "name": "toBe", "pass": false}}], "failureMessages": ["Error: expect(received).toBe(expected) // Object.is equality\n\nExpected: \"high\"\nReceived: undefined\n    at Object.toBe (/Users/<USER>/Desktop/github/next13-clarify/app/server-actions/ai-chat/__tests__/execution-step-collector.test.ts:35:42)\n    at Promise.finally.completed (/Users/<USER>/Desktop/github/next13-clarify/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/Users/<USER>/Desktop/github/next13-clarify/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/Users/<USER>/Desktop/github/next13-clarify/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (/Users/<USER>/Desktop/github/next13-clarify/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/Users/<USER>/Desktop/github/next13-clarify/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/Users/<USER>/Desktop/github/next13-clarify/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/Users/<USER>/Desktop/github/next13-clarify/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/Users/<USER>/Desktop/github/next13-clarify/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/Users/<USER>/Desktop/github/next13-clarify/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/Users/<USER>/Desktop/github/next13-clarify/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/Users/<USER>/Desktop/github/next13-clarify/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/Users/<USER>/Desktop/github/next13-clarify/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/Users/<USER>/Desktop/github/next13-clarify/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "ExecutionStepCollector step collection methods should add thought steps correctly", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "startAt": 1752865019795, "status": "failed", "title": "should add thought steps correctly"}, {"ancestorTitles": ["ExecutionStepCollector", "step collection methods"], "duration": 0, "failing": false, "failureDetails": [{"matcherResult": {"expected": "test", "message": "expect(received).toBe(expected) // Object.is equality\n\nExpected: \"test\"\nReceived: undefined", "name": "toBe", "pass": false}}], "failureMessages": ["Error: expect(received).toBe(expected) // Object.is equality\n\nExpected: \"test\"\nReceived: undefined\n    at Object.toBe (/Users/<USER>/Desktop/github/next13-clarify/app/server-actions/ai-chat/__tests__/execution-step-collector.test.ts:47:41)\n    at Promise.finally.completed (/Users/<USER>/Desktop/github/next13-clarify/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/Users/<USER>/Desktop/github/next13-clarify/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/Users/<USER>/Desktop/github/next13-clarify/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (/Users/<USER>/Desktop/github/next13-clarify/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/Users/<USER>/Desktop/github/next13-clarify/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/Users/<USER>/Desktop/github/next13-clarify/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/Users/<USER>/Desktop/github/next13-clarify/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/Users/<USER>/Desktop/github/next13-clarify/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/Users/<USER>/Desktop/github/next13-clarify/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/Users/<USER>/Desktop/github/next13-clarify/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/Users/<USER>/Desktop/github/next13-clarify/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/Users/<USER>/Desktop/github/next13-clarify/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/Users/<USER>/Desktop/github/next13-clarify/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "ExecutionStepCollector step collection methods should add tool call steps correctly", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "startAt": 1752865019805, "status": "failed", "title": "should add tool call steps correctly"}, {"ancestorTitles": ["ExecutionStepCollector", "step collection methods"], "duration": 2, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ExecutionStepCollector step collection methods should add tool result steps correctly", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "startAt": 1752865019806, "status": "passed", "title": "should add tool result steps correctly"}, {"ancestorTitles": ["ExecutionStepCollector", "step collection methods"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ExecutionStepCollector step collection methods should add reasoning summary steps correctly", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865019808, "status": "passed", "title": "should add reasoning summary steps correctly"}, {"ancestorTitles": ["ExecutionStepCollector", "step collection methods"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ExecutionStepCollector step collection methods should add sub-agent invocation steps correctly", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "startAt": 1752865019809, "status": "passed", "title": "should add sub-agent invocation steps correctly"}, {"ancestorTitles": ["ExecutionStepCollector", "step collection methods"], "duration": 5, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ExecutionStepCollector step collection methods should add parallel steps correctly", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "startAt": 1752865019809, "status": "passed", "title": "should add parallel steps correctly"}, {"ancestorTitles": ["ExecutionStepCollector", "step collection methods"], "duration": 5, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ExecutionStepCollector step collection methods should maintain correct step order", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "startAt": 1752865019815, "status": "passed", "title": "should maintain correct step order"}, {"ancestorTitles": ["ExecutionStepCollector", "utility methods"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ExecutionStepCollector utility methods should get correct step count", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865019820, "status": "passed", "title": "should get correct step count"}, {"ancestorTitles": ["ExecutionStepCollector", "utility methods"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ExecutionStepCollector utility methods should get steps by type", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "startAt": 1752865019820, "status": "passed", "title": "should get steps by type"}, {"ancestorTitles": ["ExecutionStepCollector", "utility methods"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ExecutionStepCollector utility methods should get steps by parallel key", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865019821, "status": "passed", "title": "should get steps by parallel key"}, {"ancestorTitles": ["ExecutionStepCollector", "utility methods"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ExecutionStepCollector utility methods should clear all steps", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865019822, "status": "passed", "title": "should clear all steps"}, {"ancestorTitles": ["ExecutionStepCollector", "utility methods"], "duration": 2, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ExecutionStepCollector utility methods should generate correct summary", "invocations": 1, "location": null, "numPassingAsserts": 8, "retryReasons": [], "startAt": 1752865019822, "status": "passed", "title": "should generate correct summary"}, {"ancestorTitles": ["ExecutionStepCollector", "utility methods"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ExecutionStepCollector utility methods should detect errors in summary", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865019824, "status": "passed", "title": "should detect errors in summary"}, {"ancestorTitles": ["ExecutionStepCollector", "parseThinkingTags"], "duration": 2, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ExecutionStepCollector parseThinkingTags should parse simple thinking tags", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "startAt": 1752865019824, "status": "passed", "title": "should parse simple thinking tags"}, {"ancestorTitles": ["ExecutionStepCollector", "parseThinkingTags"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ExecutionStepCollector parseThinkingTags should parse thinking tags with time attribute", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865019826, "status": "passed", "title": "should parse thinking tags with time attribute"}, {"ancestorTitles": ["ExecutionStepCollector", "parseThinkingTags"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ExecutionStepCollector parseThinkingTags should parse tool calls within thinking tags", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865019827, "status": "passed", "title": "should parse tool calls within thinking tags"}, {"ancestorTitles": ["ExecutionStepCollector", "parseThinkingTags"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ExecutionStepCollector parseThinkingTags should handle multiple thinking tags", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865019828, "status": "passed", "title": "should handle multiple thinking tags"}, {"ancestorTitles": ["ExecutionStepCollector", "parseThinkingTags"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ExecutionStepCollector parseThinkingTags should handle empty thinking tags", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865019828, "status": "passed", "title": "should handle empty thinking tags"}, {"ancestorTitles": ["ExecutionStepCollector", "parseThinkingTags"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ExecutionStepCollector parseThinkingTags should handle content without thinking tags", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865019828, "status": "passed", "title": "should handle content without thinking tags"}, {"ancestorTitles": ["ExecutionStepCollector", "parseThinkingTags"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ExecutionStepCollector parseThinkingTags should disable thinking parser when ENABLE_LEGACY_THINKING_TAGS is not set", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865019828, "status": "passed", "title": "should disable thinking parser when ENABLE_LEGACY_THINKING_TAGS is not set"}, {"ancestorTitles": ["ExecutionStepCollector", "standalone utility functions"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ExecutionStepCollector standalone utility functions should create new collector instance", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865019829, "status": "passed", "title": "should create new collector instance"}, {"ancestorTitles": ["ExecutionStepCollector", "standalone utility functions"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ExecutionStepCollector standalone utility functions should parse thinking content with utility function", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "startAt": 1752865019830, "status": "passed", "title": "should parse thinking content with utility function"}], "endTime": 1752865019845, "message": "  ● ExecutionStepCollector › step collection methods › should add thought steps correctly\n\n    expect(received).toBe(expected) // Object.is equality\n\n    Expected: \"high\"\n    Received: undefined\n\n      33 |       expect(steps[0].metadata.reasoning).toBe('This is a reasoning step')\n      34 |       expect(steps[1].stepOrder).toBe(1)\n    > 35 |       expect(steps[1].metadata.priority).toBe('high')\n         |                                          ^\n      36 |     })\n      37 |\n      38 |     it('should add tool call steps correctly', () => {\n\n      at Object.toBe (app/server-actions/ai-chat/__tests__/execution-step-collector.test.ts:35:42)\n\n  ● ExecutionStepCollector › step collection methods › should add tool call steps correctly\n\n    expect(received).toBe(expected) // Object.is equality\n\n    Expected: \"test\"\n    Received: undefined\n\n      45 |       expect(steps[0].metadata.toolName).toBe('searchTool')\n      46 |       expect(steps[0].metadata.args).toEqual({ query: 'test query' })\n    > 47 |       expect(steps[1].metadata.context).toBe('test')\n         |                                         ^\n      48 |     })\n      49 |\n      50 |     it('should add tool result steps correctly', () => {\n\n      at Object.toBe (app/server-actions/ai-chat/__tests__/execution-step-collector.test.ts:47:41)\n", "name": "/Users/<USER>/Desktop/github/next13-clarify/app/server-actions/ai-chat/__tests__/execution-step-collector.test.ts", "startTime": 1752865019249, "status": "failed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["useScreeningActions"], "duration": 32, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "useScreeningActions should initialize with default values", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "startAt": 1752865020342, "status": "passed", "title": "should initialize with default values"}, {"ancestorTitles": ["useScreeningActions"], "duration": 3, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "useScreeningActions should update description when setDescription is called", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020374, "status": "passed", "title": "should update description when setDescription is called"}, {"ancestorTitles": ["useScreeningActions"], "duration": 3, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "useScreeningActions should call diagnosis and rephrase hooks when handleParaphraseAndAnalyze is called", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020377, "status": "passed", "title": "should call diagnosis and rephrase hooks when handleParaphraseAndAnalyze is called"}, {"ancestorTitles": ["useScreeningActions"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "useScreeningActions should not call APIs if description is empty", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865020381, "status": "passed", "title": "should not call APIs if description is empty"}, {"ancestorTitles": ["useScreeningActions"], "duration": 2, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "useScreeningActions should update description and selectedSuggestion on handleSuggestionSelection", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865020382, "status": "passed", "title": "should update description and selectedSuggestion on handleSuggestionSelection"}, {"ancestorTitles": ["useScreeningActions"], "duration": 2, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "useScreeningActions should enable clarification after a selection is made", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020384, "status": "passed", "title": "should enable clarification after a selection is made"}, {"ancestorTitles": ["useScreeningActions"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "useScreeningActions should correctly handle the \"Use My Input\" action", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020386, "status": "passed", "title": "should correctly handle the \"Use My Input\" action"}, {"ancestorTitles": ["useScreeningActions"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "useScreeningActions should set isProcessing to true when diagnosis is streaming", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020387, "status": "passed", "title": "should set isProcessing to true when diagnosis is streaming"}, {"ancestorTitles": ["useScreeningActions"], "duration": 2, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "useScreeningActions should set isProcessing to true when rephrase is streaming", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020388, "status": "passed", "title": "should set isProcessing to true when rephrase is streaming"}, {"ancestorTitles": ["useScreeningActions"], "duration": 4, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "useScreeningActions should handle errors from the diagnosis hook", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020390, "status": "passed", "title": "should handle errors from the diagnosis hook"}], "endTime": 1752865020396, "message": "", "name": "/Users/<USER>/Desktop/github/next13-clarify/__tests__/hooks/useScreeningActions.spec.ts", "startTime": 1752865019641, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["useDragTreeLoader Race Condition Fix", "Store Reset on dragTreeId Change"], "duration": 7, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "useDragTreeLoader Race Condition Fix Store Reset on dragTreeId Change should reset store immediately when dragTreeId changes", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865020135, "status": "passed", "title": "should reset store immediately when dragTreeId changes"}, {"ancestorTitles": ["useDragTreeLoader Race Condition Fix", "Request ID Generation and Validation"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "useDragTreeLoader Race Condition Fix Request ID Generation and Validation should generate unique request IDs with correct format", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865020142, "status": "passed", "title": "should generate unique request IDs with correct format"}, {"ancestorTitles": ["useDragTreeLoader Race Condition Fix", "Request ID Generation and Validation"], "duration": 101, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "useDragTreeLoader Race Condition Fix Request ID Generation and Validation should correctly identify stale vs current requests", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020143, "status": "passed", "title": "should correctly identify stale vs current requests"}, {"ancestorTitles": ["useDragTreeLoader Race Condition Fix", "Race Condition Prevention Logic"], "duration": 114, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "useDragTreeLoader Race Condition Fix Race Condition Prevention Logic should simulate the loader logic preventing stale updates", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865020245, "status": "passed", "title": "should simulate the loader logic preventing stale updates"}, {"ancestorTitles": ["useDragTreeLoader Race Condition Fix", "Store Reset Functionality"], "duration": 7, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "useDragTreeLoader Race Condition Fix Store Reset Functionality should reset all state fields when resetDragTreeData is called", "invocations": 1, "location": null, "numPassingAsserts": 7, "retryReasons": [], "startAt": 1752865020359, "status": "passed", "title": "should reset all state fields when resetDragTreeData is called"}, {"ancestorTitles": ["useDragTreeLoader Race Condition Fix", "Integration Scenarios"], "duration": 56, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "useDragTreeLoader Race Condition Fix Integration Scenarios should handle rapid navigation A→B→A correctly", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865020366, "status": "passed", "title": "should handle rapid navigation A→B→A correctly"}], "endTime": 1752865020431, "message": "", "name": "/Users/<USER>/Desktop/github/next13-clarify/__tests__/dragTree/hooks/useDragTreeLoader.test.ts", "startTime": 1752865019638, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["useAiGeneration hook", "Duplicate Generation Prevention"], "duration": 60, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "useAiGeneration hook Duplicate Generation Prevention should only call fetch once when component mounts twice in StrictMode", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865020212, "status": "passed", "title": "should only call fetch once when component mounts twice in StrictMode"}, {"ancestorTitles": ["useAiGeneration hook", "Duplicate Generation Prevention"], "duration": 111, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "useAiGeneration hook Duplicate Generation Prevention should not start generation if generationStarted flag is already true", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020275, "status": "passed", "title": "should not start generation if generationStarted flag is already true"}, {"ancestorTitles": ["useAiGeneration hook", "Duplicate Generation Prevention"], "duration": 109, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "useAiGeneration hook Duplicate Generation Prevention should not start generation if messages already exist", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020386, "status": "passed", "title": "should not start generation if messages already exist"}], "endTime": 1752865020496, "message": "", "name": "/Users/<USER>/Desktop/github/next13-clarify/__tests__/dragTree/hooks/useAiGeneration.test.tsx", "startTime": 1752865019647, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Button Component"], "duration": 54, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Button Component renders button with children", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020503, "status": "passed", "title": "renders button with children"}, {"ancestorTitles": ["Button Component"], "duration": 12, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Button Component calls onClick when clicked", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020557, "status": "passed", "title": "calls onClick when clicked"}, {"ancestorTitles": ["Button Component"], "duration": 8, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Button Component is disabled when disabled prop is true", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020569, "status": "passed", "title": "is disabled when disabled prop is true"}, {"ancestorTitles": ["Button Component"], "duration": 3, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Button Component renders with correct type attribute", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020577, "status": "passed", "title": "renders with correct type attribute"}, {"ancestorTitles": ["Button Component"], "duration": 34, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Button Component applies full width class when fullWidth prop is true", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020581, "status": "passed", "title": "applies full width class when fullWidth prop is true"}, {"ancestorTitles": ["Button Component"], "duration": 3, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Button Component applies secondary styles when secondary prop is true", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020615, "status": "passed", "title": "applies secondary styles when secondary prop is true"}, {"ancestorTitles": ["Button Component"], "duration": 8, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Button Component applies danger styles when danger prop is true", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020618, "status": "passed", "title": "applies danger styles when danger prop is true"}, {"ancestorTitles": ["Button Component"], "duration": 10, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Button Component defaults to button type when no type is specified", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020626, "status": "passed", "title": "defaults to button type when no type is specified"}, {"ancestorTitles": ["Button Component"], "duration": 24, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Button Component does not call onClick when disabled", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020636, "status": "passed", "title": "does not call onClick when disabled"}], "endTime": 1752865020661, "message": "", "name": "/Users/<USER>/Desktop/github/next13-clarify/__tests__/components/Button.test.tsx", "startTime": 1752865020270, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["AI Chat Persistence Service - Core Validation", "Input Validation"], "duration": 27, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Persistence Service - Core Validation Input Validation should validate user ID format in createAiConversation", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020609, "status": "passed", "title": "should validate user ID format in createAiConversation"}, {"ancestorTitles": ["AI Chat Persistence Service - Core Validation", "Input Validation"], "duration": 5, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Persistence Service - Core Validation Input Validation should validate conversation ID format when provided", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020636, "status": "passed", "title": "should validate conversation ID format when provided"}, {"ancestorTitles": ["AI Chat Persistence Service - Core Validation", "Input Validation"], "duration": 2, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Persistence Service - Core Validation Input Validation should validate conversation ID format in persistConversationTurn", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020641, "status": "passed", "title": "should validate conversation ID format in persistConversationTurn"}, {"ancestorTitles": ["AI Chat Persistence Service - Core Validation", "Input Validation"], "duration": 4, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Persistence Service - Core Validation Input Validation should validate step types in addMessageToConversation", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020644, "status": "passed", "title": "should validate step types in addMessageToConversation"}, {"ancestorTitles": ["AI Chat Persistence Service - Core Validation", "Input Validation"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Persistence Service - Core Validation Input Validation should validate conversation ID format in getConversationWithMessages", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020648, "status": "passed", "title": "should validate conversation ID format in getConversationWithMessages"}, {"ancestorTitles": ["AI Chat Persistence Service - Core Validation", "Input Validation"], "duration": 6, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Persistence Service - Core Validation Input Validation should validate user ID format in getUserConversations", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020649, "status": "passed", "title": "should validate user ID format in getUserConversations"}, {"ancestorTitles": ["AI Chat Persistence Service - Core Validation", "Input Validation"], "duration": 5, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Persistence Service - Core Validation Input Validation should validate conversation ID format in deleteConversation", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020655, "status": "passed", "title": "should validate conversation ID format in deleteConversation"}, {"ancestorTitles": ["AI Chat Persistence Service - Core Validation", "Data Processing"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Persistence Service - Core Validation Data Processing should handle empty arrays correctly in persistConversationTurn", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "startAt": 1752865020660, "status": "passed", "title": "should handle empty arrays correctly in persistConversationTurn"}, {"ancestorTitles": ["AI Chat Persistence Service - Core Validation", "Data Processing"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Persistence Service - Core Validation Data Processing should normalize step orders to prevent duplicates", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "startAt": 1752865020661, "status": "passed", "title": "should normalize step orders to prevent duplicates"}, {"ancestorTitles": ["AI Chat Persistence Service - Core Validation", "Data Processing"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Persistence Service - Core Validation Data Processing should validate attachment data structure", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020661, "status": "passed", "title": "should validate attachment data structure"}, {"ancestorTitles": ["AI Chat Persistence Service - Core Validation", "ID Generation and Validation"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Persistence Service - Core Validation ID Generation and Validation should generate predictable IDs for testing", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865020662, "status": "passed", "title": "should generate predictable IDs for testing"}, {"ancestorTitles": ["AI Chat Persistence Service - Core Validation", "ID Generation and Validation"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Persistence Service - Core Validation ID Generation and Validation should validate ID prefix formats", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "startAt": 1752865020662, "status": "passed", "title": "should validate ID prefix formats"}, {"ancestorTitles": ["AI Chat Persistence Service - Core Validation", "ID Generation and Validation"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Persistence Service - Core Validation ID Generation and Validation should handle optional conversation ID parameter", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020662, "status": "passed", "title": "should handle optional conversation ID parameter"}], "endTime": 1752865020663, "message": "", "name": "/Users/<USER>/Desktop/github/next13-clarify/app/server-actions/ai-chat/__tests__/persistence-service.simple.test.ts", "startTime": 1752865020448, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["LoadingSkeleton Component"], "duration": 53, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "LoadingSkeleton Component renders loading text", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020644, "status": "passed", "title": "renders loading text"}, {"ancestorTitles": ["LoadingSkeleton Component"], "duration": 7, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "LoadingSkeleton Component renders skeleton elements", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020697, "status": "passed", "title": "renders skeleton elements"}, {"ancestorTitles": ["LoadingSkeleton Component"], "duration": 2, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "LoadingSkeleton Component has proper container styling", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020705, "status": "passed", "title": "has proper container styling"}, {"ancestorTitles": ["LoadingSkeleton Component"], "duration": 2, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "LoadingSkeleton Component renders main skeleton with correct classes", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020707, "status": "passed", "title": "renders main skeleton with correct classes"}, {"ancestorTitles": ["LoadingSkeleton Component"], "duration": 5, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "LoadingSkeleton Component renders smaller skeletons with correct classes", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020709, "status": "passed", "title": "renders smaller skeletons with correct classes"}], "endTime": 1752865020714, "message": "", "name": "/Users/<USER>/Desktop/github/next13-clarify/__tests__/components/LoadingSkeleton.test.tsx", "startTime": 1752865020442, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["VisualFlowDiagram – core behaviour"], "duration": 33, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "VisualFlowDiagram – core behaviour renders DiagramView in linear mode without crashing", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020726, "status": "passed", "title": "renders DiagramView in linear mode without crashing"}, {"ancestorTitles": ["VisualFlowDiagram – core behaviour"], "duration": 3, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "VisualFlowDiagram – core behaviour renders DiagramView in radial mode without crashing", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020760, "status": "passed", "title": "renders DiagramView in radial mode without crashing"}, {"ancestorTitles": ["VisualFlowDiagram – core behaviour"], "duration": 6, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "VisualFlowDiagram – core behaviour FlowExportButton dispatches download event with correct layout", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020763, "status": "passed", "title": "FlowExportButton dispatches download event with correct layout"}], "endTime": 1752865020769, "message": "", "name": "/Users/<USER>/Desktop/github/next13-clarify/__tests__/dragTree/components/VisualFlowDiagram/core.test.tsx", "startTime": 1752865020545, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["ResearchDisplay Components", "SearchProgressIndicator"], "duration": 34, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchDisplay Components SearchProgressIndicator should render when searching is active", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865020255, "status": "passed", "title": "should render when searching is active"}, {"ancestorTitles": ["ResearchDisplay Components", "SearchProgressIndicator"], "duration": 2, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchDisplay Components SearchProgressIndicator should not render when not searching", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020289, "status": "passed", "title": "should not render when not searching"}, {"ancestorTitles": ["ResearchDisplay Components", "SearchProgressIndicator"], "duration": 3, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchDisplay Components SearchProgressIndicator should render with default message when no query provided", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020291, "status": "passed", "title": "should render with default message when no query provided"}, {"ancestorTitles": ["ResearchDisplay Components", "SearchProgressIndicator"], "duration": 3, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchDisplay Components SearchProgressIndicator should apply custom className", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020294, "status": "passed", "title": "should apply custom className"}, {"ancestorTitles": ["ResearchDisplay Components", "SearchProgressIndicator"], "duration": 5, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchDisplay Components SearchProgressIndicator should show animated elements when searching", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020297, "status": "passed", "title": "should show animated elements when searching"}, {"ancestorTitles": ["ResearchDisplay Components", "SourceCitations"], "duration": 76, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchDisplay Components SourceCitations should render source icons when provided", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020303, "status": "passed", "title": "should render source icons when provided"}, {"ancestorTitles": ["ResearchDisplay Components", "SourceCitations"], "duration": 14, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchDisplay Components SourceCitations should render numbered placeholders for sources without icons", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020379, "status": "passed", "title": "should render numbered placeholders for sources without icons"}, {"ancestorTitles": ["ResearchDisplay Components", "SourceCitations"], "duration": 11, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchDisplay Components SourceCitations should limit displayed sources to maxSources", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020393, "status": "passed", "title": "should limit displayed sources to maxSources"}, {"ancestorTitles": ["ResearchDisplay Components", "SourceCitations"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchDisplay Components SourceCitations should handle empty search results", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020404, "status": "passed", "title": "should handle empty search results"}, {"ancestorTitles": ["ResearchDisplay Components", "SourceCitations"], "duration": 4, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchDisplay Components SourceCitations should handle undefined search results", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020405, "status": "passed", "title": "should handle undefined search results"}, {"ancestorTitles": ["ResearchDisplay Components", "SourceCitations"], "duration": 25, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchDisplay Components SourceCitations should deduplicate sources by URL", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020409, "status": "passed", "title": "should deduplicate sources by URL"}, {"ancestorTitles": ["ResearchDisplay Components", "SourceCitations"], "duration": 18, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchDisplay Components SourceCitations should apply custom className", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020434, "status": "passed", "title": "should apply custom className"}, {"ancestorTitles": ["ResearchDisplay Components", "Source Menu Interaction"], "duration": 68, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchDisplay Components Source Menu Interaction should open popup menu when citations are clicked", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865020452, "status": "passed", "title": "should open popup menu when citations are clicked"}, {"ancestorTitles": ["ResearchDisplay Components", "Source Menu Interaction"], "duration": 42, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchDisplay Components Source Menu Interaction should close popup when clicking outside", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020520, "status": "passed", "title": "should close popup when clicking outside"}, {"ancestorTitles": ["ResearchDisplay Components", "Source Menu Interaction"], "duration": 30, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchDisplay Components Source Menu Interaction should close popup when pressing Escape", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020562, "status": "passed", "title": "should close popup when pressing Escape"}, {"ancestorTitles": ["ResearchDisplay Components", "Source Menu Interaction"], "duration": 40, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchDisplay Components Source Menu Interaction should open external links when source is clicked", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020593, "status": "passed", "title": "should open external links when source is clicked"}, {"ancestorTitles": ["ResearchDisplay Components", "Accessibility"], "duration": 2, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchDisplay Components Accessibility should have proper ARIA labels for source citations", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020633, "status": "passed", "title": "should have proper ARIA labels for source citations"}, {"ancestorTitles": ["ResearchDisplay Components", "Accessibility"], "duration": 8, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchDisplay Components Accessibility should handle favicon loading errors gracefully", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020635, "status": "passed", "title": "should handle favicon loading errors gracefully"}, {"ancestorTitles": ["ResearchDisplay Components", "Accessibility"], "duration": 3, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchDisplay Components Accessibility should be keyboard navigable", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020643, "status": "passed", "title": "should be keyboard navigable"}, {"ancestorTitles": ["ResearchDisplay Components", "Performance Considerations"], "duration": 206, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchDisplay Components Performance Considerations should handle large numbers of sources efficiently", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020646, "status": "passed", "title": "should handle large numbers of sources efficiently"}, {"ancestorTitles": ["ResearchDisplay Components", "Performance Considerations"], "duration": 7, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchDisplay Components Performance Considerations should not re-render unnecessarily with same props", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020852, "status": "passed", "title": "should not re-render unnecessarily with same props"}], "endTime": 1752865020860, "message": "", "name": "/Users/<USER>/Desktop/github/next13-clarify/__tests__/dragTree/components/ResearchDisplay.test.tsx", "startTime": 1752865019647, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Research Data Utilities", "hasSearchResults"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Data Utilities hasSearchResults should return true for valid metadata with search results", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020853, "status": "passed", "title": "should return true for valid metadata with search results"}, {"ancestorTitles": ["Research Data Utilities", "hasSearchResults"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Data Utilities hasSearchResults should return falsy for null metadata", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020854, "status": "passed", "title": "should return falsy for null metadata"}, {"ancestorTitles": ["Research Data Utilities", "hasSearchResults"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Data Utilities hasSearchResults should return falsy for undefined metadata", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020854, "status": "passed", "title": "should return falsy for undefined metadata"}, {"ancestorTitles": ["Research Data Utilities", "hasSearchResults"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Data Utilities hasSearchResults should return false for non-object metadata", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865020855, "status": "passed", "title": "should return false for non-object metadata"}, {"ancestorTitles": ["Research Data Utilities", "hasSearchResults"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Data Utilities hasSearchResults should return false when searchResults is missing", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020855, "status": "passed", "title": "should return false when search<PERSON><PERSON><PERSON><PERSON> is missing"}, {"ancestorTitles": ["Research Data Utilities", "hasSearchResults"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Data Utilities hasSearchResults should return false when searchResults is not an array", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020855, "status": "passed", "title": "should return false when searchResults is not an array"}, {"ancestorTitles": ["Research Data Utilities", "hasSearchResults"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Data Utilities hasSearchResults should return false when searchResults is empty array", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020855, "status": "passed", "title": "should return false when searchResults is empty array"}, {"ancestorTitles": ["Research Data Utilities", "hasSearchResults"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Data Utilities hasSearchResults should return false when searchResults contains invalid items", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020855, "status": "passed", "title": "should return false when searchResults contains invalid items"}, {"ancestorTitles": ["Research Data Utilities", "hasSearchResults"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Data Utilities hasSearchResults should return false when some search results are invalid", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020856, "status": "passed", "title": "should return false when some search results are invalid"}, {"ancestorTitles": ["Research Data Utilities", "hasSearchResults"], "duration": 3, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Data Utilities hasSearchResults should return true when all search results are valid", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020856, "status": "passed", "title": "should return true when all search results are valid"}, {"ancestorTitles": ["Research Data Utilities", "hasSearchResults"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Data Utilities hasSearchResults should handle search results without optional fields", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020859, "status": "passed", "title": "should handle search results without optional fields"}, {"ancestorTitles": ["Research Data Utilities", "isValidSearchResult"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Data Utilities isValidSearchResult should return true for valid search result", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020860, "status": "passed", "title": "should return true for valid search result"}, {"ancestorTitles": ["Research Data Utilities", "isValidSearchResult"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Data Utilities isValidSearchResult should return falsy for null or undefined", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020860, "status": "passed", "title": "should return falsy for null or undefined"}, {"ancestorTitles": ["Research Data Utilities", "isValidSearchResult"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Data Utilities isValidSearchResult should return false for non-object types", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "startAt": 1752865020860, "status": "passed", "title": "should return false for non-object types"}, {"ancestorTitles": ["Research Data Utilities", "isValidSearchResult"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Data Utilities isValidSearchResult should return false when required fields are missing", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865020861, "status": "passed", "title": "should return false when required fields are missing"}, {"ancestorTitles": ["Research Data Utilities", "isValidSearchResult"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Data Utilities isValidSearchResult should return false when required fields have wrong types", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865020861, "status": "passed", "title": "should return false when required fields have wrong types"}, {"ancestorTitles": ["Research Data Utilities", "isValidSearchResult"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Data Utilities isValidSearchResult should handle optional fields correctly", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020861, "status": "passed", "title": "should handle optional fields correctly"}, {"ancestorTitles": ["Research Data Utilities", "isValidSearchResult"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Data Utilities isValidSearchResult should validate snippets array content", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020861, "status": "passed", "title": "should validate snippets array content"}, {"ancestorTitles": ["Research Data Utilities", "Edge Cases and Data Integrity"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Data Utilities Edge Cases and Data Integrity should handle deeply nested invalid data", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020862, "status": "passed", "title": "should handle deeply nested invalid data"}, {"ancestorTitles": ["Research Data Utilities", "Edge Cases and Data Integrity"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Data Utilities Edge Cases and Data Integrity should handle circular references gracefully", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020862, "status": "passed", "title": "should handle circular references gracefully"}, {"ancestorTitles": ["Research Data Utilities", "Edge Cases and Data Integrity"], "duration": 7, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Data Utilities Edge Cases and Data Integrity should handle very large datasets", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020862, "status": "passed", "title": "should handle very large datasets"}, {"ancestorTitles": ["Research Data Utilities", "Edge Cases and Data Integrity"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Data Utilities Edge Cases and Data Integrity should validate URL formats correctly", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020869, "status": "passed", "title": "should validate URL formats correctly"}, {"ancestorTitles": ["Research Data Utilities", "Edge Cases and Data Integrity"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Data Utilities Edge Cases and Data Integrity should handle mixed valid and invalid results in validation", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020869, "status": "passed", "title": "should handle mixed valid and invalid results in validation"}, {"ancestorTitles": ["Research Data Utilities", "Type Guard Accuracy"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Data Utilities Type Guard Accuracy should narrow types correctly when hasSearchResults returns true", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865020869, "status": "passed", "title": "should narrow types correctly when hasSearchResults returns true"}, {"ancestorTitles": ["Research Data Utilities", "Type Guard Accuracy"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Data Utilities Type Guard Accuracy should work as a filter predicate", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865020869, "status": "passed", "title": "should work as a filter predicate"}], "endTime": 1752865020871, "message": "", "name": "/Users/<USER>/Desktop/github/next13-clarify/__tests__/dragTree/utils/research-utils.test.ts", "startTime": 1752865020672, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["useScreeningDiagnosis"], "duration": 9, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "useScreeningDiagnosis should call submit with the correct data", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020926, "status": "passed", "title": "should call submit with the correct data"}, {"ancestorTitles": ["useScreeningDiagnosis"], "duration": 2, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "useScreeningDiagnosis should show an error if user is not authenticated", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020935, "status": "passed", "title": "should show an error if user is not authenticated"}, {"ancestorTitles": ["useScreeningDiagnosis"], "duration": 2, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "useScreeningDiagnosis should call onScreenObjectUpdate when the object result changes", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020937, "status": "passed", "title": "should call onScreenObjectUpdate when the object result changes"}, {"ancestorTitles": ["useScreeningDiagnosis"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "useScreeningDiagnosis should map isLoading to isStreaming", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020939, "status": "passed", "title": "should map isLoading to isStreaming"}], "endTime": 1752865020942, "message": "", "name": "/Users/<USER>/Desktop/github/next13-clarify/__tests__/hooks/useScreeningDiagnosis.test.ts", "startTime": 1752865020723, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["useScreeningRephrase"], "duration": 15, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "useScreeningRephrase should call append when startRephrase is called", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020920, "status": "passed", "title": "should call append when start<PERSON><PERSON><PERSON><PERSON> is called"}, {"ancestorTitles": ["useScreeningRephrase"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "useScreeningRephrase should show an error if user is not authenticated", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020935, "status": "passed", "title": "should show an error if user is not authenticated"}, {"ancestorTitles": ["useScreeningRephrase"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "useScreeningRephrase should call onRephrasedUpdate with parsed suggestions", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020937, "status": "passed", "title": "should call onRephrasedUpdate with parsed suggestions"}, {"ancestorTitles": ["useScreeningRephrase"], "duration": 2, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "useScreeningRephrase should map isLoading to isStreaming", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020938, "status": "passed", "title": "should map isLoading to isStreaming"}], "endTime": 1752865020942, "message": "", "name": "/Users/<USER>/Desktop/github/next13-clarify/__tests__/hooks/useScreeningRephrase.test.ts", "startTime": 1752865020694, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Research Integration Tests", "Data Validation Functions", "hasSearchResults type guard"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Integration Tests Data Validation Functions hasSearchResults type guard should validate metadata with search results correctly", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020937, "status": "passed", "title": "should validate metadata with search results correctly"}, {"ancestorTitles": ["Research Integration Tests", "Data Validation Functions", "hasSearchResults type guard"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Integration Tests Data Validation Functions hasSearchResults type guard should reject invalid metadata structures", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "startAt": 1752865020938, "status": "passed", "title": "should reject invalid metadata structures"}, {"ancestorTitles": ["Research Integration Tests", "Data Validation Functions", "hasSearchResults type guard"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Integration Tests Data Validation Functions hasSearchResults type guard should validate all search results in array", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020939, "status": "passed", "title": "should validate all search results in array"}, {"ancestorTitles": ["Research Integration Tests", "Data Validation Functions", "isValidSearchResult type guard"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Integration Tests Data Validation Functions isValidSearchResult type guard should validate complete search results", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020939, "status": "passed", "title": "should validate complete search results"}, {"ancestorTitles": ["Research Integration Tests", "Data Validation Functions", "isValidSearchResult type guard"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Integration Tests Data Validation Functions isValidSearchResult type guard should reject incomplete or invalid results", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "startAt": 1752865020939, "status": "passed", "title": "should reject incomplete or invalid results"}, {"ancestorTitles": ["Research Integration Tests", "Data Validation Functions", "isValidSearchResult type guard"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Integration Tests Data Validation Functions isValidSearchResult type guard should handle optional fields correctly", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020940, "status": "passed", "title": "should handle optional fields correctly"}, {"ancestorTitles": ["Research Integration Tests", "Research Data Processing"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Integration Tests Research Data Processing should handle large datasets efficiently", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020940, "status": "passed", "title": "should handle large datasets efficiently"}, {"ancestorTitles": ["Research Integration Tests", "Research Data Processing"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Integration Tests Research Data Processing should deduplicate search results by URL", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "startAt": 1752865020941, "status": "passed", "title": "should deduplicate search results by URL"}, {"ancestorTitles": ["Research Integration Tests", "Research Content States"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Integration Tests Research Content States should handle different content statuses", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "startAt": 1752865020941, "status": "passed", "title": "should handle different content statuses"}, {"ancestorTitles": ["Research Integration Tests", "Research Content States"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Integration Tests Research Content States should validate research content structure", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "startAt": 1752865020942, "status": "passed", "title": "should validate research content structure"}, {"ancestorTitles": ["Research Integration Tests", "Research Option Configuration"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Integration Tests Research Option Configuration should validate research option configurations", "invocations": 1, "location": null, "numPassingAsserts": 10, "retryReasons": [], "startAt": 1752865020942, "status": "passed", "title": "should validate research option configurations"}, {"ancestorTitles": ["Research Integration Tests", "Research Option Configuration"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Integration Tests Research Option Configuration should handle URL template replacement", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020942, "status": "passed", "title": "should handle URL template replacement"}, {"ancestorTitles": ["Research Integration Tests", "Error <PERSON> and <PERSON> Cases"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Integration Tests Error Handling and Edge Cases should handle malformed data gracefully", "invocations": 1, "location": null, "numPassingAsserts": 16, "retryReasons": [], "startAt": 1752865020943, "status": "passed", "title": "should handle malformed data gracefully"}, {"ancestorTitles": ["Research Integration Tests", "Error <PERSON> and <PERSON> Cases"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Integration Tests Error Handling and Edge Cases should handle circular references without crashing", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020944, "status": "passed", "title": "should handle circular references without crashing"}, {"ancestorTitles": ["Research Integration Tests", "Error <PERSON> and <PERSON> Cases"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Integration Tests Error Handling and Edge Cases should validate timestamp formats", "invocations": 1, "location": null, "numPassingAsserts": 8, "retryReasons": [], "startAt": 1752865020944, "status": "passed", "title": "should validate timestamp formats"}, {"ancestorTitles": ["Research Integration Tests", "Performance and Memory"], "duration": 2, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Integration Tests Performance and Memory should handle repeated validations efficiently", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020945, "status": "passed", "title": "should handle repeated validations efficiently"}, {"ancestorTitles": ["Research Integration Tests", "Performance and Memory"], "duration": 19, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Integration Tests Performance and Memory should not leak memory with large datasets", "invocations": 1, "location": null, "numPassingAsserts": 11, "retryReasons": [], "startAt": 1752865020947, "status": "passed", "title": "should not leak memory with large datasets"}], "endTime": 1752865020967, "message": "", "name": "/Users/<USER>/Desktop/github/next13-clarify/__tests__/dragTree/research-integration.test.tsx", "startTime": 1752865020784, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Suggestion Utils", "generateSuggestionId"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Suggestion Utils generateSuggestionId generates unique IDs", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865021017, "status": "passed", "title": "generates unique IDs"}, {"ancestorTitles": ["Suggestion Utils", "generateSuggestionId"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Suggestion Utils generateSuggestionId includes timestamp in ID", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021019, "status": "passed", "title": "includes timestamp in ID"}, {"ancestorTitles": ["Suggestion Utils", "createSuggestionsFromText"], "duration": 25, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Suggestion Utils createSuggestionsFromText creates suggestions for existing text", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "startAt": 1752865021019, "status": "passed", "title": "creates suggestions for existing text"}, {"ancestorTitles": ["Suggestion Utils", "createSuggestionsFromText"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Suggestion Utils createSuggestionsFromText handles multiple occurrences of the same text", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021044, "status": "passed", "title": "handles multiple occurrences of the same text"}, {"ancestorTitles": ["Suggestion Utils", "createSuggestionsFromText"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Suggestion Utils createSuggestionsFromText warns when text is not found", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865021045, "status": "passed", "title": "warns when text is not found"}, {"ancestorTitles": ["Suggestion Utils", "createSuggestionsFromText"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Suggestion Utils createSuggestionsFromText handles empty suggestions array", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021046, "status": "passed", "title": "handles empty suggestions array"}, {"ancestorTitles": ["Suggestion Utils", "createSuggestionsFromText"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Suggestion Utils createSuggestionsFromText handles errors gracefully", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021046, "status": "passed", "title": "handles errors gracefully"}, {"ancestorTitles": ["Suggestion Utils", "createSuggestionsFromText"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Suggestion Utils createSuggestionsFromText does not log warnings in production", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021046, "status": "passed", "title": "does not log warnings in production"}, {"ancestorTitles": ["Suggestion Utils", "getAllSuggestions"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Suggestion Utils getAllSuggestions returns all suggestions in the document", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865021047, "status": "passed", "title": "returns all suggestions in the document"}, {"ancestorTitles": ["Suggestion Utils", "getAllSuggestions"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Suggestion Utils getAllSuggestions returns empty array when no suggestions exist", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021048, "status": "passed", "title": "returns empty array when no suggestions exist"}, {"ancestorTitles": ["Suggestion Utils", "acceptAllSuggestions"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Suggestion Utils acceptAllSuggestions accepts all suggestions in reverse order", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "startAt": 1752865021049, "status": "passed", "title": "accepts all suggestions in reverse order"}, {"ancestorTitles": ["Suggestion Utils", "acceptAllSuggestions"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Suggestion Utils acceptAllSuggestions handles documents with no suggestions", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021049, "status": "passed", "title": "handles documents with no suggestions"}, {"ancestorTitles": ["Suggestion Utils", "rejectAllSuggestions"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Suggestion Utils rejectAllSuggestions rejects all suggestions in reverse order", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "startAt": 1752865021050, "status": "passed", "title": "rejects all suggestions in reverse order"}, {"ancestorTitles": ["Suggestion Utils", "clearAllSuggestions"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Suggestion Utils clearAllSuggestions calls rejectAllSuggestions", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021051, "status": "passed", "title": "calls rejectAllSuggestions"}, {"ancestorTitles": ["Suggestion Utils", "createDemoSuggestions"], "duration": 6, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Suggestion Utils createDemoSuggestions creates predefined demo suggestions", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "startAt": 1752865021052, "status": "passed", "title": "creates predefined demo suggestions"}, {"ancestorTitles": ["Suggestion Utils", "applySuggestionsFromAPI"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Suggestion Utils applySuggestionsFromAPI successfully applies suggestions from API", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021058, "status": "passed", "title": "successfully applies suggestions from API"}, {"ancestorTitles": ["Suggestion Utils", "applySuggestionsFromAPI"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Suggestion Utils applySuggestionsFromAPI handles API errors gracefully", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021059, "status": "passed", "title": "handles API errors gracefully"}, {"ancestorTitles": ["Suggestion Utils", "applySuggestionsFromAPI"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Suggestion Utils applySuggestionsFromAPI handles empty suggestions from API", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021059, "status": "passed", "title": "handles empty suggestions from API"}, {"ancestorTitles": ["Suggestion Utils", "applySuggestionsFromAPI"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Suggestion Utils applySuggestionsFromAPI handles network errors", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021059, "status": "passed", "title": "handles network errors"}, {"ancestorTitles": ["Suggestion Utils", "applySuggestionsFromAPI"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Suggestion Utils applySuggestionsFromAPI does not log in production environment", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021060, "status": "passed", "title": "does not log in production environment"}], "endTime": 1752865021072, "message": "", "name": "/Users/<USER>/Desktop/github/next13-clarify/__tests__/components/editor/suggestion-utils.test.ts", "startTime": 1752865020884, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Rate Limiting Concept Verification", "basic rate limiting functionality"], "duration": 58, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Rate Limiting Concept Verification basic rate limiting functionality should limit concurrent requests to specified maximum", "invocations": 1, "location": null, "numPassingAsserts": 11, "retryReasons": [], "startAt": 1752865020142, "status": "passed", "title": "should limit concurrent requests to specified maximum"}, {"ancestorTitles": ["Rate Limiting Concept Verification", "basic rate limiting functionality"], "duration": 50, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Rate Limiting Concept Verification basic rate limiting functionality should handle sequential execution with limit of 1", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865020201, "status": "passed", "title": "should handle sequential execution with limit of 1"}, {"ancestorTitles": ["Rate Limiting Concept Verification", "basic rate limiting functionality"], "duration": 7, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Rate Limiting Concept Verification basic rate limiting functionality should work with high concurrency limits", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865020251, "status": "passed", "title": "should work with high concurrency limits"}, {"ancestorTitles": ["Rate Limiting Concept Verification", "error handling"], "duration": 27, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Rate Limiting Concept Verification error handling should handle task errors without breaking the limiter", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865020259, "status": "passed", "title": "should handle task errors without breaking the limiter"}, {"ancestorTitles": ["Rate Limiting Concept Verification", "search tools integration concept"], "duration": 29, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Rate Limiting Concept Verification search tools integration concept should demonstrate how rate limiting would work in search context", "invocations": 1, "location": null, "numPassingAsserts": 25, "retryReasons": [], "startAt": 1752865020286, "status": "passed", "title": "should demonstrate how rate limiting would work in search context"}, {"ancestorTitles": ["Rate Limiting Summary"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Rate Limiting Summary should document rate limiting behavior for search tools", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020316, "status": "passed", "title": "should document rate limiting behavior for search tools"}, {"ancestorTitles": ["Rate limiter additional coverage"], "duration": 12, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Rate limiter additional coverage should preserve FIFO order when limit < queue length", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020318, "status": "passed", "title": "should preserve FIFO order when limit < queue length"}, {"ancestorTitles": ["Rate limiter additional coverage"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Rate limiter additional coverage should handle synchronous task functions", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020330, "status": "passed", "title": "should handle synchronous task functions"}, {"ancestorTitles": ["Rate limiter additional coverage"], "duration": 37, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Rate limiter additional coverage should correctly decrement counters when task throws synchronously", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020331, "status": "passed", "title": "should correctly decrement counters when task throws synchronously"}, {"ancestorTitles": ["Rate limiter additional coverage"], "duration": 723, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Rate limiter additional coverage should never exceed concurrency under heavy load", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020369, "status": "passed", "title": "should never exceed concurrency under heavy load"}, {"ancestorTitles": ["Rate limiter additional coverage"], "duration": 7, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Rate limiter additional coverage should throw for zero or negative limits", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021092, "status": "passed", "title": "should throw for zero or negative limits"}], "endTime": 1752865021100, "message": "", "name": "/Users/<USER>/Desktop/github/next13-clarify/__tests__/dragTree/utils/plimit-rate-limiting.test.ts", "startTime": 1752865019647, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["AI Chat Validation Utilities", "validateMessageRole"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Validation Utilities validateMessageRole should validate correct message roles", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "startAt": 1752865021058, "status": "passed", "title": "should validate correct message roles"}, {"ancestorTitles": ["AI Chat Validation Utilities", "validateMessageRole"], "duration": 6, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Validation Utilities validateMessageRole should throw error for invalid roles", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865021060, "status": "passed", "title": "should throw error for invalid roles"}, {"ancestorTitles": ["AI Chat Validation Utilities", "assertAiIdPrefix"], "duration": 13, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Validation Utilities assertAiIdPrefix should validate correct ID formats", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "startAt": 1752865021066, "status": "passed", "title": "should validate correct ID formats"}, {"ancestorTitles": ["AI Chat Validation Utilities", "assertAiIdPrefix"], "duration": 2, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Validation Utilities assertAiIdPrefix should throw error for invalid ID formats", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "startAt": 1752865021087, "status": "passed", "title": "should throw error for invalid ID formats"}, {"ancestorTitles": ["AI Chat Validation Utilities", "assertAiIdPrefix"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Validation Utilities assertAiIdPrefix should validate non-string IDs", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865021090, "status": "passed", "title": "should validate non-string IDs"}, {"ancestorTitles": ["AI Chat Validation Utilities", "ID validation functions"], "duration": 2, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Validation Utilities ID validation functions should validate conversation IDs", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021091, "status": "passed", "title": "should validate conversation IDs"}, {"ancestorTitles": ["AI Chat Validation Utilities", "ID validation functions"], "duration": 2, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Validation Utilities ID validation functions should validate message IDs", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021093, "status": "passed", "title": "should validate message IDs"}, {"ancestorTitles": ["AI Chat Validation Utilities", "ID validation functions"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Validation Utilities ID validation functions should validate step IDs", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021095, "status": "passed", "title": "should validate step IDs"}, {"ancestorTitles": ["AI Chat Validation Utilities", "ID validation functions"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Validation Utilities ID validation functions should validate attachment IDs", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021096, "status": "passed", "title": "should validate attachment IDs"}, {"ancestorTitles": ["AI Chat Validation Utilities", "validateStepOrder"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Validation Utilities validateStepOrder should validate correct step orders", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "startAt": 1752865021096, "status": "passed", "title": "should validate correct step orders"}, {"ancestorTitles": ["AI Chat Validation Utilities", "validateStepOrder"], "duration": 21, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Validation Utilities validateStepOrder should throw error for invalid step orders", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "startAt": 1752865021097, "status": "passed", "title": "should throw error for invalid step orders"}, {"ancestorTitles": ["AI Chat Validation Utilities", "validateStepType"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Validation Utilities validateStepType should validate correct step types", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "startAt": 1752865021118, "status": "passed", "title": "should validate correct step types"}, {"ancestorTitles": ["AI Chat Validation Utilities", "validateStepType"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Validation Utilities validateStepType should throw error for invalid step types", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865021118, "status": "passed", "title": "should throw error for invalid step types"}, {"ancestorTitles": ["AI Chat Validation Utilities", "validateAttachmentData"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Validation Utilities validateAttachmentData should validate correct attachment data", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021119, "status": "passed", "title": "should validate correct attachment data"}, {"ancestorTitles": ["AI Chat Validation Utilities", "validateAttachmentData"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Validation Utilities validateAttachmentData should throw error for invalid fileName", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021119, "status": "passed", "title": "should throw error for invalid fileName"}, {"ancestorTitles": ["AI Chat Validation Utilities", "validateAttachmentData"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Validation Utilities validateAttachmentData should throw error for invalid fileType", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021120, "status": "passed", "title": "should throw error for invalid fileType"}, {"ancestorTitles": ["AI Chat Validation Utilities", "validateAttachmentData"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Validation Utilities validateAttachmentData should throw error for invalid fileSize", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865021120, "status": "passed", "title": "should throw error for invalid fileSize"}, {"ancestorTitles": ["AI Chat Validation Utilities", "validateAttachmentData"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Validation Utilities validateAttachmentData should throw error for invalid URL", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865021121, "status": "passed", "title": "should throw error for invalid URL"}, {"ancestorTitles": ["AI Chat Validation Utilities", "validateUserId"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Validation Utilities validateUserId should validate correct user IDs", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021122, "status": "passed", "title": "should validate correct user IDs"}, {"ancestorTitles": ["AI Chat Validation Utilities", "validateUserId"], "duration": 3, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "AI Chat Validation Utilities validateUserId should throw error for invalid user IDs", "invocations": 1, "location": null, "numPassingAsserts": 7, "retryReasons": [], "startAt": 1752865021122, "status": "passed", "title": "should throw error for invalid user IDs"}], "endTime": 1752865021127, "message": "", "name": "/Users/<USER>/Desktop/github/next13-clarify/app/libs/ai-chat/__tests__/validation.test.ts", "startTime": 1752865020876, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Node U<PERSON>", "createNodeMetadata"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Node Utils createNodeMetadata should create metadata with parentId, level, and createdAt", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865021154, "status": "passed", "title": "should create metadata with parentId, level, and createdAt"}, {"ancestorTitles": ["Node U<PERSON>", "createNewTreeNode"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Node Utils createNewTreeNode should create a question node with the correct structure", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "startAt": 1752865021155, "status": "passed", "title": "should create a question node with the correct structure"}, {"ancestorTitles": ["Node U<PERSON>", "createNewTreeNode"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Node Utils createNewTreeNode should create a category node with a default child question", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "startAt": 1752865021155, "status": "passed", "title": "should create a category node with a default child question"}, {"ancestorTitles": ["Node U<PERSON>", "createNewTreeNode"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Node Utils createNewTreeNode should use default labels if none are provided", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021156, "status": "passed", "title": "should use default labels if none are provided"}, {"ancestorTitles": ["Node U<PERSON>", "createNodesFromText"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Node Utils createNodesFromText should create multiple nodes from an array of strings", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865021156, "status": "passed", "title": "should create multiple nodes from an array of strings"}], "endTime": 1752865021157, "message": "", "name": "/Users/<USER>/Desktop/github/next13-clarify/__tests__/dragTree/utils/node-utils.test.ts", "startTime": 1752865020963, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["rateLimiter utility"], "duration": 2, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "rateLimiter utility allows first call in production", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021162, "status": "passed", "title": "allows first call in production"}, {"ancestorTitles": ["rateLimiter utility"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "rateLimiter utility blocks immediate successive call within window in production", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021164, "status": "passed", "title": "blocks immediate successive call within window in production"}, {"ancestorTitles": ["rateLimiter utility"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "rateLimiter utility allows call after window passes", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021164, "status": "passed", "title": "allows call after window passes"}, {"ancestorTitles": ["rateLimiter utility"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "rateLimiter utility returns correct retry-after seconds", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865021165, "status": "passed", "title": "returns correct retry-after seconds"}, {"ancestorTitles": ["rateLimiter utility"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "rateLimiter utility uses shorter window in development environment", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021166, "status": "passed", "title": "uses shorter window in development environment"}], "endTime": 1752865021167, "message": "", "name": "/Users/<USER>/Desktop/github/next13-clarify/__tests__/rateLimiter.test.ts", "startTime": 1752865020980, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Tree Helper Functions", "getExistingQuestions"], "duration": 3, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Helper Functions getExistingQuestions should extract all question labels from direct children", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021155, "status": "passed", "title": "should extract all question labels from direct children"}, {"ancestorTitles": ["Tree Helper Functions", "getExistingQuestions"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Helper Functions getExistingQuestions should return empty array when no questions exist", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021159, "status": "passed", "title": "should return empty array when no questions exist"}, {"ancestorTitles": ["Tree Helper Functions", "getExistingQuestions"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Helper Functions getExistingQuestions should filter out non-question children", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021160, "status": "passed", "title": "should filter out non-question children"}, {"ancestorTitles": ["Tree Helper Functions", "getExistingSubcategories"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Helper Functions getExistingSubcategories should extract all category labels from direct children", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021161, "status": "passed", "title": "should extract all category labels from direct children"}, {"ancestorTitles": ["Tree Helper Functions", "getExistingSubcategories"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Helper Functions getExistingSubcategories should return empty array when no subcategories exist", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021162, "status": "passed", "title": "should return empty array when no subcategories exist"}, {"ancestorTitles": ["Tree Helper Functions", "getExistingSubcategories"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Helper Functions getExistingSubcategories should include nested categories from direct children", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021163, "status": "passed", "title": "should include nested categories from direct children"}, {"ancestorTitles": ["Tree Helper Functions", "treeNodeToMarkdown"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Helper Functions treeNodeToMarkdown should convert tree structure to hierarchical markdown", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "startAt": 1752865021164, "status": "passed", "title": "should convert tree structure to hierarchical markdown"}, {"ancestorTitles": ["Tree Helper Functions", "treeNodeToMarkdown"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Helper Functions treeNodeToMarkdown should handle custom starting level", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865021166, "status": "passed", "title": "should handle custom starting level"}, {"ancestorTitles": ["Tree Helper Functions", "treeNodeToMarkdown"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Helper Functions treeNodeToMarkdown should handle empty tree node", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021167, "status": "passed", "title": "should handle empty tree node"}, {"ancestorTitles": ["Tree Helper Functions", "treeNodeToMarkdown"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Helper Functions treeNodeToMarkdown should maintain proper nesting for deep hierarchies", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "startAt": 1752865021167, "status": "passed", "title": "should maintain proper nesting for deep hierarchies"}, {"ancestorTitles": ["Tree Helper Functions", "getOriginalContextFromTree"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Helper Functions getOriginalContextFromTree should extract project title and main categories", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "startAt": 1752865021168, "status": "passed", "title": "should extract project title and main categories"}, {"ancestorTitles": ["Tree Helper Functions", "getOriginalContextFromTree"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Helper Functions getOriginalContextFromTree should handle tree with no categories", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021168, "status": "passed", "title": "should handle tree with no categories"}, {"ancestorTitles": ["Tree Helper Functions", "parseQuestionsFromMarkdown"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Helper Functions parseQuestionsFromMarkdown should parse bullet point questions", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021170, "status": "passed", "title": "should parse bullet point questions"}, {"ancestorTitles": ["Tree Helper Functions", "parseQuestionsFromMarkdown"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Helper Functions parseQuestionsFromMarkdown should parse markdown header questions (#### level)", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021170, "status": "passed", "title": "should parse markdown header questions (#### level)"}, {"ancestorTitles": ["Tree Helper Functions", "parseQuestionsFromMarkdown"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Helper Functions parseQuestionsFromMarkdown should handle mixed bullet points and headers", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021171, "status": "passed", "title": "should handle mixed bullet points and headers"}, {"ancestorTitles": ["Tree Helper Functions", "parseQuestionsFromMarkdown"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Helper Functions parseQuestionsFromMarkdown should return empty array for markdown without questions", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021171, "status": "passed", "title": "should return empty array for markdown without questions"}, {"ancestorTitles": ["Tree Helper Functions", "parseSubtreeFromMarkdown"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Helper Functions parseSubtreeFromMarkdown should parse markdown with categories and questions", "invocations": 1, "location": null, "numPassingAsserts": 9, "retryReasons": [], "startAt": 1752865021171, "status": "passed", "title": "should parse markdown with categories and questions"}, {"ancestorTitles": ["Tree Helper Functions", "parseSubtreeFromMarkdown"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Helper Functions parseSubtreeFromMarkdown should handle questions without categories", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "startAt": 1752865021172, "status": "passed", "title": "should handle questions without categories"}, {"ancestorTitles": ["Tree Helper Functions", "parseSubtreeFromMarkdown"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Helper Functions parseSubtreeFromMarkdown should generate unique IDs for parsed nodes", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865021172, "status": "passed", "title": "should generate unique IDs for parsed nodes"}, {"ancestorTitles": ["Tree Helper Functions", "parseSubtreeFromMarkdown"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Helper Functions parseSubtreeFromMarkdown should handle nested category hierarchies", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "startAt": 1752865021172, "status": "passed", "title": "should handle nested category hierarchies"}], "endTime": 1752865021176, "message": "", "name": "/Users/<USER>/Desktop/github/next13-clarify/__tests__/dragTree/utils/treeHelpers.test.ts", "startTime": 1752865020955, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Database Utils", "reconstructTreeFromDatabase"], "duration": 4, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Database Utils reconstructTreeFromDatabase should correctly reconstruct a tree from database data", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021277, "status": "passed", "title": "should correctly reconstruct a tree from database data"}, {"ancestorTitles": ["Database Utils", "reconstructTreeFromDatabase"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Database Utils reconstructTreeFromDatabase should filter out inactive nodes", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021282, "status": "passed", "title": "should filter out inactive nodes"}, {"ancestorTitles": ["Database Utils", "reconstructTreeFromDatabase"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Database Utils reconstructTreeFromDatabase should return null if tree_structure is missing", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021284, "status": "passed", "title": "should return null if tree_structure is missing"}, {"ancestorTitles": ["Database Utils", "reconstructTreeFromDatabase"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Database Utils reconstructTreeFromDatabase should return null if nodes are missing", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021284, "status": "passed", "title": "should return null if nodes are missing"}, {"ancestorTitles": ["Database Utils", "convertTreeToDbStructure"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Database Utils convertTreeToDbStructure should correctly convert a tree node to database structure", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021284, "status": "passed", "title": "should correctly convert a tree node to database structure"}], "endTime": 1752865021285, "message": "", "name": "/Users/<USER>/Desktop/github/next13-clarify/__tests__/dragTree/utils/database-utils.test.ts", "startTime": 1752865021147, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Prisma Middleware Tree Validation", "Middleware Trigger Conditions"], "duration": 4, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Prisma Middleware Tree Validation Middleware Trigger Conditions should skip validation for non-DragTree models", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021274, "status": "passed", "title": "should skip validation for non-DragTree models"}, {"ancestorTitles": ["Prisma Middleware Tree Validation", "Middleware Trigger Conditions"], "duration": 2, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Prisma Middleware Tree Validation Middleware Trigger Conditions should skip validation for read-only operations", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021278, "status": "passed", "title": "should skip validation for read-only operations"}, {"ancestorTitles": ["Prisma Middleware Tree Validation", "Middleware Trigger Conditions"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Prisma Middleware Tree Validation Middleware Trigger Conditions should trigger validation for DragTree mutations", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021280, "status": "passed", "title": "should trigger validation for DragTree mutations"}, {"ancestorTitles": ["Prisma Middleware Tree Validation", "Tree ID Resolution"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Prisma Middleware Tree Validation Tree ID Resolution should extract treeId from DragTree operations", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021280, "status": "passed", "title": "should extract treeId from DragTree operations"}, {"ancestorTitles": ["Prisma Middleware Tree Validation", "Tree ID Resolution"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Prisma Middleware Tree Validation Tree ID Resolution should extract treeId from DragTreeNode data", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021280, "status": "passed", "title": "should extract treeId from DragTreeNode data"}, {"ancestorTitles": ["Prisma Middleware Tree Validation", "Tree ID Resolution"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Prisma Middleware Tree Validation Tree ID Resolution should lookup treeId from existing node when not in args", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021281, "status": "passed", "title": "should lookup treeId from existing node when not in args"}, {"ancestorTitles": ["Prisma Middleware Tree Validation", "Validation Logic"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Prisma Middleware Tree Validation Validation Logic should pass validation when tree_structure is null/undefined", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021281, "status": "passed", "title": "should pass validation when tree_structure is null/undefined"}, {"ancestorTitles": ["Prisma Middleware Tree Validation", "Validation Logic"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Prisma Middleware Tree Validation Validation Logic should pass validation when IDs perfectly align", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021282, "status": "passed", "title": "should pass validation when IDs perfectly align"}, {"ancestorTitles": ["Prisma Middleware Tree Validation", "Validation Logic"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Prisma Middleware Tree Validation Validation Logic should fail validation when active nodes are missing from tree", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021282, "status": "passed", "title": "should fail validation when active nodes are missing from tree"}, {"ancestorTitles": ["Prisma Middleware Tree Validation", "Validation Logic"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Prisma Middleware Tree Validation Validation Logic should fail validation when tree references missing active nodes", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021282, "status": "passed", "title": "should fail validation when tree references missing active nodes"}, {"ancestorTitles": ["Prisma Middleware Tree Validation", "Edge Cases"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Prisma Middleware Tree Validation Edge Cases should handle empty tree structures", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021287, "status": "passed", "title": "should handle empty tree structures"}, {"ancestorTitles": ["Prisma Middleware Tree Validation", "Edge Cases"], "duration": 15, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Prisma Middleware Tree Validation Edge Cases should handle operations without treeId", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021288, "status": "passed", "title": "should handle operations without treeId"}, {"ancestorTitles": ["Prisma Middleware Tree Validation", "Edge Cases"], "duration": 2, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Prisma Middleware Tree Validation Edge Cases should handle complex hierarchies correctly", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021303, "status": "passed", "title": "should handle complex hierarchies correctly"}], "endTime": 1752865021308, "message": "", "name": "/Users/<USER>/Desktop/github/next13-clarify/__tests__/dragTree/utils/prisma-middleware.test.ts", "startTime": 1752865021097, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Research Hooks Integration", "Hook State Management"], "duration": 2, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Hooks Integration Hook State Management should handle research state transitions", "invocations": 1, "location": null, "numPassingAsserts": 8, "retryReasons": [], "startAt": 1752865021297, "status": "passed", "title": "should handle research state transitions"}, {"ancestorTitles": ["Research Hooks Integration", "Hook State Management"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Hooks Integration Hook State Management should validate research configuration objects", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "startAt": 1752865021299, "status": "passed", "title": "should validate research configuration objects"}, {"ancestorTitles": ["Research Hooks Integration", "Hook State Management"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Hooks Integration Hook State Management should handle external research URL generation", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021299, "status": "passed", "title": "should handle external research URL generation"}, {"ancestorTitles": ["Research Hooks Integration", "Validation Logic"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Hooks Integration Validation Logic should validate research execution conditions", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "startAt": 1752865021300, "status": "passed", "title": "should validate research execution conditions"}, {"ancestorTitles": ["Research Hooks Integration", "Validation Logic"], "duration": 14, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Hooks Integration Validation Logic should handle research option types correctly", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021300, "status": "passed", "title": "should handle research option types correctly"}, {"ancestorTitles": ["Research Hooks Integration", "Erro<PERSON>"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Hooks Integration Error Handling should handle API response structures", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865021314, "status": "passed", "title": "should handle API response structures"}, {"ancestorTitles": ["Research Hooks Integration", "Erro<PERSON>"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Hooks Integration Error Handling should validate content structures", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "startAt": 1752865021314, "status": "passed", "title": "should validate content structures"}, {"ancestorTitles": ["Research Hooks Integration", "State Calculations"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Hooks Integration State Calculations should determine content visibility correctly", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "startAt": 1752865021315, "status": "passed", "title": "should determine content visibility correctly"}, {"ancestorTitles": ["Research Hooks Integration", "State Calculations"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Research Hooks Integration State Calculations should handle content ID resolution", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021315, "status": "passed", "title": "should handle content ID resolution"}], "endTime": 1752865021316, "message": "", "name": "/Users/<USER>/Desktop/github/next13-clarify/__tests__/dragTree/hooks/research-hooks.test.ts", "startTime": 1752865021150, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Tree Utils", "calculateNodeLevel"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Utils calculateNodeLevel should return the correct level for a node", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865021315, "status": "passed", "title": "should return the correct level for a node"}, {"ancestorTitles": ["Tree Utils", "calculateNodeLevel"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Utils calculateNodeLevel should return -1 for a node that does not exist", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021316, "status": "passed", "title": "should return -1 for a node that does not exist"}, {"ancestorTitles": ["Tree Utils", "buildNodeMap"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Utils buildNodeMap should create a map of all nodes in the tree", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "startAt": 1752865021316, "status": "passed", "title": "should create a map of all nodes in the tree"}, {"ancestorTitles": ["Tree Utils", "addNodeToTree"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Utils addNodeToTree should add a new node to the correct parent", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021316, "status": "passed", "title": "should add a new node to the correct parent"}, {"ancestorTitles": ["Tree Utils", "deleteNodeFromTree"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Utils deleteNodeFromTree should delete a node from the tree", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021317, "status": "passed", "title": "should delete a node from the tree"}, {"ancestorTitles": ["Tree Utils", "editNodeInTree"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Utils editNodeInTree should edit the label of a node", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021317, "status": "passed", "title": "should edit the label of a node"}, {"ancestorTitles": ["Tree Utils", "reorderNodeInTree"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Utils reorderNodeInTree should reorder nodes within a parent", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865021318, "status": "passed", "title": "should reorder nodes within a parent"}, {"ancestorTitles": ["Tree Utils", "toggleNodeInterest"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Utils toggleNodeInterest should toggle the isInterestedIn flag of a node", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021318, "status": "passed", "title": "should toggle the isInterestedIn flag of a node"}, {"ancestorTitles": ["Tree Utils", "collectInterestedNodes"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Utils collectInterestedNodes should collect all nodes with isInterestedIn set to true", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865021318, "status": "passed", "title": "should collect all nodes with isInterestedIn set to true"}, {"ancestorTitles": ["Tree Utils", "resetAllInterestInTree"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Utils resetAllInterestInTree should set isInterestedIn to false for all nodes", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021319, "status": "passed", "title": "should set isInterestedIn to false for all nodes"}, {"ancestorTitles": ["Tree Utils", "collectAllNodeIds"], "duration": 2, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Utils collectAllNodeIds should collect all node IDs from the tree", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021320, "status": "passed", "title": "should collect all node IDs from the tree"}], "endTime": 1752865021324, "message": "", "name": "/Users/<USER>/Desktop/github/next13-clarify/__tests__/dragTree/utils/tree-utils.test.ts", "startTime": 1752865021169, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Tree Validation Utilities (Core Logic)", "extractIdsFromTreeStructure"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Validation Utilities (Core Logic) extractIdsFromTreeStructure extracts all IDs from a simple tree structure", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021315, "status": "passed", "title": "extracts all IDs from a simple tree structure"}, {"ancestorTitles": ["Tree Validation Utilities (Core Logic)", "extractIdsFromTreeStructure"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Validation Utilities (Core Logic) extractIdsFromTreeStructure handles deep nested hierarchies correctly", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021317, "status": "passed", "title": "handles deep nested hierarchies correctly"}, {"ancestorTitles": ["Tree Validation Utilities (Core Logic)", "extractIdsFromTreeStructure"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Validation Utilities (Core Logic) extractIdsFromTreeStructure handles single node tree (root only)", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021317, "status": "passed", "title": "handles single node tree (root only)"}, {"ancestorTitles": ["Tree Validation Utilities (Core Logic)", "extractIdsFromTreeStructure"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Validation Utilities (Core Logic) extractIdsFromTreeStructure prevents infinite loops with circular references", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021317, "status": "passed", "title": "prevents infinite loops with circular references"}, {"ancestorTitles": ["Tree Validation Utilities (Core Logic)", "extractIdsFromTreeStructure"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Validation Utilities (Core Logic) extractIdsFromTreeStructure returns empty set for invalid input types", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "startAt": 1752865021317, "status": "passed", "title": "returns empty set for invalid input types"}, {"ancestorTitles": ["Tree Validation Utilities (Core Logic)", "extractIdsFromTreeStructure"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Validation Utilities (Core Logic) extractIdsFromTreeStructure returns empty set when root_id is missing", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021318, "status": "passed", "title": "returns empty set when root_id is missing"}, {"ancestorTitles": ["Tree Validation Utilities (Core Logic)", "extractIdsFromTreeStructure"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Validation Utilities (Core Logic) extractIdsFromTreeStructure returns empty set when hierarchy is missing", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021318, "status": "passed", "title": "returns empty set when hierarchy is missing"}, {"ancestorTitles": ["Tree Validation Utilities (Core Logic)", "extractIdsFromTreeStructure"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Validation Utilities (Core Logic) extractIdsFromTreeStructure handles missing children arrays gracefully", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021318, "status": "passed", "title": "handles missing children arrays gracefully"}, {"ancestorTitles": ["Tree Validation Utilities (Core Logic)", "validateTreeAlignmentRaw"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Validation Utilities (Core Logic) validateTreeAlignmentRaw validates TRUE when tree IDs exactly match active IDs", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021319, "status": "passed", "title": "validates TRUE when tree IDs exactly match active IDs"}, {"ancestorTitles": ["Tree Validation Utilities (Core Logic)", "validateTreeAlignmentRaw"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Validation Utilities (Core Logic) validateTreeAlignmentRaw validates FALSE when active IDs are missing from tree", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021319, "status": "passed", "title": "validates FALSE when active IDs are missing from tree"}, {"ancestorTitles": ["Tree Validation Utilities (Core Logic)", "validateTreeAlignmentRaw"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Validation Utilities (Core Logic) validateTreeAlignmentRaw validates FALSE when active IDs contain extra nodes not in tree", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021319, "status": "passed", "title": "validates FALSE when active IDs contain extra nodes not in tree"}, {"ancestorTitles": ["Tree Validation Utilities (Core Logic)", "validateTreeAlignmentRaw"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Validation Utilities (Core Logic) validateTreeAlignmentRaw validates TRUE for empty tree and empty active set", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021319, "status": "passed", "title": "validates TRUE for empty tree and empty active set"}, {"ancestorTitles": ["Tree Validation Utilities (Core Logic)", "validateTreeAlignmentRaw"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Validation Utilities (Core Logic) validateTreeAlignmentRaw validates FALSE when sizes differ (more in tree than active)", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021319, "status": "passed", "title": "validates FALSE when sizes differ (more in tree than active)"}, {"ancestorTitles": ["Tree Validation Utilities (Core Logic)", "validateTreeAlignmentRaw"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Validation Utilities (Core Logic) validateTreeAlignmentRaw validates FALSE when sizes differ (more active than in tree)", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021320, "status": "passed", "title": "validates FALSE when sizes differ (more active than in tree)"}, {"ancestorTitles": ["Tree Validation Utilities (Core Logic)", "validateTreeAlignmentRaw"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Validation Utilities (Core Logic) validateTreeAlignmentRaw handles complex tree structures correctly", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021320, "status": "passed", "title": "handles complex tree structures correctly"}, {"ancestorTitles": ["Tree Validation Utilities (Core Logic)", "Integration Tests - Prisma Middleware Logic"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Validation Utilities (Core Logic) Integration Tests - Prisma Middleware Logic mimics middleware validation for valid tree-node alignment", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021320, "status": "passed", "title": "mimics middleware validation for valid tree-node alignment"}, {"ancestorTitles": ["Tree Validation Utilities (Core Logic)", "Integration Tests - Prisma Middleware Logic"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Tree Validation Utilities (Core Logic) Integration Tests - Prisma Middleware Logic detects misalignment that would trigger middleware error", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021320, "status": "passed", "title": "detects misalignment that would trigger middleware error"}], "endTime": 1752865021322, "message": "", "name": "/Users/<USER>/Desktop/github/next13-clarify/__tests__/dragTree/utils/tree-validation.test.ts", "startTime": 1752865021177, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["ID Generation Utilities", "generateIdWithPrefix"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ID Generation Utilities generateIdWithPrefix should generate IDs with correct prefix format", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021359, "status": "passed", "title": "should generate IDs with correct prefix format"}, {"ancestorTitles": ["ID Generation Utilities", "generateIdWithPrefix"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ID Generation Utilities generateIdWithPrefix should generate IDs with custom length parameter", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021360, "status": "passed", "title": "should generate IDs with custom length parameter"}, {"ancestorTitles": ["ID Generation Utilities", "generateIdWithPrefix"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ID Generation Utilities generateIdWithPrefix should handle different prefixes correctly", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865021360, "status": "passed", "title": "should handle different prefixes correctly"}, {"ancestorTitles": ["ID Generation Utilities", "generateIdWithPrefix"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ID Generation Utilities generateIdWithPrefix should only contain valid characters in the random part", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021361, "status": "passed", "title": "should only contain valid characters in the random part"}, {"ancestorTitles": ["ID Generation Utilities", "generateDeterministicId"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ID Generation Utilities generateDeterministicId should generate same ID for same inputs", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021361, "status": "passed", "title": "should generate same ID for same inputs"}, {"ancestorTitles": ["ID Generation Utilities", "generateDeterministicId"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ID Generation Utilities generateDeterministicId should include node type in hash when provided", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021361, "status": "passed", "title": "should include node type in hash when provided"}, {"ancestorTitles": ["ID Generation Utilities", "generateDeterministicId"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ID Generation Utilities generateDeterministicId should respect custom hash length", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021362, "status": "passed", "title": "should respect custom hash length"}, {"ancestorTitles": ["ID Generation Utilities", "generateDeterministicId"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ID Generation Utilities generateDeterministicId should trim whitespace from labels", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021362, "status": "passed", "title": "should trim whitespace from labels"}, {"ancestorTitles": ["ID Generation Utilities", "generateDeterministicId"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ID Generation Utilities generateDeterministicId should handle empty node type gracefully", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021362, "status": "passed", "title": "should handle empty node type gracefully"}, {"ancestorTitles": ["ID Generation Utilities", "generateUniqueNodeId"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ID Generation Utilities generateUniqueNodeId should use deterministic approach for named labels", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021362, "status": "passed", "title": "should use deterministic approach for named labels"}, {"ancestorTitles": ["ID Generation Utilities", "generateUniqueNodeId"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ID Generation Utilities generateUniqueNodeId should handle different hash lengths", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021363, "status": "passed", "title": "should handle different hash lengths"}, {"ancestorTitles": ["ID Generation Utilities", "generateDragTreeId"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ID Generation Utilities generateDragTreeId should generate drag tree IDs with correct prefix", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021363, "status": "passed", "title": "should generate drag tree IDs with correct prefix"}, {"ancestorTitles": ["ID Generation Utilities", "generateDragTreeNodeId"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ID Generation Utilities generateDragTreeNodeId should generate category node IDs with correct prefix", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021363, "status": "passed", "title": "should generate category node IDs with correct prefix"}, {"ancestorTitles": ["ID Generation Utilities", "generateDragTreeNodeId"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ID Generation Utilities generateDragTreeNodeId should generate question node IDs with correct prefix", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021363, "status": "passed", "title": "should generate question node IDs with correct prefix"}, {"ancestorTitles": ["ID Generation Utilities", "generateDragTreeNodeId"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ID Generation Utilities generateDragTreeNodeId should be deterministic for normal labels", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021363, "status": "passed", "title": "should be deterministic for normal labels"}, {"ancestorTitles": ["ID Generation Utilities", "generateDragTreeNodeId"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ID Generation Utilities generateDragTreeNodeId should respect custom hash length", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021364, "status": "passed", "title": "should respect custom hash length"}, {"ancestorTitles": ["ID Generation Utilities", "Hash Length Constants"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ID Generation Utilities Hash Length Constants should have expected hash length values", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "startAt": 1752865021364, "status": "passed", "title": "should have expected hash length values"}, {"ancestorTitles": ["ID Generation Utilities", "DragTreeIdPrefix Enum"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ID Generation Utilities DragTreeIdPrefix Enum should have expected prefix values", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "startAt": 1752865021364, "status": "passed", "title": "should have expected prefix values"}, {"ancestorTitles": ["ID Generation Utilities", "Edge Cases"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ID Generation Utilities Edge Cases should handle empty strings gracefully", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021365, "status": "passed", "title": "should handle empty strings gracefully"}, {"ancestorTitles": ["ID Generation Utilities", "Edge Cases"], "duration": 2, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ID Generation Utilities Edge Cases should handle very long labels", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021365, "status": "passed", "title": "should handle very long labels"}, {"ancestorTitles": ["ID Generation Utilities", "Edge Cases"], "duration": 1, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ID Generation Utilities Edge Cases should handle special characters in labels", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021367, "status": "passed", "title": "should handle special characters in labels"}, {"ancestorTitles": ["ID Generation Utilities", "Edge Cases"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ID Generation Utilities Edge Cases should handle unicode characters in labels", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021368, "status": "passed", "title": "should handle unicode characters in labels"}, {"ancestorTitles": ["ID Generation Utilities", "Edge Cases"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ID Generation Utilities Edge Cases should generate valid IDs for different tree IDs", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865021368, "status": "passed", "title": "should generate valid IDs for different tree IDs"}, {"ancestorTitles": ["ID Generation Utilities", "Edge Cases"], "duration": 0, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ID Generation Utilities Edge Cases should handle null and undefined inputs gracefully", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021368, "status": "passed", "title": "should handle null and undefined inputs gracefully"}], "endTime": 1752865021369, "message": "", "name": "/Users/<USER>/Desktop/github/next13-clarify/__tests__/dragTree/utils/id-generation.test.ts", "startTime": 1752865021191, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["ResearchButtonUI", "Basic Rendering"], "duration": 134, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchButtonUI Basic Rendering should render the research button", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020373, "status": "passed", "title": "should render the research button"}, {"ancestorTitles": ["ResearchButtonUI", "Basic Rendering"], "duration": 13, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchButtonUI Basic Rendering should show streaming state when isStreaming is true", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020508, "status": "passed", "title": "should show streaming state when isStreaming is true"}, {"ancestorTitles": ["ResearchButtonUI", "Basic Rendering"], "duration": 12, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchButtonUI Basic Rendering should apply correct styling for reactflow variant", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020522, "status": "passed", "title": "should apply correct styling for reactflow variant"}, {"ancestorTitles": ["ResearchButtonUI", "Basic Rendering"], "duration": 7, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchButtonUI Basic Rendering should apply correct styling for treeview variant", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020535, "status": "passed", "title": "should apply correct styling for treeview variant"}, {"ancestorTitles": ["ResearchButtonUI", "Dropdown Menu Behavior"], "duration": 186, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchButtonUI Dropdown Menu Behavior should open dropdown menu when button is clicked", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865020542, "status": "passed", "title": "should open dropdown menu when button is clicked"}, {"ancestorTitles": ["ResearchButtonUI", "Dropdown Menu Behavior"], "duration": 78, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchButtonUI Dropdown Menu Behavior should show disabled state for Clarify option when content exists", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865020728, "status": "passed", "title": "should show disabled state for Clarify option when content exists"}, {"ancestorTitles": ["ResearchButtonUI", "Dropdown Menu Behavior"], "duration": 88, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchButtonUI Dropdown Menu Behavior should show streaming state for Clarify option when streaming", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865020806, "status": "passed", "title": "should show streaming state for Clarify option when streaming"}, {"ancestorTitles": ["ResearchButtonUI", "Research Option Selection"], "duration": 98, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchButtonUI Research Option Selection should execute internal research for Quick Research option", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865020894, "status": "passed", "title": "should execute internal research for Quick Research option"}, {"ancestorTitles": ["ResearchButtonUI", "Research Option Selection"], "duration": 145, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchButtonUI Research Option Selection should show external confirmation dialog for external options", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865020992, "status": "passed", "title": "should show external confirmation dialog for external options"}, {"ancestorTitles": ["ResearchButtonUI", "Research Option Selection"], "duration": 181, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchButtonUI Research Option Selection should execute external research when confirmed", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021137, "status": "passed", "title": "should execute external research when confirmed"}, {"ancestorTitles": ["ResearchButtonUI", "Research Option Selection"], "duration": 75, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchButtonUI Research Option Selection should cancel external research when cancelled", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "startAt": 1752865021318, "status": "passed", "title": "should cancel external research when cancelled"}, {"ancestorTitles": ["ResearchButtonUI", "Research Option Selection"], "duration": 44, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchButtonUI Research Option Selection should show error toast if validation fails", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021393, "status": "passed", "title": "should show error toast if validation fails"}, {"ancestorTitles": ["ResearchButtonUI", "Research Option Selection"], "duration": 42, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchButtonUI Research Option Selection should handle research execution failure", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "startAt": 1752865021437, "status": "passed", "title": "should handle research execution failure"}, {"ancestorTitles": ["ResearchButtonUI", "Hover Events"], "duration": 4, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchButtonUI Hover Events should call onMouseEnter when mouse enters the button area", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021479, "status": "passed", "title": "should call on<PERSON>ouse<PERSON>nter when mouse enters the button area"}, {"ancestorTitles": ["ResearchButtonUI", "Hover Events"], "duration": 3, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "ResearchButtonUI Hover Events should call onMouseLeave when mouse leaves the button area", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752865021483, "status": "passed", "title": "should call on<PERSON>ouse<PERSON><PERSON><PERSON> when mouse leaves the button area"}], "endTime": 1752865021487, "message": "", "name": "/Users/<USER>/Desktop/github/next13-clarify/__tests__/dragTree/components/ResearchButtonUI.test.tsx", "startTime": 1752865019631, "status": "passed", "summary": ""}], "wasInterrupted": false}