'use client'

import { useEffect } from 'react'
import { useSearchParams } from 'next/navigation'

/**
 * UTM capture component mounted globally in the app layout.
 * - Captures only a safe allowlist of marketing parameters from the URL
 * - Stores them as a short-lived cookie until we can persist to DB post-auth
 * - Keeps the latest (last-touch) values in the cookie
 *
 * Server action will persist both utm_first and utm_last on first auth,
 * and subsequently update utm_last when new UTMs appear for returning users.
 */

const ALLOWED_KEYS = [
  'utm_source',
  'utm_medium',
  'utm_campaign',
  'utm_term',
  'utm_content',
  'utm_id',
  // Common ad click identifiers
  'gclid',
  'fbclid',
  'msclkid',
] as const

type AllowedKey = (typeof ALLOWED_KEYS)[number]

export type CapturedUTMParams = Partial<Record<AllowedKey, string>> & {
  landing_url?: string
  captured_at?: string
}

const COOKIE_NAME = '__utm'
const COOKIE_MAX_AGE_SECONDS = 60 * 60 * 24 * 90 // 90 days

function setCookie(name: string, value: string, maxAgeSeconds: number): void {
  const secure =
    typeof window !== 'undefined' && window.location.protocol === 'https:'
  document.cookie = [
    `${name}=${encodeURIComponent(value)}`,
    `Max-Age=${maxAgeSeconds}`,
    'Path=/',
    'SameSite=Lax',
    secure ? 'Secure' : '',
  ]
    .filter(Boolean)
    .join('; ')
}

export default function UTMCaptureClient(): null {
  const searchParams = useSearchParams()

  useEffect(() => {
    if (!searchParams) return

    // Build an allowlisted UTM object from the current URL
    const utm: CapturedUTMParams = {}
    let found = false

    ALLOWED_KEYS.forEach(key => {
      const v = searchParams.get(key)
      if (v) {
        utm[key] = v
        found = true
      }
    })

    if (!found) return

    // Always record the current landing URL and timestamp
    utm.landing_url =
      typeof window !== 'undefined' ? window.location.href : undefined
    utm.captured_at = new Date().toISOString()

    // Keep last-touch in cookie: overwrite any previous cookie with new values
    setCookie(COOKIE_NAME, JSON.stringify(utm), COOKIE_MAX_AGE_SECONDS)
  }, [searchParams])

  return null
}
