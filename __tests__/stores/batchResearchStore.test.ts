/**
 * Unit tests for batchResearchStore - Persistent batch research state management
 *
 * Tests core batch research store functionality including:
 * - Selection persistence across dragTree IDs
 * - Set serialization/deserialization for localStorage
 * - Multiple concurrent dragTree support
 * - Clear selections functionality
 * - Coach mode state management
 * - Tree expansion state management
 */

import { act, renderHook } from '@testing-library/react'
import { useBatchResearchStore } from '@/app/stores/batchResearchStore'

describe('useBatchResearchStore', () => {
  const testDragTreeId1 = 'dragTree_test123'
  const testDragTreeId2 = 'dragTree_test456'
  const testNodeIds = ['node1', 'node2', 'node3']

  beforeEach(() => {
    // Reset store state before each test
    act(() => {
      const store = useBatchResearchStore.getState()
      // Clear all selections for test dragTrees
      store.clearSelections(testDragTreeId1)
      store.clearSelections(testDragTreeId2)
      // Reset other state
      store.setCoachModeOpen(testDragTreeId1, false)
      store.setCoachModeOpen(testDragTreeId2, false)
      store.setCurrentQuestionId(testDragTreeId1, null)
      store.setCurrentQuestionId(testDragTreeId2, null)
    })
  })

  describe('Selection Management', () => {
    it('should toggle selections correctly', () => {
      const { result } = renderHook(() => useBatchResearchStore())

      // Initially no selections
      expect(result.current.getSelections(testDragTreeId1).size).toBe(0)

      // Toggle selection on
      act(() => {
        result.current.toggleSelection(testDragTreeId1, 'node1')
      })

      expect(result.current.getSelections(testDragTreeId1).has('node1')).toBe(
        true
      )
      expect(result.current.getSelections(testDragTreeId1).size).toBe(1)

      // Toggle selection off
      act(() => {
        result.current.toggleSelection(testDragTreeId1, 'node1')
      })

      expect(result.current.getSelections(testDragTreeId1).has('node1')).toBe(
        false
      )
      expect(result.current.getSelections(testDragTreeId1).size).toBe(0)
    })

    it('should set multiple selections at once', () => {
      const { result } = renderHook(() => useBatchResearchStore())

      const selectionsSet = new Set(['node1', 'node2', 'node3'])

      act(() => {
        result.current.setSelections(testDragTreeId1, selectionsSet)
      })

      const retrievedSelections = result.current.getSelections(testDragTreeId1)
      expect(retrievedSelections.size).toBe(3)
      expect(retrievedSelections.has('node1')).toBe(true)
      expect(retrievedSelections.has('node2')).toBe(true)
      expect(retrievedSelections.has('node3')).toBe(true)
    })

    it('should clear all selections', () => {
      const { result } = renderHook(() => useBatchResearchStore())

      // First add some selections
      act(() => {
        result.current.setSelections(testDragTreeId1, new Set(testNodeIds))
      })

      expect(result.current.getSelections(testDragTreeId1).size).toBe(3)

      // Clear selections
      act(() => {
        result.current.clearSelections(testDragTreeId1)
      })

      expect(result.current.getSelections(testDragTreeId1).size).toBe(0)
    })

    it('should select all provided node IDs', () => {
      const { result } = renderHook(() => useBatchResearchStore())

      act(() => {
        result.current.selectAll(testDragTreeId1, testNodeIds)
      })

      const selections = result.current.getSelections(testDragTreeId1)
      expect(selections.size).toBe(3)
      testNodeIds.forEach(nodeId => {
        expect(selections.has(nodeId)).toBe(true)
      })
    })
  })

  describe('Multi-DragTree Isolation', () => {
    it('should maintain separate selections for different dragTrees', () => {
      const { result } = renderHook(() => useBatchResearchStore())

      // Add selections to first dragTree
      act(() => {
        result.current.toggleSelection(testDragTreeId1, 'node1')
        result.current.toggleSelection(testDragTreeId1, 'node2')
      })

      // Add different selections to second dragTree
      act(() => {
        result.current.toggleSelection(testDragTreeId2, 'node3')
      })

      // Verify isolation
      const selections1 = result.current.getSelections(testDragTreeId1)
      const selections2 = result.current.getSelections(testDragTreeId2)

      expect(selections1.size).toBe(2)
      expect(selections1.has('node1')).toBe(true)
      expect(selections1.has('node2')).toBe(true)
      expect(selections1.has('node3')).toBe(false)

      expect(selections2.size).toBe(1)
      expect(selections2.has('node1')).toBe(false)
      expect(selections2.has('node2')).toBe(false)
      expect(selections2.has('node3')).toBe(true)
    })

    it('should clear selections only for specified dragTree', () => {
      const { result } = renderHook(() => useBatchResearchStore())

      // Add selections to both dragTrees
      act(() => {
        result.current.setSelections(
          testDragTreeId1,
          new Set(['node1', 'node2'])
        )
        result.current.setSelections(testDragTreeId2, new Set(['node3']))
      })

      // Clear only first dragTree
      act(() => {
        result.current.clearSelections(testDragTreeId1)
      })

      // Verify only first dragTree was cleared
      expect(result.current.getSelections(testDragTreeId1).size).toBe(0)
      expect(result.current.getSelections(testDragTreeId2).size).toBe(1)
      expect(result.current.getSelections(testDragTreeId2).has('node3')).toBe(
        true
      )
    })
  })

  describe('Tree Expansion State', () => {
    it('should manage expanded nodes correctly', () => {
      const { result } = renderHook(() => useBatchResearchStore())

      // Initially no expanded nodes
      expect(result.current.getExpandedNodes(testDragTreeId1).size).toBe(0)

      // Toggle expansion
      act(() => {
        result.current.toggleExpanded(testDragTreeId1, 'category1')
      })

      expect(
        result.current.getExpandedNodes(testDragTreeId1).has('category1')
      ).toBe(true)

      // Toggle again to collapse
      act(() => {
        result.current.toggleExpanded(testDragTreeId1, 'category1')
      })

      expect(
        result.current.getExpandedNodes(testDragTreeId1).has('category1')
      ).toBe(false)
    })

    it('should set multiple expanded nodes at once', () => {
      const { result } = renderHook(() => useBatchResearchStore())

      const expandedSet = new Set(['category1', 'category2'])

      act(() => {
        result.current.setExpandedNodes(testDragTreeId1, expandedSet)
      })

      const retrievedExpanded = result.current.getExpandedNodes(testDragTreeId1)
      expect(retrievedExpanded.size).toBe(2)
      expect(retrievedExpanded.has('category1')).toBe(true)
      expect(retrievedExpanded.has('category2')).toBe(true)
    })
  })

  describe('Coach Mode State', () => {
    it('should manage coach mode state per dragTree', () => {
      const { result } = renderHook(() => useBatchResearchStore())

      // Initially coach mode is off
      expect(result.current.isCoachModeOpen(testDragTreeId1)).toBe(false)

      // Enable coach mode
      act(() => {
        result.current.setCoachModeOpen(testDragTreeId1, true)
      })

      expect(result.current.isCoachModeOpen(testDragTreeId1)).toBe(true)

      // Disable coach mode
      act(() => {
        result.current.setCoachModeOpen(testDragTreeId1, false)
      })

      expect(result.current.isCoachModeOpen(testDragTreeId1)).toBe(false)
    })

    it('should manage current question ID per dragTree', () => {
      const { result } = renderHook(() => useBatchResearchStore())

      // Initially no current question
      expect(result.current.getCurrentQuestionId(testDragTreeId1)).toBe(null)

      // Set current question
      act(() => {
        result.current.setCurrentQuestionId(testDragTreeId1, 'question1')
      })

      expect(result.current.getCurrentQuestionId(testDragTreeId1)).toBe(
        'question1'
      )

      // Clear current question
      act(() => {
        result.current.setCurrentQuestionId(testDragTreeId1, null)
      })

      expect(result.current.getCurrentQuestionId(testDragTreeId1)).toBe(null)
    })

    it('should isolate coach mode state between dragTrees', () => {
      const { result } = renderHook(() => useBatchResearchStore())

      // Enable coach mode for first dragTree only
      act(() => {
        result.current.setCoachModeOpen(testDragTreeId1, true)
        result.current.setCurrentQuestionId(testDragTreeId1, 'question1')
      })

      // Verify isolation
      expect(result.current.isCoachModeOpen(testDragTreeId1)).toBe(true)
      expect(result.current.isCoachModeOpen(testDragTreeId2)).toBe(false)
      expect(result.current.getCurrentQuestionId(testDragTreeId1)).toBe(
        'question1'
      )
      expect(result.current.getCurrentQuestionId(testDragTreeId2)).toBe(null)
    })
  })

  describe('Set Serialization', () => {
    it('should handle empty selections correctly', () => {
      const { result } = renderHook(() => useBatchResearchStore())

      // Empty selections should return empty Set
      const emptySelections = result.current.getSelections('nonexistent_tree')
      expect(emptySelections).toBeInstanceOf(Set)
      expect(emptySelections.size).toBe(0)
    })

    it('should handle empty expanded nodes correctly', () => {
      const { result } = renderHook(() => useBatchResearchStore())

      // Empty expanded nodes should return empty Set
      const emptyExpanded = result.current.getExpandedNodes('nonexistent_tree')
      expect(emptyExpanded).toBeInstanceOf(Set)
      expect(emptyExpanded.size).toBe(0)
    })
  })

  describe('Edge Cases', () => {
    it('should handle undefined dragTreeId gracefully', () => {
      const { result } = renderHook(() => useBatchResearchStore())

      // Should not throw errors with undefined dragTreeId
      expect(() => {
        result.current.getSelections('')
        result.current.getExpandedNodes('')
        result.current.isCoachModeOpen('')
        result.current.getCurrentQuestionId('')
      }).not.toThrow()
    })

    it('should handle duplicate selections correctly', () => {
      const { result } = renderHook(() => useBatchResearchStore())

      // Add same selection multiple times (odd number = selected)
      act(() => {
        result.current.toggleSelection(testDragTreeId1, 'node1') // on
        result.current.toggleSelection(testDragTreeId1, 'node1') // off
        result.current.toggleSelection(testDragTreeId1, 'node1') // on
      })

      // Should be selected after odd number of toggles
      expect(result.current.getSelections(testDragTreeId1).has('node1')).toBe(
        true
      )
      expect(result.current.getSelections(testDragTreeId1).size).toBe(1)
    })
  })
})
