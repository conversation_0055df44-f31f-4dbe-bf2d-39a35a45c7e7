'use client'

import React from 'react'
import { UpgradeHint } from '@/app/components/upgrade'

type UpgradeNoticeProps = {
  message: string
  href?: string
  className?: string
}

/**
 * @deprecated Use UpgradeHint from @/app/components/upgrade instead
 * This component is kept for backward compatibility but will be removed
 */
export const UpgradeNotice: React.FC<UpgradeNoticeProps> = ({
  message,
  href = '/subscription',
  className,
}) => {
  // Use the new UpgradeHint component with emerald-teal styling
  return (
    <UpgradeHint
      message={message}
      context="feature-gate"
      className={className}
      onClick={() => {
        window.location.href = href
      }}
    />
  )
}

export default UpgradeNotice
