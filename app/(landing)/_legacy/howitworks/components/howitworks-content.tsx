// "use client";

// import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
// import But<PERSON> from "@/app/components/Button";
// import { toast } from "react-hot-toast";

// const illustratedPrompts = [
//   {
//     name: "Product requirement doc",
//     description:
//       "Ex: Create a requirements doc for homepage and success metrics",
//     context:
//       " We are xxx company specialized in...the reason to make this new homepage is due to...we want to create a homepage that can...and the success metrics are...",
//   },
//   {
//     name: "Marketing campaign brief",
//     description:
//       "Ex: Create a marketing campaign brief for email campaign and list out action items",
//     context:
//       " We are in... xxx industry, this email campaign is initialized by... hence we want to test out this channel for that specific audience group...currently we are using xyz system to send email...",
//   },
//   {
//     name: "Software design spec",
//     description:
//       "Ex: Think step by step and create the design doc for NLP chatbot as detailed as possible with some starter code",
//     context:
//       " We want to build chatbot in our xxx product, the rationale is... hence we want to add this feature to improve......currently we are using xyz tech stack...",
//   },
// ];

// export const HowItWorksContent = () => {
//   const handleCopyText = (text: string) => {
//     if (text) {
//       navigator.clipboard.writeText(text).then(
//         () => {
//           toast.success("Copied to clipboard!");
//         },
//         (err) => {
//           toast.error("Could not copy text: ", err);
//         }
//       );
//     }
//   };
//   const handleCopyExamplePrompt = () => {
//     handleCopyText(
//       "I want you to based on this context give the step by step execution plan for my planning home gym, context below: {The user wants to build a small house in the backyard, around 5x3 meters in size, to use as a personal gym for working out at home. The small house should include a full-length mirror for posture correction. An AC should be installed to maintain a temperature of around 22-23 degrees Celsius during workouts. There is no specific brand preference for the AC, but it should be safe and energy efficient. The small house must have a backsquat rack and a bench press similar to those found in a gym, with a 45lb bar and the ability to support different weights totaling 300+ lb. The bench press should have safety supports on both sides. The design of the small house should be simple, with bright lighting that is bright enough, preferably white light. The material used should absorb noise, especially when weights are dropped, so a flooring material similar to what is found in a gym is desired. The flooring should be black in color, similar to carpet, with smaller pieces that can be customized to fit the size of the small house. Additionally, a sound system is desired, allowing the user to play music from their iPhone via Bluetooth.}"
//     );
//     // Add a delay before opening the new window
//     setTimeout(() => {
//       window.open("https://chat.openai.com/", "_blank");
//     }, 2000); // delay in milliseconds, 1000 ms = 1 second
//   };

//   return (
//     <>
//       {/* First section: problem */}
//       <div className="px-10 pb-20">
//         <h2 className="text-center text-6xl text-white font-extrabold mb-10 pt-16">
//           This is about how I think the product
//         </h2>
//         <h2 className="text-center text-4xl text-white font-extrabold mb-4 pb-4">
//           Some tech jargons will be used and more wordy
//         </h2>
//         <h2 className="text-center text-2xl text-white font-extrabold mb-10 pb-16 underline">
//           Skipping this page is fine
//         </h2>
//         <h2 className="text-center text-4xl text-white font-extrabold mb-10 underline">
//           Problem
//         </h2>
//         <h2 className="text-center text-4xl text-white font-extrabold mb-10">
//           ChatGPT is awesome, but there is a problem
//         </h2>
//         <h2 className="text-center text-4xl text-white font-extrabold mb-10">
//           A simple prompt alone does not work 🙅
//         </h2>
//         <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-3">
//           {illustratedPrompts.map((item) => (
//             <Card
//               key={item.description}
//               className="bg-[#192339] border-none text-white"
//             >
//               <CardHeader>
//                 <CardTitle className="flex items-center gap-x-2">
//                   <div>
//                     <p className="text-lg">{item.name}</p>
//                     {/* <p className="text-zinc-400 text-sm">{item.title}</p> */}
//                   </div>
//                 </CardTitle>
//                 <CardContent className="pt-4 px-0 text-zinc-300">
//                   {item.description}
//                 </CardContent>
//               </CardHeader>
//             </Card>
//           ))}
//         </div>
//         <h2 className="text-center text-2xl text-gray-300 my-5">
//           Outputs are <strong>truly impressive but USELESS</strong>, because
//           they are <strong>irrelevant</strong> to your needs 🫤
//           <br />
//           BTW they are good for brainstorming
//         </h2>
//       </div>

//       {/* Second section: common solution */}
//       <div className="px-10 pb-20">
//         <h2 className="text-center text-4xl text-white font-extrabold mb-10 underline">
//           Garbage questions -{">"} Garbage answers
//         </h2>
//         <h2 className="text-center text-4xl text-white font-extrabold mb-10">
//           Sounds fair, give them more context then? 🤔
//         </h2>
//         <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-3">
//           {illustratedPrompts.map((item) => (
//             <Card
//               key={item.description}
//               className="bg-[#192339] border-none text-white"
//             >
//               <CardHeader>
//                 <CardTitle className="flex items-center gap-x-2">
//                   <div>
//                     <p className="text-lg">{item.name}</p>
//                     {/* <p className="text-zinc-400 text-sm">{item.title}</p> */}
//                   </div>
//                 </CardTitle>
//                 <CardContent className="pt-4 px-0 text-zinc-300">
//                   {item.description} using context below:
//                   <br />
//                   <br />
//                   {<strong>{item.context}</strong>}
//                 </CardContent>
//               </CardHeader>
//             </Card>
//           ))}
//         </div>
//         <h2 className="text-center text-2xl text-gray-300 my-5">
//           ......If I already have such a detailed and structured context, why
//           would I need AI beyond refinement? 🤦‍♀️
//           <br />
//         </h2>
//       </div>

//       {/* Third section: our solution */}
//       <div className="px-10 pb-20">
//         <h2 className="text-center text-4xl text-white font-extrabold mb-10 underline">
//           So how can I collect the context?
//           <br />
//         </h2>
//         <h2 className="text-center text-4xl text-white font-extrabold mb-10">
//           Ask the clarification questions!
//           <br />
//         </h2>
//         <Card className="bg-[#192339] border-none text-white text-xl">
//           <CardHeader>
//             <CardContent className="pt-4 px-0 text-zinc-300">
//               Boss: I want to include AI in my product
//               <br />
//               <br />
//               Me:
//               <li>
//                 What do you mean by AI? analytics, chatbot, recommendation?
//               </li>
//               <li>What are the objectives? revenue, cost saving, marketing?</li>
//               <li>What are the constraints? time, budget, resources?</li>
//               ...and so on
//             </CardContent>
//           </CardHeader>
//         </Card>
//         <h2 className="text-center text-4xl text-white font-extrabold mb-10 pt-6">
//           But I am lazy 🥱 asking questions = structuring the problem
//         </h2>

//         <h2 className="text-center text-4xl text-white font-extrabold mb-16">
//           structuring the problem = system 2 thinking = hard
//         </h2>
//         <h2 className="text-center text-4xl text-white font-extrabold mb-10 underline">
//           OR!? I can use AI to do (part of) it for me 🤖
//         </h2>
//         <h2 className="text-center text-3xl text-white font-extrabold mb-10">
//           In order to do that, AI needs to be able to 1. ask questions, 2. has
//           some basic "intelligence" and 3. continue the conversation
//         </h2>
//         <h2 className="text-center text-8xl text-white font-extrabold mb-10">
//           Isn't this ChatGPT!?
//         </h2>
//         <Card className="bg-[#192339] border-none text-white text-xl">
//           <CardHeader>
//             <CardContent className="pt-4 px-0 text-zinc-300">
//               Below is my home gym plan as an example:
//               <br />
//               <br />
//               Me: I want to build a small house in the backyard to use as a
//               personal gym for working out at home.
//               <br />
//               <br />
//               🤖:
//               <li>
//                 1. What are the specific size requirements for the small house?
//               </li>
//               <li>
//                 2. Are there any specific features or design preferences the
//                 user wants for the small house?
//               </li>
//               <li>
//                 3. What equipment or amenities does the user want to have inside
//                 the small house for working out?
//               </li>
//               <br />
//               <br />
//               ...and so on
//             </CardContent>
//           </CardHeader>
//         </Card>

//         {/* <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-3"></div> */}
//         <h2 className="text-center text-2xl text-gray-300 my-5">
//           At the end, we will have the TRUE and RELIABLE contexts in minutes 🎉
//         </h2>
//         <h2 className="text-center text-2xl text-gray-300 my-5 underline">
//           Having the contexts unlocks all the possibilities of generative AI 🤖
//         </h2>
//         <div className="flex flex-row justify-center">
//           <h2 className="text-center text-2xl text-gray-300 mr-4">
//             Ex. Want to see the detailed plan using my home gym context?
//           </h2>
//           <Button onClick={() => handleCopyExamplePrompt()}>
//             Paste this to ChatGPT
//           </Button>
//         </div>
//         <br />
//       </div>
//     </>
//   );
// };
