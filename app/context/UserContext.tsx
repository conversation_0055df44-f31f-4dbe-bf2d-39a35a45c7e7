"use client";

import { createContext, useContext } from "react";
import { User } from "@prisma/client";

const UserContext = createContext<User | null>(null);

export const UserProvider = UserContext.Provider;

export const useUser = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
};
