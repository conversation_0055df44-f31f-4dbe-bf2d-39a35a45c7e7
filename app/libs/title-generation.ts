import { generateText } from 'ai'
import {
  getModelConfig,
  getModelFromConfig,
  getProviderNameForUsage,
} from '@/app/libs/model-config'
import { createAIUsage } from '@/app/server-actions/log_ai_usage'
import { AIUsageType } from '@prisma/client'

/**
 * Generate a conversation title using a dedicated title generation model configuration
 * This runs asynchronously after the first turn to avoid blocking the user
 * Uses gpt-4.1-mini for all tiers for cost efficiency
 */
export async function generateTitleWithWeakModel(
  prompt: string,
  userId: string,
  conversationId: string
): Promise<string> {
  try {
    // Require authenticated user; do not allow anonymous title generation
    if (!userId || !conversationId) {
      throw new Error('Unauthorized title generation request')
    }
    // Use dedicated title generation configuration
    const modelConfig = await getModelConfig(userId, 'title_generation')
    const model = getModelFromConfig(modelConfig)

    const systemMessage = 'Give a short descriptive title for this conversation'
    const userMessage = prompt.slice(0, 250)

    const result = await generateText({
      model: model,
      maxOutputTokens: modelConfig.maxOutputTokens,
      temperature: modelConfig.temperature,
      messages: [
        {
          role: 'system',
          content: systemMessage,
        },
        {
          role: 'user',
          content: userMessage,
        },
      ],
    })

    const { text, usage } = result

    // Clean up the response
    const cleanTitle = text
      .trim()
      .replace(/^"|"$/g, '') // Remove quotes
      .replace(/\.+$/g, '') // Remove trailing dots
      .slice(0, 50) // Limit length

    // Log AI usage
    try {
      await createAIUsage({
        userId: userId,
        entityType: 'aipane',
        entityId: conversationId,
        aiProvider: getProviderNameForUsage(modelConfig),
        modelName: modelConfig.model,
        usageType: AIUsageType.CHAT, // Using CHAT type as most appropriate for title generation
        inputPrompt: userMessage,
        messages: [
          { role: 'system', content: systemMessage },
          { role: 'user', content: userMessage },
          { role: 'assistant', content: text },
        ],
        metadata: {
          operation: 'title_generation',
          originalPromptLength: prompt.length,
          truncatedPromptLength: userMessage.length,
          generatedTitle: cleanTitle,
          tokenUsage: usage,
        },
        config: {
          maxOutputTokens: modelConfig.maxOutputTokens,
          temperature: modelConfig.temperature,
        },
      })
    } catch (error) {
      console.error('Failed to log AI usage for title generation:', error)
      // Don't fail title generation if logging fails
    }

    return cleanTitle || 'Chat'
  } catch (error) {
    console.error('Failed to generate title:', error)
    // On auth error or generation error, do not auto-assign a title; let caller decide
    throw error
  }
}
