import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { ChevronDown, ChevronRight } from 'lucide-react'
import { cn } from '@/lib/utils'

type CategoryToggleButtonProps = {
  isCollapsed: boolean
  isPersistent: boolean
  isHovered: boolean
  handleToggleCollapse: (e: React.MouseEvent) => void
}

/**
 * Category Toggle Button Component
 *
 * Memoized to prevent unnecessary re-renders when parent components update
 * but the toggle state hasn't changed.
 */
const CategoryToggleButton: React.FC<CategoryToggleButtonProps> = ({
  isCollapsed,
  isPersistent,
  isHovered,
  handleToggleCollapse,
}) => {
  return (
    <Button
      variant="ghost"
      size="sm"
      className={cn(
        'h-7 w-7 sm:h-6 sm:w-6 p-0 rounded-full transition-all duration-200 hover:bg-blue-100 hover:scale-110 touch-manipulation',
        {
          'bg-blue-50 scale-105': isPersistent || isHovered,
          'hover:bg-blue-100': !isPersistent,
        }
      )}
      onClick={handleToggleCollapse}
      title={isCollapsed ? 'Expand category' : 'Collapse category'}
    >
      {isCollapsed ? (
        <ChevronRight className="h-3.5 w-3.5 sm:h-3 sm:w-3 text-blue-600" />
      ) : (
        <ChevronDown className="h-3.5 w-3.5 sm:h-3 sm:w-3 text-blue-600" />
      )}
    </Button>
  )
}

// Memoize to prevent unnecessary re-renders
export default React.memo(CategoryToggleButton)
