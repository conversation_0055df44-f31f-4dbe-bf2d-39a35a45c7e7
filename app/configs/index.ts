// The max allowed ACTIVE conversations
// Mainly avoid abuse or weird bug that leads to infinite loop
export const maxActiveConversations = 5;

// nth really special about this date.
// Just we have previously launched the product, if we find the old users to try again
// It is better to grant them more conversations to try it
// If ever we launch again, we can change this date, but ensure we have documented the change HERE in the code comment
export const activeConversationCutoffDate = new Date("2024-10-01");

// Min char required to start rephrase or clarify, used in EmptyState
export const minChars = 30;

// Define as constant here, so if we have feature that point to other site, we can easily do it
export const chatgptSiteUrl = "https://chat.openai.com";

export const claudeSiteUrl = "https://claude.ai";

export const copilotSiteUrl = "https://copilot.microsoft.com/?showntbk=1";

export const geminiSiteUrl = "https://gemini.google.com";

// When we do rag generative search, how many links to use
// 4 means we consider the top 4 links only
// Adding this number will consume more tokens, max is 9
export const numberOfLinksToUse = 4;

export const contactEmail = "<EMAIL>";

// alternative is openai, decide which API service to use
// We got Azure credits, let's use it first, we can redeem OpenAI / more Azure later
export const modelServiceProvider = "azure";

export const youtubeEmbedUrlId = "TGg4XdImTf4";

// The limit of notebooks a user can generate in one conversation
export const notebookCountLimit = 5;

export type Language = {
  value: string;
  label: string;
};

export enum LanguageEnum {
  English = "English",
  Spanish = "Spanish",
  Japanese = "Japanese",
  Chinese = "Chinese",
}

export const languages: Language[] = [
  { value: LanguageEnum.English, label: "English" },
  { value: LanguageEnum.Spanish, label: "Español" },
  { value: LanguageEnum.Japanese, label: "日本語" },
  { value: LanguageEnum.Chinese, label: "中文" },
];
