import { contactEmail } from '@/app/configs'

type HeaderProps = {
  currentActiveConversations: number
  windowMaxActiveConversations: number
}

const Header: React.FC<HeaderProps> = ({
  currentActiveConversations,
  windowMaxActiveConversations,
}) => {
  const isOverLimit = currentActiveConversations >= windowMaxActiveConversations

  return (
    <header className="text-center mb-4">
      <h1 className="text-3xl font-bold my-2">
        {isOverLimit ? (
          <span className="text-red-500">Active Sessions Limit Reached</span>
        ) : (
          <span className="text-gray-800">Let&apos;s bring some clarity</span>
        )}
      </h1>
      {isOverLimit && (
        <p className="text-gray-600">
          You currently have {currentActiveConversations} active sessions, which
          exceeds our current maximum limit of {windowMaxActiveConversations}.
          <br />
          You can contact us at{' '}
          <a
            href={`mailto:${contactEmail}`}
            className="text-blue-600 hover:underline"
          >
            {contactEmail}
          </a>{' '}
          to increase your limit.
        </p>
      )}
    </header>
  )
}

export default Header
