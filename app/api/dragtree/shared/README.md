# DragTree Research API

This directory contains shared utilities and configurations for the DragTree research functionality.

## Quick Research API Endpoint

### Overview

The research API provides intelligent research generation using GPT-4o-mini, following the same patterns as the screening API for consistency.

### API Endpoints

#### `POST /api/dragtree/research_generate`

Real LLM API endpoint using GPT-4o-mini for comprehensive research generation.

**Request Type**: `ResearchGenerateRequestType`

```typescript
{
  contentId: string
  questionText: string
  researchType?: string
}
```

**Features**:

- Uses Azure OpenAI with GPT-4o-mini model
- **Language Support**: Automatically detects preferred language from drag tree settings
- **Context Awareness**: Includes original screening question for better research focus
- Unified prompt system for consistency across all research methods
- Structured research output with markdown formatting
- Database logging with OpenAI usage tracking
- Status updates (INITIALIZED → PROCESSING → COMPLETED)

#### `POST /api/dragtree/research_generate-simulator`

Simulator endpoint for development and testing.

### Centralized Prompts (`research-prompts.ts`)

All research prompts are centralized in one location for consistency with a **unified approach** for both internal and external use:

#### `createResearchPrompt(questionText: string, screeningQuestion?: string, language: string = 'English')`

**Unified prompt** for both API and external tools with comprehensive guidelines:

- **Language Support**: Automatically includes `Please generate the language in ${language}` following the same pattern as question generation
- **Context Awareness**: Includes original screening question when available
- **Structured Format**: Overview, Key Findings, Analysis, Recommendations, Risk Assessment, Next Steps
- **Quality Standards**: Actionable insights with practical implementation focus
- **Markdown Output**: Clean formatting for both API responses and external tools

#### Legacy Compatibility Functions

These functions now redirect to the unified prompt for backward compatibility:

- `createExternalToolPrompt()` - Generates a **simplified** prompt for external services:
  - If an original user ask is provided → `This is the original user ask: "<original ask>". Now we want you to research: "<research question>".`
  - Otherwise → `Now we want you to research: "<research question>".`
- `createCopyPastePrompt()` - Alias for unified prompt

**Benefits of Unified Approach**:

- Single prompt for all use cases (internal API + external tools)
- Consistent quality across different research methods
- Simplified maintenance and updates
- Language support for all research types

### UI Toggle Integration

#### Header Toggle

- **Location**: DragTree header component
- **Icon**: FiCpu (CPU icon)
- **States**:
  - "Real LLM" (green) - Uses GPT-4o-mini API
  - "Simulator" (gray) - Uses mock data

#### Global State Management

- **Store**: `useUIStore` in `app/stores/ui_store.ts`
- **Key**: `useRealLLMAPI: boolean`
- **Setter**: `setUseRealLLMAPI(useReal: boolean)`

### Research Lifecycle Integration

The `useResearchLifecycle` hook automatically detects the UI toggle state:

- **Real API Mode**: Calls `/api/dragtree/research_generate`
- **Simulator Mode**: Calls `/api/dragtree/research_generate-simulator`

### Type Safety

All request/response types are defined in `app/types/api.ts`:

- `ResearchGenerateRequestType` - API request structure
- Input validation enforced at API level
- TypeScript types ensure consistency across components

### Usage Examples

#### Component Usage

```typescript
// Research button - automatically uses UI toggle
const { startResearch } = useResearchLifecycle({
  nodeId: 'node-id',
  questionText: 'Research question',
})

// Access toggle state
const { useRealLLMAPI, setUseRealLLMAPI } = useUIStore()
```

#### API Call

```typescript
const response = await fetch('/api/dragtree/research_generate', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    contentId: 'content-id',
    questionText: 'What are the market opportunities?',
    researchType: 'RESEARCH',
  }),
})
```

### Benefits

1. **Consistency**: Same patterns as screening API
2. **Centralized Prompts**: Single source of truth for all research prompts
3. **Type Safety**: Full TypeScript support with proper types
4. **Flexible Usage**: Toggle between real API and simulator
5. **Quality Research**: Structured prompts ensure comprehensive analysis
6. **External Tool Support**: Optimized prompts for ChatGPT, Claude, etc.

### Future Enhancements

- Additional research types (competitive analysis, market research, etc.)
- Prompt versioning and A/B testing
- Research template customization
- Integration with external research APIs
