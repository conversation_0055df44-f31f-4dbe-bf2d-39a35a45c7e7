'use server'

import Stripe from 'stripe'
import { stripe } from '@/lib/stripe/config'
import prisma from '@/app/libs/prismadb'
import { SubscriptionTier, SubscriptionService } from '@prisma/client'

/**
 * Convert Unix timestamp to ISO string
 */
const toDateTime = (secs: number) => {
  const t = new Date(0)
  t.setSeconds(secs)
  return t
}

/**
 * Comprehensive Stripe subscription state synchronization
 * This function fetches the latest subscription data from Stripe and updates our database
 */
export async function syncStripeState(customerId: string): Promise<void> {
  try {
    console.log(`🔄 Syncing Stripe state for customer: ${customerId}`)

    // Fetch all subscriptions for this customer from Stripe
    const subscriptions = await stripe.subscriptions.list({
      customer: customerId,
      status: 'all', // Include all statuses (active, canceled, etc.)
      limit: 100,
    })

    console.log(
      `📊 Found ${subscriptions.data.length} subscriptions for customer ${customerId}`
    )

    // Find the user in our database
    const user = await prisma.user.findFirst({
      where: { subscription_customer_id: customerId },
    })

    if (!user) {
      console.error(`❌ No user found for Stripe customer: ${customerId}`)
      return
    }

    // Process each subscription
    for (const subscription of subscriptions.data) {
      await syncSubscriptionRecord(subscription, user.id)
    }

    // Optional: fetch and snapshot recent invoices for audit trail
    const invoices = await stripe.invoices.list({
      customer: customerId,
      limit: 50,
    })
    for (const invoice of invoices.data) {
      await upsertInvoiceSnapshot(invoice)
    }

    // Optional: snapshot recent payment intents
    const paymentIntents = await stripe.paymentIntents.list({
      customer: customerId,
      limit: 50,
    })
    for (const pi of paymentIntents.data) {
      await upsertPaymentIntentSnapshot(pi)
    }

    // Update user's subscription status based on active subscriptions
    await updateUserSubscriptionStatus(user.id, subscriptions.data)

    console.log(
      `✅ Successfully synced Stripe state for customer: ${customerId}`
    )
  } catch (error) {
    console.error(
      `❌ Error syncing Stripe state for customer ${customerId}:`,
      error
    )
    throw error
  }
}

/**
 * Sync individual subscription record
 */
async function syncSubscriptionRecord(
  subscription: Stripe.Subscription,
  userId: string
): Promise<void> {
  const subscriptionData = {
    id: subscription.id,
    customer_id: subscription.customer as string,
    user_id: userId,
    status: subscription.status,
    quantity: subscription.items.data[0]?.quantity || 1,
    cancel_at_period_end: subscription.cancel_at_period_end,
    current_period_start: toDateTime(subscription.current_period_start),
    current_period_end: toDateTime(subscription.current_period_end),
    ended_at: subscription.ended_at ? toDateTime(subscription.ended_at) : null,
    cancel_at: subscription.cancel_at
      ? toDateTime(subscription.cancel_at)
      : null,
    canceled_at: subscription.canceled_at
      ? toDateTime(subscription.canceled_at)
      : null,
    trial_start: subscription.trial_start
      ? toDateTime(subscription.trial_start)
      : null,
    trial_end: subscription.trial_end
      ? toDateTime(subscription.trial_end)
      : null,
    metadata: subscription.metadata
      ? JSON.stringify(subscription.metadata)
      : null,
  }

  // Get price and product info
  const priceId = subscription.items.data[0]?.price?.id
  const productId = subscription.items.data[0]?.price?.product as string

  if (!priceId || !productId) {
    console.warn(
      `⚠️ Subscription ${subscription.id} missing price or product data`
    )
    return
  }

  // Upsert subscription record
  await prisma.subscription.upsert({
    where: { id: subscription.id },
    update: subscriptionData,
    create: {
      ...subscriptionData,
      price_id: priceId,
      product_id: productId,
    },
  })

  console.log(
    `📝 Synced subscription record: ${subscription.id} (${subscription.status})`
  )
}

async function upsertInvoiceSnapshot(invoice: Stripe.Invoice): Promise<void> {
  try {
    const priceId = invoice.lines?.data?.[0]?.price?.id
    const productId =
      (invoice.lines?.data?.[0]?.price?.product as string) || undefined
    const subscriptionId = (invoice.subscription as string) || undefined

    const user = invoice.customer
      ? await prisma.user.findFirst({
          where: { subscription_customer_id: invoice.customer as string },
          select: { id: true },
        })
      : null

    await (prisma as any).invoice.upsert({
      where: { id: invoice.id },
      update: {
        customer_id: (invoice.customer as string) ?? '',
        user_id: user?.id,
        subscription_id: subscriptionId,
        product_id: productId,
        price_id: priceId,
        status: invoice.status ?? 'unknown',
        currency: invoice.currency ?? 'usd',
        number: invoice.number ?? null,
        hosted_invoice_url: invoice.hosted_invoice_url ?? null,
        invoice_pdf: invoice.invoice_pdf ?? null,
        subtotal: (invoice.subtotal as number | null) ?? null,
        total: (invoice.total as number) ?? 0,
        amount_paid: (invoice.amount_paid as number | null) ?? null,
        amount_due: (invoice.amount_due as number | null) ?? null,
        attempted: invoice.attempted ?? null,
        paid: invoice.paid ?? null,
        period_start: toDateTime(invoice.period_start ?? 0),
        period_end: toDateTime(invoice.period_end ?? 0),
        due_date: invoice.due_date ? toDateTime(invoice.due_date) : null,
        metadata: invoice.metadata ? JSON.stringify(invoice.metadata) : null,
      },
      create: {
        id: invoice.id,
        customer_id: (invoice.customer as string) ?? '',
        user_id: user?.id ?? null,
        subscription_id: subscriptionId ?? null,
        product_id: productId ?? null,
        price_id: priceId ?? null,
        status: invoice.status ?? 'unknown',
        currency: invoice.currency ?? 'usd',
        number: invoice.number ?? null,
        hosted_invoice_url: invoice.hosted_invoice_url ?? null,
        invoice_pdf: invoice.invoice_pdf ?? null,
        subtotal: (invoice.subtotal as number | null) ?? null,
        total: (invoice.total as number) ?? 0,
        amount_paid: (invoice.amount_paid as number | null) ?? null,
        amount_due: (invoice.amount_due as number | null) ?? null,
        attempted: invoice.attempted ?? null,
        paid: invoice.paid ?? null,
        period_start: toDateTime(invoice.period_start ?? 0),
        period_end: toDateTime(invoice.period_end ?? 0),
        due_date: invoice.due_date ? toDateTime(invoice.due_date) : null,
        metadata: invoice.metadata ? JSON.stringify(invoice.metadata) : null,
      },
    })
  } catch (error) {
    console.error('Failed to upsert invoice snapshot:', error)
  }
}

async function upsertPaymentIntentSnapshot(
  pi: Stripe.PaymentIntent
): Promise<void> {
  try {
    const user = pi.customer
      ? await prisma.user.findFirst({
          where: { subscription_customer_id: pi.customer as string },
          select: { id: true },
        })
      : null

    // Retrieve receipt_url from latest_charge if available
    let receiptUrl: string | null = null
    if (typeof pi.latest_charge === 'string' && pi.latest_charge) {
      try {
        const charge = await stripe.charges.retrieve(pi.latest_charge)
        receiptUrl = (charge as any)?.receipt_url ?? null
      } catch (err) {
        console.warn('Could not retrieve charge for receipt_url:', err)
      }
    }

    await (prisma as any).paymentIntent.upsert({
      where: { id: pi.id },
      update: {
        customer_id: (pi.customer as string) ?? null,
        user_id: user?.id ?? null,
        amount: (pi.amount as number) ?? 0,
        currency: pi.currency ?? 'usd',
        status: pi.status,
        payment_method: (pi.payment_method as string) ?? null,
        latest_charge: (pi.latest_charge as string) ?? null,
        receipt_url: receiptUrl,
        description: pi.description ?? null,
        metadata: pi.metadata ? JSON.stringify(pi.metadata) : null,
        stripe_created_at: toDateTime(pi.created),
      },
      create: {
        id: pi.id,
        customer_id: (pi.customer as string) ?? null,
        user_id: user?.id ?? null,
        amount: (pi.amount as number) ?? 0,
        currency: pi.currency ?? 'usd',
        status: pi.status,
        payment_method: (pi.payment_method as string) ?? null,
        latest_charge: (pi.latest_charge as string) ?? null,
        receipt_url: receiptUrl,
        description: pi.description ?? null,
        metadata: pi.metadata ? JSON.stringify(pi.metadata) : null,
        stripe_created_at: toDateTime(pi.created),
      },
    })
  } catch (error) {
    console.error('Failed to upsert payment intent snapshot:', error)
  }
}

/**
 * Update user's subscription status based on active subscriptions
 */
async function updateUserSubscriptionStatus(
  userId: string,
  subscriptions: Stripe.Subscription[]
): Promise<void> {
  // Find the most relevant active subscription
  const activeSubscription = subscriptions.find(
    sub => sub.status === 'active' || sub.status === 'trialing'
  )

  if (activeSubscription) {
    // User has an active subscription - set to PRO
    await prisma.user.update({
      where: { id: userId },
      data: {
        subscription_tier: SubscriptionTier.PRO,
        subscription_service: SubscriptionService.STRIPE,
        subscription_customer_id: activeSubscription.customer as string,
        subscription_id: activeSubscription.id,
        subscription_end_date: toDateTime(
          activeSubscription.current_period_end
        ),
        subscription_cancel_pending: activeSubscription.cancel_at_period_end,
      },
    })

    console.log(
      `✅ Updated user ${userId} to PRO tier (subscription: ${activeSubscription.id})`
    )
  } else {
    // No active subscription - check if user should be downgraded
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { subscription_tier: true },
    })

    // Only downgrade if user is not GUEST (GUEST tier is permanent)
    if (user && user.subscription_tier !== SubscriptionTier.GUEST) {
      await prisma.user.update({
        where: { id: userId },
        data: {
          subscription_tier: SubscriptionTier.FREE,
          subscription_end_date: null,
          subscription_cancel_pending: false,
          subscription_id: null,
          // Keep customer_id for potential reactivation
        },
      })

      console.log(
        `⬇️ Downgraded user ${userId} to FREE tier (no active subscriptions)`
      )
    }
  }
}

/**
 * Sync all customers that haven't been updated recently
 * This can be used for periodic reconciliation
 */
export async function syncStaleCustomers(
  hoursThreshold: number = 24
): Promise<void> {
  const cutoffDate = new Date(Date.now() - hoursThreshold * 60 * 60 * 1000)

  const staleUsers = await prisma.user.findMany({
    where: {
      subscription_customer_id: { not: null },
      updated_at: { lt: cutoffDate },
    },
    select: { id: true, subscription_customer_id: true },
  })

  console.log(
    `🔄 Found ${staleUsers.length} users with stale subscription data`
  )

  for (const user of staleUsers) {
    if (user.subscription_customer_id) {
      try {
        await syncStripeState(user.subscription_customer_id)
      } catch (error) {
        console.error(`❌ Failed to sync user ${user.id}:`, error)
      }
    }
  }

  console.log(`✅ Completed stale customer sync`)
}
