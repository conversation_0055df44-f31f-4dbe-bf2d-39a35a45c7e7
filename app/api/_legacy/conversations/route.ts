// // This ia a route for user to create a new conversation

// import getCurrentUser from "@/app/actions/getCurrentUser";
// import { NextResponse } from "next/server";

// import prisma from "@/app/libs/prismadb";
// import { ConversationType, UserStatus } from "@prisma/client";
// import getPromptById from "@/app/actions/getPromptById";

// export async function POST(request: Request) {
//   try {
//     const currentUser = await getCurrentUser();
//     const body = await request.json();
//     const { coworker, description } = body;

//     const baseUrl =
//       process.env.NODE_ENV === "development"
//         ? "http://localhost:3000"
//         : process.env.NEXT_PUBLIC_SERVER_URL;

//     if (!currentUser?.id || !currentUser?.email) {
//       return new NextResponse("Unauthorized", { status: 400 });
//     }

//     // Block them from accessing API if they are not active
//     if (currentUser?.status !== UserStatus.ACTIVE) {
//       return new NextResponse("Unauthorized", { status: 401 });
//     }

//     if (!coworker || !description) {
//       return new NextResponse("No coworker selected nor description", {
//         status: 400,
//       });
//     }

//     // the clarification component, eg: You are my...
//     const baseClarifyPrompt = await getPromptById("base_clarification");
//     // directive part, eg: Our objective is to craft a first draft PRD...
//     const promptDirective = await getPromptById(coworker);

//     // Example of formatted system message:
//     // '''
//     // baseClarifyPrompt part
//     // You are my brilliant...... The process is as follows:
//     // .......

//     // directive part
//     // vvvvvvvvv append the first ask to the base prompt vvvvvvvvv
//     // Our objective is to craft a first draft PRD...
//     // .......
//     // ^^^^^^^^^ append the first ask to the base prompt ^^^^^^^^^

//     // description part
//     // vvvvvvvvv append the first ask to the base prompt vvvvvvvvv
//     // Below is the first ask from the user: """I want to redesign the checkout flow such that the purchase funnel is shorter."""
//     // ^^^^^^^^^ append the first ask to the base prompt ^^^^^^^^^
//     // '''

//     // If we allow user to add more directives to the clarify prompt, we can use the same pattern here
//     const formattedSystemMessage = `${baseClarifyPrompt?.content}\n\n${promptDirective?.content}\n\nBelow is the first ask from the user: """${description}"""`;
//     console.log(formattedSystemMessage, "formattedSystemMessage");
//     const newConversation = await prisma.conversation.create({
//       data: {
//         creator_id: currentUser.id,
//         conversation_type: ConversationType.SELF_CLARIFY,
//         prompt_id: promptDirective?.id,
//         config: {
//           prompt: {
//             system_message: formattedSystemMessage,
//             // description is what user input in EmptyState compoenent
//             description: description,
//           },
//         },
//       },
//     });

//     const newSystemMessage = await prisma.message.create({
//       data: {
//         content: formattedSystemMessage,
//         creator_id: "system",
//         conversation: {
//           connect: { id: newConversation.id },
//         },
//       },
//     });

//     // Remove config from the return conversation since it contains system prompt
//     const returnConversation = {
//       id: newConversation.id,
//       creator_id: newConversation.creator_id,
//       title: newConversation.title,
//       conversation_type: newConversation.conversation_type,
//       conversation_status: newConversation.conversation_status,
//       is_hidden: newConversation.is_hidden,
//       created_at: newConversation.created_at,
//       updated_at: newConversation.updated_at,
//       prompt_id: newConversation.prompt_id,
//     };

//     return NextResponse.json(returnConversation);
//   } catch (error) {
//     return new NextResponse("Internal Error", { status: 500 });
//   }
// }
