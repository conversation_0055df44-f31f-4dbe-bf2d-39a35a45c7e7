import { NextResponse } from 'next/server'
import prisma from '@/app/libs/prismadb'

/**
 * Debug endpoint to test database performance
 * GET /api/debug/db-performance
 */
export async function GET() {
  const startTime = Date.now()
  const timings: Record<string, number> = {}

  try {
    // Test 1: Simple connection test
    const connectionStart = Date.now()
    await prisma.$queryRaw`SELECT 1 as test`
    timings.connection = Date.now() - connectionStart

    // Test 2: Count conversations
    const countStart = Date.now()
    const conversationCount = await prisma.aiConversation.count()
    timings.conversationCount = Date.now() - countStart

    // Test 3: Simple conversation query
    const queryStart = Date.now()
    const sampleConversation = await prisma.aiConversation.findFirst({
      select: {
        id: true,
        title: true,
        createdAt: true,
      },
      orderBy: { createdAt: 'desc' },
    })
    timings.simpleQuery = Date.now() - queryStart

    // Test 4: Message count for sample conversation
    let messageCount = 0
    let messageQueryTime = 0
    if (sampleConversation) {
      const messageStart = Date.now()
      messageCount = await prisma.aiMessage.count({
        where: { conversationId: sampleConversation.id },
      })
      messageQueryTime = Date.now() - messageStart
    }
    timings.messageCount = messageQueryTime

    const totalTime = Date.now() - startTime

    return NextResponse.json({
      success: true,
      performance: {
        totalTime: `${totalTime}ms`,
        breakdown: {
          connection: `${timings.connection}ms`,
          conversationCount: `${timings.conversationCount}ms`,
          simpleQuery: `${timings.simpleQuery}ms`,
          messageCount: `${timings.messageCount}ms`,
        },
      },
      data: {
        conversationCount,
        sampleConversation: sampleConversation?.id,
        messageCount,
      },
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    const totalTime = Date.now() - startTime

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        performance: {
          totalTime: `${totalTime}ms`,
          breakdown: timings,
        },
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    )
  }
}
