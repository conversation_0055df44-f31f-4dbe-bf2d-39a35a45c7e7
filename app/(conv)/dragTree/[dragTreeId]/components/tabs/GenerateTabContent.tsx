'use client'

import React, { useState, useEffect, useRef, startTransition } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Progress } from '@/components/ui/progress'
import {
  FiCopy,
  FiCheck,
  FiSettings,
  FiZap,
  FiEdit2,
  <PERSON>Eye,
  <PERSON>Loader,
} from 'react-icons/fi'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { toast } from 'react-hot-toast'
import type { Tab } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useTabStore'
import { useTabStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useTabStore'
import { useAssetStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useAssetStore'
import TiptapAiDocEditor, {
  EditStatus,
} from '@/app/components/editor/TiptapAiDocEditor'
import { useAiGeneration } from '@/app/(conv)/dragTree/[dragTreeId]/hooks/useAiGeneration'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { logEventWithContext } from '@/app/libs/logging'
import { useNavigationStore } from '@/app/stores/navigation_store'
import { useAiPaneStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useAiPaneStore'
import { convertTiptapJsonToMarkdown } from '@/app/components/editor/utils'
import { getAIGenerationContent } from '@/app/server-actions/drag-tree/get_ai_generation_content'
import { cn } from '@/lib/utils'
import { useSession } from 'next-auth/react'
import { SubscriptionTier } from '@prisma/client'
import { hasPermission } from '@/app/configs/tier-permissions'
// Removed unused Select imports

type GenerateTabContentProps = {
  tab: Tab
  dragTreeId: string
}

type GenerationState = 'idle' | 'generating' | 'completed'

const GenerateTabContent: React.FC<GenerateTabContentProps> = ({
  tab,
  dragTreeId,
}) => {
  const [copied, setCopied] = useState<boolean>(false)
  const [showContextDialog, setShowContextDialog] = useState<boolean>(false)
  const [showSettingsModal, setShowSettingsModal] = useState<boolean>(false)
  const [isEditingTitle, setIsEditingTitle] = useState<boolean>(false)
  const [editableTitle, setEditableTitle] = useState<string>(tab.title || '')
  const [scrollProgress, setScrollProgress] = useState<number>(0)
  const [editorStatus, setEditorStatus] = useState<EditStatus>('idle')

  // Permission checks
  const { data: session } = useSession()
  const tier = ((session?.user as any)?.subscription?.tier ||
    (session?.user as any)?.subscription_tier ||
    SubscriptionTier.FREE) as SubscriptionTier

  const canEditTitle = hasPermission(tier, 'canEditAiGenerationTitle')
  const canEditContent = hasPermission(tier, 'canEditAiGenerationContent')

  const contentRef = useRef<HTMLDivElement>(null)
  const addAsset = useAssetStore(state => state.addAsset)
  const assets = useAssetStore(state => state.assets)
  const updateTabTitle = useTabStore(state => state.updateTabTitle)
  const updateAiPaneData = useTabStore(state => state.updateTabAiPaneData)

  // Removed unused setModel
  // Drag tree data & navigation (select specific slices to avoid global re-renders)
  const nodeContent = useDragTreeStore(state => state.nodeContent)
  const findNodeById = useDragTreeStore(state => state.findNodeById)
  const { navigateToTreeNode } = useNavigationStore()

  // Context loading state - prevent generation until all content is loaded
  const canGenerate = useAiPaneStore(state =>
    state.isAllSelectedContentLoaded()
  )

  // Use the proven useAiGeneration hook instead of manual streaming
  const {
    markdown,
    isStreaming,
    isCompleted,
    generationId,
    generationPhase,
    retryGeneration,
  } = useAiGeneration({
    prompt: tab.aiPaneData?.prompt || '',
    model: '', // Model now controlled server-side - keeping for compatibility
    contextIds: tab.aiPaneData?.contextIds || [],
    tabId: tab.id,
    dragTreeId: dragTreeId,
    canGenerate: canGenerate, // Prevent generation until all content is loaded
    language: tab.aiPaneData?.language, // Pass language override if specified
  })

  // Debug context state (must be after useAiGeneration to access generationPhase)
  useEffect(() => {
    console.log('🎯 [Generate Tab] Context state:', {
      canGenerate,
      tabId: tab.id,
      contextIds: tab.aiPaneData?.contextIds || [],
      contextIdsCount: (tab.aiPaneData?.contextIds || []).length,
      generationPhase,
    })
  }, [canGenerate, tab.id, tab.aiPaneData?.contextIds, generationPhase])

  const [finalContent, setFinalContent] = useState<string>('')
  const [isLoadingExistingContent, setIsLoadingExistingContent] =
    useState<boolean>(false)

  // Unified asset identifier (handles both freshly generated and previously saved docs)
  const assetId = tab.aiPaneData?.assetId || generationId || null
  const generationState: GenerationState = isStreaming
    ? 'generating'
    : isCompleted || markdown || finalContent
      ? 'completed'
      : 'idle'

  // Initialize with asset content if available or fetch from DB
  useEffect(() => {
    const init = async () => {
      if (tab.aiPaneData?.assetContent) {
        setFinalContent(tab.aiPaneData.assetContent)
        return
      }

      // If we have an assetId but no content, fetch from DB
      if (tab.aiPaneData?.assetId && !finalContent) {
        setIsLoadingExistingContent(true)
        try {
          const dbGeneration = await getAIGenerationContent(
            tab.aiPaneData.assetId
          )
          if (dbGeneration?.content) {
            setFinalContent(dbGeneration.content)
            // Persist content, context IDs, and user prompt in tab state to avoid refetch
            const contextIds =
              (dbGeneration.config as any)?.settings?.contextIds ||
              (dbGeneration.config as any)?.contextIds ||
              []
            const userPrompt =
              (dbGeneration.config as any)?.settings?.userPrompt || ''

            updateAiPaneData(tab.id, {
              assetContent: dbGeneration.content,
              contextIds: contextIds,
              ...(userPrompt && { prompt: userPrompt }),
            })
          }
        } catch (err) {
          console.error('Failed to fetch generation content:', err)
        } finally {
          setIsLoadingExistingContent(false)
        }
      }
    }

    init()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // Auto-fix: If tab has assetId but no contextIds, fetch them from database config
  useEffect(() => {
    const restoreContextIds = async () => {
      if (
        tab.aiPaneData?.assetId &&
        (!tab.aiPaneData?.contextIds || tab.aiPaneData.contextIds.length === 0)
      ) {
        try {
          console.log(
            '[GenerateTabContent] Auto-fixing missing contextIds from database config for assetId:',
            tab.aiPaneData.assetId
          )
          const dbGeneration = await getAIGenerationContent(
            tab.aiPaneData.assetId
          )
          if (dbGeneration && dbGeneration.config) {
            // Handle both old and new config structures
            const contextIds =
              (dbGeneration.config as any)?.settings?.contextIds ||
              (dbGeneration.config as any)?.contextIds ||
              []

            const userPrompt =
              (dbGeneration.config as any)?.settings?.userPrompt || ''

            const updateData: any = {}

            if (contextIds.length > 0) {
              console.log(
                '[GenerateTabContent] Restored contextIds from database:',
                contextIds
              )
              updateData.contextIds = contextIds

              // Also sync with AI Pane store context selection
              const aiPaneStore = useAiPaneStore.getState()
              const currentContext = aiPaneStore.settings.context
              const updatedContext = currentContext.map(item => ({
                ...item,
                selected: contextIds.includes(item.id),
              }))
              aiPaneStore.setContext(updatedContext)
            }

            if (userPrompt) {
              console.log(
                '[GenerateTabContent] Restored user prompt from database:',
                userPrompt.substring(0, 100) + '...'
              )
              updateData.prompt = userPrompt
            }

            if (Object.keys(updateData).length > 0) {
              updateAiPaneData(tab.id, updateData)
            } else {
              console.log(
                '[GenerateTabContent] No data to restore from database config:',
                dbGeneration.config
              )
            }
          }
        } catch (err) {
          console.error('Failed to restore contextIds from database:', err)
        }
      }
    }

    restoreContextIds()
  }, [
    tab.aiPaneData?.assetId,
    tab.aiPaneData?.contextIds,
    tab.id,
    updateAiPaneData,
  ])

  // Update editable title when tab title changes
  useEffect(() => {
    setEditableTitle(tab.title || '')
  }, [tab.title])

  // Handle scroll progress tracking
  useEffect(() => {
    const handleScroll = () => {
      if (!contentRef.current) return
      const element = contentRef.current
      const scrollTop = element.scrollTop
      const scrollHeight = element.scrollHeight - element.clientHeight
      const progress = scrollHeight > 0 ? (scrollTop / scrollHeight) * 100 : 0
      setScrollProgress(Math.min(100, Math.max(0, progress)))
    }

    const contentElement = contentRef.current
    if (contentElement) {
      contentElement.addEventListener('scroll', handleScroll)
      return () => contentElement.removeEventListener('scroll', handleScroll)
    }
  }, [finalContent, markdown])

  // Update final content when streaming completes
  useEffect(() => {
    if (!isStreaming && markdown && generationId) {
      setFinalContent(markdown)

      // ------------------------------------------------------------------
      // If this tab already has an associated asset we're done.
      // ------------------------------------------------------------------
      if (tab.aiPaneData?.assetId) return

      // ------------------------------------------------------------------
      // The generation is now persisted in database by the API route.
      // Create local asset using the database generation ID and server title.
      // ------------------------------------------------------------------
      if (tab.aiPaneData?.prompt) {
        const currentAssets = useAssetStore.getState().assets
        const existing = currentAssets.find(a => a.id === generationId)

        if (!existing) {
          // Fetch the server's canonical title instead of generating client-side
          getAIGenerationContent(generationId)
            .then(dbGeneration => {
              const assetTitle =
                dbGeneration?.title ||
                generateAssetTitle(tab.aiPaneData?.prompt || '')
              const assetVersion = dbGeneration?.version ?? 1 // CRITICAL: Include version for edit persistence

              addAsset({
                id: generationId, // Use the database ID
                title: assetTitle,
                content: markdown,
                type: 'generate',
                model: '', // Model controlled server-side - keeping for compatibility
                prompt: tab.aiPaneData?.prompt || '',
                contextIds: tab.aiPaneData?.contextIds || [],
                dragTreeId: dragTreeId,
                persistedInDb: true,
                isContentLoaded: true,
                createdAt: new Date(),
                viewed: false,
                version: assetVersion, // CRITICAL: Store version to enable edit persistence
              })

              // Also update the tab title with the server's canonical title
              updateTabTitle(tab.id, assetTitle)
            })
            .catch(error => {
              console.error(
                'Failed to fetch server title, using fallback:',
                error
              )
              // Fallback to client-generated title if server fetch fails
              const assetTitle = generateAssetTitle(
                tab.aiPaneData?.prompt || ''
              )
              const fallbackVersion = 1 // Default version if DB fetch fails

              addAsset({
                id: generationId,
                title: assetTitle,
                content: markdown,
                type: 'generate',
                model: '', // Model controlled server-side - keeping for compatibility
                prompt: tab.aiPaneData?.prompt || '',
                contextIds: tab.aiPaneData?.contextIds || [],
                dragTreeId: dragTreeId,
                persistedInDb: true,
                isContentLoaded: true,
                createdAt: new Date(),
                viewed: false,
                version: fallbackVersion, // CRITICAL: Ensure version is always set
              })
            })
        }

        // Persist generationId on the tab so we never attempt to save again.
        updateAiPaneData(tab.id, { assetId: generationId })
      }
    }
  }, [
    isStreaming,
    markdown,
    generationId,
    tab.aiPaneData,
    addAsset,
    dragTreeId,
    tab.id,
    updateAiPaneData,
    updateTabTitle,
  ])

  // Generate asset title from prompt
  const generateAssetTitle = (prompt: string): string => {
    if (!prompt) return 'Generated Content'

    // Take first few words of prompt as title
    const words = prompt.split(' ').slice(0, 5)
    let title = words.join(' ')

    if (title.length > 50) {
      title = title.substring(0, 47) + '...'
    }

    return title || 'Generated Content'
  }

  // Get content to display - only two states: loading or content
  const getDisplayContent = () => {
    if (generationState === 'completed' && finalContent) {
      return finalContent
    }
    if (generationState === 'generating' && markdown) {
      return markdown
    }
    // Show loading for any other state (loading existing content, waiting to generate, etc.)
    return null // Will show loading spinner instead of placeholder text
  }

  const displayContent = getDisplayContent()

  // Handle copy to clipboard
  const handleCopy = async () => {
    let contentToCopy =
      generationState === 'completed' ? finalContent : displayContent

    // Detect and convert Tiptap JSON (rich-text) to markdown for clipboard
    if (contentToCopy) {
      try {
        const possibleJson = JSON.parse(contentToCopy)
        if (possibleJson && possibleJson.type === 'doc') {
          contentToCopy = convertTiptapJsonToMarkdown(possibleJson)
        }
      } catch {
        // Not JSON – no conversion needed
      }
    }

    if (contentToCopy) {
      try {
        await navigator.clipboard.writeText(contentToCopy)
        setCopied(true)
        setTimeout(() => setCopied(false), 2000)

        // Log the copy event
        logEventWithContext(
          'click_aipane_copyGenerated',
          session?.user?.id,
          dragTreeId,
          {
            content_length: contentToCopy.length,
            generation_state: generationState,
            asset_id: tab.aiPaneData?.assetId || '',
          }
        )
      } catch (err) {
        console.error('Failed to copy content:', err)
      }
    }
  }

  // Handle title editing
  const handleTitleSave = () => {
    const newTitle =
      editableTitle.trim() || generateAssetTitle(tab.aiPaneData?.prompt || '')

    if (newTitle !== tab.title) {
      // Update tab title for immediate UI feedback
      updateTabTitle(tab.id, newTitle)

      // Update asset store title if asset exists
      if (assetId) {
        const { updateAsset } = useAssetStore.getState()
        updateAsset(assetId, { title: newTitle })

        // Persist to DB with optimistic locking
        const asset = useAssetStore
          .getState()
          .assets.find(a => a.id === assetId)
        if (asset?.version !== undefined) {
          startTransition(() => {
            import('@/app/server-actions/drag-tree/update_ai_generation').then(
              async mod => {
                const result = await mod.updateAIGenerationTitle(
                  assetId,
                  newTitle,
                  asset.version!
                )
                if (result.success && result.newVersion) {
                  // Sync version locally
                  updateAsset(assetId, { version: result.newVersion })
                }
              }
            )
          })
        }
      }
    }
    setIsEditingTitle(false)
  }

  const handleTitleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleTitleSave()
    } else if (e.key === 'Escape') {
      setEditableTitle(tab.title || '')
      setIsEditingTitle(false)
    }
  }

  // Handle tiptap content updates with database persistence
  const handleTiptapUpdate = async (jsonContent: any) => {
    if (!canEditContent) {
      toast.error(
        'Permission denied: You cannot edit AI generation content with your current tier.'
      )
      return
    }

    try {
      // Convert tiptap JSON to markdown for local preview & copy functionality
      const markdownContent = convertTiptapJsonToMarkdown(jsonContent)

      // Immediate UI feedback
      setFinalContent(markdownContent)

      // Let the asset store deal with optimistic locking & server sync
      const { updateAssetContent } = useAssetStore.getState()
      if (assetId) {
        // The asset store now handles debouncing and server persistence
        updateAssetContent(assetId, markdownContent, jsonContent)
      }
    } catch (error) {
      console.error('Failed to update AI generation content:', error)
    }
  }

  // Note: Generation starts automatically via useAiGeneration hook

  return (
    <div className="flex flex-col h-full min-h-0">
      {/* Top Bar */}
      <div className="flex-shrink-0 border-b">
        {/* Progress Bar */}
        {generationState === 'completed' && scrollProgress > 0 && (
          <Progress
            value={scrollProgress}
            className="h-1 w-full rounded-none"
          />
        )}

        <div className="flex items-center justify-between p-3">
          <div className="flex items-center space-x-3 flex-1 min-w-0">
            <div className="p-2 bg-blue-100 rounded-lg flex-shrink-0">
              <FiZap className="w-4 h-4 text-blue-600" />
            </div>
            <div className="flex-1 min-w-0">
              {/* Editable Title */}
              <div className="flex items-center space-x-2 mb-1">
                {isEditingTitle ? (
                  <Input
                    value={editableTitle}
                    onChange={e => setEditableTitle(e.target.value)}
                    onBlur={handleTitleSave}
                    onKeyDown={handleTitleKeyDown}
                    className="text-lg font-semibold h-7 px-2 py-1"
                    autoFocus
                  />
                ) : (
                  <div
                    className={cn(
                      'flex items-center space-x-2 rounded px-2 py-1 -mx-2 -my-1 transition-colors group',
                      canEditTitle
                        ? 'cursor-pointer hover:bg-gray-50'
                        : 'cursor-default'
                    )}
                    onClick={() => {
                      if (canEditTitle) {
                        setIsEditingTitle(true)
                      } else {
                        toast.error(
                          'Permission denied: You cannot edit AI generation titles with your current tier.'
                        )
                      }
                    }}
                    title={
                      canEditTitle
                        ? 'Click to edit title'
                        : 'Title editing not available with your current tier'
                    }
                  >
                    <h2 className="text-lg font-semibold text-gray-900 truncate">
                      {tab.title || 'Generated Content'}
                    </h2>
                    <FiEdit2 className="w-3 h-3 text-gray-400 group-hover:text-gray-600 transition-colors" />
                  </div>
                )}
              </div>

              {/* Model info moved to Settings modal to declutter UI */}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {/* Retry button when failed */}
            {generationPhase === 'failed' && (
              <Button
                variant="outline"
                size="sm"
                onClick={retryGeneration}
                className="flex items-center space-x-2 text-orange-600 border-orange-300 hover:bg-orange-50"
              >
                <FiZap className="w-4 h-4" />
                <span>Retry</span>
              </Button>
            )}

            {/* Copy button when content is available */}
            {(generationPhase === 'completed' ||
              generationPhase === 'generating') && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopy}
                className="flex items-center"
                title={copied ? 'Copied!' : 'Copy'}
              >
                {copied ? (
                  <FiCheck className="w-4 h-4 text-green-600" />
                ) : (
                  <FiCopy className="w-4 h-4" />
                )}
              </Button>
            )}

            {/* Settings Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSettingsModal(true)}
              className="flex items-center space-x-2"
            >
              <FiSettings className="w-4 h-4" />
              <span>Settings</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Settings Modal */}
      <Dialog open={showSettingsModal} onOpenChange={setShowSettingsModal}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <FiSettings className="w-5 h-5" />
              <span>Generation Settings</span>
              {assetId && (
                <button
                  onClick={() => {
                    navigator.clipboard.writeText(assetId)
                    toast.success('Document ID copied to clipboard!')
                  }}
                  className="ml-2 text-blue-600 hover:text-blue-800 font-mono text-xs bg-gray-100 px-2 py-1 rounded border hover:bg-gray-200 transition-colors"
                  title="Click to copy document ID"
                >
                  {assetId}
                </button>
              )}
            </DialogTitle>
          </DialogHeader>

          <div className="overflow-y-auto space-y-6 max-h-[60vh] pr-2">
            {/* User Prompt Section - Only show if prompt exists */}
            {tab.aiPaneData?.prompt && (
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <FiEye className="w-4 h-4 text-blue-600" />
                  <h3 className="font-medium text-gray-900">User Prompt</h3>
                </div>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div
                    className="text-sm text-gray-800 whitespace-pre-wrap break-words max-w-none"
                    style={{
                      wordBreak: 'break-word',
                      overflowWrap: 'anywhere',
                      maxWidth: '100%',
                    }}
                  >
                    {tab.aiPaneData.prompt}
                  </div>
                </div>
              </div>
            )}

            {/* Model Information */}
            <div className="space-y-3">
              <h3 className="font-medium text-gray-900">
                Model & Configuration
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="text-sm">
                    <span className="font-medium text-gray-700">Model:</span>
                    <span className="ml-2 text-gray-900">
                      Server-controlled
                    </span>
                  </div>
                  <div className="text-sm">
                    <span className="font-medium text-gray-700">Type:</span>
                    <span className="ml-2 text-gray-900 capitalize">
                      {tab.aiPaneData?.type || 'Generate'}
                    </span>
                  </div>
                </div>
                <div className="space-y-2">
                  {(tab.aiPaneData?.contextIds.length || 0) > 0 && (
                    <div className="text-sm">
                      <span className="font-medium text-gray-700">
                        Context Items:
                      </span>
                      <button
                        onClick={() => {
                          setShowContextDialog(true)
                          setShowSettingsModal(false)
                        }}
                        className="ml-2 text-blue-600 underline hover:text-blue-800"
                        title="View and navigate to context items"
                      >
                        {tab.aiPaneData?.contextIds.length || 0} selected
                      </button>
                    </div>
                  )}
                  <div className="text-sm">
                    <span className="font-medium text-gray-700">Status:</span>
                    <span className="ml-2 text-gray-900 capitalize">
                      {generationState}
                    </span>
                  </div>
                  {assetId &&
                    (() => {
                      const asset = assets.find(a => a.id === assetId)
                      return asset?.createdAt ? (
                        <div className="text-sm">
                          <span className="font-medium text-gray-700">
                            Generated:
                          </span>
                          <span className="ml-2 text-gray-900">
                            {asset.createdAt.toLocaleString()}
                          </span>
                        </div>
                      ) : null
                    })()}
                </div>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Content Area */}
      <div
        className="flex-grow p-4 relative overflow-y-auto group"
        ref={contentRef}
      >
        {generationState === 'completed' ? (
          <TiptapAiDocEditor
            content={finalContent || markdown}
            onJSONChange={canEditContent ? handleTiptapUpdate : undefined}
            isStreaming={false}
            isReadOnly={!canEditContent}
            placeholder={
              canEditContent
                ? 'Edit generated content...'
                : 'Content editing not available with your current tier'
            }
            showBubbleMenu={canEditContent}
            onStatusChange={setEditorStatus}
          />
        ) : generationState === 'generating' && displayContent ? (
          <div className="prose dark:prose-invert max-w-none whitespace-pre-wrap">
            {displayContent}
          </div>
        ) : (
          <div className="flex items-center justify-center h-64">
            <div className="flex flex-col items-center gap-3 text-gray-500">
              <FiLoader className="w-8 h-8 animate-spin" />
              <span className="text-sm">
                {isLoadingExistingContent
                  ? 'Loading existing content...'
                  : generationPhase === 'generating'
                    ? 'Generating content...'
                    : 'Preparing generation...'}
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Bottom Bar */}
      <div className="flex-shrink-0 p-2 border-t">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="text-xs text-gray-500">
            {generationPhase === 'idle' && !canGenerate && (
              <div className="flex items-center space-x-2 text-orange-600">
                <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse" />
                <span>
                  Loading context content... (
                  {(tab.aiPaneData?.contextIds || []).length} items selected)
                </span>
              </div>
            )}
            {generationPhase === 'idle' &&
              canGenerate &&
              `Ready to generate content${(tab.aiPaneData?.contextIds || []).length > 0 ? ` with ${(tab.aiPaneData?.contextIds || []).length} context items` : ''}`}
            {generationPhase === 'generating' && (
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                <span>Generating content...</span>
              </div>
            )}
            {generationPhase === 'completed' && (
              <div className="flex items-center space-x-2">
                {editorStatus === 'saving' && (
                  <>
                    <div className="w-2 h-2 bg-amber-500 rounded-full animate-pulse" />
                    <span>Saving...</span>
                  </>
                )}
                {editorStatus === 'saved' && (
                  <>
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                    <span className="text-green-700">Saved</span>
                  </>
                )}
                {editorStatus === 'idle' && <span>All changes saved</span>}
              </div>
            )}
            {generationPhase === 'failed' && (
              <div className="flex items-center space-x-2 text-red-600">
                <div className="w-2 h-2 bg-red-500 rounded-full" />
                <span>Generation failed • Click retry to try again</span>
              </div>
            )}
          </div>

          {generationPhase === 'generating' && markdown && (
            <div className="text-xs text-gray-500">
              {markdown.split(' ').length} words generated
            </div>
          )}

          {generationPhase === 'completed' && markdown && (
            <div className="text-xs text-gray-500">
              {markdown.split(' ').filter(w => w.length > 0).length} words •{' '}
              {markdown.length} characters
            </div>
          )}
        </div>
      </div>

      {/* Context Items Dialog */}
      <Dialog open={showContextDialog} onOpenChange={setShowContextDialog}>
        <DialogContent className="max-w-2xl max-h-[70vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>
              Context Items ({tab.aiPaneData?.contextIds.length || 0})
            </DialogTitle>
          </DialogHeader>
          <div className="overflow-y-auto space-y-2 max-h-[60vh]">
            {tab.aiPaneData?.contextIds?.map((nodeId, index) => {
              const node = findNodeById(nodeId)
              const nodeLabel = node?.label || `Item ${index + 1}`

              const contentMap = nodeContent.get(nodeId)
              let previewText = 'No content available'

              if (contentMap && contentMap.size > 0) {
                const firstContentItem = Array.from(contentMap.values())[0]
                let text = (firstContentItem as any)?.contentText || ''

                // Convert content to readable format
                if (typeof text === 'object' && (text as any).type === 'doc') {
                  text = convertTiptapJsonToMarkdown(text)
                } else if (typeof text === 'string') {
                  try {
                    const parsed = JSON.parse(text)
                    if (parsed && parsed.type === 'doc') {
                      text = convertTiptapJsonToMarkdown(parsed)
                    }
                  } catch (_e) {}
                }
                previewText =
                  typeof text === 'string' ? text : 'Content available'
              }

              return (
                <button
                  key={nodeId}
                  onClick={() => {
                    navigateToTreeNode(nodeId)
                    setShowContextDialog(false)
                  }}
                  className="w-full text-left p-3 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors duration-200 group"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded font-mono">
                          {index + 1}
                        </span>
                        <div className="text-sm font-medium text-gray-900 group-hover:text-blue-700 truncate">
                          {nodeLabel}
                        </div>
                      </div>
                      <div className="text-xs text-gray-500 mt-2 line-clamp-3">
                        {previewText.substring(0, 200)}
                        {previewText.length > 200 && '...'}
                      </div>
                    </div>
                    <div className="ml-2 text-xs text-gray-400 group-hover:text-blue-500 flex-shrink-0">
                      Click to navigate
                    </div>
                  </div>
                </button>
              )
            })}

            {(!tab.aiPaneData?.contextIds ||
              tab.aiPaneData.contextIds.length === 0) && (
              <div className="text-center text-gray-500 py-8">
                No context items selected
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default React.memo(GenerateTabContent)
