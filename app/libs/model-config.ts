import { SubscriptionTier } from '@prisma/client'
import prisma from '@/app/libs/prismadb'
import { getModelFromProvider } from '@/app/libs/model-provider-factory'
import {
  LLM_MODEL_CONFIG,
  ModelConfig,
  APIRoute,
} from '@/app/configs/llm-models'

export const DEFAULT_MAX_PROMPT_CHARS = 1_000_000

// In-memory cache for user subscription tiers to reduce DB pressure
type TierCacheEntry = { tier: SubscriptionTier; cachedAt: number }
const USER_TIER_CACHE = new Map<string, TierCacheEntry>()
const TIER_CACHE_TTL_MS = 5 * 60 * 1000 // 5 minutes

/**
 * Get user's subscription tier from database
 * @param userId - The user's ID
 * @returns Promise<SubscriptionTier> - The user's subscription tier
 * @throws Error if user not found
 */
export async function getUserSubscriptionTier(
  userId: string
): Promise<SubscriptionTier> {
  // Check cache first
  const now = Date.now()
  const cached = USER_TIER_CACHE.get(userId)
  if (cached && now - cached.cachedAt < TIER_CACHE_TTL_MS) {
    return cached.tier
  }

  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { subscription_tier: true },
  })

  if (!user) {
    throw new Error(`User not found: ${userId}`)
  }

  // Update cache
  USER_TIER_CACHE.set(userId, { tier: user.subscription_tier, cachedAt: now })

  return user.subscription_tier
}

/**
 * Get model configuration for a user and API route
 * @param userId - The user's ID
 * @param apiRoute - The API route requesting the model config
 * @returns Promise<ModelConfig> - The model configuration to use
 * @throws Error if user not found or config not found
 */
export async function getModelConfig(
  userId: string,
  apiRoute: APIRoute
): Promise<ModelConfig> {
  const tier = await getUserSubscriptionTier(userId)

  const tierConfig = LLM_MODEL_CONFIG[tier]
  if (!tierConfig) {
    throw new Error(`No configuration found for tier: ${tier}`)
  }

  const modelConfig = tierConfig[apiRoute]
  if (!modelConfig) {
    throw new Error(
      `No model configuration found for tier: ${tier}, route: ${apiRoute}`
    )
  }

  return { ...modelConfig, providerOptions: modelConfig.providerOptions ?? {} }
}

/**
 * Get model configuration directly from a known subscription tier (no DB lookup)
 * Use this when you already trust the user's tier (e.g., from session).
 */
export function getModelConfigForTier(
  tier: SubscriptionTier,
  apiRoute: APIRoute
): ModelConfig {
  const tierConfig = LLM_MODEL_CONFIG[tier]
  if (!tierConfig) {
    throw new Error(`No configuration found for tier: ${tier}`)
  }

  const modelConfig = tierConfig[apiRoute]
  if (!modelConfig) {
    throw new Error(
      `No model configuration found for tier: ${tier}, route: ${apiRoute}`
    )
  }

  return { ...modelConfig, providerOptions: modelConfig.providerOptions ?? {} }
}

/**
 * Get model instance from configuration
 * @param modelConfig - The model configuration
 * @returns Model instance for the configured provider
 */
export function getModelFromConfig(modelConfig: ModelConfig) {
  const { model, model_provider, model_provider_api } = modelConfig
  return getModelFromProvider(model, model_provider, model_provider_api)
}

/**
 * Get AI provider name for usage tracking based on model configuration
 * Maps the actual provider configuration to usage tracking names
 * @param modelConfig - The model configuration
 * @returns Provider name for AI usage logging
 */
export function getProviderNameForUsage(modelConfig: ModelConfig): string {
  const { model_provider, model_provider_api } = modelConfig

  switch (model_provider) {
    case 'azure':
      // Map Azure provider API to specific usage tracking names
      switch (model_provider_api) {
        case 'azure_eastus2':
          return 'azure_openai_eastus2'
        case 'azure_standard':
        default:
          return 'azure_openai_standard'
      }
    case 'openai':
      return 'openai'
    case 'anthropic':
      return 'anthropic'
    case 'google':
      return 'google'
    default:
      // Fallback with warning
      console.warn(
        `⚠️ Unknown model provider: ${model_provider}. Using as-is for usage tracking.`
      )
      return model_provider
  }
}

/**
 * Use a known session-like object to retrieve model config without DB when possible.
 * Falls back to DB lookup if the tier is not present in session.
 */
export async function getModelConfigFromSession(
  sessionLike: { user?: { id?: string; subscription_tier?: SubscriptionTier } },
  apiRoute: APIRoute
): Promise<ModelConfig> {
  const tier = sessionLike?.user?.subscription_tier
  if (tier) {
    return getModelConfigForTier(tier, apiRoute)
  }
  const userId = sessionLike?.user?.id
  if (userId) {
    return getModelConfig(userId, apiRoute)
  }
  throw new Error('Unable to resolve user tier from session or userId')
}

/** Cache management helpers for tests and admin tools */
export function clearUserTierCache(userId?: string) {
  if (userId) {
    USER_TIER_CACHE.delete(userId)
  } else {
    USER_TIER_CACHE.clear()
  }
}

export function getTierCacheStats() {
  const now = Date.now()
  let oldest = Infinity
  USER_TIER_CACHE.forEach(entry => {
    oldest = Math.min(oldest, now - entry.cachedAt)
  })
  return {
    size: USER_TIER_CACHE.size,
    oldestEntryAgeMs: USER_TIER_CACHE.size > 0 ? oldest : 0,
    ttlMs: TIER_CACHE_TTL_MS,
  }
}
