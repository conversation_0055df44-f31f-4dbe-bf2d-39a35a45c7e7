#!/bin/bash
# Utility script to manage Telegram forum topics for branches

set -a
source .env
set +a

# Function to get repository name
get_repo_name() {
    local repo_name=""
    if git remote get-url origin >/dev/null 2>&1; then
        repo_name=$(git remote get-url origin | sed 's/.*\///' | sed 's/\.git$//')
    else
        repo_name=$(basename "$(git rev-parse --show-toplevel 2>/dev/null || pwd)")
    fi
    echo "$repo_name"
}

# Function to list all topics (dynamic discovery)
list_topics() {
    echo "📋 Current Branch Topics (Dynamic Discovery):"
    echo "============================================="

    local repo_name=$(get_repo_name)
    echo "Repository: $repo_name"
    echo ""

    # Note: Telegram API doesn't provide direct topic listing
    # Topics are discovered dynamically when notifications are sent
    echo "ℹ️  Topics are discovered and created dynamically when needed."
    echo "   Format: ${repo_name}:branch:BRANCH_NAME"
    echo ""
    echo "🔍 To see active topics:"
    echo "   1. Check your Telegram forum group"
    echo "   2. Look for topics matching: ${repo_name}:branch:*"
    echo ""
    echo "🧪 To test/create a topic for current branch:"
    echo "   bash ./scripts/manage_topics.sh test"
    echo ""
}

# Function to create topic for specific branch (no local storage)
create_topic() {
    local branch="$1"

    if [ -z "$branch" ]; then
        echo "❌ Error: Branch name required"
        echo "Usage: $0 create <branch-name>"
        return 1
    fi

    echo "🔨 Creating topic for branch: $branch"

    local repo_name=$(get_repo_name)
    local topic_name="${repo_name}:branch:${branch}"

    echo "Repository: $repo_name"
    echo "Topic name: $topic_name"

    # Create forum topic
    CREATE_RESPONSE=$(curl -s -X POST "https://api.telegram.org/bot$TG_TOKEN/createForumTopic" \
      -H "Content-Type: application/json" \
      -d "{
        \"chat_id\": \"$TG_CHAT\",
        \"name\": \"$topic_name\",
        \"icon_color\": 0x6FB9F0
      }")

    # Extract topic ID from response
    topic_id=$(echo "$CREATE_RESPONSE" | grep -o '"message_thread_id":[0-9]*' | cut -d: -f2)

    if [ -n "$topic_id" ]; then
        echo "✅ Created topic $topic_id for branch $branch"
        echo "   Topic name: $topic_name"
        echo "   Check your Telegram forum group to see the new topic"
        echo ""
        echo "ℹ️  Note: No local storage used - topic discovered dynamically when needed"
    else
        echo "❌ Failed to create topic for branch $branch"
        echo "Response: $CREATE_RESPONSE"

        # Check if topic might already exist
        if echo "$CREATE_RESPONSE" | grep -q "Bad Request"; then
            echo ""
            echo "💡 Topic might already exist. Check your Telegram forum group."
        fi
        return 1
    fi
}

# Function to provide info about topic management (no local storage to delete)
delete_mapping() {
    local branch="$1"

    if [ -z "$branch" ]; then
        echo "❌ Error: Branch name required"
        echo "Usage: $0 delete <branch-name>"
        return 1
    fi

    local repo_name=$(get_repo_name)
    local topic_name="${repo_name}:branch:${branch}"

    echo "ℹ️  Topic Management Info for: $branch"
    echo "======================================"
    echo ""
    echo "🔍 Topic name: $topic_name"
    echo ""
    echo "📋 Since this system uses dynamic discovery (no local storage):"
    echo "   • Topics are discovered automatically when needed"
    echo "   • No local mappings to delete"
    echo "   • To remove a topic, close it manually in Telegram:"
    echo ""
    echo "🛠️  Manual topic management in Telegram:"
    echo "   1. Open your Telegram forum group"
    echo "   2. Find the topic: $topic_name"
    echo "   3. Long-press the topic → Close Topic"
    echo ""
    echo "✅ The system will automatically handle closed/missing topics"
}

# Function to test notification for specific branch
test_branch() {
    local branch="$1"

    if [ -z "$branch" ]; then
        branch=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "test-branch")
    fi

    local repo_name=$(get_repo_name)
    local topic_name="${repo_name}:branch:${branch}"

    echo "📤 Testing notification system for branch: $branch"
    echo "Repository: $repo_name"
    echo "Expected topic: $topic_name"
    echo ""

    # Create a test notification using the main script
    echo "🚀 Sending test notification..."
    bash ./scripts/notify_telegram.sh "🧪 Test notification from manage_topics.sh" "ManagementTest"

    if [ $? -eq 0 ]; then
        echo ""
        echo "✅ Test notification sent successfully!"
        echo "📱 Check your Telegram forum group for:"
        echo "   • Topic: $topic_name"
        echo "   • Test message with repository and branch info"
        echo ""
        echo "🎯 This confirms the system is working and fully portable!"
    else
        echo ""
        echo "❌ Test notification failed"
        echo "🔧 Run the setup script to diagnose issues:"
        echo "   bash ./scripts/setup_telegram_forum.sh"
    fi
}

# Function to show help
show_help() {
    echo "🔧 Telegram Topics Management"
    echo "============================="
    echo ""
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  list                    List all current branch topics"
    echo "  create <branch>         Create topic for specific branch"
    echo "  delete <branch>         Delete branch mapping (topic remains in Telegram)"
    echo "  test [branch]           Test notification for branch (current branch if not specified)"
    echo "  help                    Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 list"
    echo "  $0 create feature/new-ui"
    echo "  $0 test main"
    echo "  $0 delete old-branch"
    echo ""
}

# Main script logic
case "$1" in
    "list")
        list_topics
        ;;
    "create")
        create_topic "$2"
        ;;
    "delete")
        delete_mapping "$2"
        ;;
    "test")
        test_branch "$2"
        ;;
    "help"|"--help"|"-h"|"")
        show_help
        ;;
    *)
        echo "❌ Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
