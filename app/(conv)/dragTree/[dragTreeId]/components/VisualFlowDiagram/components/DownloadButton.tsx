'use client'

import React from 'react'
import { Download, Loader2 } from 'lucide-react'
import { Button } from '@/components/ui/button'

type DownloadButtonProps = {
  onDownload: () => void
  isDownloading: boolean
}

/**
 * Simple download button following ReactFlow's official pattern
 * Positioned as floating button, not integrated with controls
 * Follows ReactFlow's download example approach
 */
export const DownloadButton: React.FC<DownloadButtonProps> = ({
  onDownload,
  isDownloading,
}) => {
  return (
    <Button
      onClick={onDownload}
      disabled={isDownloading}
      variant="outline"
      size="sm"
      className="absolute top-4 right-4 z-10 bg-white/90 backdrop-blur-sm border-gray-200 hover:bg-white/95 hover:border-gray-300 shadow-lg transition-all duration-200 flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900"
      title={isDownloading ? 'Downloading...' : 'Download diagram as PNG'}
    >
      {isDownloading ? (
        <>
          <Loader2 className="w-4 h-4 animate-spin" />
          <span>Downloading...</span>
        </>
      ) : (
        <>
          <Download className="w-4 h-4" />
          <span>Download PNG</span>
        </>
      )}
    </Button>
  )
}

// Memoize to prevent unnecessary re-renders (no props to compare)
export default React.memo(DownloadButton)
