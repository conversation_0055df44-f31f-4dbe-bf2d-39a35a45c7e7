import PromptCard from './PromptCard'
import { prompts } from './promptObjects'
import CustomPrompt from './CustomPrompt'
import { Separator } from '@/components/ui/separator'

// The PromptCardList component displays a list of PromptCard components
const PromptCardList: React.FC = () => {
  return (
    <div className="flex flex-col h-full">
      {/* Non-scrollable section for the header, switch, and custom prompt input */}
      <div className="flex-shrink-0 p-4">
        <CustomPrompt />
        <Separator className="bg-gray-300 my-4" />
      </div>

      {/* Scrollable section for the prompt cards */}
      <div className="flex-grow overflow-auto px-4">
        <h1 className="text-lg font-bold tracking-tighter sm:text-lg mb-4">
          Examples
        </h1>
        <div className="grid gap-6 pb-12">
          {prompts.map((prompt, index) => (
            <PromptCard
              key={index}
              title={prompt.title}
              description={prompt.description}
              tags={prompt.tags || []}
            />
          ))}
        </div>
      </div>
    </div>
  )
}

export default PromptCardList
