'use server'

import prisma from '@/app/libs/prismadb'
import { AIUsageType } from '@prisma/client'

export type CreateAIUsageType = {
  userId: string
  entityType: string
  entityId: string
  aiProvider: string
  modelName: string
  usageType: AIUsageType
  inputPrompt: string
  messages: any[]
  metadata?: Record<string, any>
  config?: Record<string, any>
}

export type AIUsageResult = {
  success: boolean
  data?: any
  error?: string
}

// Create a new AI usage record
export const createAIUsage = async (
  request: CreateAIUsageType
): Promise<AIUsageResult> => {
  const {
    userId,
    entityType,
    entityId,
    aiProvider,
    modelName,
    usageType,
    inputPrompt,
    messages,
    metadata,
    config,
  } = request

  try {
    const aiUsage = await prisma.aIUsage.create({
      data: {
        user_id: userId,
        entity_type: entityType,
        entity_id: entityId,
        ai_provider: aiProvider,
        model_name: modelName,
        usage_type: usageType,
        input_prompt: inputPrompt,
        messages: messages,
        metadata: metadata || {},
        config: config || {},
      },
    })

    console.log(`✅ [AI Usage] Logged usage: ${usageType} for user ${userId}`)

    return {
      success: true,
      data: {
        id: aiUsage.id,
        message: 'AI usage logged successfully',
      },
    }
  } catch (error: any) {
    console.error('❌ [AI Usage] Error logging AI usage:', error)
    return {
      success: false,
      error: error.message || 'Failed to log AI usage',
    }
  }
}

export default createAIUsage
