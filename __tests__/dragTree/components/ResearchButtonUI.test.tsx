/**
 * Tests for ResearchButtonUI Component
 *
 * Tests the pure UI component for research functionality:
 * - Rendering different states
 * - User interactions
 * - Dropdown menu behavior
 * - External site confirmation dialog
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { toast } from 'react-hot-toast'
import ResearchButtonUI from '@/app/(conv)/dragTree/[dragTreeId]/components/shared/ResearchButtonUI'
import type { ResearchActionHandlers } from '@/app/(conv)/dragTree/[dragTreeId]/components/shared/hooks/useResearchActions'

// Mock dependencies
jest.mock('react-hot-toast', () => ({
  toast: {
    error: jest.fn(),
    success: jest.fn(),
  },
}))

// No longer mocking useUIStore as it's not used in the component
// jest.mock('@/app/stores/ui_store', () => ({
//   useUIStore: jest.fn(),
// }))
// const mockUseUIStore = useUIStore as jest.MockedFunction<typeof useUIStore>

const mockToast = toast as jest.Mocked<typeof toast>

describe('ResearchButtonUI', () => {
  const mockActionHandlers: ResearchActionHandlers = {
    executeInternalResearch: jest.fn(),
    executeExternalResearch: jest.fn(),
    validateResearchAction: jest.fn(),
    getFullQuestionPath: jest.fn(),
  }

  const defaultProps = {
    hasExistingContent: false,
    isStreaming: false,
    searchResults: null,
    variant: 'treeview' as const,
    className: '',
    actionHandlers: mockActionHandlers,
    onClick: jest.fn(),
  }

  beforeEach(() => {
    jest.clearAllMocks()

    // No longer need to mock useUIStore
    // mockUseUIStore.mockReturnValue({})
    ;(mockActionHandlers.validateResearchAction as jest.Mock).mockReturnValue({
      canExecute: true,
    })
    ;(mockActionHandlers.getFullQuestionPath as jest.Mock).mockReturnValue(
      'Test question path'
    )
  })

  describe('Basic Rendering', () => {
    it('should render the research button', () => {
      render(<ResearchButtonUI {...defaultProps} />)

      const button = screen.getByRole('button')
      expect(button).toBeInTheDocument()
      expect(button).toHaveAttribute('title', 'Research Options')
    })

    it('should show streaming state when isStreaming is true', () => {
      render(<ResearchButtonUI {...defaultProps} isStreaming={true} />)

      const button = screen.getByRole('button')
      expect(button).toHaveAttribute('title', 'Research in progress...')
      expect(button).toHaveClass('animate-pulse')
    })

    it('should apply correct styling for reactflow variant', () => {
      render(<ResearchButtonUI {...defaultProps} variant="reactflow" />)

      const button = screen.getByRole('button')
      expect(button).toHaveClass('hover:bg-blue-100')
    })

    it('should apply correct styling for treeview variant', () => {
      render(<ResearchButtonUI {...defaultProps} variant="treeview" />)

      const button = screen.getByRole('button')
      expect(button).toHaveClass('rounded-full')
      expect(button).toHaveClass('bg-gradient-to-r')
    })
  })

  describe('Dropdown Menu Behavior', () => {
    it('should open dropdown menu when button is clicked', async () => {
      const user = userEvent.setup()
      render(<ResearchButtonUI {...defaultProps} />)

      const button = screen.getByRole('button')
      await user.click(button)

      await waitFor(() => {
        expect(screen.getByText('Quick Research')).toBeInTheDocument()
        expect(screen.getByText('Open in ChatGPT')).toBeInTheDocument()
        expect(screen.getByText('External Tools')).toBeInTheDocument()
      })
    })

    it('should show disabled state for Clarify option when content exists', async () => {
      const user = userEvent.setup()
      render(<ResearchButtonUI {...defaultProps} hasExistingContent={true} />)

      const button = screen.getByRole('button')
      await user.click(button)

      await waitFor(() => {
        const clarifyOption = screen
          .getByText('Quick Research')
          .closest('[role="menuitem"]')
        expect(clarifyOption).toHaveClass('opacity-50')
        expect(clarifyOption).toHaveClass('cursor-not-allowed')
        expect(screen.getByText('✓ Done')).toBeInTheDocument()
      })
    })

    it('should show streaming state for Clarify option when streaming', async () => {
      const user = userEvent.setup()
      render(<ResearchButtonUI {...defaultProps} isStreaming={true} />)

      const button = screen.getByRole('button')
      await user.click(button)

      await waitFor(() => {
        expect(screen.getByText('⏳ Researching...')).toBeInTheDocument()
      })
    })
  })

  describe('Research Option Selection', () => {
    it('should execute internal research for Quick Research option', async () => {
      const user = userEvent.setup()
      render(<ResearchButtonUI {...defaultProps} />)

      const button = screen.getByRole('button')
      await user.click(button)

      await waitFor(() => {
        const clarifyOption = screen.getByText('Quick Research')
        expect(clarifyOption).toBeInTheDocument()
      })

      await user.click(screen.getByText('Quick Research'))

      expect(mockActionHandlers.executeInternalResearch).toHaveBeenCalledWith(
        'Test question path'
      )
    })

    it('should show external confirmation dialog for external options', async () => {
      const user = userEvent.setup()
      render(<ResearchButtonUI {...defaultProps} />)

      const button = screen.getByRole('button')
      await user.click(button)

      await waitFor(() => {
        const chatgptOption = screen.getByText('Open in ChatGPT')
        expect(chatgptOption).toBeInTheDocument()
      })

      await user.click(screen.getByText('Open in ChatGPT'))

      await waitFor(() => {
        expect(
          screen.getByText('Continue to Open in ChatGPT?')
        ).toBeInTheDocument()
        expect(
          screen.getByText(/You're about to open an external site/)
        ).toBeInTheDocument()
      })
    })

    it('should execute external research when confirmed', async () => {
      const user = userEvent.setup()
      render(<ResearchButtonUI {...defaultProps} />)

      const button = screen.getByRole('button')
      await user.click(button)

      await user.click(screen.getByText('Open in ChatGPT'))

      await waitFor(() => {
        expect(screen.getByText('Continue')).toBeInTheDocument()
      })

      await user.click(screen.getByText('Continue'))

      expect(mockActionHandlers.executeExternalResearch).toHaveBeenCalled()
    })

    it('should cancel external research when cancelled', async () => {
      const user = userEvent.setup()
      render(<ResearchButtonUI {...defaultProps} />)

      const button = screen.getByRole('button')
      await user.click(button)

      await user.click(screen.getByText('Open in ChatGPT'))

      await waitFor(() => {
        expect(screen.getByText('Cancel')).toBeInTheDocument()
      })

      await user.click(screen.getByText('Cancel'))

      await waitFor(() => {
        expect(
          screen.queryByText('Continue to Open in ChatGPT?')
        ).not.toBeInTheDocument()
      })
      expect(mockActionHandlers.executeExternalResearch).not.toHaveBeenCalled()
    })

    it('should show error toast if validation fails', async () => {
      ;(mockActionHandlers.validateResearchAction as jest.Mock).mockReturnValue(
        {
          canExecute: false,
          reason: 'Validation failed!',
        }
      )

      const user = userEvent.setup()
      render(<ResearchButtonUI {...defaultProps} />)

      const button = screen.getByRole('button')
      await user.click(button)

      await user.click(screen.getByText('Quick Research'))

      expect(mockToast.error).toHaveBeenCalledWith('Validation failed!')
    })

    it('should handle research execution failure', async () => {
      const consoleSpy = jest
        .spyOn(console, 'error')
        .mockImplementation(() => {})

      ;(
        mockActionHandlers.executeInternalResearch as jest.Mock
      ).mockRejectedValue(new Error('API Error'))

      const user = userEvent.setup()
      render(<ResearchButtonUI {...defaultProps} />)

      const button = screen.getByRole('button')
      await user.click(button)

      await user.click(screen.getByText('Quick Research'))

      await waitFor(() => {
        // We expect the error to be handled inside the component,
        // typically by logging it. Here we ensure it doesn't crash the test.
        expect(mockActionHandlers.executeInternalResearch).toHaveBeenCalled()
      })

      expect(consoleSpy).toHaveBeenCalledWith(
        'Research execution failed:',
        expect.any(Error)
      )

      consoleSpy.mockRestore()
    })
  })

  // New tests for onMouseEnter and onMouseLeave
  describe('Hover Events', () => {
    it('should call onMouseEnter when mouse enters the button area', () => {
      const mockOnMouseEnter = jest.fn()
      render(
        <ResearchButtonUI {...defaultProps} onMouseEnter={mockOnMouseEnter} />
      )

      const buttonWrapper = screen.getByRole('button').parentElement
      fireEvent.mouseEnter(buttonWrapper!)

      expect(mockOnMouseEnter).toHaveBeenCalledTimes(1)
    })

    it('should call onMouseLeave when mouse leaves the button area', () => {
      const mockOnMouseLeave = jest.fn()
      render(
        <ResearchButtonUI {...defaultProps} onMouseLeave={mockOnMouseLeave} />
      )

      const buttonWrapper = screen.getByRole('button').parentElement
      fireEvent.mouseLeave(buttonWrapper!)

      expect(mockOnMouseLeave).toHaveBeenCalledTimes(1)
    })
  })
})
