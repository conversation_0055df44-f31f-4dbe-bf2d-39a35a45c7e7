/**
 * Performance Monitoring Utility for DragTree System
 *
 * Tracks payload sizes, load times, memory usage, and provides
 * before/after comparisons for optimization efforts
 */

export interface PerformanceMetrics {
  timestamp: string
  operation: string
  duration: number
  payloadSize: number
  memoryUsage?: number
  nodeCount?: number
  errorCount?: number
  cacheHitRate?: number
}

export interface PayloadComparison {
  before: {
    size: number
    loadTime: number
    endpoint: string
  }
  after: {
    size: number
    loadTime: number
    endpoint: string
  }
  improvement: {
    sizeReduction: number
    timeReduction: number
    percentageImprovement: number
  }
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = []
  private isEnabled: boolean = process.env.NODE_ENV === 'development'
  private maxMetricsHistory: number = 1000

  /**
   * Start timing an operation
   */
  startTiming(
    operation: string
  ): (additionalData?: Partial<PerformanceMetrics>) => PerformanceMetrics {
    const startTime = performance.now()
    const startMemory = this.getMemoryUsage()

    return (additionalData?: Partial<PerformanceMetrics>) => {
      const endTime = performance.now()
      const endMemory = this.getMemoryUsage()

      const metrics: PerformanceMetrics = {
        timestamp: new Date().toISOString(),
        operation,
        duration: endTime - startTime,
        payloadSize: additionalData?.payloadSize || 0,
        memoryUsage: endMemory - startMemory,
        nodeCount: additionalData?.nodeCount,
        errorCount: additionalData?.errorCount || 0,
        cacheHitRate: additionalData?.cacheHitRate,
        ...additionalData,
      }

      this.recordMetrics(metrics)
      return metrics
    }
  }

  /**
   * Record performance metrics
   */
  recordMetrics(metrics: PerformanceMetrics): void {
    if (!this.isEnabled) return

    this.metrics.push(metrics)

    // Keep only recent metrics to prevent memory leaks
    if (this.metrics.length > this.maxMetricsHistory) {
      this.metrics = this.metrics.slice(-this.maxMetricsHistory)
    }

    // Log to console in development
    this.logMetrics(metrics)
  }

  /**
   * Compare payload sizes between old and new implementations
   */
  comparePayloads(
    beforeMetrics: { size: number; loadTime: number; endpoint: string },
    afterMetrics: { size: number; loadTime: number; endpoint: string }
  ): PayloadComparison {
    const sizeReduction = beforeMetrics.size - afterMetrics.size
    const timeReduction = beforeMetrics.loadTime - afterMetrics.loadTime
    const percentageImprovement = (sizeReduction / beforeMetrics.size) * 100

    const comparison: PayloadComparison = {
      before: beforeMetrics,
      after: afterMetrics,
      improvement: {
        sizeReduction,
        timeReduction,
        percentageImprovement,
      },
    }

    if (this.isEnabled) {
      console.log('📊 [Performance Comparison]', {
        operation: `${beforeMetrics.endpoint} → ${afterMetrics.endpoint}`,
        sizeBefore: `${(beforeMetrics.size / 1024).toFixed(2)}KB`,
        sizeAfter: `${(afterMetrics.size / 1024).toFixed(2)}KB`,
        sizeReduction: `${(sizeReduction / 1024).toFixed(2)}KB (${percentageImprovement.toFixed(1)}%)`,
        timeBefore: `${beforeMetrics.loadTime.toFixed(2)}ms`,
        timeAfter: `${afterMetrics.loadTime.toFixed(2)}ms`,
        timeReduction: `${timeReduction.toFixed(2)}ms`,
        improvement: percentageImprovement > 0 ? '✅ IMPROVED' : '❌ DEGRADED',
      })
    }

    return comparison
  }

  /**
   * Get performance summary for a specific operation
   */
  getOperationSummary(operation: string): {
    count: number
    averageDuration: number
    averagePayloadSize: number
    totalDataTransferred: number
    errorRate: number
  } {
    const operationMetrics = this.metrics.filter(m => m.operation === operation)

    if (operationMetrics.length === 0) {
      return {
        count: 0,
        averageDuration: 0,
        averagePayloadSize: 0,
        totalDataTransferred: 0,
        errorRate: 0,
      }
    }

    const totalDuration = operationMetrics.reduce(
      (sum, m) => sum + m.duration,
      0
    )
    const totalPayloadSize = operationMetrics.reduce(
      (sum, m) => sum + m.payloadSize,
      0
    )
    const totalErrors = operationMetrics.reduce(
      (sum, m) => sum + (m.errorCount || 0),
      0
    )

    return {
      count: operationMetrics.length,
      averageDuration: totalDuration / operationMetrics.length,
      averagePayloadSize: totalPayloadSize / operationMetrics.length,
      totalDataTransferred: totalPayloadSize,
      errorRate: (totalErrors / operationMetrics.length) * 100,
    }
  }

  /**
   * Generate performance dashboard data
   */
  getDashboardData(): {
    summary: Record<string, any>
    recentMetrics: PerformanceMetrics[]
    trends: Record<string, number[]>
  } {
    const operations = Array.from(new Set(this.metrics.map(m => m.operation)))
    const summary: Record<string, any> = {}

    operations.forEach(operation => {
      summary[operation] = this.getOperationSummary(operation)
    })

    // Get recent metrics (last 50)
    const recentMetrics = this.metrics.slice(-50)

    // Calculate trends (last 10 measurements per operation)
    const trends: Record<string, number[]> = {}
    operations.forEach(operation => {
      const operationMetrics = this.metrics
        .filter(m => m.operation === operation)
        .slice(-10)
        .map(m => m.duration)
      trends[operation] = operationMetrics
    })

    return {
      summary,
      recentMetrics,
      trends,
    }
  }

  /**
   * Log metrics to console with formatting
   */
  private logMetrics(metrics: PerformanceMetrics): void {
    const icon = metrics.errorCount && metrics.errorCount > 0 ? '❌' : '✅'
    const payloadInfo =
      metrics.payloadSize > 0
        ? ` | ${(metrics.payloadSize / 1024).toFixed(2)}KB`
        : ''
    const nodeInfo = metrics.nodeCount ? ` | ${metrics.nodeCount} nodes` : ''
    const memoryInfo = metrics.memoryUsage
      ? ` | ${(metrics.memoryUsage / 1024 / 1024).toFixed(2)}MB`
      : ''

    console.log(
      `${icon} [Performance] ${metrics.operation}: ${metrics.duration.toFixed(2)}ms${payloadInfo}${nodeInfo}${memoryInfo}`
    )
  }

  /**
   * Get current memory usage (if available)
   */
  private getMemoryUsage(): number {
    if (typeof window !== 'undefined' && 'memory' in performance) {
      return (performance as any).memory.usedJSHeapSize || 0
    }
    return 0
  }

  /**
   * Clear all metrics (useful for testing)
   */
  clearMetrics(): void {
    this.metrics = []
  }

  /**
   * Enable/disable monitoring
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled
  }

  /**
   * Export metrics for external analysis
   */
  exportMetrics(): PerformanceMetrics[] {
    return [...this.metrics]
  }
}

// Singleton instance
export const performanceMonitor = new PerformanceMonitor()

/**
 * Decorator for measuring function performance
 */
export function measurePerformance(operation: string) {
  return function <T extends (...args: any[]) => any>(
    target: any,
    propertyName: string,
    descriptor: TypedPropertyDescriptor<T>
  ) {
    const method = descriptor.value!

    descriptor.value = ((...args: any[]) => {
      const endTiming = performanceMonitor.startTiming(
        `${operation}.${propertyName}`
      )

      try {
        const result = method.apply(target, args)

        // Handle both sync and async functions
        if (result instanceof Promise) {
          return result
            .then(value => {
              endTiming()
              return value
            })
            .catch(error => {
              endTiming({ errorCount: 1 })
              throw error
            })
        } else {
          endTiming()
          return result
        }
      } catch (error) {
        endTiming({ errorCount: 1 })
        throw error
      }
    }) as T

    return descriptor
  }
}

/**
 * Hook for React components to measure render performance
 */
export function usePerformanceTracking(componentName: string) {
  const endTiming = performanceMonitor.startTiming(`render.${componentName}`)

  // Call endTiming in useEffect to measure complete render cycle
  return endTiming
}
