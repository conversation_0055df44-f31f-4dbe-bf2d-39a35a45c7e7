import { describe, it, expect, beforeEach } from '@jest/globals'
import { FiFileText, FiMessageSquare } from 'react-icons/fi'
import {
  generateAssetType,
  isGenerateTab,
  createGenerateTabId,
  getGenerateTabData,
} from '@/lib/asset-types/generate'
import {
  chatAssetType,
  isChatTab,
  createChatTabId,
  getChatTabData,
  generateChatAssetTitle,
} from '@/lib/asset-types/chat'
import { assetTypeRegistry } from '@/lib/asset-types/registry'
import type { Tab } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useTabStore'

describe('Generate Asset Type', () => {
  beforeEach(() => {
    assetTypeRegistry.clear()
    assetTypeRegistry.register(generateAssetType)
  })

  it('should have correct basic properties', () => {
    expect(generateAssetType.id).toBe('generate')
    expect(generateAssetType.displayName).toBe('Generate')
    expect(generateAssetType.icon).toBe(FiFileText)
    expect(generateAssetType.getAssetTypeColor()).toBe('text-blue-600')
  })

  it('should create correct tab title', () => {
    const asset = { title: 'My Generation' }
    expect(generateAssetType.createTabTitle(asset)).toBe(
      'Generate - My Generation'
    )
  })

  it('should identify generate type correctly', () => {
    expect(generateAssetType.isAssetType('generate')).toBe(true)
    expect(generateAssetType.isAssetType('chat')).toBe(false)
    expect(generateAssetType.isAssetType('other')).toBe(false)
  })

  it('should create asset tab data correctly', () => {
    const asset = {
      id: 'asset-123',
      model: 'gpt-4',
      prompt: 'Generate content',
      contextIds: ['ctx-1', 'ctx-2'],
      content: 'Generated content',
    }

    const tabData = generateAssetType.createAssetTabData!(asset)

    expect(tabData).toEqual({
      type: 'generate',
      model: 'gpt-4',
      prompt: 'Generate content',
      contextIds: ['ctx-1', 'ctx-2'],
      settings: {},
      assetContent: 'Generated content',
      assetId: 'asset-123',
      generationPhase: 'completed',
    })
  })

  it('should handle missing contextIds', () => {
    const asset = {
      id: 'asset-123',
      model: 'gpt-4',
      prompt: 'Generate content',
      content: 'Generated content',
    }

    const tabData = generateAssetType.createAssetTabData!(asset)
    expect(tabData?.contextIds).toEqual([])
  })
})

describe('Chat Asset Type', () => {
  beforeEach(() => {
    assetTypeRegistry.clear()
    assetTypeRegistry.register(chatAssetType)
  })

  it('should have correct basic properties', () => {
    expect(chatAssetType.id).toBe('chat')
    expect(chatAssetType.displayName).toBe('Chat')
    expect(chatAssetType.icon).toBe(FiMessageSquare)
    expect(chatAssetType.getAssetTypeColor()).toBe('text-green-600')
  })

  it('should create correct tab title', () => {
    const asset = { title: 'My Chat' }
    expect(chatAssetType.createTabTitle(asset)).toBe('My Chat')
  })

  it('should identify chat type correctly', () => {
    expect(chatAssetType.isAssetType('chat')).toBe(true)
    expect(chatAssetType.isAssetType('generate')).toBe(false)
    expect(chatAssetType.isAssetType('other')).toBe(false)
  })

  it('should create asset tab data correctly', () => {
    const asset = {
      id: 'chat-123',
      model: 'gpt-4',
      prompt: 'Chat prompt',
      contextIds: ['ctx-1'],
      content: 'Chat content',
      messages: [
        { role: 'user', content: 'Hello' },
        { role: 'assistant', content: 'Hi there!' },
      ],
    }

    const tabData = chatAssetType.createAssetTabData!(asset)

    expect(tabData).toEqual({
      type: 'chat',
      model: 'gpt-4',
      prompt: 'Chat prompt',
      contextIds: ['ctx-1'],
      settings: {},
      conversationId: 'chat-123',
      assetContent: 'Chat content',
      assetMessages: asset.messages,
      assetId: 'chat-123',
    })
  })
})

describe('Generate Helper Functions', () => {
  it('should identify generate tabs correctly', () => {
    const generateTab: Tab = {
      id: 'tab-1',
      title: 'Generate Tab',
      fullTitle: 'Generate Tab',
      type: 'generate',
      isClosable: true,
      aiPaneData: {
        type: 'generate',
        model: 'gpt-4',
        prompt: 'test',
        contextIds: [],
        settings: {},
      },
    }

    const chatTab: Tab = {
      id: 'tab-2',
      title: 'Chat Tab',
      fullTitle: 'Chat Tab',
      type: 'chat',
      isClosable: true,
      aiPaneData: {
        type: 'chat',
        model: 'gpt-4',
        prompt: 'test',
        contextIds: [],
        settings: {},
      },
    }

    expect(isGenerateTab(generateTab)).toBe(true)
    expect(isGenerateTab(chatTab)).toBe(false)
  })

  it('should create generate tab ID correctly', () => {
    expect(createGenerateTabId('asset-123')).toBe('asset-generate-asset-123')
  })

  it('should extract generate tab data correctly', () => {
    const generateTab: Tab = {
      id: 'tab-1',
      title: 'Generate Tab',
      fullTitle: 'Generate Tab',
      type: 'generate',
      isClosable: true,
      aiPaneData: {
        type: 'generate',
        model: 'gpt-4',
        prompt: 'test prompt',
        contextIds: ['ctx-1'],
        settings: {},
        assetId: 'asset-123',
        assetContent: 'content',
        generationPhase: 'completed',
      },
    }

    const data = getGenerateTabData(generateTab)
    expect(data).toEqual({
      assetId: 'asset-123',
      content: 'content',
      generationPhase: 'completed',
      model: 'gpt-4',
      prompt: 'test prompt',
      contextIds: ['ctx-1'],
    })
  })

  it('should return null for non-generate tabs', () => {
    const chatTab: Tab = {
      id: 'tab-1',
      title: 'Chat Tab',
      fullTitle: 'Chat Tab',
      type: 'chat',
      isClosable: true,
    }

    expect(getGenerateTabData(chatTab)).toBeNull()
  })
})

describe('Chat Helper Functions', () => {
  it('should identify chat tabs correctly', () => {
    const chatTab: Tab = {
      id: 'tab-1',
      title: 'Chat Tab',
      fullTitle: 'Chat Tab',
      type: 'chat',
      isClosable: true,
      aiPaneData: {
        type: 'chat',
        model: 'gpt-4',
        prompt: 'test',
        contextIds: [],
        settings: {},
      },
    }

    const generateTab: Tab = {
      id: 'tab-2',
      title: 'Generate Tab',
      fullTitle: 'Generate Tab',
      type: 'generate',
      isClosable: true,
    }

    expect(isChatTab(chatTab)).toBe(true)
    expect(isChatTab(generateTab)).toBe(false)
  })

  it('should create chat tab ID correctly', () => {
    expect(createChatTabId('chat-123')).toBe('asset-chat-chat-123')
  })

  it('should extract chat tab data correctly', () => {
    const chatTab: Tab = {
      id: 'tab-1',
      title: 'Chat Tab',
      fullTitle: 'Chat Tab',
      type: 'chat',
      isClosable: true,
      aiPaneData: {
        type: 'chat',
        model: 'gpt-4',
        prompt: 'test prompt',
        contextIds: ['ctx-1'],
        settings: {},
        assetId: 'chat-123',
        conversationId: 'conv-123',
        assetMessages: [{ role: 'user', content: 'Hello' }],
      },
    }

    const data = getChatTabData(chatTab)
    expect(data).toEqual({
      assetId: 'chat-123',
      conversationId: 'conv-123',
      messages: [{ role: 'user', content: 'Hello' }],
      model: 'gpt-4',
      prompt: 'test prompt',
      contextIds: ['ctx-1'],
    })
  })

  it('should return null for non-chat tabs', () => {
    const generateTab: Tab = {
      id: 'tab-1',
      title: 'Generate Tab',
      fullTitle: 'Generate Tab',
      type: 'generate',
      isClosable: true,
    }

    expect(getChatTabData(generateTab)).toBeNull()
  })
})

describe('generateChatAssetTitle', () => {
  it('should return short messages as-is', () => {
    const shortMessage = 'Hello world'
    expect(generateChatAssetTitle(shortMessage)).toBe('Hello world')
  })

  it('should truncate long messages at word boundaries', () => {
    const longMessage =
      'This is a very long message that should be truncated at a reasonable word boundary'
    const result = generateChatAssetTitle(longMessage)

    expect(result.length).toBeLessThanOrEqual(53) // 50 + '...'
    expect(result.endsWith('...')).toBe(true)
    // Should not cut words in the middle - check that the last word before ... is complete
    const withoutEllipsis = result.replace(/\.\.\.$/, '')
    expect(withoutEllipsis.endsWith(' ')).toBe(false) // Should end with a complete word, not space
  })

  it('should handle messages with no spaces', () => {
    const noSpaceMessage = 'a'.repeat(60)
    const result = generateChatAssetTitle(noSpaceMessage)

    expect(result.length).toBe(53) // 50 + '...'
    expect(result.endsWith('...')).toBe(true)
  })

  it('should trim whitespace', () => {
    const messageWithWhitespace = '  Hello world  '
    expect(generateChatAssetTitle(messageWithWhitespace)).toBe('Hello world')
  })
})
