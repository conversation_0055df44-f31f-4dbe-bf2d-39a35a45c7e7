#!/bin/bash
# Helper script to commit Telegram topic cache for team sharing

set -e

CACHE_FILE=".telegram_topic_cache"

echo "🔍 Checking Telegram topic cache status..."

if [ ! -f "$CACHE_FILE" ]; then
    echo "❌ No topic cache file found ($CACHE_FILE)"
    echo "   Run some notifications first to create topic mappings"
    exit 1
fi

echo "📋 Current topic mappings:"
cat "$CACHE_FILE" | while IFS=: read topic_name topic_id; do
    if [ -n "$topic_name" ] && [ -n "$topic_id" ]; then
        branch=$(echo "$topic_name" | cut -d: -f3)
        echo "   🌿 $branch → Topic ID: $topic_id"
    fi
done

echo ""

# Check if file has changes
if git diff --quiet "$CACHE_FILE" 2>/dev/null && git diff --cached --quiet "$CACHE_FILE" 2>/dev/null; then
    echo "✅ Topic cache is already up to date in git"
    exit 0
fi

echo "📝 Topic cache has changes, preparing to commit..."

# Add the cache file
git add "$CACHE_FILE"

# Get current branch for commit message
CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")

# Commit with descriptive message
COMMIT_MSG="Add/update Telegram topic mappings for branch: $CURRENT_BRANCH

This allows team members to reuse existing Telegram forum topics
instead of creating duplicates when working on this branch."

git commit -m "$COMMIT_MSG"

echo ""
echo "✅ Topic cache committed successfully!"
echo "🚀 Push to share with your team:"
echo "   git push"
echo ""
echo "💡 Team members can now pull and reuse existing topic mappings"
