// Create file with rate limiter utility
// In-memory rate limiter utility for serverless functions
// NOTE: Works within the same instance. For production-level multi-instance rate limiting, use external store (Redis, etc.)

// Shorter window to tolerate React Strict-Mode double calls when running
// `next dev` locally. We detect this via NODE_ENV === 'development'.
const LOCAL_DEV_WINDOW_MS = 500

const lastCallMap = new Map<string, number>()

/**
 * Check if the request identified by `key` is within the cooldown window.
 * Returns true if the caller should be rate-limited (i.e. block/early exit).
 * If not rate-limited, the timestamp is updated.
 *
 * @param key        unique identifier for the caller + endpoint (e.g. `${userId}:dragtree:list`)
 * @param windowMs   cooldown window in milliseconds
 */
export function isRateLimited(key: string, windowMs: number): boolean {
  // In local *development server* (`next dev`) use shorter window to allow
  // React-Strict-Mode double fetches. All other environments (preview, prod,
  // staging) use the full window.
  const isLocalDev = process.env.NODE_ENV === 'development'
  const effectiveWindow = isLocalDev ? LOCAL_DEV_WINDOW_MS : windowMs

  const now = Date.now()
  const last = lastCallMap.get(key) ?? 0

  if (now - last < effectiveWindow) {
    return true
  }

  lastCallMap.set(key, now)
  return false
}

/**
 * Get remaining seconds until rate limit window expires (for Retry-After header)
 */
export function getRetryAfterSeconds(key: string, windowMs: number): number {
  const isLocalDev = process.env.NODE_ENV === 'development'
  const effectiveWindow = isLocalDev ? LOCAL_DEV_WINDOW_MS : windowMs

  const now = Date.now()
  const last = lastCallMap.get(key) ?? 0
  const remaining = effectiveWindow - (now - last)

  return Math.ceil(remaining / 1000)
}
