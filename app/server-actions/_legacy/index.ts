// Export specific functions from each module (now in legacy)
export { fetchAllFeedbacks } from '@/app/(legacy)/_server-actions/feedback'
export {
  fetchIssueTree,
  checkIssueTreeIsActive,
  setIssueTreeStatus,
} from '@/app/(legacy)/_server-actions/issue-tree'
export { fetchSubtrees } from '@/app/(legacy)/_server-actions/subtree'
export { fetchSearches } from '@/app/(legacy)/_server-actions/search'
export { fetchRags } from '@/app/(legacy)/_server-actions/rag'
export {
  fetchProduct,
  fetchPrice,
} from '@/app/(legacy)/_server-actions/product'

// Also export the types if they are needed elsewhere
export type { fetchedFeedbacksType } from '@/app/(legacy)/_server-actions/feedback'
export type { fetchedIssueTreeType } from '@/app/(legacy)/_server-actions/issue-tree'
export type { fetchedSubtreesType } from '@/app/(legacy)/_server-actions/subtree'
export type { fetchedSearchesType } from '@/app/(legacy)/_server-actions/search'
export type { fetchedRagsType } from '@/app/(legacy)/_server-actions/rag'
