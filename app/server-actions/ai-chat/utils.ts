import { generateAiAttachmentId } from '@/lib/id'
import { validateAttachmentData } from '@/app/libs/ai-chat/validation'
import type { ExecutionStepData, AttachmentData } from './types'

/**
 * Normalizes step type to lowercase for consistent storage
 */
function normalizeStepType(type: string): string {
  return type.toLowerCase()
}

/**
 * Normalizes step orders to prevent duplicate constraint violations
 * Sorts steps by original stepOrder and reassigns as 0, 1, 2, etc.
 * Also normalizes step types to lowercase for consistent database storage.
 * Optimized with early-exit for already-ordered sequences.
 */
export function normalizeStepOrders(
  steps: ExecutionStepData[]
): ExecutionStepData[] {
  if (!steps || steps.length === 0) return steps

  // Early-exit optimization: check if steps are already ordered correctly
  const isAlreadyOrdered = steps.every(
    (step, index) => step.stepOrder === index
  )

  // Even when order is correct, normalize type for consistency
  if (isAlreadyOrdered) {
    return steps.map(step => ({ ...step, type: normalizeStepType(step.type) }))
  }

  // Sort by original stepOrder to maintain intended sequence
  const sortedSteps = [...steps].sort((a, b) => a.stepOrder - b.stepOrder)

  // Reassign stepOrder as 0, 1, 2, etc. and normalize step types
  return sortedSteps.map((step, index) => ({
    ...step,
    stepOrder: index,
    type: normalizeStepType(step.type), // Normalize to lowercase
  }))
}

/**
 * Creates attachment data for Prisma create operation
 * Reduces code duplication across multiple functions
 */
export function buildAttachmentCreate(attachment: AttachmentData) {
  // Validate attachment data
  validateAttachmentData(attachment)

  return {
    id: generateAiAttachmentId(),
    fileName: attachment.fileName,
    fileType: attachment.fileType,
    fileSize: attachment.fileSize,
    url: attachment.url,
  }
}
