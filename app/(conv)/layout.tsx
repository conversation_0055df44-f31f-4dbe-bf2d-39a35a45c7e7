import React from 'react'
import ConvLayoutClient from '@/app/(conv)/components/layout/ConvLayoutClient'
import { auth } from '@/auth'
import { getDragTreesByUserId } from '@/app/server-actions/drag-tree'
import type { DragTreeData } from '@/app/libs/sidebar-events'

type ConvLayoutProps = {
  children: React.ReactNode
}

const ConvLayout = async ({ children }: ConvLayoutProps) => {
  // Fetch session server-side so we can query user-specific drag trees in parallel
  const session = await auth()

  let initialDragTrees: DragTreeData[] = []

  if (session?.user?.id) {
    const sidebarResult = await getDragTreesByUserId(session.user.id)
    if (sidebarResult.success && Array.isArray(sidebarResult.data)) {
      initialDragTrees = sidebarResult.data as DragTreeData[]
    }
  }

  return (
    <ConvLayoutClient initialDragTrees={initialDragTrees}>
      {children}
    </ConvLayoutClient>
  )
}

export default ConvLayout
