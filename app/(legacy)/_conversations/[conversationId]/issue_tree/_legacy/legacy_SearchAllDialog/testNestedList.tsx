// import React, { useState } from "react";
// import toast, { Toaster } from "react-hot-toast";
// import { Button } from "@/components/ui/button";

// interface CheckboxNode {
//   label: string;
//   children?: CheckboxNode[];
// }

// const checkboxData: CheckboxNode[] = [
//   {
//     label: "Applied AI",
//     children: [
//       {
//         label: "Applied AI Engineering",
//         children: [{ label: "AI Ops" }, { label: "AI Biz" }],
//       },
//       { label: "Applied AI Infrastructure" },
//       { label: "Applied AI Product" },
//     ],
//   },
//   { label: "Communications" },
//   { label: "Corporate Security" },
//   { label: "Data Science" },
//   { label: "Finance" },
// ];

// const CheckboxTree: React.FC<{ nodes: CheckboxNode[] }> = ({ nodes }) => {
//   const [checkedItems, setCheckedItems] = useState<{ [key: string]: boolean }>(
//     {}
//   );

//   const handleCheckboxChange = (label: string, children?: CheckboxNode[]) => {
//     const newCheckedItems = { ...checkedItems };
//     const isChecked = !checkedItems[label];

//     const updateChildren = (children: CheckboxNode[], isChecked: boolean) => {
//       children.forEach((child) => {
//         newCheckedItems[child.label] = isChecked;
//         if (child.children) {
//           updateChildren(child.children, isChecked);
//         }
//       });
//     };

//     newCheckedItems[label] = isChecked;
//     if (children) {
//       updateChildren(children, isChecked);
//     }

//     setCheckedItems(newCheckedItems);
//   };

//   const renderTree = (nodes: CheckboxNode[], level = 0) => {
//     return nodes.map((node) => (
//       <div key={node.label} style={{ paddingLeft: `${level * 20}px` }}>
//         <label>
//           <input
//             type="checkbox"
//             checked={!!checkedItems[node.label]}
//             onChange={() => handleCheckboxChange(node.label, node.children)}
//           />
//           {node.label}
//         </label>
//         {node.children && renderTree(node.children, level + 1)}
//       </div>
//     ));
//   };

//   const handleButtonClick = () => {
//     const selectedItems = Object.keys(checkedItems).filter(
//       (key) => checkedItems[key]
//     );
//     toast.success(`Selected items: ${selectedItems.join(", ")}`);
//   };

//   return (
//     <div>
//       {/* <Toaster /> */}
//       {renderTree(nodes)}
//       <Button onClick={handleButtonClick}>Show Selected Items</Button>
//     </div>
//   );
// };

// const NestedCheckbox: React.FC = () => {
//   return (
//     <div>
//       <h1>Nested Checkbox</h1>
//       <CheckboxTree nodes={checkboxData} />
//     </div>
//   );
// };

// export default NestedCheckbox;
