'use client'

import React from 'react'
import { Session } from 'next-auth'
import Header from '@/app/(conv)/dragTree/[dragTreeId]/components/layout/Header'

type GeneratingStateProps = {
  session: Session | null
  showSyncTest: boolean
  onToggleSyncTest: () => void
  generationProgress: string
}

export default function GeneratingState({
  session,
  showSyncTest,
  onToggleSyncTest,
  generationProgress,
}: GeneratingStateProps) {
  return (
    <div className="flex flex-col h-screen w-full bg-white overflow-hidden animate-in fade-in duration-300">
      <div className="flex-shrink-0 border-b">
        <Header
          session={session}
          showSyncTest={showSyncTest}
          onToggleSyncTest={onToggleSyncTest}
        />
      </div>
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center animate-in slide-in-from-bottom-4 duration-500 delay-100">
          <div className="relative mb-6">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-8 h-8 bg-blue-100 rounded-full animate-pulse"></div>
            </div>
          </div>
          <h2 className="text-xl font-semibold text-gray-700 mb-2">
            Generating Clarification Questions
          </h2>
          <p className="text-gray-500 mb-4">
            {generationProgress ||
              'Our AI is analyzing your request and creating a structured clarification tree...'}
          </p>
          <div className="max-w-md mx-auto bg-blue-50 rounded-lg p-4">
            <div className="flex items-center space-x-2 text-blue-600 text-sm">
              <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
              <span>This may take a few moments</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
