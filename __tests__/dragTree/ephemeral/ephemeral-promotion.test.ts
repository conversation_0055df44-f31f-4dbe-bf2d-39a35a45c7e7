/**
 * Contract tests for ephemeral node promotion (conceptual)
 * Here we assert the expected behavior of applyTreeUpdateAtomic when called
 * to persist previously ephemeral nodes: creates + nextTreeStructure.
 */

import { applyTreeUpdateAtomic } from '@/app/server-actions/drag-tree/atomic'

jest.mock('@/app/libs/prismadb', () => ({
  __esModule: true,
  default: { $transaction: jest.fn() },
}))

const prisma = require('@/app/libs/prismadb').default

describe('Ephemeral promotion', () => {
  beforeEach(() => jest.clearAllMocks())

  it('persists ephemeral nodes via creates + structure', async () => {
    const createMany = jest.fn().mockResolvedValue({ count: 2 })
    const updateTree = jest.fn().mockResolvedValue({})
    const findManyActive = jest
      .fn()
      .mockResolvedValue([{ id: 'root' }, { id: 'x' }, { id: 'y' }])

    prisma.$transaction.mockImplementation(async (cb: any) => {
      const tx = {
        dragTreeNode: {
          createMany,
          update: jest.fn(),
          findMany: findManyActive,
        },
        dragTree: { update: updateTree },
      }
      return cb(tx)
    })

    const nextTreeStructure = {
      root_id: 'root',
      hierarchy: { root: ['x', 'y'], x: [], y: [] },
    }

    const res = await applyTreeUpdateAtomic({
      treeId: 'tree_123',
      creates: [
        { id: 'x', label: 'X', node_type: 'QUESTION' },
        { id: 'y', label: 'Y', node_type: 'QUESTION' },
      ],
      nextTreeStructure,
    })

    expect(res).toEqual({ success: true })
    expect(createMany).toHaveBeenCalled()
    expect(updateTree).toHaveBeenCalled()
  })
})
