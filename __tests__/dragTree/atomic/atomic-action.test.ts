/**
 * Tests for applyTreeUpdateAtomic server action
 * Verifies atomic behavior and end-of-transaction alignment validation.
 */

import { applyTreeUpdateAtomic } from '@/app/server-actions/drag-tree/atomic'

// Mock Prisma client used inside the server action
jest.mock('@/app/libs/prismadb', () => ({
  __esModule: true,
  default: {
    $transaction: jest.fn(),
  },
}))

const prisma = require('@/app/libs/prismadb').default

describe('applyTreeUpdateAtomic', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('commits when creates and nextTreeStructure align', async () => {
    const createMany = jest.fn().mockResolvedValue({ count: 2 })
    const updateTree = jest.fn().mockResolvedValue({})
    const findManyActive = jest
      .fn()
      .mockResolvedValue([{ id: 'root' }, { id: 'child1' }])

    // Simulate Prisma transaction
    prisma.$transaction.mockImplementation(async (cb: any) => {
      const tx = {
        dragTreeNode: {
          createMany: createMany,
          update: jest.fn(),
          findMany: findManyActive,
        },
        dragTree: {
          update: updateTree,
        },
      }
      return await cb(tx)
    })

    const nextTreeStructure = {
      root_id: 'root',
      hierarchy: { root: ['child1'], child1: [] },
    }

    const res = await applyTreeUpdateAtomic({
      treeId: 'tree_1',
      creates: [
        { id: 'root', label: 'Root', node_type: 'CATEGORY' },
        { id: 'child1', label: 'Child 1', node_type: 'QUESTION' },
      ],
      nextTreeStructure,
    })

    expect(res).toEqual({ success: true })
    expect(prisma.$transaction).toHaveBeenCalledTimes(1)
    expect(createMany).toHaveBeenCalledWith(
      expect.objectContaining({
        data: expect.arrayContaining([
          expect.objectContaining({ id: 'root', drag_tree_id: 'tree_1' }),
          expect.objectContaining({ id: 'child1', drag_tree_id: 'tree_1' }),
        ]),
        skipDuplicates: true,
      })
    )
    expect(updateTree).toHaveBeenCalledWith({
      where: { id: 'tree_1' },
      data: expect.objectContaining({ tree_structure: nextTreeStructure }),
    })
  })

  it('rolls back (throws) when alignment validation fails', async () => {
    const createMany = jest.fn().mockResolvedValue({ count: 2 })
    const updateTree = jest.fn().mockResolvedValue({})
    // Misalignment: ACTIVE nodes missing or extra vs nextTreeStructure
    const findManyActive = jest.fn().mockResolvedValue([
      { id: 'root' },
      // child1 missing → mismatch
    ])

    prisma.$transaction.mockImplementation(async (cb: any) => {
      const tx = {
        dragTreeNode: {
          createMany: createMany,
          update: jest.fn(),
          findMany: findManyActive,
        },
        dragTree: {
          update: updateTree,
        },
      }
      // The callback will throw due to alignment check; transaction should reject
      return await cb(tx)
    })

    const nextTreeStructure = {
      root_id: 'root',
      hierarchy: { root: ['child1'], child1: [] },
    }

    await expect(
      applyTreeUpdateAtomic({
        treeId: 'tree_1',
        creates: [
          { id: 'root', label: 'Root', node_type: 'CATEGORY' },
          { id: 'child1', label: 'Child 1', node_type: 'QUESTION' },
        ],
        nextTreeStructure,
      })
    ).rejects.toThrow(/out of sync|rolled back/i)

    expect(prisma.$transaction).toHaveBeenCalledTimes(1)
    // createMany and update were attempted inside the transaction before validation
    expect(createMany).toHaveBeenCalled()
    expect(updateTree).toHaveBeenCalled()
  })

  it('updates labels only and commits with alignment intact', async () => {
    const updateLabel = jest.fn().mockResolvedValue({})
    const updateTree = jest.fn().mockResolvedValue({})
    const findManyActive = jest
      .fn()
      .mockResolvedValue([{ id: 'root' }, { id: 'child1' }])

    prisma.$transaction.mockImplementation(async (cb: any) => {
      const tx = {
        dragTreeNode: {
          createMany: jest.fn(),
          update: updateLabel,
          findMany: findManyActive,
        },
        dragTree: {
          update: updateTree,
        },
      }
      return await cb(tx)
    })

    const nextTreeStructure = {
      root_id: 'root',
      hierarchy: { root: ['child1'], child1: [] },
    }

    const res = await applyTreeUpdateAtomic({
      treeId: 'tree_2',
      updateLabels: [
        { id: 'root', label: 'Root Updated' },
        { id: 'child1', label: 'Child 1 Updated' },
      ],
      nextTreeStructure,
    })

    expect(res).toEqual({ success: true })
    expect(updateLabel).toHaveBeenCalledTimes(2)
    expect(updateTree).toHaveBeenCalled()
  })

  it('marks nodes INACTIVE and commits when structure excludes them', async () => {
    const updateStatus = jest.fn().mockResolvedValue({})
    const updateTree = jest.fn().mockResolvedValue({})
    const findManyActive = jest.fn().mockResolvedValue([{ id: 'root' }])

    prisma.$transaction.mockImplementation(async (cb: any) => {
      const tx = {
        dragTreeNode: {
          createMany: jest.fn(),
          update: updateStatus,
          findMany: findManyActive,
        },
        dragTree: { update: updateTree },
      }
      return await cb(tx)
    })

    const nextTreeStructure = {
      root_id: 'root',
      hierarchy: { root: [] },
    }

    const res = await applyTreeUpdateAtomic({
      treeId: 'tree_3',
      updateStatuses: [{ id: 'child1', status: 'INACTIVE' }],
      nextTreeStructure,
    })

    expect(res).toEqual({ success: true })
    expect(updateStatus).toHaveBeenCalledWith({
      where: { id: 'child1' },
      data: { status: 'INACTIVE' },
    })
    expect(updateTree).toHaveBeenCalled()
  })

  it('fails when structure references a node marked INACTIVE', async () => {
    const updateStatus = jest.fn().mockResolvedValue({})
    const updateTree = jest.fn().mockResolvedValue({})
    const findManyActive = jest.fn().mockResolvedValue([{ id: 'root' }])

    prisma.$transaction.mockImplementation(async (cb: any) => {
      const tx = {
        dragTreeNode: {
          createMany: jest.fn(),
          update: updateStatus,
          findMany: findManyActive,
        },
        dragTree: { update: updateTree },
      }
      return await cb(tx)
    })

    const nextTreeStructure = {
      root_id: 'root',
      hierarchy: { root: ['child1'], child1: [] },
    }

    await expect(
      applyTreeUpdateAtomic({
        treeId: 'tree_4',
        updateStatuses: [{ id: 'child1', status: 'INACTIVE' }],
        nextTreeStructure,
      })
    ).rejects.toThrow(/out of sync|rolled back/i)

    expect(updateStatus).toHaveBeenCalled()
    expect(updateTree).toHaveBeenCalled()
  })

  it('structure-only update commits if active set matches', async () => {
    const updateTree = jest.fn().mockResolvedValue({})
    const findManyActive = jest
      .fn()
      .mockResolvedValue([{ id: 'root' }, { id: 'child1' }])

    prisma.$transaction.mockImplementation(async (cb: any) => {
      const tx = {
        dragTreeNode: {
          createMany: jest.fn(),
          update: jest.fn(),
          findMany: findManyActive,
        },
        dragTree: { update: updateTree },
      }
      return await cb(tx)
    })

    const nextTreeStructure = {
      root_id: 'root',
      hierarchy: { root: ['child1'], child1: [] },
    }

    const res = await applyTreeUpdateAtomic({
      treeId: 'tree_5',
      nextTreeStructure,
    })

    expect(res).toEqual({ success: true })
    expect(updateTree).toHaveBeenCalled()
  })

  it('replaces a child by creating a new node and inactivating the old one', async () => {
    const createMany = jest.fn().mockResolvedValue({ count: 1 })
    const updateStatus = jest.fn().mockResolvedValue({})
    const updateTree = jest.fn().mockResolvedValue({})
    const findManyActive = jest
      .fn()
      .mockResolvedValue([{ id: 'root' }, { id: 'child2' }])

    prisma.$transaction.mockImplementation(async (cb: any) => {
      const tx = {
        dragTreeNode: {
          createMany: createMany,
          update: updateStatus,
          findMany: findManyActive,
        },
        dragTree: { update: updateTree },
      }
      return await cb(tx)
    })

    const nextTreeStructure = {
      root_id: 'root',
      hierarchy: { root: ['child2'], child2: [] },
    }

    const res = await applyTreeUpdateAtomic({
      treeId: 'tree_6',
      creates: [{ id: 'child2', label: 'Child 2', node_type: 'QUESTION' }],
      updateStatuses: [{ id: 'child1', status: 'INACTIVE' }],
      nextTreeStructure,
    })

    expect(res).toEqual({ success: true })
    expect(createMany).toHaveBeenCalled()
    expect(updateStatus).toHaveBeenCalled()
    expect(updateTree).toHaveBeenCalled()
  })
})
