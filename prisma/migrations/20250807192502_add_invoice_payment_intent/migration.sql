-- CreateTable
CREATE TABLE "invoices" (
    "id" TEXT NOT NULL,
    "customer_id" TEXT NOT NULL,
    "user_id" TEXT,
    "subscription_id" TEXT,
    "product_id" TEXT,
    "price_id" TEXT,
    "status" TEXT NOT NULL,
    "currency" TEXT NOT NULL,
    "number" TEXT,
    "hosted_invoice_url" TEXT,
    "invoice_pdf" TEXT,
    "subtotal" BIGINT,
    "total" BIGINT NOT NULL,
    "amount_paid" BIGINT,
    "amount_due" BIGINT,
    "attempted" BOOLEAN DEFAULT false,
    "paid" BOOLEAN DEFAULT false,
    "period_start" TIMESTAMP(3),
    "period_end" TIMESTAMP(3),
    "due_date" TIMESTAMP(3),
    "metadata" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "invoices_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "payment_intents" (
    "id" TEXT NOT NULL,
    "customer_id" TEXT,
    "user_id" TEXT,
    "amount" BIGINT NOT NULL,
    "currency" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "payment_method" TEXT,
    "latest_charge" TEXT,
    "receipt_url" TEXT,
    "description" TEXT,
    "metadata" TEXT,
    "stripe_created_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "payment_intents_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "invoices_customer_id_idx" ON "invoices" USING HASH ("customer_id");

-- CreateIndex
CREATE INDEX "invoices_user_id_idx" ON "invoices" USING HASH ("user_id");

-- CreateIndex
CREATE INDEX "invoices_subscription_id_idx" ON "invoices" USING HASH ("subscription_id");

-- CreateIndex
CREATE INDEX "invoices_status_idx" ON "invoices" USING HASH ("status");

-- CreateIndex
CREATE INDEX "payment_intents_customer_id_idx" ON "payment_intents" USING HASH ("customer_id");

-- CreateIndex
CREATE INDEX "payment_intents_user_id_idx" ON "payment_intents" USING HASH ("user_id");

-- CreateIndex
CREATE INDEX "payment_intents_status_idx" ON "payment_intents" USING HASH ("status");

-- AddForeignKey
ALTER TABLE "invoices" ADD CONSTRAINT "invoices_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "invoices" ADD CONSTRAINT "invoices_subscription_id_fkey" FOREIGN KEY ("subscription_id") REFERENCES "subscriptions"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "invoices" ADD CONSTRAINT "invoices_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "invoices" ADD CONSTRAINT "invoices_price_id_fkey" FOREIGN KEY ("price_id") REFERENCES "prices"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "payment_intents" ADD CONSTRAINT "payment_intents_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;
