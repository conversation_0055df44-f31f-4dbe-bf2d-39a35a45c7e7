import Link from "next/link";
import { contactEmail } from "@/app/configs";

export const LandingFooter = () => {
  const currentYear = new Date().getFullYear();
  return (
    <footer className="text-center text-white lg:text-left">
      <div className="flex flex-col items-center justify-center py-12 md:flex-row md:justify-between md:py-8">
        <div>
          <p className="mb-4 flex items-center justify-center md:justify-start">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="currentColor"
              className="mr-3 h-5 w-5"
            >
              <path d="M1.5 8.67v8.58a3 3 0 003 3h15a3 3 0 003-3V8.67l-8.928 5.493a3 3 0 01-3.144 0L1.5 8.67z" />
              <path d="M22.5 6.908V6.75a3 3 0 00-3-3h-15a3 3 0 00-3 3v.158l9.714 5.978a1.5 1.5 0 001.572 0L22.5 6.908z" />
            </svg>
            {contactEmail}
          </p>
          <Link href="/tos">Terms of Service</Link>
          <br />
          <Link href="/privacy">Privacy Policy</Link>
        </div>
        <div>
          <div className="p-4 text-center">
            © {currentYear} Copyright: Clarify AI
          </div>
        </div>
      </div>
    </footer>
  );
};
