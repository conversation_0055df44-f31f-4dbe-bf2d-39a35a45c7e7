import {
  getCachedMessages,
  setCachedMessages,
  CachedMessage,
} from './conversationCache'
import prisma from '@/app/libs/prismadb'

// Build context: system prompt + first user message + recent messages (deprecated - use buildContextMessagesV2)
export async function buildContextMessages(
  conversationId: string,
  newUserMessage: { role: 'user'; content: string },
  maxMessages: number = 14 // Max messages to include (7 assistant + 7 user/system)
): Promise<{
  messages: { role: 'user' | 'assistant' | 'system'; content: string }[]
}> {
  // try cache first
  let cached = getCachedMessages(conversationId)
  let all: CachedMessage[]
  if (!cached) {
    const rows = await prisma.aiMessage.findMany({
      where: { conversationId },
      orderBy: { createdAt: 'asc' },
      select: { id: true, role: true, content: true, createdAt: true },
    })
    all = rows.map(r => ({
      id: r.id,
      role: r.role.toLowerCase() as 'user' | 'assistant' | 'system',
      content: r.content,
      createdAt: r.createdAt,
    }))
    setCachedMessages(conversationId, all)
  } else {
    all = cached
  }

  if (!all || all.length === 0) {
    // brand-new conversation, just system + new user
    return {
      messages: [
        {
          role: 'system',
          content:
            'You are a helpful AI assistant in a drag tree chat interface. Provide comprehensive and well-structured responses using markdown formatting when appropriate.',
        },
        newUserMessage,
      ],
    }
  }

  const systemPrompt = {
    role: 'system' as const,
    content:
      'You are a helpful AI assistant in a drag tree chat interface. Provide comprehensive and well-structured responses using markdown formatting when appropriate.',
  }

  const firstUserOrSystem = all[0]

  // Keep first historical message and recent messages up to maxMessages limit
  let context: CachedMessage[] = [firstUserOrSystem]

  // Get recent messages (excluding the first one we already have)
  const recentMessages = all.slice(1).slice(-Math.max(0, maxMessages - 2)) // -2 for system and new user message
  context.push(...recentMessages)

  const final = [systemPrompt, ...context, newUserMessage]

  return { messages: final }
}
