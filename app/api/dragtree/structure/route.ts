import { NextRequest, NextResponse } from 'next/server'
import { getDragTreeStructure } from '@/app/server-actions/drag-tree/get-tree-structure'
import { isRateLimited, getRetryAfterSeconds } from '@/app/libs/rateLimiter'
import { auth } from '@/auth'

/**
 * GET /api/dragtree/structure
 *
 * Lightweight endpoint for fetching tree structure without heavy content
 * Optimized for fast initial page loads with minimal payload
 *
 * Query Parameters:
 * - dragTreeId: string (required) - The tree ID to fetch
 * - includeMetrics: boolean (optional) - Include performance metrics in response
 */
export async function GET(request: NextRequest) {
  const startTime = performance.now()

  try {
    // Validate authentication
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Extract query parameters
    const { searchParams } = new URL(request.url)
    const dragTreeId = searchParams.get('dragTreeId')
    const includeMetrics = searchParams.get('includeMetrics') === 'true'

    if (!dragTreeId) {
      return NextResponse.json(
        { error: 'dragTreeId parameter is required' },
        { status: 400 }
      )
    }

    // Rate limiting to prevent abuse
    const rateLimitKey = `${session.user.id}:dragtree:structure:${dragTreeId}`
    if (isRateLimited(rateLimitKey, 2000)) {
      // 2 second cooldown
      const retryAfter = getRetryAfterSeconds(rateLimitKey, 2000)
      return NextResponse.json(
        { error: 'Too many requests – please wait a moment.' },
        {
          status: 429,
          headers: { 'Retry-After': retryAfter.toString() },
        }
      )
    }

    console.log(`🚀 [API] /dragtree/structure - Fetching tree: ${dragTreeId}`)

    // Fetch lightweight tree structure
    const result = await getDragTreeStructure(dragTreeId)
    const totalTime = performance.now() - startTime

    if (!result.success) {
      console.log(`❌ [API] /dragtree/structure - Failed: ${result.error}`)
      return NextResponse.json(
        { error: result.error },
        { status: result.error === 'Unauthorized' ? 401 : 404 }
      )
    }

    // Prepare response with optional metrics
    const response: any = {
      success: true,
      data: result.data,
      userMetadata: result.userMetadata,
    }

    if (includeMetrics) {
      response.metrics = {
        ...result.metrics,
        totalRequestTime: totalTime,
        endpoint: 'structure',
        timestamp: new Date().toISOString(),
      }
    }

    console.log(`✅ [API] /dragtree/structure - Success:`, {
      treeId: dragTreeId,
      nodeCount: result.data.nodes.length,
      payloadSize: `${(result.metrics.payloadSize / 1024).toFixed(2)}KB`,
      totalTime: `${totalTime.toFixed(2)}ms`,
    })

    // Add performance headers for monitoring
    const headers: Record<string, string> = {
      'X-Response-Time': `${totalTime.toFixed(2)}ms`,
      'X-Payload-Size': result.metrics.payloadSize.toString(),
      'X-Node-Count': result.data.nodes.length.toString(),
    }

    return NextResponse.json(response, { headers })
  } catch (error) {
    const totalTime = performance.now() - startTime
    console.error('❌ [API] /dragtree/structure - Unexpected error:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        requestTime: `${totalTime.toFixed(2)}ms`,
      },
      {
        status: 500,
        headers: {
          'X-Response-Time': `${totalTime.toFixed(2)}ms`,
        },
      }
    )
  }
}

/**
 * OPTIONS handler for CORS preflight requests
 */
export async function OPTIONS() {
  return NextResponse.json(
    {},
    {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    }
  )
}
