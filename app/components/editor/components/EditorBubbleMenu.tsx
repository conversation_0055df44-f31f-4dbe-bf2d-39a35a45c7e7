import { BubbleMenu, BubbleMenuProps, Editor } from '@tiptap/react'
import { FC, useState } from 'react'
import {
  BoldIcon,
  ItalicIcon,
  UnderlineIcon,
  StrikethroughIcon,
  CodeIcon,
  TableIcon,
} from 'lucide-react'

import { NodeSelector } from './node-selector'
import { ColorSelector } from './color-selector'
import { LinkSelector } from './link-selector'
import { cn } from '@/lib/utils'
import { createTableWithContent } from '../utils/table-helpers'

export type BubbleMenuItem = {
  name: string
  isActive: (editor: Editor) => boolean
  command: (editor: Editor) => void
  icon: typeof BoldIcon
}

type EditorBubbleMenuProps = Omit<BubbleMenuProps, 'children'>

export const EditorBubbleMenu: FC<EditorBubbleMenuProps> = props => {
  const [isNodeSelectorOpen, setIsNodeSelectorOpen] = useState<boolean>(false)
  const [isColorSelectorOpen, setIsColorSelectorOpen] = useState<boolean>(false)
  const [isLinkSelectorOpen, setIsLinkSelectorOpen] = useState<boolean>(false)

  const items: BubbleMenuItem[] = [
    {
      name: 'bold',
      isActive: (editor: Editor) => editor.isActive('bold'),
      command: (editor: Editor) => editor.chain().focus().toggleBold().run(),
      icon: BoldIcon,
    },
    {
      name: 'italic',
      isActive: (editor: Editor) => editor.isActive('italic'),
      command: (editor: Editor) => editor.chain().focus().toggleItalic().run(),
      icon: ItalicIcon,
    },
    {
      name: 'underline',
      isActive: (editor: Editor) => editor.isActive('underline'),
      command: (editor: Editor) =>
        editor.chain().focus().toggleUnderline().run(),
      icon: UnderlineIcon,
    },
    {
      name: 'strike',
      isActive: (editor: Editor) => editor.isActive('strike'),
      command: (editor: Editor) => editor.chain().focus().toggleStrike().run(),
      icon: StrikethroughIcon,
    },
    {
      name: 'code',
      isActive: (editor: Editor) => editor.isActive('code'),
      command: (editor: Editor) => editor.chain().focus().toggleCode().run(),
      icon: CodeIcon,
    },
    {
      name: 'table',
      isActive: (editor: Editor) => editor.isActive('table'),
      command: (editor: Editor) => createTableWithContent(editor),
      icon: TableIcon,
    },
  ]

  const bubbleMenuProps: EditorBubbleMenuProps = {
    ...props,
    shouldShow: ({ editor }) => {
      // Don't show if selection is empty
      if (editor.state.selection.empty) {
        return false
      }

      // Don't show if image is selected
      if (editor.isActive('image')) {
        return false
      }
      return true
    },
    tippyOptions: {
      moveTransition: 'transform 0.15s ease-out',
      appendTo: () => document.body,
      placement: 'top',
      popperOptions: {
        strategy: 'fixed',
        modifiers: [
          {
            name: 'preventOverflow',
            options: {
              boundary: 'viewport',
            },
          },
        ],
      },
      onHidden: () => {
        setIsNodeSelectorOpen(false)
        setIsColorSelectorOpen(false)
        setIsLinkSelectorOpen(false)
      },
    },
  }

  const toggleSelector = (
    selector: 'node' | 'color' | 'link',
    currentState: boolean
  ) => {
    setIsNodeSelectorOpen(selector === 'node' ? !currentState : false)
    setIsColorSelectorOpen(selector === 'color' ? !currentState : false)
    setIsLinkSelectorOpen(selector === 'link' ? !currentState : false)
  }

  return (
    <BubbleMenu
      {...bubbleMenuProps}
      className="flex w-fit divide-x divide-stone-200 rounded-md border border-stone-200 bg-white shadow-md"
    >
      {props.editor && (
        <>
          <NodeSelector
            editor={props.editor}
            isOpen={isNodeSelectorOpen}
            setIsOpen={() => toggleSelector('node', isNodeSelectorOpen)}
          />
          <LinkSelector
            editor={props.editor}
            isOpen={isLinkSelectorOpen}
            setIsOpen={() => toggleSelector('link', isLinkSelectorOpen)}
          />
          <div className="flex flex-shrink-0">
            {items.map((item, index) => (
              <button
                key={index}
                onClick={() => item.command(props.editor!)}
                className="p-2 text-stone-600 hover:bg-stone-100 active:bg-stone-200"
              >
                <item.icon
                  className={cn('h-4 w-4', {
                    'text-blue-500': item.isActive(props.editor!),
                  })}
                />
              </button>
            ))}
          </div>
          <ColorSelector
            editor={props.editor}
            isOpen={isColorSelectorOpen}
            setIsOpen={() => toggleSelector('color', isColorSelectorOpen)}
          />
        </>
      )}
    </BubbleMenu>
  )
}
