'use client'

import { Node<PERSON>iewWrapper, NodeViewProps } from '@tiptap/react'
import React, { useState, useRef, useEffect } from 'react'
import Mermaid<PERSON><PERSON><PERSON><PERSON><PERSON>, {
  MermaidChartRendererHandle,
} from '@/app/components/MermaidChartRenderer'
import { Button } from '@/components/ui/button'
import { toast } from 'react-hot-toast'
import { Edit3, Check, X, Co<PERSON>, Bot } from 'lucide-react'

type MermaidBlockComponentProps = NodeViewProps

export default function MermaidBlockComponent({
  node,
  updateAttributes,
  selected,
}: MermaidBlockComponentProps) {
  // Get mermaid code from node attributes (clean data access)
  const defaultExample = `graph TD
    A[📋 Project Planning] --> B{Budget Available?}
    B -->|Yes| C[🎯 Define Requirements]
    B -->|No| D[💰 Secure Funding]
    D --> C
    C --> E[👥 Assemble Team]
    E --> F[⚙️ Development Phase]
    F --> G{Quality Check}
    G -->|Pass| H[🚀 Launch]
    G -->|Fail| I[🔧 Fix Issues]
    I --> F
    H --> J[📊 Monitor & Maintain]

    %% Styling for better visual appeal
    classDef startEnd fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef process fill:#f3e5f5,stroke:#4a148c,stroke-width:2px

    class A,H,J startEnd
    class B,G decision
    class C,D,E,F,I process`

  const [code, setCode] = useState<string>(node.attrs.code || defaultExample)
  const [isEditing, setIsEditing] = useState<boolean>(false)
  const [editingCode, setEditingCode] = useState<string>(code)
  const [mermaidError, setMermaidError] = useState<string | null>(null)

  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const mermaidRef = useRef<MermaidChartRendererHandle>(null)

  // Auto-focus textarea when entering edit mode
  useEffect(() => {
    if (isEditing && textareaRef.current) {
      textareaRef.current.focus()
      textareaRef.current.select()
    }
  }, [isEditing])

  // Handle saving changes
  const handleSave = () => {
    setCode(editingCode)
    updateAttributes({ code: editingCode })
    setIsEditing(false)
    setMermaidError(null)
  }

  // Handle cancelling changes
  const handleCancel = () => {
    setEditingCode(code)
    setIsEditing(false)
  }

  // Handle starting edit mode
  const handleEdit = () => {
    setEditingCode(code)
    setIsEditing(true)
  }

  // Handle copying as markdown
  const handleCopyMarkdown = () => {
    const markdownCode = `\`\`\`mermaid\n${code}\n\`\`\``
    navigator.clipboard
      .writeText(markdownCode)
      .then(() => toast.success('Copied as markdown'))
      .catch(err => {
        console.error('Could not copy text: ', err)
        toast.error('Failed to copy')
      })
  }

  // Handle fixing with ChatGPT
  const handleFixWithChatGPT = () => {
    const prompt = `Help me fix this Mermaid diagram syntax error:

**Mermaid Code:**
\`\`\`mermaid
${code}
\`\`\`

**Error Message:**
${mermaidError}

Please provide the corrected Mermaid code with an explanation of what was wrong and how you fixed it.`

    const encodedPrompt = encodeURIComponent(prompt)
    const chatgptUrl = `https://chatgpt.com/?q=${encodedPrompt}`

    window.open(chatgptUrl, '_blank', 'noopener,noreferrer')
    toast.success('Opening ChatGPT to fix the diagram')
  }

  // Handle textarea key events
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      handleCancel()
    } else if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault()
      handleSave()
    }
  }

  return (
    <NodeViewWrapper
      className={`mermaid-block group relative rounded-lg border transition-all duration-200 ${
        selected
          ? 'border-blue-500 shadow-md'
          : 'border-gray-200 hover:border-gray-300'
      } ${isEditing ? 'bg-gray-50' : 'bg-white'}`}
      data-drag-handle
    >
      {/* Header with controls - Notion-style minimal approach */}
      <div className="flex items-center justify-between p-3 border-b border-gray-100">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-blue-100 rounded-sm flex items-center justify-center">
            <div className="w-1.5 h-1.5 bg-blue-500 rounded-sm"></div>
          </div>
          <span className="text-sm font-medium text-gray-700">
            Mermaid Diagram
          </span>
        </div>

        {/* Controls - only visible on hover or when selected, Notion-style */}
        <div
          className={`flex items-center space-x-1 transition-opacity duration-200 ${
            selected || isEditing
              ? 'opacity-100'
              : 'opacity-0 group-hover:opacity-100'
          }`}
        >
          {isEditing ? (
            <>
              <Button
                size="sm"
                variant="ghost"
                onClick={handleSave}
                className="h-7 px-2 text-green-600 hover:text-green-700 hover:bg-green-50"
                title="Save (Cmd+Enter)"
              >
                <Check className="h-3 w-3" />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={handleCancel}
                className="h-7 px-2 text-gray-500 hover:text-gray-700 hover:bg-gray-50"
                title="Cancel (Esc)"
              >
                <X className="h-3 w-3" />
              </Button>
            </>
          ) : (
            <>
              <Button
                size="sm"
                variant="ghost"
                onClick={handleCopyMarkdown}
                className="h-7 px-2 text-gray-500 hover:text-gray-700 hover:bg-gray-50"
                title="Copy as markdown"
              >
                <Copy className="h-3 w-3" />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={handleEdit}
                className="h-7 px-2 text-gray-500 hover:text-gray-700 hover:bg-gray-50"
                title="Edit diagram"
              >
                <Edit3 className="h-3 w-3" />
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Content area */}
      <div className="p-4">
        {isEditing ? (
          /* Edit mode - code on top, preview below */
          <div className="space-y-4">
            <div>
              <textarea
                ref={textareaRef}
                value={editingCode}
                onChange={e => {
                  setEditingCode(e.target.value)
                  setMermaidError(null) // Reset error on change
                }}
                onKeyDown={handleKeyDown}
                className="w-full h-48 p-3 font-mono text-sm border border-gray-300 rounded-md resize-y focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white"
                placeholder="Enter your mermaid diagram code..."
                spellCheck={false}
              />
              <div className="text-xs text-gray-500 mt-2">
                Press{' '}
                <kbd className="px-1.5 py-0.5 bg-gray-200 border border-gray-300 rounded">
                  Cmd+Enter
                </kbd>{' '}
                to save, or{' '}
                <kbd className="px-1.5 py-0.5 bg-gray-200 border border-gray-300 rounded">
                  Esc
                </kbd>{' '}
                to cancel.
              </div>
            </div>

            <div className="border-t border-gray-200 pt-4">
              <h3 className="text-sm font-semibold text-gray-600 mb-2">
                Live Preview
              </h3>
              <div className="min-h-[150px] p-4 bg-gray-50 rounded-md border border-gray-200 flex items-center justify-center">
                {editingCode ? (
                  <div className="w-full max-w-full overflow-auto">
                    <MermaidChartRenderer
                      key={editingCode} // Re-render when code changes
                      chart={editingCode}
                      onError={setMermaidError}
                    />
                  </div>
                ) : (
                  <div className="text-sm text-gray-500">
                    Start typing to see a preview
                  </div>
                )}
              </div>
              {mermaidError && (
                <div className="mt-2 p-3 bg-red-50 text-red-700 text-xs rounded-md border border-red-200 flex flex-col gap-2">
                  <div>
                    <strong>Syntax Error:</strong> {mermaidError}
                  </div>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleFixWithChatGPT}
                    className="text-red-600 border-red-200 hover:bg-red-100 self-start"
                  >
                    <Bot className="h-3 w-3 mr-1.5" />
                    Fix with AI
                  </Button>
                </div>
              )}
            </div>
          </div>
        ) : (
          /* View mode - rendered diagram */
          <div className="min-h-[200px] flex items-center justify-center">
            {code && !mermaidError ? (
              <div className="w-full max-w-full overflow-auto">
                <MermaidChartRenderer
                  ref={mermaidRef}
                  key={code}
                  chart={code}
                  onError={setMermaidError}
                />
              </div>
            ) : mermaidError ? (
              <div className="w-full p-4 bg-red-50 border border-red-200 rounded-md">
                <div className="text-red-700 text-sm font-medium mb-2">
                  Invalid Mermaid Syntax
                </div>
                <div className="text-red-600 text-xs font-mono whitespace-pre-wrap">
                  {mermaidError}
                </div>
                <div className="flex gap-2 mt-3">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleEdit}
                    className="text-red-600 border-red-200 hover:bg-red-50"
                  >
                    Edit to fix
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleFixWithChatGPT}
                    className="text-blue-600 border-blue-200 hover:bg-blue-50"
                  >
                    <Bot className="h-3 w-3 mr-1" />
                    Use ChatGPT to fix
                  </Button>
                </div>
              </div>
            ) : (
              <div className="text-gray-400 text-sm">
                Click edit to add your mermaid diagram
              </div>
            )}
          </div>
        )}
      </div>
    </NodeViewWrapper>
  )
}
