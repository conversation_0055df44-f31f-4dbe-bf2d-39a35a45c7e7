'use client'

import React, { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { Monitor, X, AlertTriangle } from 'lucide-react'
import { Button } from '@/components/ui/button'

type MobileRecommendationBannerProps = {
  dragTreeId: string
  className?: string
}

export default function MobileRecommendationBanner({
  dragTreeId,
  className,
}: MobileRecommendationBannerProps) {
  const [isVisible, setIsVisible] = useState<boolean>(false)
  const [isAnimating, setIsAnimating] = useState<boolean>(false)

  // Storage key for tracking dismissed banners per drag tree
  const storageKey = `mobile-banner-dismissed-${dragTreeId}`

  useEffect(() => {
    // Check if banner was dismissed for this specific drag tree
    const isDismissed = localStorage.getItem(storageKey) === 'true'

    if (!isDismissed) {
      // Show banner with animation delay
      const timer = setTimeout(() => {
        setIsVisible(true)
        setIsAnimating(true)
      }, 500) // Small delay for better UX

      return () => clearTimeout(timer)
    }
  }, [dragTreeId, storageKey])

  const handleDismiss = () => {
    setIsAnimating(false)

    // Store dismissal for this specific drag tree
    localStorage.setItem(storageKey, 'true')

    // Hide after animation
    setTimeout(() => {
      setIsVisible(false)
    }, 300)
  }

  if (!isVisible) {
    return null
  }

  return (
    <div
      className={cn(
        'relative bg-gradient-to-r from-amber-50 to-orange-50 border-l-4 border-amber-400 shadow-sm transition-all duration-300 ease-out',
        {
          'opacity-100 translate-y-0': isAnimating,
          'opacity-0 -translate-y-full': !isAnimating,
        },
        className
      )}
      role="banner"
      aria-live="polite"
    >
      <div className="flex items-start gap-3 p-3 pr-12">
        {/* Icon */}
        <div className="flex-shrink-0 mt-0.5">
          <div className="flex items-center justify-center w-8 h-8 bg-amber-100 rounded-full">
            <Monitor className="w-4 h-4 text-amber-600" />
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <AlertTriangle className="w-4 h-4 text-amber-600 flex-shrink-0" />
            <h3 className="text-sm font-semibold text-amber-800">
              Better Experience on Desktop
            </h3>
          </div>
          <p className="text-sm text-amber-700 leading-relaxed">
            While this mobile view works, the drag tree interface is optimized
            for desktop computers with larger screens and mouse interaction for
            the best experience.
          </p>
        </div>

        {/* Dismiss Button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleDismiss}
          className="absolute top-2 right-2 h-8 w-8 p-0 text-amber-600 hover:text-amber-800 hover:bg-amber-100 rounded-full touch-manipulation"
          title="Dismiss this message"
          aria-label="Dismiss recommendation banner"
        >
          <X className="w-4 h-4" />
        </Button>
      </div>

      {/* Optional decorative element */}
      <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-amber-200 via-amber-300 to-orange-200 opacity-60" />
    </div>
  )
}
