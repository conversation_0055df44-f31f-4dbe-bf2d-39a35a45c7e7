-- CreateEnum
CREATE TYPE "AIGenerationStatus" AS ENUM ('INITIALIZED', 'ACTIVE', 'INACTIVE');

-- CreateTable
CREATE TABLE "ai_generations" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "entity_type" TEXT NOT NULL,
    "entity_id" TEXT NOT NULL,
    "status" "AIGenerationStatus" NOT NULL DEFAULT 'INITIALIZED',
    "title" TEXT,
    "content" TEXT NOT NULL DEFAULT '',
    "generation_input" TEXT NOT NULL DEFAULT '',
    "generation_output" TEXT NOT NULL DEFAULT '',
    "config" JSONB NOT NULL DEFAULT '{}',
    "metadata" JSONB NOT NULL DEFAULT '{}',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ai_generations_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ai_generations_user_id_created_at_idx" ON "ai_generations"("user_id", "created_at");

-- CreateIndex
CREATE INDEX "ai_generations_entity_type_entity_id_status_idx" ON "ai_generations"("entity_type", "entity_id", "status");

-- AddForeignKey
ALTER TABLE "ai_generations" ADD CONSTRAINT "ai_generations_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
