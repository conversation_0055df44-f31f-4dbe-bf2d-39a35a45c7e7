/**
 * Centralized Network Stubs for E2E Testing
 *
 * This file provides consistent API mocking utilities for all e2e tests.
 * It ensures tests remain reliable by preventing actual API calls to external services.
 */
import { Page } from '@playwright/test'
import fs from 'fs'
import path from 'path'

/**
 * Fixture data types for different API responses
 */
export type FixtureType =
  | 'openai-mock-rephrase'
  | 'openai-mock-diagnosis'
  | 'openai-mock-research'

/**
 * Load fixture data from JSON file
 * @param fixtureName Name of the fixture file without extension
 * @returns Parsed JSON data
 */
export function loadFixture(fixtureName: FixtureType): any {
  const fixturePath = path.join(__dirname, 'fixtures', `${fixtureName}.json`)
  const fixtureData = fs.readFileSync(fixturePath, 'utf-8')
  return JSON.parse(fixtureData)
}

/**
 * Mock API response with static data
 * @param page Playwright page object
 * @param url URL pattern to intercept
 * @param statusCode HTTP status code to return
 * @param responseData Data to return in response body
 * @param headers Optional response headers
 */
export async function mockApiResponse(
  page: Page,
  url: string | RegExp,
  statusCode: number,
  responseData: any,
  headers: Record<string, string> = {}
): Promise<void> {
  await page.route(url, async route => {
    await route.fulfill({
      status: statusCode,
      body:
        typeof responseData === 'string'
          ? responseData
          : JSON.stringify(responseData),
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
    })
  })
}

/**
 * Mock streaming API response with chunked data
 * @param page Playwright page object
 * @param url URL pattern to intercept
 * @param chunks Array of data chunks to stream
 * @param delayMs Delay between chunks in milliseconds
 */
export async function mockStreamingApiResponse(
  page: Page,
  url: string | RegExp,
  chunks: string[]
): Promise<void> {
  await page.route(url, async route => {
    // Create a simulated streaming response by concatenating all chunks
    // Note: Playwright's route.fulfill() doesn't support actual streaming,
    // so we simulate it by returning all chunks at once in SSE format
    let responseBody = ''
    for (const chunk of chunks) {
      responseBody += `data: ${chunk}\n\n`
    }
    responseBody += 'data: [DONE]\n\n'

    await route.fulfill({
      status: 200,
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive',
      },
      body: responseBody,
    })
  })
}

/**
 * Mock rate limited API response
 * @param page Playwright page object
 * @param url URL pattern to intercept
 * @param retryAfterSeconds Seconds to wait before retry
 */
export async function mockRateLimitedResponse(
  page: Page,
  url: string | RegExp,
  retryAfterSeconds: number = 30
): Promise<void> {
  await page.route(url, async route => {
    await route.fulfill({
      status: 429,
      body: JSON.stringify({
        error: {
          message: 'Rate limit exceeded',
          type: 'rate_limit_error',
        },
      }),
      headers: {
        'Content-Type': 'application/json',
        'Retry-After': retryAfterSeconds.toString(),
      },
    })
  })
}

/**
 * Setup mocks for the screening flow
 * @param page Playwright page object
 */
export async function setupScreeningMocks(page: Page): Promise<void> {
  // Mock the rephrase endpoint - actual endpoint from useScreeningRephrase hook
  const rephraseFixture = loadFixture('openai-mock-rephrase')
  await mockStreamingApiResponse(
    page,
    /\/api\/screening\/rephrase$/,
    rephraseFixture.chunks
  )

  // Mock the diagnosis endpoint - actual endpoint from useScreeningDiagnosis hook
  const diagnosisFixture = loadFixture('openai-mock-diagnosis')
  await mockStreamingApiResponse(
    page,
    /\/api\/screening\/diagnose$/,
    diagnosisFixture.chunks
  )

  // Additionally mock simulator endpoints to ensure offline execution
  await mockStreamingApiResponse(
    page,
    /\/api\/screening\/rephrase-simulator$/,
    rephraseFixture.chunks
  )
  await mockStreamingApiResponse(
    page,
    /\/api\/screening\/diagnose-simulator$/,
    diagnosisFixture.chunks
  )
}

/**
 * Setup mocks for the dragTree flow
 * @param page Playwright page object
 * @param dragTreeId Optional specific dragTree ID to mock
 */
export async function setupDragTreeMocks(
  page: Page,
  dragTreeId?: string
): Promise<void> {
  // Mock the research generation endpoint - actual endpoint from the application
  const researchFixture = loadFixture('openai-mock-research')
  await mockStreamingApiResponse(
    page,
    /\/api\/dragtree\/research_generate/,
    researchFixture.chunks
  )

  // Mock content record creation endpoint to avoid hitting the DB
  await mockApiResponse(page, /\/api\/dragtree\/research_create/, 200, {
    success: true,
    data: {
      contentId: 'mock-content-id',
      status: 'INITIALIZED',
    },
  })

  // Mock the getDragTree server action - this is called via server actions, not direct API
  if (dragTreeId) {
    await mockApiResponse(
      page,
      /\/api\/dragtree\/structure/,
      200,
      researchFixture.dragTreeData
    )
  }
}

/**
 * Clear all network mocks
 * @param page Playwright page object
 */
export async function clearAllMocks(page: Page): Promise<void> {
  await page.unroute('**')
}
