'use client'

import React, { useState } from 'react'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { getDragTree } from '@/app/server-actions/drag-tree'
import { createDragTreeNode } from '@/app/server-actions/drag-tree'
import { Button } from '@/components/ui/button'
import { toast } from 'react-hot-toast'
import { cn } from '@/lib/utils'

const RepairTreeData: React.FC = () => {
  const { dragTreeId, loadFromDatabase } = useDragTreeStore()
  const [isRepairing, setIsRepairing] = useState(false)
  const [repairResults, setRepairResults] = useState<string[]>([])

  const repairMissingNodes = async () => {
    if (!dragTreeId) {
      toast.error('No drag tree ID available')
      return
    }

    setIsRepairing(true)
    setRepairResults([])

    try {
      // Get current database data
      const result = await getDragTree(dragTreeId)
      if (!result.success || !result.data) {
        throw new Error('Failed to load tree data')
      }

      const hierarchy = (result.data.tree_structure as any)?.hierarchy || {}
      const nodes = result.data.nodes || []
      const nodeIds = new Set(nodes.map((n: any) => n.id))

      // Find all referenced nodes
      const allReferencedNodes = new Set<string>()
      Object.keys(hierarchy).forEach(parentId => {
        allReferencedNodes.add(parentId)
        hierarchy[parentId].forEach((childId: string) => {
          allReferencedNodes.add(childId)
        })
      })

      // Find missing nodes
      const missingNodes = Array.from(allReferencedNodes).filter(
        id => !nodeIds.has(id)
      )

      if (missingNodes.length === 0) {
        toast.success('No missing nodes found - tree is consistent!')
        setRepairResults(['✅ Tree is already consistent'])
        return
      }

      console.log(
        `🔧 Repairing ${missingNodes.length} missing nodes:`,
        missingNodes
      )

      // Create missing nodes
      const repairPromises = missingNodes.map(async nodeId => {
        const nodeType = nodeId.startsWith('cat_') ? 'CATEGORY' : 'QUESTION'
        const shortId = nodeId.slice(-8)
        const label =
          nodeType === 'CATEGORY'
            ? `Category ${shortId}`
            : `Question ${shortId}`

        try {
          const createResult = await createDragTreeNode({
            dragTreeId,
            nodeType: nodeType as 'CATEGORY' | 'QUESTION',
            label,
            nodeId, // Use the existing ID
          })

          if (createResult.success) {
            return `✅ Created ${nodeType.toLowerCase()}: ${label}`
          } else {
            return `❌ Failed to create ${nodeId}: ${createResult.error}`
          }
        } catch (error) {
          return `❌ Error creating ${nodeId}: ${error}`
        }
      })

      const results = await Promise.all(repairPromises)
      setRepairResults(results)

      const successful = results.filter(r => r.startsWith('✅')).length
      const failed = results.filter(r => r.startsWith('❌')).length

      if (successful > 0) {
        toast.success(`Repaired ${successful} missing nodes`)
        // Reload the tree data after repair
        setTimeout(() => {
          loadFromDatabase()
        }, 1000)
      }

      if (failed > 0) {
        toast.error(`Failed to repair ${failed} nodes`)
      }
    } catch (error) {
      console.error('Repair failed:', error)
      toast.error(`Repair failed: ${error}`)
      setRepairResults([`❌ Repair failed: ${error}`])
    } finally {
      setIsRepairing(false)
    }
  }

  if (!dragTreeId) {
    return <div className="p-4 text-gray-500">No drag tree ID available</div>
  }

  return (
    <div className="p-4 border rounded-lg bg-blue-50 border-blue-200">
      <h3 className="text-lg font-semibold mb-4">🔧 Repair Tree Data</h3>

      <p className="text-sm text-gray-600 mb-4">
        This tool will identify and create missing nodes that exist in the tree
        hierarchy but are missing from the database. Missing nodes appear as
        &quot;[MISSING]&quot; in the tree.
      </p>

      <Button
        onClick={repairMissingNodes}
        disabled={isRepairing}
        className="mb-4"
      >
        {isRepairing ? 'Repairing...' : 'Repair Missing Nodes'}
      </Button>

      {repairResults.length > 0 && (
        <div className="space-y-2">
          <strong>Repair Results:</strong>
          <ul className="text-sm space-y-1">
            {repairResults.map((result, index) => (
              <li
                key={index}
                className={cn('font-mono', {
                  'text-green-600': result.startsWith('✅'),
                  'text-red-600': result.startsWith('❌'),
                  'text-gray-600':
                    !result.startsWith('✅') && !result.startsWith('❌'),
                })}
              >
                {result}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  )
}

// Memoize to prevent unnecessary re-renders when no props change
export default React.memo(RepairTreeData)
