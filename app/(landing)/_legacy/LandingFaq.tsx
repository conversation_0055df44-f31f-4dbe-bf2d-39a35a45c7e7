import {
  AccordionTrigger,
  AccordionContent,
  AccordionItem,
  Accordion,
} from "@/components/ui/accordion";
import { Card<PERSON>ontent, Card } from "@/components/ui/card";
import { contactEmail } from "@/app/configs";

const FAQ = () => {
  return (
    <div className="flex justify-center items-center py-20">
      <Card className="shadow-lg max-w-2xl">
        <CardContent className="p-6">
          <h2 className="text-2xl font-semibold">FAQ</h2>
          <Accordion className="w-full mt-4" type="multiple">
            <AccordionItem value="item-1">
              <AccordionTrigger className="hover:underline-none">
                Are you just another ChatGPT wrapper?
              </AccordionTrigger>
              <AccordionContent>
                Yes. Many SaaS platforms can be described as wrappers around
                AWS. The more pertinent questions would be: What value does your
                product/tool bring? How long would it take to achieve the same
                level of solution using ChatGPT directly?
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="item-2">
              <AccordionTrigger className="hover:underline-none">
                Why not run the prompts in prompt panel directly?
              </AccordionTrigger>
              <AccordionContent>
                Two reasons for this, both of which are true.
                <br />
                <br />
                <p>
                  1. API is expensive, we cannot afford the bill for users
                  asking random questions. But if you find a use case, feel free
                  to email us: {contactEmail}
                </p>
                <br />
                <p>
                  {" "}
                  We have included the feature to copy the prompts to your
                  favorite AI tool, such as ChatGPT, Claude, or others. They
                  offer very powerful models now, and they are{" "}
                  <strong>FREE</strong>!
                </p>
                <br />
                <p>
                  2. We believe users should be able to modify the prompts based
                  on their own needs and choose their preferred models. No
                  predefined templates can understand your needs better than
                  you.
                </p>
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="item-3">
              <AccordionTrigger className="hover:underline-none">
                How will you handle my data?
              </AccordionTrigger>
              <AccordionContent>
                We prioritize your data privacy and have no interest in misusing
                it. Our main focus is on experimenting with efficient ways to
                structure difficult problems. We can guarantee that no external
                parties will have access to your responses without your explicit
                permission. Additionally, your input data will only be utilized
                for product improvement or debugging purposes, and nothing else.
                We will not use your input as training data.
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="item-4">
              <AccordionTrigger className="hover:underline-none">
                What is the motivation behind Clarify AI again?
              </AccordionTrigger>
              <AccordionContent>
                Thinking clearly and comprehensively is a challenging skill to
                acquire, especially for unfamiliar topics. Even if you are
                capable, it requires a high cognitive load to do so. For the
                first time in human history, intelligence becomes commodity, we
                think it is worth a try to develop a better tool to assist
                thinking.
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="item-5">
              <AccordionTrigger className="hover:underline-none">
                Why not use ChatGPT/Claude directly?
              </AccordionTrigger>
              <AccordionContent>
                <p>
                  Yes, you can. If you just want to search or ask
                  straightforward questions, you should definitely use Google or
                  ChatGPT/Claude directly.
                </p>
                <br />
                <p>
                  We, as active ChatGPT users, initiated this idea because we
                  are amazed by its capabilities and frustrated by its
                  limitations:
                </p>
                <p>
                  1. Providing context to the chatbot can be quite annoying and
                  hard to organize.
                </p>
                <p>
                  2. Language models can be narrow-minded, often getting stuck
                  in a loop, especially when dealing with complex problems.
                </p>
                <br />
                <p>
                  Without addressing these issues, using ChatGPT can be an
                  uphill battle, and it is more challenging to see a
                  productivity jump because you need to learn how to write
                  prompts and provide context. We want to simplify this step and
                  help you tackle complex problems faster.
                </p>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </CardContent>
      </Card>
    </div>
  );
};

export default FAQ;
