# Subscription System Testing Guide

This guide explains how to test the subscription system implementation.

## 🎯 What's Implemented

### ✅ **Complete Subscription System**

- **Database Schema**: PRO, ULTRA, BUSINESS tiers added
- **Stripe Webhooks**: Full lifecycle management (create, update, delete)
- **Session Integration**: Real-time subscription data in NextAuth sessions
- **Visual Indicators**: Pro badges and tier displays throughout the UI
- **Subscription Management**: Integrated into dragTree ProfileDialog

### ✅ **Key Features**

- **Tier-based Permissions**: Configurable limits per subscription tier
- **Real-time Updates**: Session refreshes after subscription changes
- **Visual Pro Indicators**: Avatar badges and tier labels
- **Subscription Management**: Direct Stripe portal integration
- **Upgrade Flow**: Seamless subscription purchase flow

## 🧪 Testing the System

### **Method 1: Using the Test Script (Recommended)**

Use the provided script to quickly test different subscription tiers:

```bash
# Set user to PRO tier (with 1 month expiry)
node scripts/test-subscription.js <EMAIL> PRO

# Set user to FREE tier
node scripts/test-subscription.js <EMAIL> FREE

# Set user to GUEST tier (permanent Pro features)
node scripts/test-subscription.js <EMAIL> GUEST
```

### **Method 2: Direct Database Update**

```sql
-- Set user to PRO tier
UPDATE "users"
SET
  subscription_tier = 'PRO',
  subscription_end_date = NOW() + INTERVAL '1 month',
  subscription_cancel_pending = false,
  subscription_service = 'STRIPE'
WHERE email = '<EMAIL>';

-- Set user to FREE tier
UPDATE "users"
SET
  subscription_tier = 'FREE',
  subscription_end_date = NULL,
  subscription_cancel_pending = false
WHERE email = '<EMAIL>';
```

## 🎨 Visual Changes to Test

### **1. Avatar Indicators**

- **FREE users**: Standard avatar with gray border
- **PRO users**: Avatar with purple ring + sparkle badge (✦)
- **GUEST users**: Same as PRO but shows "GUEST" label

### **2. Profile Dialog**

- **Subscription Status**: Shows current tier with colored badges
- **Expiry Information**: Displays renewal/expiry dates for paid tiers
- **Action Buttons**:
  - FREE users see "Upgrade to Pro" button
  - PRO users see "Manage Subscription" button
  - GUEST users see special guest access notice

### **3. Sidebar User Info**

- **Pro Badge**: Small gradient badge next to username
- **Avatar Ring**: Purple ring around Pro user avatars

## 🔄 Testing Flow

1. **Start as FREE user**:
   - No visual indicators
   - "Upgrade to Pro" button in profile
   - Limited conversation count (5)

2. **Upgrade to PRO**:

   ```bash
   node scripts/test-subscription.js <EMAIL> PRO
   ```
   - Refresh browser
   - See Pro badge and avatar ring
   - "Manage Subscription" button appears
   - Higher conversation limit (42)

3. **Test GUEST access**:

   ```bash
   node scripts/test-subscription.js <EMAIL> GUEST
   ```
   - Same Pro features but "GUEST" label
   - Special guest access notice in profile

4. **Return to FREE**:
   ```bash
   node scripts/test-subscription.js <EMAIL> FREE
   ```
   - All Pro indicators disappear
   - Back to upgrade button

## 🛠 Real Stripe Testing

For full Stripe integration testing:

1. **Set up Stripe Test Mode**:
   - Use Stripe test keys in environment
   - Create test products and prices
   - Configure webhook endpoint

2. **Test Subscription Flow**:
   - Visit `/subscription` page
   - Complete checkout with test card: `4242 4242 4242 4242`
   - Verify webhook processing
   - Check subscription status updates

3. **Test Cancellation**:
   - Use Stripe dashboard to cancel subscription
   - Verify webhook handles `customer.subscription.deleted`
   - Confirm user downgrade to FREE tier

## 📊 Subscription Tiers

| Tier         | Conversations | Features    | Status        |
| ------------ | ------------- | ----------- | ------------- |
| **FREE**     | 5             | Basic       | Default       |
| **PRO**      | 42            | All Premium | Paid          |
| **GUEST**    | 42            | All Premium | Complimentary |
| **ULTRA**    | 200           | Enhanced    | Reserved      |
| **BUSINESS** | 500           | Enterprise  | Reserved      |

## 🔍 Debugging

### **Check Current User Status**:

```sql
SELECT email, subscription_tier, subscription_end_date, subscription_cancel_pending
FROM "users"
WHERE email = '<EMAIL>';
```

### **Session Data**:

The session now includes:

```typescript
session.user.subscription = {
  tier: SubscriptionTier,
  expiry: string | null,
  cancelPending: boolean,
}
```

### **Common Issues**:

- **Changes not visible**: Refresh browser to update session
- **No Pro indicators**: Check database tier is set correctly
- **Stripe portal errors**: Ensure user has `subscription_customer_id`

## 🎉 Success Indicators

When testing is successful, you should see:

- ✅ Pro badge appears next to username in sidebar
- ✅ Avatar has purple ring and sparkle badge
- ✅ Profile dialog shows correct tier and status
- ✅ Subscription management buttons work correctly
- ✅ Tier-appropriate conversation limits apply

The subscription system is now fully functional and ready for production use!
