// "use client";

// import React, { useEffect } from "react";
// import { useSession } from "next-auth/react";
// import { Button } from "@/components/ui/button";
// import { Badge } from "@/components/ui/badge";
// import { useDragTreeStoreEnhanced } from "@/app/stores/dragtree_store_enhanced";
// import { TreeNode } from "@/app/types";

// type SimpleTreeTestProps = {
//   dragTreeId: string;
// };

// export default function SimpleTreeTest({ dragTreeId }: SimpleTreeTestProps) {
//   const { data: session } = useSession();

//   const {
//     treeData,
//     syncStatus,
//     setDragTreeId,
//     loadFromDatabase,
//     markNodeAsInterestedWithSync,
//     editNodeWithSync,
//     getInterestedNodes,
//   } = useDragTreeStoreEnhanced();

//   // Initialize store on mount
//   useEffect(() => {
//     if (session?.user?.id && dragTreeId) {
//       console.log("🚀 Setting up tree...");
//       setDragTreeId(dragTreeId, session.user.id);

//       // Load initial data
//       setTimeout(() => {
//         loadFromDatabase();
//       }, 100);
//     }
//   }, [session?.user?.id, dragTreeId, setDragTreeId, loadFromDatabase]);

//   // Action handlers
//   const handleMarkInterested = async (nodeId: string) => {
//     console.log("📌 Marking node as interested:", nodeId);
//     await markNodeAsInterestedWithSync(nodeId);
//   };

//   const handleEditLabel = async (nodeId: string, currentLabel: string) => {
//     const newLabel = prompt("Enter new label:", currentLabel);
//     if (newLabel && newLabel !== currentLabel) {
//       console.log("✏️ Editing node label:", nodeId, "->", newLabel);
//       await editNodeWithSync(nodeId, newLabel);
//     }
//   };

//   const handleManualLoad = () => {
//     console.log("📥 Manual load triggered");
//     loadFromDatabase();
//   };

//   // Render tree recursively
//   const renderTreeNode = (
//     node: TreeNode,
//     depth: number = 0
//   ): React.ReactNode => {
//     const indent = depth * 20;
//     const isInterested = node.isInterestedIn;

//     return (
//       <div key={node.id} style={{ marginLeft: `${indent}px` }} className="py-1">
//         <div className="flex items-center gap-2 p-3 border rounded-lg bg-white shadow-sm">
//           <Badge variant={node.type === "category" ? "default" : "secondary"}>
//             {node.type}
//           </Badge>

//           <span className="flex-1 font-medium">{node.label}</span>

//           {isInterested && (
//             <Badge variant="outline" className="bg-green-50 text-green-700">
//               ❤️ Interested
//             </Badge>
//           )}

//           <div className="flex gap-1">
//             <Button
//               size="sm"
//               variant="outline"
//               onClick={() => handleMarkInterested(node.id)}
//               disabled={isInterested}
//               title="Mark as interested"
//             >
//               {isInterested ? "✓" : "♡"}
//             </Button>

//             <Button
//               size="sm"
//               variant="outline"
//               onClick={() => handleEditLabel(node.id, node.label)}
//               title="Edit label"
//             >
//               ✏️
//             </Button>
//           </div>
//         </div>

//         {node.children.map((child) => renderTreeNode(child, depth + 1))}
//       </div>
//     );
//   };

//   const getSyncStatusBadge = () => {
//     switch (syncStatus) {
//       case "idle":
//         return <Badge variant="secondary">⏸️ Idle</Badge>;
//       case "syncing":
//         return (
//           <Badge className="bg-blue-100 text-blue-800">🔄 Syncing...</Badge>
//         );
//       case "success":
//         return <Badge className="bg-green-100 text-green-800">✅ Synced</Badge>;
//       case "error":
//         return <Badge variant="destructive">❌ Error</Badge>;
//       default:
//         return <Badge variant="secondary">❓ Unknown</Badge>;
//     }
//   };

//   const interestedCount = getInterestedNodes().length;

//   return (
//     <div className="max-w-5xl mx-auto p-6 space-y-6">
//       {/* Header */}
//       <div className="bg-white rounded-lg shadow-sm border p-6">
//         <h2 className="text-2xl font-bold mb-4">Simple Drag Tree Store Test</h2>

//         {/* Status Panel */}
//         <div className="grid grid-cols-3 gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
//           <div>
//             <div className="font-medium text-sm text-gray-600">Sync Status</div>
//             {getSyncStatusBadge()}
//           </div>
//           <div>
//             <div className="font-medium text-sm text-gray-600">
//               Interested Nodes
//             </div>
//             <Badge variant="outline">{interestedCount} nodes</Badge>
//           </div>
//           <div>
//             <div className="font-medium text-sm text-gray-600">Tree ID</div>
//             <Badge variant="outline" className="text-xs">
//               {dragTreeId}
//             </Badge>
//           </div>
//         </div>

//         {/* Controls */}
//         <div className="flex gap-2 mb-6">
//           <Button onClick={handleManualLoad} variant="outline">
//             📥 Reload from DB
//           </Button>
//         </div>

//         {/* Instructions */}
//         <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm">
//           <div className="font-medium text-blue-900 mb-2">Instructions:</div>
//           <ul className="text-blue-800 space-y-1">
//             <li>• Click ♡ to mark a node as interested (syncs to database)</li>
//             <li>• Click ✏️ to edit a node's label (syncs to database)</li>
//             <li>• Changes are automatically saved with optimistic updates</li>
//           </ul>
//         </div>
//       </div>

//       {/* Tree Display */}
//       <div className="bg-white rounded-lg shadow-sm border p-6">
//         <h3 className="text-lg font-semibold mb-4">Tree Structure</h3>
//         {treeData ? (
//           <div className="space-y-2">{renderTreeNode(treeData)}</div>
//         ) : (
//           <div className="text-center py-8 text-gray-500">
//             No tree data. Click "Reload from DB" to load.
//           </div>
//         )}
//       </div>

//       {/* Interested Nodes Summary */}
//       {interestedCount > 0 && (
//         <div className="bg-green-50 border border-green-200 rounded-lg p-6">
//           <h3 className="text-lg font-semibold text-green-900 mb-4">
//             Interested Nodes ({interestedCount})
//           </h3>
//           <div className="space-y-2">
//             {getInterestedNodes().map((node) => (
//               <div
//                 key={node.id}
//                 className="flex items-center gap-2 p-2 bg-white rounded border"
//               >
//                 <Badge
//                   variant={node.type === "category" ? "default" : "secondary"}
//                 >
//                   {node.type}
//                 </Badge>
//                 <span className="font-medium">{node.label}</span>
//               </div>
//             ))}
//           </div>
//         </div>
//       )}
//     </div>
//   );
// }
