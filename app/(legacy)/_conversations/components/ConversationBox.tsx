'use client'

import { useCallback } from 'react'
import { useRouter } from 'next/navigation'
import clsx from 'clsx'
import { ConversationLayoutType } from '@/app/types'
import { Badge } from '@/components/ui/badge'
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from '@/components/ui/hover-card'

type ConversationBoxProps = {
  data: ConversationLayoutType
  selected?: boolean
}

const ConversationBox: React.FC<ConversationBoxProps> = ({
  data,
  selected,
}) => {
  const router = useRouter()

  const handleClick = useCallback(() => {
    router.push(`/conversations/${data.id}`)
  }, [data, router])

  const getBgClass = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'bg-green-200 bg-opacity-50'
      case 'ACTIVE':
        return 'bg-pink-200 bg-opacity-50'
      case 'EXAMPLE':
        return 'bg-orange-200 bg-opacity-50'
      default:
        return 'bg-gray-200 bg-opacity-50'
    }
  }

  const displayTitle =
    data.title || data.config_prompt_description || 'Untitled'

  return (
    <HoverCard>
      <HoverCardTrigger>
        {' '}
        <div
          onClick={handleClick}
          className={clsx(
            'w-full relative flex items-center space-x-3 p-3 rounded-lg transition cursor-pointer',
            'hover:bg-neutral-100',
            selected ? 'bg-neutral-100' : 'bg-white'
          )}
        >
          <div className="min-w-0 flex-1">
            <div className="focus:outline-none">
              <span className="absolute inset-0" aria-hidden="true" />
              <div className="flex justify-between items-center mb-1">
                <p className="text-md font-medium text-gray-900 truncate">
                  {displayTitle}
                </p>
              </div>
              <p className={`truncate text-sm`}>
                {data.issuetree_updated_at?.toLocaleString() ||
                  data.created_at.toLocaleString()}
              </p>
              <Badge
                className={`text-xs rounded-full px-2 py-1 text-black ${getBgClass(
                  data.conversation_status
                )}`}
              >
                {data.conversation_status}
              </Badge>
            </div>
          </div>
        </div>
      </HoverCardTrigger>
      <HoverCardContent>{displayTitle}</HoverCardContent>
    </HoverCard>
  )
}

export default ConversationBox
