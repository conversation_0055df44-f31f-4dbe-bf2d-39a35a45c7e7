import { streamText } from 'ai'
import { NextResponse } from 'next/server'
import { OpenAIUsageType } from '@prisma/client'
import { azure } from '@ai-sdk/azure'
import { logOpenAIUsage_serverAction } from '@/app/(legacy)/_server-actions/log_openai_usage'
import { RephraseRequestType } from '@/app/types/api'

export const maxDuration = 60

export async function POST(req: Request) {
  // TODO: Re-implement with AI SDK 5 - disabled for now to unblock core features
  return new Response(
    JSON.stringify({ error: 'Rephrase temporarily disabled' }),
    {
      status: 503,
      headers: { 'Content-Type': 'application/json' },
    }
  )

  try {
    const requestData: RephraseRequestType = await req.json()

    if (!requestData.userId) {
      return NextResponse.json(
        { message: 'Unauthorized. User ID is required.' },
        { status: 401 }
      )
    }
    const { userId, description } = requestData

    const formattedSystemMessage = createSystemMessage(description)

    const model_name = 'gpt-4o-mini'

    const result = streamText({
      model: azure(model_name),
      messages: [
        {
          role: 'system',
          content: formattedSystemMessage,
        },
      ],
      onFinish: async ({ text }) => {
        await logOpenAIUsage_serverAction({
          open_ai_usage_type: OpenAIUsageType.REPHRASE_PROBLEM,
          model_name: model_name,
          input_text: formattedSystemMessage,
          output_text: text,
          userId: userId,
          conversationId: 'dummy_conversation_for_rephrase',
        })
      },
    })

    return result.toUIMessageStreamResponse()
  } catch {
    return NextResponse.json({ message: 'Error' }, { status: 500 })
  }
}

// Helper function to create the system message
function createSystemMessage(description: string): string {
  return `Given a user requirement, your task is to rephrase it into four different problem statements, each highlighting a unique aspect of the requirement. For each rephrased statement:
- **Begin with a focus label in brackets that dynamically reflects the main theme or unique aspect of that statement.** The focus label should be concise, specific, or utilize key terms from the user's requirement. Do not hardcode the labels; generate them based on the content.
- **Follow the specific instructions for each rephrased statement as detailed below.**

**Instructions:**
1
   - **Objective:** Restate the initial requirement clearly and concisely without missing any details.
   - **Focus Label:** Reflect the core objective or key feature.
2
   - **Objective:** Translate the initial requirement into a user story that highlights how fulfilling this requirement impacts a specific group within the organization.
   - **Focus Label:** Reflect the specific group and the impact.
3
   - **Objective:** Reframe the initial requirement as a user story that addresses potential risks or challenges implied by the strategic goals.
   - **Focus Label:** Reflect the specific risks or challenges.
4
   - **Objective:** Rewrite the initial requirement as a user story from a strategic/high-level perspective, emphasizing forward-looking goals and potential changes that may affect the organization.
   - **Focus Label:** Reflect the strategic/high-level perspective.

**User story format:** As <some role>, I want <some goal> so that <some achieved outcome>.

**Additional Guidelines:**

- **Include All Original Details:** Preserve all relevant details from the original requirement, including any figures, names, challenges, or keywords.
- **Self-Contained Statements:** Ensure each rephrased statement is self-contained and understandable on its own.
- **Formatting:** Start each rephrased statement with its number and include the bracketed focus label on the same line as the statement.
- **Tone and Tense:** Maintain the tone and tense used in the user's original statement.

User request: {${description}}\n
Your next line starts with 1. [
`
}
