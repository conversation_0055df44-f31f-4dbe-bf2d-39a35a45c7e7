# Design Doc: Multi-Layer Memory System

- **Status:** Draft
- **Author:** O3 Agent
- **Date:** July 16, 2025

---

## 1. Overview

Personalised assistance requires **memory** – a structured representation of a user’s past actions and expressed intent.
This document proposes a two-layer memory architecture:

1. **DragTree-level memory** – fine-grained, per-tree behavioural summary (our **initial focus**).
2. **User-level memory** – cross-tree aggregation (marked _TODO_ for a later phase).

The system ingests **interaction logs** (e.g. _“batch quick research on 10 nodes”_, _“downloaded diagram”_), periodically summarises them with an LLM, and stores the result as versioned **memory snapshots** which can be injected into future prompts.

---

## 2. Goals

1. **Understand Intent** – Capture what the user is researching, generating and chatting about.
2. **Lightweight Logging** – Cheap, append-only writes that never block UI interactions.
3. **Temporal Accuracy** – Keep track of _when_ something happened so we can summarise incrementally.
4. **Versioned Summaries** – Every summarisation run produces a new immutable snapshot (`v1`, `v2`, …) to aid regression analysis.
5. **Minimal Surface Area** – Re-use existing helpers (Next 13 server-actions, Prisma) and avoid new services.

---

## 3. Core Data Model _(Prisma syntax)_ – **DragTree-first**

````prisma
// ---------------------------------------------------------------------
// NOTE ON IDS
// ---------------------------------------------------------------------
//   • log_<cuid>      – user_interaction_logs.id
//   • mem_<cuid>      – user_memories.id
//   • tree_mem_<cuid> – dragtree_memories.id  (Phase 4)
// ---------------------------------------------------------------------

model InteractionLog {
  id             String   @id @default(cuid())          @map("id")        // log_<cuid>
  user_id        String
  entity_type    String                                   // e.g. "DRAGTREE", "ISSUETREE"
  entity_id      String                                   // the specific row id – enforced at app layer
  event_type     InteractionEventType
  event_summary  String?                                  // human-readable 1-liner for quick lookup
  event_payload  Json            @default("{}")          // full details (schema per event)
  created_at     DateTime        @default(now())

  // Relations
  user           User            @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([entity_type, entity_id, created_at])
  @@map("interaction_logs")
}

enum InteractionEventType {
  BATCH_QUICK_RESEARCH
  QUICK_RESEARCH
  DOWNLOAD_DIAGRAM
  GENERATE_DOCUMENT
  CHAT_MESSAGE
}

model MemorySnapshot {
  id                String   @id @default(cuid())          @map("id")        // mem_<cuid>
  user_id           String
  entity_type       String
  entity_id         String
  version           Int
  generation_input  String   @db.Text                      // prompt sent to LLM
  generation_output String   @db.Text                      // model response (Markdown)
  created_at        DateTime @default(now())
  updated_at        DateTime @updatedAt

  user              User     @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([entity_type, entity_id, version])
  @@map("memory_snapshots")
}

// One table to rule them all – `MemorySnapshot` covers both user-level and entity-level memories via `entity_type` / `entity_id`.

_Schema conventions follow existing patterns:_ snake_case columns, `created_at` / `updated_at`, and human-readable ID prefixes.

---

## 4. Event Taxonomy

_(What goes into `event_payload`?)_

| Event Type             | `event_payload` schema (examples)                         |
| ---------------------- | --------------------------------------------------------- |
| `BATCH_QUICK_RESEARCH` | `{ "node_labels": [ "Market size", "Competition", … ] }`  |
| `QUICK_RESEARCH`       | `{ "node_label": "Pricing strategy" }`                    |
| `DOWNLOAD_DIAGRAM`     | `{ "diagram_type": "MERMAID", "format": "svg" }`          |
| `GENERATE_DOCUMENT`    | `{ "template_id": "doc_swot", "title": "SWOT Analysis" }` |
| `CHAT_MESSAGE`         | `{ "message": "How do I add more competitors?" }`         |

We deliberately keep the payload **un-typed** (`Json`) to stay flexible; clients must version their sub-schemas in the payload when introducing breaking changes.

---

## 5. Aggregation & Summarisation Pipeline

```mermaid
sequenceDiagram
  participant Cron as 🕑 Nightly Cron
  participant DB as 🗄️ Postgres
  participant LLM as 🤖 OpenAI
  participant Prisma as `prisma.memorySnapshot.create()`

  Cron->>DB: SELECT * FROM interaction_logs WHERE created_at > last_snapshot_time
  Cron-->>Cron: Group by entity_type, entity_id
  loop per user
    Cron->>LLM: "Summarise the following interactions …"
    LLM-->>Cron: Markdown summary
    Cron->>Prisma: INSERT memory_snapshots (version +1)
  end
````

**Frequency:** daily at 03:00 UTC (changeable via Vercel Cron).
**Prompt skeleton:**

> "You are a customer-insights analyst. Summarise the user’s behaviour _with emphasis on chat intent_.
> Use bullet points. First list their main objectives, then any recurring pain points, then preferred features."

---

## 6. Integration Points

1. **Thin logging helper** (`libs/logger/interaction.ts`)

   ```ts
   export async function logInteraction(params: {
     entityType: string
     entityId: string
     eventType: InteractionEventType
     summary?: string
     payload: JsonValue
   }) {
     const session = await getServerAuthSession()
     if (!session?.user?.id) return
     await prisma.interactionLog.create({
       data: {
         user_id: session.user.id,
         entity_type: params.entityType,
         entity_id: params.entityId,
         event_type: params.eventType,
         event_summary: params.summary ?? null,
         event_payload: params.payload,
       },
     })
   }
   ```

   _Call this from UI actions or server-actions (e.g. `useBatchResearch`, `downloadDiagram`)._

2. **Cron job** (`app/server-actions/memory/generateDailySnapshot.ts`)
   - Queries `interaction_logs` since last `memory_snapshot` per `(entity_type, entity_id)`.
   - Batches logs and calls Azure OpenAI in bulk.
   - Inserts new `memory_snapshot` rows.

3. **Chat pipeline** (`libs/ai-chat/pipeline.ts`)
   - On chat start → fetch latest `memory_snapshot` for the relevant context (user-level plus tree-level) and inject into system prompt.
   - Guard with feature flag `ENABLE_MEMORY_V1`.

---

## 7. Rationale & Trade-offs

| Decision                     | Rationale                                                                     | Alternatives                                            |
| ---------------------------- | ----------------------------------------------------------------------------- | ------------------------------------------------------- |
| Separate log & memory tables | Keeps raw data for future re-processing; snapshots are small & cheap to read. | Overwrite metadata JSON in `User` → loses history.      |
| JSON payload                 | Flexible, avoids frequent migrations as we add events.                        | Fully normalised schema per event type – heavy.         |
| Nightly cron                 | Batches tokens → cheaper; users rarely need second-level freshness.           | Real-time summarisation (expensive, more moving parts). |
| Versioned snapshots          | Allows diffing & rollback.                                                    | Single mutable row per user.                            |

**Privacy Considerations**

- Chat messages may contain PII.
  → Mask obvious e-mails / phone numbers before logging.
- Provide user-facing toggle to disable memory collection (GDPR).

---

## 8. Implementation Roadmap

### Phase 1 — Instrumentation (≈ 2 days)

1. Add Prisma models (`interaction_logs`, `memory_snapshots`) + migration.
2. Create `logInteraction` helper (entity-agnostic).
3. Wire helper into:
   - Quick Research (single & batch)
   - Diagram download
   - Document generation
   - Chat send button.
4. Unit tests – ensure log writes & enum correctness.

### Phase 2 — Summarisation Cron (≈ 3 days)

1. Skeleton cron function (`generateDailySnapshot`).
2. For each `(entity_type, entity_id)` that has new logs → batch them.
3. Use **Azure OpenAI** [`/chat/completions?api-version=2024-05-01-preview`] with up to 16 parallel inputs (max batch) – see official docs – to cut cost.
   Pass array of messages: `{ role:"system", content: prompt }`, `{ role:"user", content: logs }`.
4. Store `generation_input` & `generation_output` in `memory_snapshots`.
5. Schedule via [Vercel Cron](https://vercel.com/docs/cron-jobs) e.g. `0 3 * * *` → call `/api/cron/generate-memory` route (ISR-cached).
6. Protect route with internal token.

### Phase 3 — Chat Integration (≈ 2 days)

1. Feature flag `ENABLE_MEMORY_V1` (enum `EnvVarFlag`).
2. Inject latest `user_memory.summary_text` into chat system prompt.
3. A/B test conversion & satisfaction metrics.

### Phase 4 — Advanced Analytics (stretch)

1. Add embedding column to `memory_snapshots` for semantic retrieval.
2. Surface entity memories in UI side-panels.

---

## 9. Open Questions

1. **Event explosion?** – Should we throttle chat message logging (e.g. keep last 50)?
2. **LLM cost control** – Use `token_count` aggregate to cap per-user prompt size.
3. **User opt-out UX** – Where in settings should we place a toggle?
4. **Memory expiration** – Auto-archive snapshots > 90 days?
5. **GDPR export** – Provide API to download raw logs + summaries.

---

_End of document._

**Unit Tests**
• Validate enum coverage – any UI event must map to `InteractionEventType`.
• Ensure `event_summary` always <= 1000 chars and ❌ fails otherwise.
• Round-trip: log events → cron → snapshot created.
