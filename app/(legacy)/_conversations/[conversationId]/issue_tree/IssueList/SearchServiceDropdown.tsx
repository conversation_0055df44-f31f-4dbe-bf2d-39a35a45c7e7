import React from 'react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import mixpanel from '@/app/libs/mixpanel'
import { searchServiceProviders } from './index'

export const SearchServiceDropdown: React.FC<{
  selectedSearchService: string
  setSelectedSearchService: (value: string) => void
}> = ({ selectedSearchService, setSelectedSearchService }) => (
  <DropdownMenu>
    <DropdownMenuTrigger asChild>
      <Button variant="outline" className="w-40">
        {
          searchServiceProviders.find(sp => sp.id === selectedSearchService)
            ?.display_text
        }
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent className="w-56">
      <DropdownMenuLabel>
        Select your favourite for DIY search
      </DropdownMenuLabel>
      <DropdownMenuSeparator />
      <DropdownMenuRadioGroup
        value={selectedSearchService}
        onValueChange={newValue => {
          setSelectedSearchService(newValue)
          mixpanel.track('select_search_service', { service: newValue })
        }}
      >
        {searchServiceProviders.map(({ id, display_text }) => (
          <DropdownMenuRadioItem key={id} value={id}>
            {display_text}
          </DropdownMenuRadioItem>
        ))}
      </DropdownMenuRadioGroup>
    </DropdownMenuContent>
  </DropdownMenu>
)
