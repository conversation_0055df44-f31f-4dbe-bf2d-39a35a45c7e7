import type { SupportedLanguageCode } from './languages'

export type ScreenObjectType = {
  is_problem_clear?: boolean
  problem_ambiguity?: string
  intention?: string[]
  entity?: string[]
  score?: number
  pass?: boolean
}

export const MOCK_REPHRASED_DESCRIPTIONS: string[] = [
  'How can I develop a comprehensive go-to-market strategy for launching eco-friendly apparel products in the diverse Asian market, considering local consumer preferences, regulatory requirements, and cultural nuances that impact sustainable fashion adoption?',
  '[User Needs] As an investor, I want to acquire out-of-state investment properties to generate positive cash flow, enabling me to diversify my portfolio and enhance my financial stability.',
  'How do I effectively identify, segment, and target the right customer demographics for eco-conscious clothing in Asian markets, while building authentic brand relationships and measuring consumer engagement across different cultural contexts?',
  'What comprehensive market research and competitive analysis framework should I implement to understand the eco-friendly apparel market dynamics in Asia, including regulatory compliance, supply chain optimization, and long-term sustainability goals for market penetration?',
]

export const MOCK_SCREEN_OBJECT: ScreenObjectType = {
  is_problem_clear: false,
  problem_ambiguity:
    "The scope of 'Asia' is too broad and lacks specific target markets or customer segments",
  intention: [
    'Launch new product line',
    'Enter Asian markets',
    'Promote eco-friendly products',
  ],
  entity: ['Eco-friendly apparel', 'Asian market', 'Go-to-market strategy'],
  score: 2,
  pass: false,
}

export const VALIDATION_CONSTANTS = {
  MIN_DESCRIPTION_LENGTH: 10,
  SIMULATION_DELAY: 2000,
  NAVIGATION_DELAY: 1000,
} as const

/**
 * Helper function to get mock screen object with language consideration
 * (Currently returns the same data but can be extended for different languages)
 */
export const getMockScreenObject = (
  _language: SupportedLanguageCode // Currently unused parameter
): ScreenObjectType => {
  // For now, return the same mock data regardless of language
  // This can be extended to have language-specific mock data in the future
  return MOCK_SCREEN_OBJECT
}

/**
 * Helper function to get mock rephrased descriptions with language consideration
 * (Currently returns the same data but can be extended for different languages)
 */
export const getMockRephraseDescriptions = (
  _language: SupportedLanguageCode // Currently unused parameter
): string[] => {
  // For now, return the same mock data regardless of language
  // This can be extended to have language-specific mock data in the future
  return MOCK_REPHRASED_DESCRIPTIONS
}
