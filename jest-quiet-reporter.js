/**
 * Custom Jest Reporter for Clean Output
 * - Shows minimal output when all tests pass
 * - Shows detailed output when tests fail
 */

class QuietReporter {
  constructor(globalConfig) {
    this._globalConfig = globalConfig
    this._hasFailures = false
    this._testResults = []
  }

  onRunStart() {
    this._hasFailures = false
    this._testResults = []
    this._startTime = Date.now()
  }

  onTestFileResult(test, testResult) {
    this._testResults.push({ test, testResult })

    // Check if this test file has failures
    if (testResult.numFailingTests > 0) {
      this._hasFailures = true

      // Show detailed output for failed tests immediately
      console.log(`\n❌ FAIL ${test.path}`)

      testResult.testResults.forEach(result => {
        if (result.status === 'failed') {
          console.log(
            `  ❌ ${result.ancestorTitles.join(' › ')} › ${result.title}`
          )
          if (result.failureMessages && result.failureMessages.length > 0) {
            result.failureMessages.forEach(message => {
              console.log(`    ${message}`)
            })
          }
        }
      })
    }
  }

  onRunComplete(testContexts, results) {
    const { numFailedTests, numPassedTests, numTotalTests, testResults } =
      results

    if (this._hasFailures || numFailedTests > 0) {
      // Show detailed summary for failures
      console.log(
        `\n❌ Test Suites: ${testResults.filter(r => r.numFailingTests > 0).length} failed, ${testResults.filter(r => r.numFailingTests === 0).length} passed, ${testResults.length} total`
      )
      console.log(
        `❌ Tests:       ${numFailedTests} failed, ${numPassedTests} passed, ${numTotalTests} total`
      )

      const runtime = results.runTime
        ? `${(results.runTime / 1000).toFixed(3)}s`
        : `${((Date.now() - this._startTime) / 1000).toFixed(3)}s`
      console.log(`⏱️  Time:        ${runtime}`)
      console.log('')
    } else {
      // All tests passed - show minimal summary
      const passedSuites = testResults.filter(
        result => result.numFailingTests === 0
      ).length
      const totalSuites = testResults.length

      console.log(
        `\n✅ Test Suites: ${passedSuites} passed, ${totalSuites} total`
      )
      console.log(
        `✅ Tests:       ${numPassedTests} passed, ${numTotalTests} total`
      )

      // Show timing info
      const runtime = results.runTime
        ? `${(results.runTime / 1000).toFixed(3)}s`
        : `${((Date.now() - this._startTime) / 1000).toFixed(3)}s`
      console.log(`⏱️  Time:        ${runtime}`)

      // Show coverage info if available
      if (results.coverageMap && this._globalConfig.collectCoverage) {
        console.log('📊 Coverage:    Generated (see coverage/ directory)')
      }

      console.log('🎉 All tests passed!\n')
    }
  }
}

module.exports = QuietReporter
