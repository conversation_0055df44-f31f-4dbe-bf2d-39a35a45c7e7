/**
 * Jest mock for @ai-sdk/react
 * Provides mock implementations for AI SDK 5 hooks
 * @jest-environment node
 */

// This file is a mock, not a test

export const useChat = jest.fn(() => ({
  messages: [],
  sendMessage: jest.fn(),
  status: 'idle', // or 'streaming' when a test needs it
  append: jest.fn(), // Legacy compatibility
  isLoading: false, // Legacy compatibility
}))

// Create a shared mock implementation
const mockObjectHook = () => ({
  object: null,
  submit: jest.fn(),
  isLoading: false,
})

export const useObject = jest.fn(mockObjectHook)
export const experimental_useObject = jest.fn(mockObjectHook)

// Add other hooks as needed
export const useCompletion = jest.fn(() => ({
  completion: '',
  complete: jest.fn(),
  isLoading: false,
}))
