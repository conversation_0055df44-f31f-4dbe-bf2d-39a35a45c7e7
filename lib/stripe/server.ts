'use server'

import <PERSON><PERSON> from 'stripe'
import { stripe } from '@/lib/stripe/config'
import { NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { auth } from '@/auth'
import prisma from '@/app/libs/prismadb'

async function resolveBaseUrl(): Promise<string> {
  // Prefer request headers to dynamically infer base URL (works on Vercel/Node)
  try {
    const h = await headers()
    const host = h.get('x-forwarded-host') ?? h.get('host')
    const proto =
      h.get('x-forwarded-proto') ??
      (process.env.NODE_ENV === 'development' ? 'http' : 'https')
    if (host) return `${proto}://${host}`
  } catch {
    // headers() can throw if called outside a request context
  }
  // Fallback to envs if headers unavailable
  return (
    process.env.NEXT_PUBLIC_SERVER_URL ||
    process.env.NEXTAUTH_URL ||
    (process.env.NODE_ENV === 'development' ? 'http://localhost:3000' : '')
  )
}

export async function checkoutWithStripe(
  _user: unknown,
  price_id: string
): Promise<{ sessionId: string } | NextResponse> {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { errorRedirect: 'Unauthorized' },
        { status: 401 }
      )
    }
    if (!price_id) {
      return NextResponse.json(
        { errorRedirect: 'Invalid price' },
        { status: 400 }
      )
    }

    const dbUser = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { id: true, email: true, subscription_customer_id: true },
    })
    if (!dbUser) {
      return NextResponse.json(
        { errorRedirect: 'User not found' },
        { status: 404 }
      )
    }

    const baseUrl = await resolveBaseUrl()

    const params: Stripe.Checkout.SessionCreateParams = {
      mode: 'subscription',
      line_items: [
        {
          price: price_id,
          quantity: 1,
        },
      ],
      metadata: { user_id: dbUser.id, email: dbUser.email ?? '' },
      customer: dbUser.subscription_customer_id ?? undefined,
      cancel_url: `${baseUrl}/subscription`,
      success_url: `${baseUrl}/subscription/success?session_id={CHECKOUT_SESSION_ID}`,
      allow_promotion_codes: true,
    }

    const sessionObj = await stripe.checkout.sessions.create(params)
    return { sessionId: sessionObj.id }
  } catch (error) {
    console.error('Error creating checkout session:', error)
    return NextResponse.json({
      errorRedirect: 'Failed to create checkout session',
    })
  }
}

export async function createStripePortal(_user: unknown) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { errorRedirect: 'Unauthorized' },
        { status: 401 }
      )
    }

    const dbUser = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { subscription_customer_id: true },
    })

    if (!dbUser?.subscription_customer_id) {
      return NextResponse.json(
        {
          errorRedirect:
            'No subscription_customer_id, could not create portal.',
        },
        { status: 400 }
      )
    }

    const baseUrl = await resolveBaseUrl()

    const params: Stripe.BillingPortal.SessionCreateParams = {
      customer: dbUser.subscription_customer_id,
      return_url: `${baseUrl}/conversations`,
    }

    const { url } = await stripe.billingPortal.sessions.create(params)
    if (!url) {
      throw new Error('Could not create billing portal')
    }
    return url
  } catch (error) {
    console.error(error)
    return NextResponse.json({
      errorRedirect: 'Failed to create billing portal',
    })
  }
}
