'use client'

import { useCallback } from 'react'
import { useSelectionStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useSelectionStore'
import {
  useAiPaneStore,
  ContextItem,
} from '@/app/(conv)/dragTree/[dragTreeId]/stores/useAiPaneStore'
import { useDragTreeStore } from '@/app/stores/dragtree_store/store'

/**
 * Hook for integrating node selection with AI pane context
 *
 * Handles:
 * - Converting selected node IDs to AI pane context items
 * - Pre-populating AI pane context when selection is used
 * - Ensuring only researched nodes are included in context
 */
export const useSelectionToContext = () => {
  const selectedNodeIds = useSelectionStore(state => state.selectedNodeIds)

  const setContext = useAiPaneStore(state => state.setContext)

  // Get node data from drag tree store
  const frontendTreeStructure = useDragTreeStore(
    state => state.frontendTreeStructure
  )
  const nodeContent = useDragTreeStore(state => state.nodeContent)
  const researchedNodeIds = useDragTreeStore(state => state.researchedNodeIds)
  const fetchNodeContentBatch = useDragTreeStore(
    state => state.fetchNodeContentBatch
  )

  /**
   * Find a node in the tree structure by ID
   */
  const findNodeById = useCallback(
    (nodeId: string): any => {
      if (!frontendTreeStructure) return null

      const findNode = (node: any): any => {
        if (node.id === nodeId) return node
        for (const child of node.children || []) {
          const found = findNode(child)
          if (found) return found
        }
        return null
      }

      return findNode(frontendTreeStructure)
    },
    [frontendTreeStructure]
  )

  /**
   * Get the first content ID for a node (needed for fetching content)
   */
  const getFirstContentIdForNode = useCallback(
    (nodeId: string): string | null => {
      const contentMap = nodeContent.get(nodeId)
      if (contentMap && contentMap.size > 0) {
        return Array.from(contentMap.keys())[0]
      }
      return null
    },
    [nodeContent]
  )

  /**
   * Check if a node has been researched
   */
  const isNodeResearched = useCallback(
    (nodeId: string): boolean => {
      const hasContent =
        nodeContent.has(nodeId) && nodeContent.get(nodeId)!.size > 0
      const isMarkedResearched = researchedNodeIds.has(nodeId)
      return hasContent || isMarkedResearched
    },
    [nodeContent, researchedNodeIds]
  )

  /**
   * Convert selected node IDs to AI pane context items
   */
  const convertSelectionToContext = useCallback((): ContextItem[] => {
    const contextItems: ContextItem[] = []

    for (const nodeId of Array.from(selectedNodeIds)) {
      // Only include researched nodes
      if (!isNodeResearched(nodeId)) continue

      const node = findNodeById(nodeId)
      if (!node) continue

      // Get content for the node
      const nodeContentMap = nodeContent.get(nodeId)
      let content = ''
      if (nodeContentMap && nodeContentMap.size > 0) {
        // Get the first content item's text
        const firstContent = Array.from(nodeContentMap.values())[0]
        content = firstContent.contentText || ''
      }

      const contextItem: ContextItem = {
        id: nodeId,
        title: node.label || `Node ${nodeId}`,
        type: node.type === 'question' ? 'question' : 'category',
        parentId: node.parentId,
        categoryId: node.type === 'question' ? node.parentId : nodeId,
        content: content,
        selected: true, // Pre-select all items from selection
      }

      contextItems.push(contextItem)
    }

    return contextItems
  }, [selectedNodeIds, isNodeResearched, findNodeById, nodeContent])

  /**
   * Apply selected nodes as AI pane context
   * Ensures content is properly loaded in nodeContent map for token calculation
   */
  const applySelectionAsContext = useCallback(async () => {
    const contextItems = convertSelectionToContext()

    if (contextItems.length > 0) {
      console.log(
        '🎯 [Selection] Applying',
        contextItems.length,
        'selected nodes as AI context:',
        contextItems.map(item => ({
          id: item.id,
          title: item.title,
          hasContent: !!item.content,
        }))
      )

      // Ensure content is loaded in nodeContent map for proper token calculation
      const batchPairs: { nodeId: string; contentId: string }[] = []

      for (const item of contextItems) {
        const contentMap = nodeContent.get(item.id)
        const firstItem = contentMap ? Array.from(contentMap.values())[0] : null
        const needsFetch =
          !contentMap ||
          contentMap.size === 0 ||
          !firstItem?.contentText ||
          firstItem.contentText.trim().length === 0

        if (needsFetch) {
          const firstContentId = getFirstContentIdForNode(item.id)
          if (firstContentId) {
            batchPairs.push({ nodeId: item.id, contentId: firstContentId })
          }
        }
      }

      // Fetch missing content if needed
      if (batchPairs.length > 0) {
        console.log(
          '🎯 [Selection] Fetching content for',
          batchPairs.length,
          'nodes'
        )
        try {
          await fetchNodeContentBatch(batchPairs)
          console.log('🎯 [Selection] Content fetched successfully')
        } catch (error) {
          console.error('🎯 [Selection] Error fetching content:', error)
        }
      }

      // Clear existing context first to prevent conflicts
      setContext([])

      // Set new context after ensuring content is loaded
      // This allows the ContextSelection component to properly calculate tokens
      setTimeout(() => {
        setContext(contextItems)
        console.log(
          '🎯 [Selection] Context items set with loaded content, token calculation should work'
        )
      }, 150) // Slightly longer delay to ensure content is fully loaded
    } else {
      console.warn('⚠️ [Selection] No researched nodes found in selection')
    }
  }, [
    convertSelectionToContext,
    setContext,
    nodeContent,
    getFirstContentIdForNode,
    fetchNodeContentBatch,
  ])

  /**
   * Get count of researched nodes in current selection
   */
  const getResearchedSelectionCount = useCallback((): number => {
    let count = 0
    for (const nodeId of Array.from(selectedNodeIds)) {
      if (isNodeResearched(nodeId)) {
        count++
      }
    }
    return count
  }, [selectedNodeIds, isNodeResearched])

  // Auto-apply selection as context when AI pane opens and we have selections
  // Removed this useEffect to prevent excessive database queries and infinite loops
  // Context application is now handled manually in SelectionControls

  return {
    convertSelectionToContext,
    applySelectionAsContext,
    getResearchedSelectionCount,
    isNodeResearched,
    findNodeById,
  }
}
