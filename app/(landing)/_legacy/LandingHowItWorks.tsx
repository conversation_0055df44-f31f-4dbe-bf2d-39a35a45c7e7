import Image from "next/image";

type Section = {
  title: string;
  description: string;
  imageSrc: string;
  altText: string;
  exText: string;
};

const sections: Section[] = [
  {
    title: "1. Start with an Ambiguous Thought",
    description: "Transform vague concepts",
    imageSrc: "/images/landing_v2_rephrase_screenshot.png",
    altText: "Rephrase your idea",
    exText:
      "Share your idea, no matter how vague. We'll refine and articulate, ensuring a shared vision of the problem.",
  },
  {
    title: "2. Structure the Problem Automatically",
    description: "AI-powered problem breakdown",
    imageSrc: "/images/landing_v2_clarify_screenshot.png",
    altText: "Automatic issue tree creation",
    exText:
      "We automatically generate an organized issue tree, allowing you to focus on solving the problem itself.",
  },
  {
    title: "3. Assist with Deep-Dive Research",
    description: "Contextual information gathering",
    imageSrc: "/images/landing_v2_research_screenshot.png",
    altText: "Gather relevant research and context",
    exText:
      "Contribute your insights or let us conduct real-time research to gather comprehensive information for you.",
  },
  {
    title: "4. Master AI-Powered Problem Solving",
    description: "Interactive AI prompt panel",
    imageSrc: "/images/landing_v2_prompt_screenshot.png",
    altText: "Utilize AI prompts effectively",
    exText:
      "Use our interactive prompt panel to leverage structured insights, generating quality outputs like an AI expert.",
  },
];

const HowItWorks = async () => {
  return (
    <>
      <div className="py-8 text-center">
        <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-10 text-white">
          How It Works 🤔
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-1 lg:grid-cols-2 gap-10">
          {sections.map((section, index) => (
            <div
              key={index}
              className="flex flex-col h-full justify-between items-center space-y-5 bg-gray-800/50 rounded-lg p-5 shadow-md"
            >
              <h3 className="text-2xl font-semibold text-white">
                {section.title}
              </h3>
              <Image
                src={section.imageSrc}
                alt={section.altText}
                width={500}
                height={300}
              />
              {section.exText && (
                <h3 className="text-xl text-gray-300 self-end">
                  {section.exText}
                </h3>
              )}
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default HowItWorks;
