<?xml version="1.0" encoding="UTF-8"?>
<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with subtle gradient for depth -->
  <defs>
    <radialGradient id="bgGradient" cx="50%" cy="30%" r="80%">
      <stop offset="0%" style="stop-color:#1e293b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0f172a;stop-opacity:1" />
    </radialGradient>
    <linearGradient id="lightbulbGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="branchGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#34d399;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#10b981;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Background circle -->
  <circle cx="32" cy="32" r="30" fill="url(#bgGradient)" stroke="#3b82f6" stroke-width="2"/>

  <!-- Central lightbulb (inspiration/idea) -->
  <g transform="translate(32, 16)">
    <!-- Lightbulb body -->
    <path d="M-6 -2 C-6 -8, 6 -8, 6 -2 C6 2, 4 4, 4 6 L-4 6 C-4 4, -6 2, -6 -2 Z"
          fill="url(#lightbulbGradient)" stroke="#fbbf24" stroke-width="1"/>
    <!-- Lightbulb base -->
    <rect x="-3" y="6" width="6" height="3" rx="1" fill="#64748b"/>
    <!-- Light rays -->
    <line x1="-10" y1="-4" x2="-8" y2="-4" stroke="#fbbf24" stroke-width="1.5" stroke-linecap="round"/>
    <line x1="8" y1="-4" x2="10" y2="-4" stroke="#fbbf24" stroke-width="1.5" stroke-linecap="round"/>
    <line x1="-7" y1="-8" x2="-6" y2="-7" stroke="#fbbf24" stroke-width="1.5" stroke-linecap="round"/>
    <line x1="6" y1="-7" x2="7" y2="-8" stroke="#fbbf24" stroke-width="1.5" stroke-linecap="round"/>
    <line x1="0" y1="-12" x2="0" y2="-10" stroke="#fbbf24" stroke-width="1.5" stroke-linecap="round"/>
  </g>

  <!-- Tree structure growing from the idea -->
  <!-- Main trunk -->
  <line x1="32" y1="25" x2="32" y2="35" stroke="#64748b" stroke-width="3" stroke-linecap="round"/>

  <!-- Primary branches (key insights) -->
  <line x1="32" y1="35" x2="22" y2="42" stroke="#64748b" stroke-width="2.5" stroke-linecap="round"/>
  <line x1="32" y1="35" x2="42" y2="42" stroke="#64748b" stroke-width="2.5" stroke-linecap="round"/>

  <!-- Secondary branches -->
  <line x1="22" y1="42" x2="18" y2="48" stroke="#64748b" stroke-width="2" stroke-linecap="round"/>
  <line x1="22" y1="42" x2="26" y2="48" stroke="#64748b" stroke-width="2" stroke-linecap="round"/>
  <line x1="42" y1="42" x2="38" y2="48" stroke="#64748b" stroke-width="2" stroke-linecap="round"/>
  <line x1="42" y1="42" x2="46" y2="48" stroke="#64748b" stroke-width="2" stroke-linecap="round"/>

  <!-- Branch nodes (insights/questions) -->
  <circle cx="22" cy="42" r="4" fill="url(#branchGradient)" stroke="#34d399" stroke-width="1"/>
  <circle cx="42" cy="42" r="4" fill="url(#branchGradient)" stroke="#34d399" stroke-width="1"/>

  <!-- Leaf nodes (final insights) -->
  <circle cx="18" cy="48" r="2.5" fill="#3b82f6" stroke="#60a5fa" stroke-width="1"/>
  <circle cx="26" cy="48" r="2.5" fill="#3b82f6" stroke="#60a5fa" stroke-width="1"/>
  <circle cx="38" cy="48" r="2.5" fill="#3b82f6" stroke="#60a5fa" stroke-width="1"/>
  <circle cx="46" cy="48" r="2.5" fill="#3b82f6" stroke="#60a5fa" stroke-width="1"/>

  <!-- Subtle drag indicator -->
  <g transform="translate(50, 10)" opacity="0.6">
    <path d="M1 2 L3 2 L3 1 C3 0.5 3.5 0 4 0 C4.5 0 5 0.5 5 1 L5 3 L5 4 C5 4.5 4.5 5 4 5 L2 5 C1.5 5 1 4.5 1 4 L1 2 Z"
          fill="#94a3b8"/>
  </g>

  <!-- Growth sparkles (inspiration) -->
  <g opacity="0.8">
    <circle cx="15" cy="25" r="1" fill="#fbbf24"/>
    <circle cx="49" cy="30" r="1" fill="#fbbf24"/>
    <circle cx="25" cy="55" r="1" fill="#60a5fa"/>
    <circle cx="39" cy="55" r="1" fill="#60a5fa"/>
  </g>
</svg>