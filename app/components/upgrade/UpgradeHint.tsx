'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { FiArrowRight } from 'react-icons/fi'
import { cn } from '@/lib/utils'
import { logEventWithContext } from '@/app/libs/logging'
import { SubscriptionTier } from '@prisma/client'
import type { UpgradeHintProps } from './types'

/**
 * Unified upgrade hint component for inline feature restrictions
 * Replaces red warning-style upgrade notices with friendly emerald-teal styling
 */
export const UpgradeHint: React.FC<UpgradeHintProps> = ({
  message,
  context,
  currentTier,
  onClick,
  className,
  showButton = true,
  buttonText = 'Upgrade',
}) => {
  const router = useRouter()
  const { data: session } = useSession()

  const handleClick = () => {
    // Get current tier for analytics
    const tier =
      currentTier ||
      (((session?.user as any)?.subscription_tier ||
        (session?.user as any)?.subscription?.tier ||
        SubscriptionTier.FREE) as SubscriptionTier)

    // Log analytics event
    logEventWithContext('click_upgrade_hint', session?.user?.id, undefined, {
      context,
      current_tier: tier,
      message: message.slice(0, 100), // Truncate for analytics
    })

    if (onClick) {
      onClick()
    } else {
      router.push('/subscription')
    }
  }

  return (
    <div
      className={cn(
        'rounded-lg border border-emerald-200 bg-emerald-50 text-emerald-800 text-sm px-4 py-3',
        className
      )}
      role="alert"
    >
      <div className="flex items-center justify-between gap-3">
        <div className="flex-1 font-medium">{message}</div>
        {showButton && (
          <button
            onClick={handleClick}
            className={cn(
              'shrink-0 inline-flex items-center rounded-md px-3 py-1.5 text-xs font-medium transition-all duration-200',
              'bg-gradient-to-r from-emerald-500 to-teal-600 text-white',
              'hover:from-emerald-600 hover:to-teal-700 hover:shadow-md',
              'transform hover:scale-105 active:scale-95',
              'focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2'
            )}
          >
            <span>{buttonText}</span>
            <FiArrowRight size={12} className="ml-1" />
          </button>
        )}
      </div>
    </div>
  )
}
