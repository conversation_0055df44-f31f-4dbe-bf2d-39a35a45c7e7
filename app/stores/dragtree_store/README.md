# DragTree Store Refactored

This directory contains the refactored DragTree store that has been split into multiple utility modules for better maintainability and readability.

## File Structure

```
dragtree_store/
├── index.ts                 # Main export file
├── store.ts                 # Refactored main store implementation
├── dragtree_store.ts        # Original store (legacy)
├── utils/
│   ├── database-utils.ts    # Database conversion utilities
│   ├── tree-utils.ts        # Tree manipulation utilities
│   ├── node-utils.ts        # Node creation and metadata utilities
│   └── sync-utils.ts        # Database synchronization utilities
└── README.md               # This file
```

## Refactoring Benefits

### Before (967 lines in single file)

- All utility functions mixed with store implementation
- Hard to read and maintain
- Difficult for LLMs to process due to size
- Helper functions scattered throughout the store

### After (Separated into focused modules)

- **store.ts** (654 lines): Clean store implementation using utilities
- **database-utils.ts**: Database conversion functions
- **tree-utils.ts**: Tree traversal and manipulation functions
- **node-utils.ts**: Node creation and metadata utilities
- **sync-utils.ts**: Database synchronization helpers

## Module Overview

### `database-utils.ts`

Handles conversion between frontend `TreeNode` format and database `DragTree` format:

- `reconstructTreeFromDatabase()` - Convert DB format to frontend tree
- `convertTreeToDbStructure()` - Convert frontend tree to DB format
- `DatabaseDragTree` type definition

### `tree-utils.ts`

Pure functions for tree manipulation and traversal:

- `calculateNodeLevel()` - Get node depth in tree
- `findNodeById()` - Find node by ID
- `addNodeToTree()` - Add new node to tree
- `deleteNodeFromTree()` - Remove node from tree
- `editNodeInTree()` - Update node label
- `reorderNodeInTree()` - Reorder children
- `toggleNodeInterest()` - Toggle interest status
- `validateTreeConstraints()` - Validate operations
- And more tree utilities...

### `node-utils.ts`

Node creation and metadata utilities:

- `createNewTreeNode()` - Create properly structured nodes
- `createNodeMetadata()` - Generate node metadata
- `createNodesFromText()` - Create multiple nodes from text array

### `sync-utils.ts`

Database synchronization helpers:

- `debouncedDbSync()` - Debounce database operations
- `executeAtomicOperations()` - Execute multiple DB ops atomically
- `PendingOperationsTracker` - Track pending operations

## Usage

Import the store and utilities as needed:

```typescript
// Main store
import { useDragTreeStore } from '@/app/stores/dragtree_store'

// Specific utilities (if needed outside store)
import {
  reconstructTreeFromDatabase,
  calculateNodeLevel,
  createNewTreeNode,
} from '@/app/stores/dragtree_store'
```

## Key Improvements

1. **Separation of Concerns**: Each module has a specific responsibility
2. **Pure Functions**: Tree utilities are pure functions that can be easily tested
3. **Type Safety**: Better type annotations throughout
4. **Readability**: Store methods are now much cleaner and easier to understand
5. **Maintainability**: Changes to specific functionality can be made in focused modules
6. **Testability**: Utility functions can be tested independently
7. **LLM Friendly**: Smaller, focused files are easier for AI models to process
8. **Performance Optimized**: Individual selectors prevent unnecessary re-renders

## Performance Best Practices

### **Use Individual Selectors**

```typescript
// ✅ Good: Only re-renders when specific nodeContent changes
const nodeContentMap = useDragTreeStore(state => state.nodeContent.get(nodeId))
const updateNodeContent = useDragTreeStore(state => state.updateNodeContent)

// ❌ Bad: Re-renders on any store change
const { nodeContent, updateNodeContent } = useDragTreeStore()
```

### **Optimize Component Props**

```typescript
// ✅ Good: React.memo can work effectively
<OutlineNode
  node={node}
  isClicked={interactionState.isClicked}
  isHovered={interactionState.isHovered}
/>

// ❌ Bad: Spread objects break React.memo
<OutlineNode {...props} {...interactionState} />
```

### **Granular State Access**

- Use `state.frontendTreeStructure` for tree structure
- Use `state.nodeContent.get(nodeId)` for specific node content
- Use individual action selectors like `state.updateNodeContent`
- Avoid broad subscriptions that cause unnecessary re-renders

## Migration Notes

The refactored store maintains 100% API compatibility with the original store. All existing components using `useDragTreeStore` will continue to work without changes.

The original `dragtree_store.ts` file is kept for reference but should be considered legacy. New development should use the refactored structure.

**Recent Optimizations**: Components have been updated to use individual selectors and proper React.memo patterns for optimal performance.
