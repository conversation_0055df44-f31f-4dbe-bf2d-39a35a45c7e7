import { NextRequest, NextResponse } from 'next/server'
import { syncStaleCustomers } from '@/lib/stripe/sync'

export const runtime = 'nodejs'
export const dynamic = 'force-dynamic'

// Simple endpoint that can be wired to a cron job to reconcile stale users
export async function POST(req: NextRequest) {
  try {
    const { hours } = (await req.json().catch(() => ({ hours: 24 }))) as {
      hours?: number
    }
    await syncStaleCustomers(typeof hours === 'number' ? hours : 24)
    return NextResponse.json({ ok: true })
  } catch (error) {
    console.error('Reconciliation failed:', error)
    return NextResponse.json(
      { error: 'Reconciliation failed' },
      { status: 500 }
    )
  }
}
