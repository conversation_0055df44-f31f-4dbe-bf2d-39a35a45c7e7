import { customAlphabet } from 'nanoid'
import CryptoJS from 'crypto-js'
import { DEFAULT_NODE_LABELS } from '@/app/(conv)/dragTree/[dragTreeId]/constants/node-types'

/**
 * Custom alphabet for IDs - only alphanumeric characters (a-zA-Z0-9)
 * No dashes, underscores, or other special characters in the random part
 */
const ID_ALPHABET =
  'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
const generateAlphanumericId = customAlphabet(ID_ALPHABET, 21)

/**
 * Common ID prefixes for drag tree entities
 * Users provide these without underscore, function adds it
 */
export enum DragTreeIdPrefix {
  TREE = 'tree',
  CATEGORY_NODE = 'cat',
  QUESTION_NODE = 'que',
  NODE_CONTENT = 'cont', // Short for content, 4 chars, recognizable
}

/**
 * AI Chat ID prefixes for extensible chat schema
 * Users provide these without underscore, function adds it
 */
export enum AiChatIdPrefix {
  THREAD = 'thread',
  MESSAGE = 'msg',
  STEP = 'step',
  FILE = 'file',
}

/**
 * Default hash length configurations
 */
export const HASH_LENGTHS = {
  SHORT: 8, // For UI display, less collision resistance
  MEDIUM: 12, // Default for most use cases
  LONG: 16, // High collision resistance
  FULL: 32, // Maximum collision resistance (full SHA-256 truncated)
} as const

/**
 * Robust hash function using crypto-js SHA-256
 * Works in both browser and Node.js environments
 * Much stronger than simple hash functions - no collision concerns
 *
 * @param input - The string to hash
 * @param length - Desired hash length (default: 12 characters)
 * @returns Base36 hash string of specified length (only a-z0-9)
 */
function robustHash(
  input: string,
  length: number = HASH_LENGTHS.MEDIUM
): string {
  // Use SHA-256 for cryptographically strong hash
  const hash = CryptoJS.SHA256(input).toString(CryptoJS.enc.Hex)

  // Convert hex to base36 for shorter, URL-safe string (only a-z0-9)
  const base36Hash = BigInt('0x' + hash.slice(0, 16)).toString(36)

  // Truncate or pad to desired length
  return base36Hash.slice(0, length).padEnd(length, '0')
}

/**
 * Generates a random unique ID with the provided prefix
 * Uses custom alphabet to ensure only a-zA-Z0-9 characters (no dashes)
 *
 * @param prefix - The prefix to use (without underscore)
 * @param len - The length of the random part (default: 21)
 *
 * @example
 * // For drag trees (always random, only alphanumeric)
 * generateIdWithPrefix("tree")  // → "tree_A1b2C3d4E5f6G7h8I9j0K"
 * generateIdWithPrefix("session", 16)  // → "session_A1b2C3d4E5f6G7h8"
 */
export function generateIdWithPrefix(prefix: string, len: number = 21): string {
  // Function handles underscore, user provides prefix without it
  // Use custom alphabet generator to ensure only a-zA-Z0-9
  const customGen = customAlphabet(ID_ALPHABET, len)
  return `${prefix}_${customGen()}`
}

/**
 * Generates a deterministic ID based on input data using robust hashing
 * This ensures frontend and backend generate identical IDs from same input
 * Uses SHA-256 for collision resistance - no worries about hash collisions
 *
 * @param prefix - The prefix to use (without underscore)
 * @param dragTreeId - The parent drag tree ID
 * @param label - The node label/text
 * @param nodeType - Optional node type for additional uniqueness
 * @param hashLength - Length of hash part (default: MEDIUM = 12)
 *
 * @example
 * // For drag tree nodes (deterministic, collision-resistant, only a-z0-9)
 * generateDeterministicId("cat", "tree_abc123", "Financial Planning")
 * // → "cat_k2x9m7n4q8w1" (same hash every time, strong collision resistance)
 *
 * generateDeterministicId("que", "tree_abc123", "What is your budget?", "QUESTION", HASH_LENGTHS.LONG)
 * // → "que_k2x9m7n4q8w1p5r3" (longer hash for extra collision resistance)
 */
export function generateDeterministicId(
  prefix: string,
  dragTreeId: string,
  label: string,
  nodeType?: string,
  hashLength: number = HASH_LENGTHS.MEDIUM
): string {
  // Create deterministic hash from tree ID + label + type
  const hashInput = `${dragTreeId}:${label.trim()}:${nodeType || ''}`
  const hash = robustHash(hashInput, hashLength)
  return `${prefix}_${hash}`
}

/**
 * Generates a unique deterministic ID for new nodes that might have duplicate labels
 * This is specifically for "New Category" / "New Question" that need to be unique
 * Uses label + timestamp + random element for uniqueness while remaining deterministic
 *
 * @param prefix - The prefix to use (without underscore)
 * @param dragTreeId - The parent drag tree ID
 * @param label - The node label/text
 * @param nodeType - Optional node type for additional uniqueness
 * @param hashLength - Length of hash part (default: MEDIUM = 12)
 *
 * @example
 * // For new nodes that need to be unique
 * generateUniqueNodeId("cat", "tree_abc123", "New Category", "CATEGORY")
 * // → "cat_k2x9m7n4q8w1p5r3" (includes timestamp + random for uniqueness)
 */
export function generateUniqueNodeId(
  prefix: string,
  dragTreeId: string,
  label: string,
  nodeType?: string,
  hashLength: number = HASH_LENGTHS.MEDIUM
): string {
  // For nodes with generic labels like "New Category" or "New Question",
  // add timestamp and random element to ensure uniqueness
  const isGenericLabel =
    label === DEFAULT_NODE_LABELS.NEW_CATEGORY ||
    label === DEFAULT_NODE_LABELS.NEW_QUESTION

  let hashInput: string
  if (isGenericLabel) {
    // Include timestamp and random element for uniqueness
    const timestamp = Date.now().toString(36) // Base36 timestamp
    const randomPart = generateAlphanumericId().slice(0, 6) // 6 chars from our alphabet
    hashInput = `${dragTreeId}:${label}:${
      nodeType || ''
    }:${timestamp}:${randomPart}`
  } else {
    // Use regular deterministic approach for named nodes
    hashInput = `${dragTreeId}:${label.trim()}:${nodeType || ''}`
  }

  const hash = robustHash(hashInput, hashLength)
  return `${prefix}_${hash}`
}

/**
 * Convenience functions for common use cases
 */

// Drag Tree IDs (always random, created once, only alphanumeric)
export function generateDragTreeId(): string {
  return generateIdWithPrefix(DragTreeIdPrefix.TREE)
}

// Drag Tree Node IDs (deterministic for consistency between FE/BE, unique for new nodes)
export function generateDragTreeNodeId(
  dragTreeId: string,
  label: string,
  nodeType: 'CATEGORY' | 'QUESTION',
  hashLength: number = HASH_LENGTHS.MEDIUM,
  forceUnique: boolean = false
): string {
  const prefix =
    nodeType === 'CATEGORY'
      ? DragTreeIdPrefix.CATEGORY_NODE
      : DragTreeIdPrefix.QUESTION_NODE

  // Use unique generation for new nodes or when explicitly requested
  if (
    forceUnique ||
    label === DEFAULT_NODE_LABELS.NEW_CATEGORY ||
    label === DEFAULT_NODE_LABELS.NEW_QUESTION
  ) {
    return generateUniqueNodeId(prefix, dragTreeId, label, nodeType, hashLength)
  }

  return generateDeterministicId(
    prefix,
    dragTreeId,
    label,
    nodeType,
    hashLength
  )
}

// Content IDs (deterministic based on tree + node)
export function generateDragTreeNodeContentId(
  dragTreeId: string,
  dragTreeNodeId: string,
  hashLength: number = HASH_LENGTHS.MEDIUM
): string {
  return generateDeterministicId(
    DragTreeIdPrefix.NODE_CONTENT,
    dragTreeId,
    dragTreeNodeId,
    'content',
    hashLength
  )
}

/**
 * AI Chat ID generation functions
 * These are always random since they represent unique conversation instances
 */

// AI Conversation IDs (random, unique for each conversation)
export function generateAiConversationId(): string {
  return generateIdWithPrefix(AiChatIdPrefix.THREAD)
}

// AI Message IDs (random, unique for each message)
export function generateAiMessageId(): string {
  return generateIdWithPrefix(AiChatIdPrefix.MESSAGE)
}

// AI Execution Step IDs (random, unique for each step)
export function generateAiExecutionStepId(): string {
  return generateIdWithPrefix(AiChatIdPrefix.STEP)
}

// AI Attachment IDs (random, unique for each file)
export function generateAiAttachmentId(): string {
  return generateIdWithPrefix(AiChatIdPrefix.FILE)
}
