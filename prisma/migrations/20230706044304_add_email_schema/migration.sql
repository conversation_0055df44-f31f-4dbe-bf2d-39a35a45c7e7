-- CreateEnum
CREATE TYPE "EmailStatus" AS ENUM ('DELIVERED', 'FAILED');

-- CreateTable
CREATE TABLE "emails" (
    "id" TEXT NOT NULL,
    "status" "EmailStatus" NOT NULL DEFAULT 'DELIVERED',
    "from" TEXT NOT NULL,
    "to" TEXT NOT NULL,
    "subject" TEXT NOT NULL,
    "text" TEXT NOT NULL,
    "html" TEXT NOT NULL,
    "config" JSONB NOT NULL DEFAULT '{}',
    "user_id" TEXT,
    "conversation_id" TEXT,
    "prompt_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "emails_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "emails_id_idx" ON "emails" USING HASH ("id");

-- CreateIndex
CREATE INDEX "emails_conversation_id_idx" ON "emails" USING HASH ("conversation_id");

-- CreateIndex
CREATE INDEX "emails_user_id_idx" ON "emails" USING HASH ("user_id");

-- CreateIndex
CREATE INDEX "emails_prompt_id_idx" ON "emails" USING HASH ("prompt_id");

-- AddForeignKey
ALTER TABLE "emails" ADD CONSTRAINT "emails_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "emails" ADD CONSTRAINT "emails_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "conversations"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "emails" ADD CONSTRAINT "emails_prompt_id_fkey" FOREIGN KEY ("prompt_id") REFERENCES "prompts"("id") ON DELETE SET NULL ON UPDATE CASCADE;
