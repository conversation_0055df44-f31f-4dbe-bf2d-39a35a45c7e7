/**
 * Research Display UI Component
 *
 * Pure UI component for displaying and editing research content.
 * Separated from business logic for better maintainability.
 *
 * This component only handles:
 * - UI rendering and styling
 * - User interaction events for editing
 * - Visual states and animations
 * - Content display and formatting
 *
 * Business logic is handled by:
 * - useResearchState hook
 * - useResearchLifecycle hook
 */

import React, { useRef, useEffect } from 'react'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import {
  Eye,
  EyeOff,
  Loader2,
  RefreshCw,
  AlertTriangle,
  ExternalLink,
  Edit3,
} from 'lucide-react'
import { DragTreeNodeContentStatus } from '@prisma/client'
import { cn } from '@/lib/utils'
import { motion, AnimatePresence } from 'framer-motion'
import {
  SearchProgressIndicator,
  SourceCitations,
} from '@/app/(conv)/dragTree/[dragTreeId]/components/SearchResultsDisplay'
import type { ResearchState } from '@/app/(conv)/dragTree/[dragTreeId]/components/shared/hooks/useResearchState'
// Removed unused toast import

export type ResearchDisplayUIProps = {
  // Research state (from hook)
  researchState: ResearchState

  // Content and streaming
  localContent: string
  streamingContent: string
  isStreaming: boolean

  // Event handlers
  onContentChange: (content: string) => void
  onToggleVisibility: () => void
  onToggleEdit: () => void
  onMouseEnter: () => void
  onMouseLeave: () => void
  onRetryResearch: () => void
  onOpenInTab?: () => void

  // Configuration
  className?: string
  nodeId?: string
}

/**
 * Pure UI component for research display functionality.
 * Does not contain business logic - only handles UI concerns.
 */
const ResearchDisplayUI: React.FC<ResearchDisplayUIProps> = ({
  researchState,
  localContent,
  streamingContent,
  isStreaming,
  onContentChange,
  onToggleVisibility,
  onToggleEdit,
  onMouseEnter,
  onMouseLeave,
  onRetryResearch,
  onOpenInTab,
  className = '',
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  const {
    status,
    showContent,
    isPersistent,
    isEditable,
    isSaving,
    justSaved,
    searchResults,
    currentSearchQuery,
    isSearching,
  } = researchState

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    }
  }, [localContent, streamingContent])

  // Don't render anything if no content exists and not processing
  if (!status && !isStreaming) {
    return null
  }

  const displayContent = isStreaming ? streamingContent : localContent
  const isProcessing = status === DragTreeNodeContentStatus.PROCESSING
  const isActive = status === DragTreeNodeContentStatus.ACTIVE
  const isInactive = status === DragTreeNodeContentStatus.INACTIVE
  const hasResearched = !!searchResults && searchResults.length > 0

  const getStatusColor = () => {
    if (isStreaming || isProcessing) return 'bg-amber-400'
    if ((isActive && displayContent.trim().length > 0) || hasResearched)
      return 'bg-green-400'
    if (isInactive) return 'bg-red-400'
    return 'bg-gray-400'
  }

  const getButtonTitle = () => {
    if (isStreaming || isProcessing) return 'Research in progress...'
    if (isActive)
      return isPersistent
        ? 'Hide research content'
        : 'Click to pin research content'
    if (isInactive) return 'Research failed - click to retry'
    return 'Research content'
  }

  // Removed unused handleCopyContent function

  // Handle textarea content changes
  const handleContentChange = (
    event: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    onContentChange(event.target.value)
  }

  // Handle click events for UI buttons
  const handleButtonClick = (event: React.MouseEvent, action: () => void) => {
    event.preventDefault()
    event.stopPropagation()
    action()
  }

  return (
    <div
      className={cn('relative', className)}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      {/* Research Button */}
      <div className="flex items-center gap-1 mb-1">
        <Button
          variant="ghost"
          size="sm"
          onClick={isInactive ? onRetryResearch : onToggleVisibility}
          className={cn(
            'h-5 px-2 py-0 text-xs font-medium transition-all duration-200',
            'bg-slate-50 hover:bg-slate-100 border border-slate-200',
            'text-slate-600 hover:text-slate-700',
            {
              'bg-blue-50 border-blue-200 text-blue-700':
                showContent && isPersistent,
            }
          )}
          title={getButtonTitle()}
        >
          <div className="flex items-center gap-1.5">
            {/* Status indicator */}
            <div
              className={cn(
                'w-1.5 h-1.5 rounded-full transition-colors duration-200',
                getStatusColor(),
                {
                  'animate-pulse': isStreaming || isProcessing,
                }
              )}
            />

            <span>Research</span>

            {/* State icons */}
            {(isStreaming || isProcessing) && (
              <Loader2 className="h-3 w-3 animate-spin" />
            )}
            {isInactive && <RefreshCw className="h-3 w-3" />}
            {isActive &&
              !isStreaming &&
              (showContent && isPersistent ? (
                <EyeOff className="h-3 w-3" />
              ) : (
                <Eye className="h-3 w-3" />
              ))}
          </div>
        </Button>

        {/* Open in Tab Button */}
        {onOpenInTab && isActive && displayContent.trim() && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onOpenInTab}
            className={cn(
              'h-5 px-2 py-0 text-xs font-medium transition-all duration-200',
              'bg-slate-50 hover:bg-slate-100 border border-slate-200',
              'text-slate-600 hover:text-slate-700'
            )}
            title="Open in separate tab"
          >
            <ExternalLink className="h-3 w-3" />
          </Button>
        )}

        {/* Save indicator */}
        <AnimatePresence>
          {isSaving && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="text-xs text-blue-500"
            >
              Saving...
            </motion.div>
          )}
          {justSaved && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="text-xs text-green-500"
            >
              Saved ✓
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Content Area */}
      <AnimatePresence>
        {showContent && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="overflow-hidden"
          >
            <div className="space-y-2">
              {/* Search Progress Indicator */}
              {isSearching && currentSearchQuery && (
                <SearchProgressIndicator
                  isSearching={true}
                  currentQuery={currentSearchQuery}
                />
              )}

              {/* Content Display/Edit */}
              {isEditable ? (
                <div className="relative">
                  <Textarea
                    ref={textareaRef}
                    value={displayContent}
                    onChange={handleContentChange}
                    className={cn(
                      'min-h-[100px] max-h-64 resize-none border-slate-200 overflow-y-auto',
                      'focus:border-blue-300 focus:ring-1 focus:ring-blue-200',
                      'text-xs leading-tight'
                    )}
                    placeholder="Research content will appear here..."
                    disabled={isStreaming || isProcessing}
                  />

                  {/* Edit indicator */}
                  <div className="absolute top-2 right-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={e => handleButtonClick(e, onToggleEdit)}
                      className="h-6 w-6 p-0 opacity-60 hover:opacity-100"
                    >
                      <Edit3
                        className={cn(
                          'h-3 w-3',
                          researchState.isEditable && 'text-blue-500'
                        )}
                      />
                    </Button>
                  </div>
                </div>
              ) : (
                <div
                  className={cn(
                    'relative p-2 bg-slate-50 border border-slate-200 rounded-md',
                    'text-xs leading-tight text-slate-700',
                    'hover:bg-slate-100 transition-colors duration-200 cursor-pointer',
                    'max-h-80 overflow-y-auto'
                  )}
                  onClick={e => handleButtonClick(e, onToggleEdit)}
                >
                  {displayContent.trim() ? (
                    <pre className="whitespace-pre-wrap font-sans text-xs leading-tight">
                      {displayContent}
                    </pre>
                  ) : (
                    <div className="text-slate-400 italic">
                      {isStreaming || isProcessing
                        ? 'Generating research content...'
                        : 'No research content yet'}
                    </div>
                  )}

                  {/* Edit hint */}
                  {isActive && displayContent.trim() && (
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0 opacity-60 hover:opacity-100"
                      >
                        <Edit3
                          className={cn(
                            'h-3 w-3',
                            researchState.isEditable && 'text-blue-500'
                          )}
                        />
                      </Button>
                    </div>
                  )}
                </div>
              )}

              {/* Source Citations */}
              {searchResults && searchResults.length > 0 && (
                <SourceCitations
                  searchResults={searchResults}
                  className="mt-2"
                />
              )}

              {/* Error State */}
              {isInactive && (
                <div className="p-2 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4" />
                    <span>
                      Research failed. Click the button above to retry.
                    </span>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default React.memo(ResearchDisplayUI)
