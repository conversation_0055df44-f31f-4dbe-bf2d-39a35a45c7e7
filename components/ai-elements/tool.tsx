'use client'

import { ChevronDownIcon, WrenchIcon, GlobeIcon } from 'lucide-react'
import type { ComponentProps, ReactNode } from 'react'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import { cn } from '@/lib/utils'
import type { ToolUIPart } from 'ai'

export type ToolProps = ComponentProps<typeof Collapsible> & {
  variant?: 'default' | 'subtle'
}

export const Tool = ({
  className,
  variant = 'subtle',
  ...props
}: ToolProps) => (
  <Collapsible
    className={cn(
      'not-prose mb-2 block w-full max-w-[720px] text-xs',
      variant === 'default' &&
        'rounded border border-gray-200/60 bg-gray-50/30',
      variant === 'subtle' && 'border-0 bg-transparent',
      className
    )}
    {...props}
  />
)

export type ToolHeaderProps = {
  type: ToolUIPart['type']
  state: ToolUIPart['state']
  className?: string
  label?: string
  isExpandable?: boolean
}

export const ToolHeader = ({
  className,
  type,
  state,
  label,
  isExpandable = true,
  ...props
}: ToolHeaderProps) => {
  const toolType = String(type || '')
  const isWebSearch = toolType.includes('web_search')
  const isStreaming = state === 'input-streaming' || state === 'input-available'
  const displayLabel =
    label ??
    (isWebSearch
      ? isStreaming
        ? 'Searching…'
        : 'Searched'
      : toolType || 'Tool')
  const showRightStatus =
    !isWebSearch && displayLabel !== 'Searched' && displayLabel !== 'Searching…'

  const Content = (
    <div
      className={cn(
        'flex w-full items-center gap-1.5 px-1.5 py-0.5 text-[11px] max-w-[720px] rounded-md',
        !isExpandable && 'cursor-default'
      )}
    >
      <ChevronDownIcon
        className={cn(
          'size-2 text-gray-400 transition-transform data-[state=open]:rotate-180',
          !isExpandable && 'invisible'
        )}
      />
      {isWebSearch ? (
        <GlobeIcon className="size-2 text-gray-500" />
      ) : (
        <WrenchIcon className="size-2 text-gray-500" />
      )}
      <span className="font-medium text-[11px] text-gray-600 truncate">
        {displayLabel}
      </span>
      {showRightStatus && (
        <span className="ml-auto text-[10px] text-gray-400">
          {state === 'input-streaming'
            ? 'Pending'
            : state === 'input-available'
              ? 'Running'
              : state === 'output-error'
                ? 'Error'
                : state === 'output-available'
                  ? 'Done'
                  : ''}
        </span>
      )}
    </div>
  )

  if (!isExpandable) {
    return (
      <div
        className={cn(
          'block w-full max-w-[720px] hover:bg-gray-50 rounded-md',
          className
        )}
      >
        {Content}
      </div>
    )
  }

  return (
    <CollapsibleTrigger
      className={cn(
        'block w-full max-w-[720px] hover:bg-gray-50 rounded-md',
        className
      )}
      {...props}
    >
      {Content}
    </CollapsibleTrigger>
  )
}

export type ToolContentProps = ComponentProps<typeof CollapsibleContent>

export const ToolContent = ({ className, ...props }: ToolContentProps) => (
  <CollapsibleContent
    className={cn(
      'text-popover-foreground outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:slide-out-to-top-1 data-[state=open]:slide-in-from-top-1',
      className
    )}
    {...props}
  />
)

export type ToolInputProps = ComponentProps<'div'> & {
  input: ToolUIPart['input']
}

export const ToolInput = ({ className, input, ...props }: ToolInputProps) => {
  // Hide parameters by default for cleaner UI - users can expand if needed
  const hasImportantParams = input && Object.keys(input).length > 0
  if (!hasImportantParams) return null

  // Show only essential parameters in a more compact way
  const essentialParams = Object.entries(input || {}).reduce(
    (acc, [key, value]) => {
      // Show key parameters like query, search terms, etc.
      if (
        key.includes('query') ||
        key.includes('search') ||
        key.includes('url') ||
        key.includes('question')
      ) {
        acc[key] = value
      }
      return acc
    },
    {} as Record<string, any>
  )

  if (Object.keys(essentialParams).length === 0) return null

  return (
    <div className={cn('px-2 pb-1.5', className)} {...props}>
      <div className="text-xs text-gray-500 font-mono bg-gray-50/50 px-1.5 py-1 rounded">
        {JSON.stringify(essentialParams, null, 0)}
      </div>
    </div>
  )
}

export type ToolOutputProps = ComponentProps<'div'> & {
  output: ReactNode
  errorText: ToolUIPart['errorText']
}

export const ToolOutput = ({
  className,
  output,
  errorText,
  ...props
}: ToolOutputProps) => {
  if (!(output || errorText)) {
    return null
  }

  const isString = typeof output === 'string'
  const tooLongString = isString && (output as string).length > 500

  if (tooLongString && !errorText) {
    return null
  }

  return (
    <div className={cn('px-2 pb-1.5', className)} {...props}>
      {errorText && (
        <div className="text-xs text-red-500 bg-red-50/50 px-1.5 py-1 rounded">
          {errorText}
        </div>
      )}
      {output && (
        <div className="text-xs text-gray-700">
          {isString ? (
            <>
              {(output as string).slice(0, 200)}
              {(output as string).length > 200 ? '...' : ''}
            </>
          ) : (
            output
          )}
        </div>
      )}
    </div>
  )
}
