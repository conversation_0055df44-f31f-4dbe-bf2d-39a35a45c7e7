'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { useParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { useChat } from '@ai-sdk/react'
import { DefaultChatTransport } from 'ai'
import { toast } from 'react-hot-toast'
import debounce from 'lodash/debounce'
import { Notebook, NotebookStatus } from '@prisma/client'
import {
  getNotebookById,
  getNotebookStatusById,
  getNotebookTitleById,
  updateNotebook,
} from '@/app/(legacy)/_server-actions/notebook'
import { generateNotebookRequestType } from '@/app/types/api'
import { LoadingSkeleton } from '@/app/components/LoadingSkeleton'
import type { Msg } from '@/app/types/ai-sdk5'
import { extractTextContent } from '@/app/types/ai-sdk5'
import { TiptapExtensions } from '../extensions'
import {
  Editor,
  JSONContent,
  EditorContent as TiptapEditorContent,
  useEditor,
} from '@tiptap/react'
import { EditorBubbleMenu } from '../components'
import { Textarea } from '@/components/ui/textarea'
import Link from 'next/link'
import { TiptapEditorProps } from '../props'
import {
  convertEditorContentToMarkdown,
  copyToClipboard,
  getInitialContent,
  GetInitialContentType,
  processMessageContent,
} from '../utils'
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet'
import { MoreVertical } from 'lucide-react'
import { format } from 'date-fns'
import { Button } from '@/components/ui/button'
import { Copy } from 'lucide-react'

type EditorProps = {
  notebook: Notebook
}

enum NotebookSaveStatus {
  SAVED = 'Saved',
  UNSAVED = 'Unsaved',
  ERROR = 'Error saving',
}

export default function NotebookEditor({ notebook }: EditorProps) {
  const params = useParams()
  const session = useSession()
  const conversationId = params.conversationId as string
  const [saveStatus, setSaveStatus] = useState<NotebookSaveStatus>(
    NotebookSaveStatus.SAVED
  )
  const [isMounted, setIsMounted] = useState<boolean>(false)
  const [title, setTitle] = useState<string>(
    notebook.title || 'Untitled Notebook'
  )
  const [showTiptapEditor, setShowTiptapEditor] = useState<boolean>(
    notebook.status !== NotebookStatus.INITIALIZED
  )

  const { editor, setGeneratedContent, setIsEditable } = useNotebookEditor(
    notebook,
    (status: string) => setSaveStatus(status as NotebookSaveStatus),
    (partialNotebook: Partial<Notebook>) =>
      debouncedUpdateNotebook(partialNotebook)
  )

  const chat = useChat<Msg>({
    transport: new DefaultChatTransport({
      api: '/api/notebook/generate_notebook',
      body: {
        userId: session?.data?.user?.id || '',
        conversationId: conversationId,
        notebookId: notebook.id,
      } as generateNotebookRequestType,
    }),
    onError(error: any) {
      console.error('Error:', error)
      toast.error('An error occurred. Please try again.')
    },
    onFinish: ({ message }: { message: Msg }) => {
      toast.success('Finished Response Generation!', { duration: 2000 })
      const content = extractTextContent(message)
      const notebookJSON = processMessageContent(content)
      setGeneratedContent(notebookJSON)
      setShowTiptapEditor(true)
      setIsEditable(true)
    },
  })

  // Extract properties for AI SDK 5 compatibility
  const { messages } = chat
  const isLoading = chat.status === 'streaming' || chat.status === 'submitted'

  // Create append function for compatibility
  const append = useCallback(
    (
      message: { role: 'user' | 'assistant'; content: string },
      options?: any
    ) => {
      chat.sendMessage({ text: message.content }, options)
    },
    [chat]
  )

  // Flags to prevent duplicate actions
  const hasRunRef = useRef<boolean>(false)
  const hasFetchedNewNotebook = useRef<boolean>(false)
  const hasFetchedTitle = useRef<boolean>(false)

  useEffect(() => {
    // Due to next js cache issue, this notebook may not be updated if user revisits the page
    // So we need to double check against the data in DB
    // TODO: Find a better way to solve this
    const initializeNotebook = async () => {
      if (
        notebook.status === NotebookStatus.ACTIVE &&
        !hasFetchedTitle.current
      ) {
        // Update the latest title from DB if it is ACTIVE notebook
        hasFetchedTitle.current = true
        const latestNotebook = await getNotebookTitleById(notebook.id)
        if (latestNotebook) {
          setTitle(latestNotebook.title)
        }
      } else if (
        notebook.status === NotebookStatus.INITIALIZED &&
        !hasRunRef.current &&
        !isLoading
      ) {
        hasRunRef.current = true
        // Double check to avoid duplicate generation
        const latestNotebook = await getNotebookStatusById(notebook.id)

        // If status is still INITIALIZED in DB, safe to generate
        // Since the first step is to set status to INACTIVE, no longer INITIALIZED
        if (
          latestNotebook &&
          latestNotebook.status === NotebookStatus.INITIALIZED
        ) {
          await updateNotebook(notebook.id, {
            status: NotebookStatus.INACTIVE,
          })
          append({ role: 'user', content: 'Generate a notebook' })
        } else if (
          // If status is ACTIVE in DB, it means it generated before
          // So we can directly set the generated content to the editor
          latestNotebook &&
          latestNotebook.status === NotebookStatus.ACTIVE
        ) {
          setGeneratedContent(
            getInitialContent(latestNotebook as GetInitialContentType)
          )
          setShowTiptapEditor(true)
          setIsEditable(true)
        }
      }
    }

    initializeNotebook()
  }, [notebook, isLoading, append])

  useEffect(() => {
    setIsMounted(true)
  }, [])

  // This is the hacky way to get the new notebook content
  // After we change something in notebook and save to DB, back to main page and revisit the page
  // the content is still the old one, somehow it is cached
  // I have tried setting force-dynamic and revalidate on the page but no luck
  // TODO: Find a better way to solve this
  useEffect(() => {
    console.log('Hit getNewNotebook useEffect')
    const getNewNotebook = async () => {
      const newNotebook = await getNotebookById(notebook.id)
      if (
        newNotebook &&
        newNotebook?.updated_at !== notebook.updated_at &&
        newNotebook.id === notebook.id &&
        newNotebook.status === NotebookStatus.ACTIVE
      ) {
        setGeneratedContent(getInitialContent(newNotebook as Notebook))
      }
    }
    // If notebook.updated_at is within 5 seconds, do not run getNewNotebook
    if (
      notebook.updated_at &&
      Date.now() - new Date(notebook.updated_at).getTime() < 5000
    ) {
      return
    }
    // Prevent infinite loop
    if (!hasFetchedNewNotebook.current) {
      hasFetchedNewNotebook.current = true
      console.log('Fetching new notebook')
      getNewNotebook()
    }
  }, [notebook.id])

  const debouncedUpdateNotebook = useCallback(
    debounce(async (partialNotebook: Partial<Notebook>) => {
      setSaveStatus(NotebookSaveStatus.UNSAVED)
      try {
        await updateNotebook(notebook.id, partialNotebook)
        setSaveStatus(NotebookSaveStatus.SAVED)
      } catch (error) {
        console.error('Failed to update notebook title:', error)
        setSaveStatus(NotebookSaveStatus.ERROR)
      }
    }, 3000),
    [notebook.id]
  )

  useEffect(() => {
    return () => {
      debouncedUpdateNotebook.cancel()
    }
  }, [debouncedUpdateNotebook])

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTitle = e.target.value
    if (newTitle.length >= 100) {
      toast.error('Title is limited to 100 characters')
      return
    }
    setTitle(newTitle)
    setSaveStatus(NotebookSaveStatus.UNSAVED)
    debouncedUpdateNotebook({ title: newTitle })
  }

  if (!isMounted) {
    return <LoadingSkeleton />
  }

  const lastAssistantMessage =
    extractTextContent(
      messages.filter(message => message.role === 'assistant').pop() ||
        ({} as Msg)
    ) || ''

  return (
    <>
      <EditorHeader
        title={title}
        handleTitleChange={handleTitleChange}
        saveStatus={saveStatus}
        conversationId={conversationId}
        editor={editor}
        notebook={notebook}
      />
      <NotebookEditorContent
        editor={editor}
        showTiptapEditor={showTiptapEditor}
        lastAssistantMessage={lastAssistantMessage}
        isMounted={isMounted}
      />
      <ScrollToTopButton />
    </>
  )
}

export const EditorHeader: React.FC<{
  title: string
  handleTitleChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  saveStatus: string
  conversationId: string
  editor: Editor | null
  notebook: Notebook
}> = ({
  title,
  handleTitleChange,
  saveStatus,
  conversationId,
  editor,
  notebook,
}) => (
  <div className="flex flex-row justify-between items-center max-w-screen-2xl sticky top-0 py-2 bg-white shadow z-0">
    <div className="text-xl font-semibold p-4 flex-grow mr-4 max-w-[40%]">
      <input
        type="text"
        value={title}
        onChange={handleTitleChange}
        className="w-full bg-transparent border-b border-gray-300 hover:border-gray-500 focus:outline-none focus:border-blue-500 truncate"
        placeholder="Enter title (max 100 characters)"
        title={title}
      />
    </div>
    <div className="flex flex-row flex-shrink-0 space-x-2 items-center">
      <div
        className={`rounded-lg px-3 py-2 text-sm ${
          saveStatus === NotebookSaveStatus.UNSAVED
            ? 'bg-red-100 text-red-700'
            : 'bg-stone-100 text-gray-700'
        } transition-colors duration-200 h-9 flex items-center justify-center w-24`}
      >
        {saveStatus}
      </div>
      <Link
        href={`/conversations/${conversationId}`}
        className="hover:bg-blue-200 rounded-lg bg-blue-100 px-3 py-2 text-sm text-blue-700 whitespace-nowrap transition-colors duration-200 h-9 flex items-center"
      >
        Back to main page
      </Link>
      <Button
        onClick={() => copyEditorContentAsMarkdown(editor)}
        className="hover:bg-green-300 rounded-lg bg-green-200 px-3 py-2 text-sm text-green-700 transition-colors duration-200 h-9 flex items-center"
      >
        Copy to clipboard
      </Button>
      <NotebookMetadataSheet notebook={notebook} />
    </div>
  </div>
)

const NotebookMetadataSheet: React.FC<{ notebook: Notebook }> = ({
  notebook,
}) => {
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(
      () => {
        toast.success('Copied to clipboard!')
      },
      err => {
        console.error('Could not copy text: ', err)
        toast.error('Failed to copy to clipboard')
      }
    )
  }

  return (
    <Sheet>
      <SheetTrigger asChild>
        <button className="hover:bg-gray-200 rounded-lg p-1 text-gray-700 transition-colors duration-200">
          <MoreVertical size={24} />
        </button>
      </SheetTrigger>
      <SheetContent>
        <SheetHeader>
          <SheetTitle>Notebook Metadata</SheetTitle>
        </SheetHeader>
        <div className="mt-4 space-y-4">
          <div>
            <h3 className="font-semibold flex items-center justify-between">
              Generation Prompt
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(notebook.generation_input || '')}
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy
              </Button>
            </h3>
            <Textarea
              readOnly
              value={notebook.generation_input || 'N/A'}
              className="w-full mt-2 p-2 text-sm text-gray-400 border rounded-md resize-none"
              rows={15}
            />
          </div>
          <div>
            <h3 className="font-semibold">Created At:</h3>
            <p className="text-sm text-gray-600">
              {notebook.created_at
                ? format(new Date(notebook.created_at), 'PPpp')
                : 'N/A'}
            </p>
          </div>
          <div>
            <h3 className="font-semibold">Status:</h3>
            <p className="text-sm text-gray-600">{notebook.status}</p>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  )
}

const NotebookEditorContent: React.FC<{
  editor: Editor | null
  showTiptapEditor: boolean
  lastAssistantMessage: string
  isMounted: boolean
}> = ({ editor, showTiptapEditor, lastAssistantMessage, isMounted }) => (
  <div className="relative min-h-[500px] w-full max-w-screen-2xl border-stone-200 bg-white p-8 sm:p-12 mb-[calc(20vh)] sm:mb-0 sm:rounded-lg sm:border sm:shadow-lg overflow-auto">
    {showTiptapEditor ? (
      <>
        {isMounted && <EditorBubbleMenu editor={editor} />}
        {isMounted && <TiptapEditorContent editor={editor} />}
      </>
    ) : (
      <Textarea
        value={lastAssistantMessage}
        readOnly
        className="w-full h-full text-lg bg-gray-50 text-gray-800"
        placeholder="Starting to generate content...hold on"
        rows={20}
      />
    )}
  </div>
)

const ScrollToTopButton: React.FC = () => {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  return (
    <div className="fixed bottom-4 right-4">
      <button
        onClick={scrollToTop}
        className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-full shadow-lg transition-colors duration-200"
      >
        ↑
      </button>
    </div>
  )
}

const useNotebookEditor = (
  notebook: Notebook,
  setSaveStatus: (status: string) => void,
  debouncedUpdateNotebook: (partialNotebook: Partial<Notebook>) => void
) => {
  const [isEditable, setIsEditable] = useState<boolean>(
    notebook.status === NotebookStatus.ACTIVE
  )
  const [generatedContent, setGeneratedContent] = useState<any>(null)

  const editor = useEditor({
    extensions: TiptapExtensions,
    editorProps: TiptapEditorProps,
    editable: isEditable,
    content: getInitialContent(notebook),
    onUpdate: ({ editor }) => {
      setSaveStatus(NotebookSaveStatus.UNSAVED)
      // Clean JSON to deal with mermaidComponent node
      const cleanedJSON = cleanJSON(editor.getJSON())
      debouncedUpdateNotebook({ content: JSON.stringify(cleanedJSON) })
    },
    autofocus: 'start',
    immediatelyRender: false,
  })

  useEffect(() => {
    if (editor && generatedContent) {
      editor.commands.setContent(generatedContent)
    }
  }, [generatedContent, editor])

  return {
    editor,
    isEditable,
    generatedContent,
    setGeneratedContent,
    setIsEditable,
  }
}

const cleanJSON = (json: JSONContent): JSONContent => {
  if (!json.content || !Array.isArray(json.content)) return json

  return {
    ...json,
    content: json.content.map((node: JSONContent) => {
      // Skip non-mermaidComponent node
      if (node.type !== 'mermaidComponent') return node

      // Clean mermaidComponent node
      const { attrs, content } = node
      // If code is null, but content is not empty, set code to the text of the first child
      // Remove content to save space
      if (attrs?.code === null && content?.[0]?.content?.[0]?.text) {
        const { content: _, ...restNode } = node
        return {
          ...restNode,
          attrs: { ...attrs, code: content[0].content[0].text },
        }
      }
      return node
    }),
  }
}

const copyEditorContentAsMarkdown = (editor: Editor | null) => {
  if (editor) {
    const finalMarkdown = convertEditorContentToMarkdown(editor)
    copyToClipboard(finalMarkdown)
  } else {
    toast.error('Editor content is not available')
  }
}
