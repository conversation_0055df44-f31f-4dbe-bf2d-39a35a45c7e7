/**
 * Unit tests for useAiGeneration hook - focusing on duplicate generation prevention
 */

import React, { StrictMode } from 'react'
import { render, act, waitFor } from '@testing-library/react'
import { useAiGeneration } from '@/app/(conv)/dragTree/[dragTreeId]/hooks/useAiGeneration'
import { useTabStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useTabStore'

// Mock dependencies
jest.mock('@ai-sdk/react', () => ({
  useChat: jest.fn(() => ({
    messages: [],
    sendMessage: jest.fn(),
    status: 'idle',
  })),
}))

jest.mock('next-auth/react', () => ({
  useSession: jest.fn(() => ({
    data: { user: { id: 'test-user-id' } },
  })),
}))

jest.mock('@/app/stores/ui_store', () => ({
  useUIStore: jest.fn(() => true),
}))

jest.mock('@/app/(conv)/dragTree/[dragTreeId]/stores/useAiPaneStore', () => ({
  useAiPaneStore: jest.fn(selector => {
    const state = {
      settings: { context: [], promptTemplate: 'default' },
    }
    return selector ? selector(state) : state
  }),
}))

jest.mock('@/app/stores/dragtree_store', () => ({
  useDragTreeStore: jest.fn(selector => {
    const state = {
      nodeContent: new Map(),
    }
    return selector ? selector(state) : state
  }),
}))

// Mock fetch globally
global.fetch = jest.fn()

// Test component that uses the hook
const TestComponent: React.FC<{
  tabId: string
  dragTreeId: string
  prompt: string
  model: string
  contextIds: string[]
}> = ({ tabId, dragTreeId, prompt, model, contextIds }) => {
  const { markdown, isStreaming, isCompleted } = useAiGeneration({
    prompt,
    model,
    contextIds,
    tabId,
    dragTreeId,
  })

  return (
    <div data-testid="test-component">
      <div data-testid="markdown">{markdown}</div>
      <div data-testid="streaming">{isStreaming ? 'true' : 'false'}</div>
      <div data-testid="completed">{isCompleted ? 'true' : 'false'}</div>
    </div>
  )
}

describe('useAiGeneration hook', () => {
  beforeEach(() => {
    jest.clearAllMocks()

    // Reset tab store to clean state
    useTabStore.getState().resetTabs()

    // Mock fetch to resolve successfully
    ;(global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      headers: new Map([['X-Generation-Id', 'test-generation-id']]),
      body: new ReadableStream(),
    })
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  describe('Duplicate Generation Prevention', () => {
    it('should only call fetch once when component mounts twice in StrictMode', async () => {
      const sendMessageMock = jest.fn()
      const useChat = require('@ai-sdk/react').useChat as jest.Mock

      useChat.mockReturnValue({
        messages: [],
        sendMessage: sendMessageMock,
        status: 'idle',
      })

      // Add a tab to the store that doesn't have generationStarted flag
      const tabId = useTabStore.getState().addAiTab({
        type: 'generate',
        model: 'gpt-4',
        prompt: 'Test prompt',
        contextIds: [],
        settings: {},
      })

      // Render component in StrictMode (which causes double mounting in development)
      const { rerender } = render(
        <StrictMode>
          <TestComponent
            tabId={tabId}
            dragTreeId="test-drag-tree"
            prompt="Test prompt"
            model="gpt-4"
            contextIds={[]}
          />
        </StrictMode>
      )

      // Wait for any effects to complete
      await waitFor(() => {
        // sendMessage should only be called once despite StrictMode double mounting
        expect(sendMessageMock).toHaveBeenCalledTimes(1)
      })

      // Verify the generationStarted flag was set in the tab store
      const tab = useTabStore.getState().tabs.find(t => t.id === tabId)
      expect(tab?.aiPaneData?.generationStarted).toBe(true)

      // Remount the component again to simulate navigation back
      rerender(
        <StrictMode>
          <TestComponent
            tabId={tabId}
            dragTreeId="test-drag-tree"
            prompt="Test prompt"
            model="gpt-4"
            contextIds={[]}
          />
        </StrictMode>
      )

      // Wait for effects and verify sendMessage is still only called once
      await waitFor(() => {
        expect(sendMessageMock).toHaveBeenCalledTimes(1)
      })
    })

    it('should not start generation if generationStarted flag is already true', async () => {
      const appendMock = jest.fn()
      const useChat = require('@ai-sdk/react').useChat as jest.Mock

      useChat.mockReturnValue({
        messages: [],
        append: appendMock,
        isLoading: false,
      })

      // Add a tab with generationStarted flag already set
      const tabId = useTabStore.getState().addAiTab({
        type: 'generate',
        model: 'gpt-4',
        prompt: 'Test prompt',
        contextIds: [],
        settings: {},
      })

      // Manually set the generationStarted flag
      useTabStore.getState().updateTabAiPaneData(tabId, {
        generationStarted: true,
      })

      render(
        <TestComponent
          tabId={tabId}
          dragTreeId="test-drag-tree"
          prompt="Test prompt"
          model="gpt-4"
          contextIds={[]}
        />
      )

      // Wait for effects and verify append is never called
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 100))
      })

      expect(appendMock).not.toHaveBeenCalled()
    })

    it('should not start generation if messages already exist', async () => {
      const appendMock = jest.fn()
      const useChat = require('@ai-sdk/react').useChat as jest.Mock

      useChat.mockReturnValue({
        messages: [{ role: 'user', content: 'Previous message' }],
        append: appendMock,
        isLoading: false,
      })

      const tabId = useTabStore.getState().addAiTab({
        type: 'generate',
        model: 'gpt-4',
        prompt: 'Test prompt',
        contextIds: [],
        settings: {},
      })

      render(
        <TestComponent
          tabId={tabId}
          dragTreeId="test-drag-tree"
          prompt="Test prompt"
          model="gpt-4"
          contextIds={[]}
        />
      )

      // Wait for effects and verify append is never called when messages exist
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 100))
      })

      expect(appendMock).not.toHaveBeenCalled()
    })
  })
})
