-- CreateEnum
CREATE TYPE "AIUsageType" AS ENUM ('SCREENING_PROBLEM_ANALYSIS', 'SCREENING_REPHRASE', 'GENERATE_QUESTION', 'NODE_QUICK_RESEARCH');

-- CreateTable
CREATE TABLE "ai_usage" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "entity_type" TEXT NOT NULL,
    "entity_id" TEXT NOT NULL,
    "ai_provider" TEXT NOT NULL,
    "model_name" TEXT NOT NULL,
    "usage_type" "AIUsageType" NOT NULL,
    "input_prompt" TEXT NOT NULL,
    "messages" JSONB NOT NULL DEFAULT '[]',
    "metadata" JSONB NOT NULL DEFAULT '{}',
    "config" JSONB NOT NULL DEFAULT '{}',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ai_usage_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ai_usage_id_idx" ON "ai_usage" USING HASH ("id");

-- CreateIndex
CREATE INDEX "ai_usage_user_id_idx" ON "ai_usage" USING HASH ("user_id");

-- CreateIndex
CREATE INDEX "ai_usage_entity_type_idx" ON "ai_usage" USING HASH ("entity_type");

-- CreateIndex
CREATE INDEX "ai_usage_entity_id_idx" ON "ai_usage" USING HASH ("entity_id");

-- CreateIndex
CREATE INDEX "ai_usage_ai_provider_idx" ON "ai_usage" USING HASH ("ai_provider");

-- CreateIndex
CREATE INDEX "ai_usage_model_name_idx" ON "ai_usage" USING HASH ("model_name");

-- CreateIndex
CREATE INDEX "ai_usage_usage_type_idx" ON "ai_usage" USING HASH ("usage_type");

-- CreateIndex
CREATE INDEX "ai_usage_created_at_idx" ON "ai_usage"("created_at");

-- AddForeignKey
ALTER TABLE "ai_usage" ADD CONSTRAINT "ai_usage_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
