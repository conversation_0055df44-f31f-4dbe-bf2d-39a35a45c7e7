/**
 * Tree Utilities
 *
 * Collection of pure utility functions for tree manipulation and traversal.
 * These functions implement the core tree operations without side effects.
 *
 * Key Functions:
 * - Tree traversal and node finding
 * - Node addition, deletion, and reordering
 * - Interest tracking and collection
 * - Tree validation and constraint checking
 *
 * Design Principles:
 * - Pure functions without side effects
 * - Immutable operations that return new tree structures
 * - Performance optimized for large trees
 * - Type-safe operations with proper validation
 */

import { TreeNode } from '@/app/types'
import { TreeNodeType } from '@/app/(conv)/dragTree/[dragTreeId]/constants/node-types'

/**
 * Calculate the depth/level of a node in the tree
 * Root node is level 0, direct children are level 1, etc.
 *
 * Used for:
 * - UI indentation calculations
 * - Tree structure validation
 * - Depth-based operations
 */
export function calculateNodeLevel(
  tree: TreeNode,
  targetNodeId: string
): number {
  function findNodeLevel(
    node: TreeNode,
    targetId: string,
    currentLevel: number = 0
  ): number | null {
    if (node.id === targetId) {
      return currentLevel
    }

    for (const child of node.children) {
      const foundLevel = findNodeLevel(child, targetId, currentLevel + 1)
      if (foundLevel !== null) {
        return foundLevel
      }
    }

    return null
  }

  const level = findNodeLevel(tree, targetNodeId)
  return level !== null ? level : -1
}

/**
 * Find a node by ID in the tree structure
 */
export function findNodeById(
  tree: TreeNode | null,
  nodeId: string
): TreeNode | null {
  if (!tree) return null

  if (tree.id === nodeId) {
    return tree
  }

  for (const child of tree.children) {
    const found = findNodeById(child, nodeId)
    if (found) {
      return found
    }
  }

  return null
}

/**
 * Find the parent node of a target node
 */
export function findParentNode(
  tree: TreeNode,
  targetId: string
): TreeNode | null {
  for (const child of tree.children) {
    if (child.id === targetId) {
      return tree
    }
    const foundParent = findParentNode(child, targetId)
    if (foundParent) return foundParent
  }
  return null
}

/**
 * Collect all node IDs from a tree or subtree (including the root)
 */
export function collectAllNodeIds(node: TreeNode): string[] {
  const nodeIds: string[] = [node.id]
  node.children.forEach(child => {
    nodeIds.push(...collectAllNodeIds(child))
  })
  return nodeIds
}

/**
 * Build a flat map of all nodes in the tree for O(1) lookups
 */
export function buildNodeMap(tree: TreeNode | null): Map<string, TreeNode> {
  const nodeMap = new Map<string, TreeNode>()

  if (tree) {
    const addToMap = (node: TreeNode): void => {
      nodeMap.set(node.id, node)
      node.children.forEach(addToMap)
    }
    addToMap(tree)
  }

  return nodeMap
}

/**
 * Collect all nodes that are marked as interested
 */
export function collectInterestedNodes(tree: TreeNode | null): TreeNode[] {
  if (!tree) return []

  const interestedNodes: TreeNode[] = []

  function collectRecursive(node: TreeNode): void {
    if (node.isInterestedIn) {
      interestedNodes.push(node)
    }
    node.children.forEach(collectRecursive)
  }

  collectRecursive(tree)
  return interestedNodes
}

/**
 * Reset all interest markers in the tree
 */
export function resetAllInterestInTree(tree: TreeNode): TreeNode {
  const clonedTree = structuredClone(tree) as TreeNode

  function resetRecursive(node: TreeNode): void {
    node.isInterestedIn = false
    node.children.forEach(resetRecursive)
  }

  resetRecursive(clonedTree)
  return clonedTree
}

/**
 * Add a new node to a parent in the tree structure
 */
export function addNodeToTree(
  tree: TreeNode,
  parentId: string,
  newNode: TreeNode
): TreeNode | null {
  const clonedTree = structuredClone(tree) as TreeNode

  function addToClone(node: TreeNode): boolean {
    if (node.id === parentId) {
      node.children.push(newNode)
      return true
    }
    return node.children.some(child => addToClone(child))
  }

  return addToClone(clonedTree) ? clonedTree : null
}

/**
 * Delete a node from the tree structure
 */
export function deleteNodeFromTree(
  tree: TreeNode,
  targetId: string
): TreeNode | null {
  const clonedTree = structuredClone(tree) as TreeNode

  function deleteRecursive(node: TreeNode, nodeToDeleteId: string): boolean {
    const index = node.children.findIndex(child => child.id === nodeToDeleteId)
    if (index !== -1) {
      node.children.splice(index, 1)
      return true
    }
    return node.children.some(child => deleteRecursive(child, nodeToDeleteId))
  }

  return deleteRecursive(clonedTree, targetId) ? clonedTree : null
}

/**
 * Edit a node's label in the tree structure
 */
export function editNodeInTree(
  tree: TreeNode,
  nodeId: string,
  newLabel: string
): TreeNode | null {
  const clonedTree = structuredClone(tree) as TreeNode

  function editInClone(node: TreeNode): boolean {
    if (node.id === nodeId) {
      node.label = newLabel
      return true
    }
    return node.children.some(child => editInClone(child))
  }

  return editInClone(clonedTree) ? clonedTree : null
}

/**
 * Reorder children within a parent node
 */
export function reorderNodeInTree(
  tree: TreeNode,
  parentId: string,
  oldIndex: number,
  newIndex: number
): TreeNode | null {
  const clonedTree = structuredClone(tree) as TreeNode

  function reorderInClone(node: TreeNode): boolean {
    if (node.id === parentId) {
      const len = node.children.length
      if (oldIndex < 0 || newIndex < 0 || oldIndex >= len || newIndex >= len) {
        return false
      }
      const [movedNode] = node.children.splice(oldIndex, 1)
      if (!movedNode) return false
      node.children.splice(newIndex, 0, movedNode)
      return true
    }
    return node.children.some(child => reorderInClone(child))
  }

  return reorderInClone(clonedTree) ? clonedTree : null
}

/**
 * Toggle interest status of a node
 */
export function toggleNodeInterest(
  tree: TreeNode,
  nodeId: string
): TreeNode | null {
  const clonedTree = structuredClone(tree) as TreeNode

  function markInClone(node: TreeNode): boolean {
    if (node.id === nodeId) {
      node.isInterestedIn = !node.isInterestedIn
      return true
    }
    return node.children.some(child => markInClone(child))
  }

  return markInClone(clonedTree) ? clonedTree : null
}

/**
 * Add multiple new nodes to a parent (for similar questions/categories)
 */
export function addMultipleNodesToTree(
  tree: TreeNode,
  parentId: string,
  newNodes: TreeNode[]
): TreeNode | null {
  const clonedTree = structuredClone(tree) as TreeNode

  function addNodesToClone(node: TreeNode): boolean {
    if (node.id === parentId) {
      node.children.push(...newNodes)
      return true
    }
    return node.children.some(child => addNodesToClone(child))
  }

  return addNodesToClone(clonedTree) ? clonedTree : null
}

/**
 * Validate tree constraints (e.g., categories must have at least one child)
 */
export function validateTreeConstraints(
  tree: TreeNode,
  nodeId: string,
  operation: 'delete' | 'edit' | 'add'
): { valid: boolean; message?: string } {
  // For delete operations, check if deleting the last child under a category
  if (operation === 'delete') {
    const parentNode = findParentNode(tree, nodeId)
    if (
      parentNode &&
      parentNode.type === TreeNodeType.CATEGORY &&
      parentNode.children.length === 1
    ) {
      return {
        valid: false,
        message:
          'Cannot delete the last item under a category. Categories must have at least one question.',
      }
    }

    // Prevent deleting the root node
    if (nodeId === tree.id) {
      return {
        valid: false,
        message: 'Cannot delete the root node',
      }
    }
  }

  return { valid: true }
}

/**
 * Get the path from root to a specific node
 */
const getNodePath = (root: TreeNode, targetId: string): TreeNode[] | null => {
  if (root.id === targetId) {
    return [root]
  }

  for (const child of root.children) {
    const childPath = getNodePath(child, targetId)
    if (childPath) {
      return [root, ...childPath]
    }
  }

  return null
}

/**
 * Get formatted path string for a node
 */
export const getFormattedPath = (root: TreeNode, targetId: string): string => {
  const path = getNodePath(root, targetId)
  if (!path || path.length === 0) {
    return 'Unknown Node'
  }

  // Skip the root node in the path display and join with " > "
  return (
    path
      .slice(1)
      .map(node => node.label)
      .join(' > ') || path[0].label
  )
}
