// For loading subtree

import { NextResponse } from 'next/server'
import prisma from '@/app/libs/prismadb'
import { SubtreeStatus } from '@prisma/client'

import { UserStatus } from '@prisma/client'

export async function POST(req: Request) {
  try {
    const { issueTreeId, currentUser } = await req.json()
    console.log('api/subtree/load:', issueTreeId, currentUser)

    if (!currentUser?.id || !currentUser?.email) {
      return new NextResponse('Unauthorized', { status: 400 })
    }

    // Block them from accessing API if they are not active
    if (currentUser?.status !== UserStatus.ACTIVE) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    // const subtrees = await getSubtreesById(issueTreeId);
    const subtrees = await prisma.subtree.findMany({
      where: {
        issue_tree_id: issueTreeId,
        status: SubtreeStatus.ACTIVE,
      },
      select: {
        id: true,
        selected_node_id: true,
        nodes: true,
        edges: true,
        generation_output: true,
      },
    })

    if (subtrees === null) {
      // Handle null case
      return NextResponse.json({ error: 'subtrees not found' })
    }
    return NextResponse.json(subtrees)
  } catch {
    return new NextResponse('Internal Error', { status: 500 })
  }
}
