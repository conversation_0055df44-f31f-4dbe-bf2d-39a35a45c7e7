# Debug Components

## Overview

Development and testing components for the dragTree system that provide utilities for debugging, performance monitoring, and data validation during development.

## Components

### **Core Debug Tools**

#### **DebugTreeData.tsx**

- **Purpose**: Load and inspect dragTree database content
- **Features**: View raw database structure, validate data integrity
- **Usage**: Development debugging of data loading issues

#### **RepairTreeData.tsx**

- **Purpose**: Fix missing or corrupted tree nodes
- **Features**: Automatic node repair, data consistency checks
- **Usage**: Repair tree structure after data migrations or bugs

#### **QuickRepair.tsx**

- **Purpose**: Fast repair utilities for common issues
- **Features**: One-click fixes for missing nodes
- **Usage**: Quick development fixes during testing

### **Testing Components**

#### **SimpleStoreTest.tsx**

- **Purpose**: Basic Zustand store functionality testing
- **Features**: Load sample data, clear store, test state management
- **Usage**: Validate store operations and state updates

#### **SimpleTreeTest.tsx**

- **Purpose**: Basic tree operations testing
- **Features**: Test tree manipulation, node operations
- **Usage**: Validate core tree functionality

#### **TestAddNode.tsx**

- **Purpose**: Test node creation functionality
- **Features**: Add categories and questions programmatically
- **Usage**: Debug node creation workflows

#### **TestDeleteNode.tsx**

- **Purpose**: Test node deletion functionality
- **Features**: Delete nodes with validation, test cascading deletes
- **Usage**: Validate deletion logic and cleanup

#### **TestNodeMetadata.tsx**

- **Purpose**: Test node metadata and hierarchy operations
- **Features**: Node level calculation, metadata inspection
- **Usage**: Debug node relationships and hierarchy

#### **TestUIImprovements.tsx**

- **Purpose**: Test UI enhancement patterns
- **Features**: Analyze node structure for UI improvements
- **Usage**: Development of UI optimization features

### **Performance Tools**

#### **PerformanceMonitor.tsx**

- **Purpose**: Real-time performance tracking for dragTree operations
- **Features**:
  - Operation timing (rebuild, add, delete, edit operations)
  - Memory usage monitoring
  - Render performance tracking
  - Color-coded performance indicators
- **Usage**: Monitor performance during development and optimization

#### **PerformanceTest.tsx**

- **Purpose**: Structured performance testing suite
- **Features**:
  - Rebuild performance testing with multiple iterations
  - Tree metrics calculation (nodes, depth, categories, questions)
  - Performance comparison across different tree sizes
- **Usage**: Benchmark performance improvements and regressions

### **Data Inspection**

#### **ViewDatabaseMetadata.tsx**

- **Purpose**: Inspect dragTree database metadata
- **Features**:
  - View node content metadata
  - Inspect research data and citations
  - Debug content versioning
- **Usage**: Debug database content and metadata issues

#### **ZustandConcurrencyTest.tsx**

- **Purpose**: Test concurrent store operations
- **Features**: Test concurrent updates, race condition detection
- **Usage**: Validate store behavior under concurrent load

### **UI Enhancement Testing**

#### **EnhancedTreeTest.tsx**

- **Purpose**: Test enhanced tree UI features
- **Features**: Advanced tree interaction testing
- **Usage**: Validate enhanced UI components

## Development Workflow

### **1. Basic Debugging**

```tsx
// Add to any dragTree page for debugging
import {
  DebugTreeData,
  SimpleStoreTest,
} from '@/app/(conv)/dragTree/[dragTreeId]/components/debug'

// In development mode
{
  process.env.NODE_ENV === 'development' && (
    <div className="debug-panel">
      <DebugTreeData />
      <SimpleStoreTest />
    </div>
  )
}
```

### **2. Performance Testing**

```tsx
import { PerformanceMonitor, PerformanceTest } from '@/app/(conv)/dragTree/[dragTreeId]/components/debug'

// Performance monitoring
<PerformanceMonitor />

// Performance testing
<PerformanceTest />
```

### **3. Data Repair**

```tsx
import { RepairTreeData, QuickRepair } from '@/app/(conv)/dragTree/[dragTreeId]/components/debug'

// For data integrity issues
<RepairTreeData />
<QuickRepair />
```

## Integration Points

### **Zustand Store Integration**

- All debug components work with the main `useDragTreeStore`
- Test components validate store operations
- Performance tools monitor store performance

### **Database Integration**

- Components interact with the three-table dragTree schema
- Metadata inspection tools work with `DragTreeNodeContent`
- Repair tools fix database inconsistencies

### **Development Environment**

- Components are development-only (not included in production builds)
- Debug panels can be toggled via environment flags
- Performance monitoring is disabled in production

## Best Practices

### **1. Use During Development**

- Include debug components during feature development
- Remove before production deployment
- Use environment flags to control visibility

### **2. Performance Monitoring**

- Enable performance monitoring for optimization work
- Use structured testing for performance comparisons
- Monitor memory usage during long operations

### **3. Data Validation**

- Use repair tools after data migrations
- Validate tree structure after major changes
- Test concurrent operations under development load

## Future Enhancements

1. **Automated Testing**: Integration with Jest/testing-library
2. **Visual Performance Charts**: Graphs and metrics visualization
3. **Export Debug Reports**: Export debugging data for analysis
4. **Remote Debugging**: Debug production issues safely
5. **A/B Testing Tools**: Compare performance between different implementations
