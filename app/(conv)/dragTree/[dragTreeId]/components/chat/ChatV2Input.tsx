'use client'

import React from 'react'
import {
  PromptInput,
  PromptInputTextarea,
  PromptInputSubmit,
} from '@/components/ai-elements/prompt-input'
import { CONTEXT_CONFIG } from '@/app/api/aipane/assistant/config'

type ChatV2InputProps = {
  input: string
  onInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void
  onSubmit: (e: React.FormEvent) => void
  disabled?: boolean
  isLoading?: boolean
  placeholder?: string
  className?: string
}

export default function ChatV2Input({
  input,
  onInputChange,
  onSubmit,
  disabled = false,
  isLoading = false,
  placeholder = 'Message AI Assistant...',
  className = '',
}: ChatV2InputProps) {
  const isOverLimit = input.length > CONTEXT_CONFIG.MAX_INPUT_CHARS
  const canSubmit =
    input.trim().length > 0 && !isOverLimit && !disabled && !isLoading

  return (
    <div className={`border-t border-gray-200 bg-white ${className}`}>
      <div className="max-w-4xl mx-auto p-4">
        <PromptInput onSubmit={onSubmit} className="flex items-end">
          <PromptInputTextarea
            value={input}
            onChange={onInputChange}
            placeholder={placeholder}
            disabled={disabled}
            className={`min-h-[44px] max-h-32 resize-none flex-1 ${
              isOverLimit ? 'text-red-600' : ''
            }`}
          />
          <PromptInputSubmit
            disabled={!canSubmit}
            className="ml-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300"
          >
            {isLoading ? 'Sending...' : 'Send'}
          </PromptInputSubmit>
        </PromptInput>

        {/* Character counter and validation */}
        <div className="flex justify-between items-center mt-2 text-xs text-gray-500">
          <div className="flex items-center gap-4">
            {isOverLimit ? (
              <span className="text-red-600 font-medium">
                ⚠️ Message too long. Limit: {CONTEXT_CONFIG.MAX_INPUT_CHARS}{' '}
                chars.
              </span>
            ) : (
              <span>Press Enter to send, Shift+Enter for new line</span>
            )}
          </div>
          <span className={isOverLimit ? 'text-red-600 font-semibold' : ''}>
            {input.length}/{CONTEXT_CONFIG.MAX_INPUT_CHARS}
          </span>
        </div>
      </div>
    </div>
  )
}
