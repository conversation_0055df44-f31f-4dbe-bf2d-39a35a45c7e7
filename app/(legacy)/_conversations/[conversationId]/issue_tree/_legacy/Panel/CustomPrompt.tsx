import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import PromptCard from './PromptCard'
import { usePanelStore } from '@/app/stores/_legacy/panel_store'
import { promptSuffix } from './promptObjects'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from '@/components/ui/hover-card'
import Link from 'next/link'

const CustomPrompt: React.FC = () => {
  const [isExpanded, setIsExpanded] = useState(false)
  const [customPrompt, setCustomPrompt] = useState('')
  const panelStore = usePanelStore()

  return (
    <>
      {/* Controls Row */}
      <div>
        <HoverCard>
          <HoverCardTrigger asChild>
            <h1 className="text-2xl px-3 pt-2 font-bold hover:underline">
              Paste the prompts into your favorite AI models ℹ️
            </h1>
          </HoverCardTrigger>
          <HoverCardContent>
            <>
              <p className="font-bold underline">
                Why don&apos;t we run the prompts for you?
              </p>
              <li className="font-bold">🫡 Respect your privacy</li>
              <p>
                You can ask more detailed follow-up questions directly without
                our involvement.
              </p>
              <br />
              <li className="font-bold">💸 Save our tokens/$</li>
              <p>Take advantage of free and powerful models.</p>
              <br />
              <li className="font-bold">🧩 Grow with innovation</li>
              <p>
                Model capabilities are evolving rapidly. You should not be
                locked in with just one, and neither should we.
              </p>
              <br />
              <p className="font-bold underline">
                I have no idea about any of these models!
              </p>
              <p>Use our order: ChatGPT/Claude -{'>'} Gemini/Others</p>
            </>
          </HoverCardContent>
        </HoverCard>
      </div>
      <div className="flex justify-between items-center p-3">
        {/* Custom Prompt Button */}
        <Button
          className="bg-yellow-200 hover:bg-yellow-300 text-zinc-500"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          {isExpanded ? 'Close Custom Prompt' : 'OPTIONAL: Custom Prompt'}
        </Button>

        {/* Switch and Label */}
        <div className="flex items-center space-x-2">
          <Label>Copy & Open New Window</Label>
          <Switch
            checked={!panelStore.isOpenNewWindow}
            onCheckedChange={panelStore.switchIsOpenNewWindow}
          />
          <Label>Copy Prompt Only</Label>
        </div>
      </div>

      {/* Expanded Custom Prompt */}
      {isExpanded && (
        <div className="p-4 m-4">
          {/* Custom Prompt Guide Links */}
          <span className="pl-4 font-bold">Learn more: </span>
          <Link
            href="https://platform.openai.com/docs/guides/prompt-engineering"
            className="underline text-zinc-700 hover:text-zinc-800"
            target="_blank"
            rel="noopener noreferrer"
          >
            Open AI Guide
          </Link>
          <span> | </span>
          <Link
            href="https://www.promptingguide.ai/"
            className="underline text-zinc-700 hover:text-zinc-800"
            target="_blank"
            rel="noopener noreferrer"
          >
            Prompting Guide
          </Link>
          <Textarea
            className="bg-zinc-50 space-y-2 rounded-lg my-2 w-full text-lg"
            value={customPrompt}
            placeholder={`Optional: Enter your custom prompt here...

Tips: Give ideal output example and ask AI to follow it [one/few shot learning]`}
            onChange={e => setCustomPrompt(e.target.value)}
            rows={4}
          />
          <PromptCard
            key={'custom_prompt'}
            title={'Custom Prompt'}
            description={
              customPrompt === ''
                ? '<< Your Prompt >>' + promptSuffix
                : customPrompt + promptSuffix
            }
            tags={[]}
          />
        </div>
      )}
    </>
  )
}

export default CustomPrompt
