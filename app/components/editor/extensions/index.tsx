import StarterKit from '@tiptap/starter-kit'
import HorizontalRule from '@tiptap/extension-horizontal-rule'
import TiptapLink from '@tiptap/extension-link'
import Placeholder from '@tiptap/extension-placeholder'
import TiptapUnderline from '@tiptap/extension-underline'
import TextStyle from '@tiptap/extension-text-style'
import { Color } from '@tiptap/extension-color'
import TaskItem from '@tiptap/extension-task-item'
import TaskList from '@tiptap/extension-task-list'
import { Markdown } from 'tiptap-markdown'
import Highlight from '@tiptap/extension-highlight'
import CodeBlockLowlight from '@tiptap/extension-code-block-lowlight'
import Table from '@tiptap/extension-table'
import TableRow from '@tiptap/extension-table-row'
import TableHeader from '@tiptap/extension-table-header'
import TableCell from '@tiptap/extension-table-cell'
import { lowlight } from 'lowlight'
import css from 'highlight.js/lib/languages/css'
import js from 'highlight.js/lib/languages/javascript'
import ts from 'highlight.js/lib/languages/typescript'
import html from 'highlight.js/lib/languages/xml'
import python from 'highlight.js/lib/languages/python'
import json from 'highlight.js/lib/languages/json'
// Import highlight.js theme for syntax highlighting colors
import 'highlight.js/styles/atom-one-dark.css'
import './styles.scss'
import mermaidExtensionV2 from './mermaid-extension-v2'
import SuggestionExtension from './suggestion-extension'

import SlashCommand from './slash-command'
import { InputRule } from '@tiptap/core'
import { keymap } from 'prosemirror-keymap'
import { Extension } from '@tiptap/core'
import TrailingNode from './trailing-node'

lowlight.registerLanguage('html', html)
lowlight.registerLanguage('css', css)
lowlight.registerLanguage('js', js)
lowlight.registerLanguage('ts', ts)
lowlight.registerLanguage('python', python)
lowlight.registerLanguage('json', json)

const CodeBlockTabKey = Extension.create({
  addProseMirrorPlugins() {
    return [
      keymap({
        Tab: (state, dispatch) => {
          const { selection } = state
          const { parent } = selection.$from

          if (parent.type.name !== 'codeBlock') {
            return false
          }

          if (dispatch) {
            // If you prefer space:
            // const tabSize = 4;
            // const spaces = " ".repeat(tabSize);
            const spaces = '\t'
            const node = state.schema.text(spaces)
            dispatch(state.tr.replaceWith(selection.from, selection.to, node))
          }
          return true
        },
      }),
    ]
  },
})

export const TiptapExtensions = [
  StarterKit.configure({
    bulletList: {
      HTMLAttributes: {
        class: 'list-disc list-outside leading-3 -mt-2',
      },
    },
    orderedList: {
      HTMLAttributes: {
        class: 'list-decimal list-outside leading-3 -mt-2',
      },
    },
    listItem: {
      HTMLAttributes: {
        class: 'leading-normal -mb-2',
      },
    },
    blockquote: {
      HTMLAttributes: {
        class: 'border-l-4 border-stone-700',
      },
    },
    codeBlock: false,
    horizontalRule: false,
    dropcursor: {
      color: '#DBEAFE',
      width: 4,
    },
    gapcursor: false,
  }),
  // patch to fix horizontal rule bug: https://github.com/ueberdosis/tiptap/pull/3859#issuecomment-1536799740
  HorizontalRule.extend({
    addInputRules() {
      return [
        new InputRule({
          find: /^(?:---|—-|___\s|\*\*\*\s)$/,
          handler: ({ state, range }) => {
            const attributes = {}

            const { tr } = state
            const start = range.from
            let end = range.to

            tr.insert(start - 1, this.type.create(attributes)).delete(
              tr.mapping.map(start),
              tr.mapping.map(end)
            )
          },
        }),
      ]
    },
  }).configure({
    HTMLAttributes: {
      class: 'mt-4 mb-6 border-t border-stone-300',
    },
  }),
  TiptapLink.configure({
    HTMLAttributes: {
      class:
        'text-stone-400 underline underline-offset-[3px] hover:text-stone-600 transition-colors cursor-pointer',
    },
  }),
  Placeholder.configure({
    placeholder: ({ node }) => {
      if (node.type.name === 'heading') {
        return `Heading ${node.attrs.level}`
      }
      return "Press '/' for commands"
    },
    includeChildren: true,
  }),
  SlashCommand,
  TiptapUnderline,
  TextStyle,
  Color,
  Highlight.configure({
    multicolor: true,
  }),
  TaskList.configure({
    HTMLAttributes: {
      class: 'not-prose pl-2',
    },
  }),
  TaskItem.configure({
    HTMLAttributes: {
      class: 'flex items-start my-4',
    },
    nested: true,
  }),
  Markdown.configure({
    html: true,
    transformCopiedText: true,
    transformPastedText: true,
    linkify: false,
    breaks: false,
  }),
  CodeBlockLowlight.configure({
    lowlight,
    HTMLAttributes: {
      class: 'rounded-md p-4 font-mono pre-code-block',
    },
  }),
  CodeBlockTabKey,
  mermaidExtensionV2,
  Table.configure({
    resizable: true,
  }),
  TableRow,
  TableHeader.extend({
    addAttributes() {
      return {
        ...this.parent?.(),
        class: {
          default: 'table-header-cell',
        },
      }
    },
    content: 'paragraph',
    parseHTML() {
      return [{ tag: 'th' }]
    },
    renderHTML({ HTMLAttributes }) {
      return ['th', HTMLAttributes, 0]
    },
  }),
  TableCell.extend({
    addAttributes() {
      return {
        ...this.parent?.(),
        class: {
          default: 'table-cell',
        },
      }
    },
    content: 'paragraph',
    parseHTML() {
      return [{ tag: 'td' }]
    },
    renderHTML({ HTMLAttributes }) {
      return ['td', HTMLAttributes, 0]
    },
  }),
  SuggestionExtension,
  TrailingNode,
]
