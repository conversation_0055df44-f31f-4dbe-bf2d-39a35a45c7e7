import React from 'react'
import Drag<PERSON>reeClient from '@/app/(conv)/dragTree/[dragTreeId]/components/Client'
import { getInitialPageData } from '@/app/server-actions/drag-tree'
import { redirect } from 'next/navigation'

type DragTreePageProps = {
  params: Promise<{
    dragTreeId: string
  }>
}

export default async function DragTreePage({ params }: DragTreePageProps) {
  const { dragTreeId } = await params

  // Server-side pre-fetch: Consolidated single call that fetches the tree *and* other
  // boot-strap data in parallel (session, sidebar list, etc.)
  const result = await getInitialPageData(dragTreeId)

  // Handle errors at the server level
  if (!result.success) {
    console.error(`Failed to load drag tree ${dragTreeId}:`, result.error)
    // Redirect to screening if tree not found or error
    redirect('/screening')
  }

  // Include userMetadata inside the initial tree data so the client-side
  // loader can evaluate tutorial status without an additional request.
  const initialTreeDataWithMetadata = {
    ...result.data.dragTree,
    userMetadata: result.data.userMetadata,
    unreadCountsByNode: result.data.unreadCountsByNode, // Add unread counts for immediate badge display
  }

  return (
    <DragTreeClient
      dragTreeId={dragTreeId}
      initialTreeData={initialTreeDataWithMetadata}
      initialGenerations={result.data.generations}
    />
  )
}
