// // This is for user to update the converation, as of now, only title

// import { NextResponse } from "next/server";
// import prisma from "@/app/libs/prismadb";

// interface IParams {
//   conversationId?: string;
//   currentUser?: any;
// }

// // This is for user to update the converation
// export async function POST(request: Request) {
//   try {
//     const body = await request.json();
//     console.log("updateData", body);
//     const { conversationId, currentUser, ...updateData } = body;
//     // const currentUser = await getCurrentUser();
//     // const updateData = await request.json();

//     if (!currentUser?.id) {
//       return new NextResponse("Unauthorized", { status: 401 });
//     }

//     const updatedConveration = await prisma.conversation.update({
//       where: {
//         id: conversationId,
//       },
//       data: updateData,
//     });

//     // Remove config from the return conversation since it contains system prompt
//     const returnConversation = {
//       id: updatedConveration.id,
//       creator_id: updatedConveration.creator_id,
//       title: updatedConveration.title,
//       conversation_type: updatedConveration.conversation_type,
//       conversation_status: updatedConveration.conversation_status,
//       is_hidden: updatedConveration.is_hidden,
//       created_at: updatedConveration.created_at,
//       updated_at: updatedConveration.updated_at,
//       prompt_id: updatedConveration.prompt_id,
//     };

//     return NextResponse.json(returnConversation);
//   } catch (error) {
//     console.log(error, "ERROR_MESSAGES");
//     return new NextResponse("Error", { status: 500 });
//   }
// }
