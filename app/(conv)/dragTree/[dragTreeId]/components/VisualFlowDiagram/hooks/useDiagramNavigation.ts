'use client'

import { useEffect } from 'react'
import { useReactFlow, Node } from 'reactflow'
import { useNavigationStore } from '@/app/stores/navigation_store'

/**
 * A hook to manage navigation behaviors in the React Flow diagram,
 * such as focusing on nodes when they are selected from an external source.
 */
export const useDiagramNavigation = (nodes: Node[]) => {
  const reactFlowInstance = useReactFlow()
  const { targetNodeId, preventReactFlowFocus } = useNavigationStore()

  useEffect(() => {
    // Only navigate if not prevented (i.e., navigation didn't come from React Flow itself)
    if (targetNodeId && nodes.length > 0 && !preventReactFlowFocus) {
      const targetNode = nodes.find(node => node.id === targetNodeId)
      if (targetNode) {
        reactFlowInstance.fitView({
          nodes: [{ id: targetNodeId }],
          duration: 300,
          padding: 0.6, // Increased from 0.4 to show more surrounding context
          maxZoom: 0.4, // Reduced from 0.8 to zoom out more and show neighboring nodes
        })
      }
    }
  }, [targetNodeId, nodes, reactFlowInstance, preventReactFlowFocus])
}
