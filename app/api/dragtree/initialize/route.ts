import { NextRequest, NextResponse } from 'next/server'
import { createDragTree } from '@/app/server-actions/drag-tree'
import {
  validateAuthentication,
  validateRequestBody,
  createSuccessResponse,
} from '@/app/libs/validation-utils'
import { initializeDragTreeSchema } from '@/app/libs/api-schemas'
import { auth } from '@/auth'
import { SubscriptionTier } from '@prisma/client'
import prisma from '@/app/libs/prismadb'
import { getTierPermissions } from '@/app/configs/tier-permissions'

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 [Drag Tree Initialize] Starting initialization...')

    // Validate authentication
    const authResult = await validateAuthentication()
    if (!authResult.success) {
      return authResult.error
    }

    // Validate request body
    const bodyResult = await validateRequestBody(
      request,
      initializeDragTreeSchema
    )
    if (!bodyResult.success) {
      return bodyResult.error
    }

    const { title, description, preferredLanguage, analysisData } =
      bodyResult.data

    // Handle both structured (object) and legacy (array) formats
    let analysisItemsArray: string[] = []
    let structuredAnalysis = {}

    if (analysisData) {
      if (Array.isArray(analysisData)) {
        // Legacy format - flat array
        analysisItemsArray = analysisData
      } else {
        // New structured format
        structuredAnalysis = analysisData
        const { intention = [], entity = [] } = analysisData

        // Flatten for prompt building with proper labeling
        if (intention.length > 0) {
          analysisItemsArray.push(
            ...intention.map((item: string) => `Intention: ${item}`)
          )
        }
        if (entity.length > 0) {
          analysisItemsArray.push(`Key entities: ${entity.join(', ')}`)
        }
      }
    }

    console.log(
      `📝 [Drag Tree Initialize] User: ${authResult.userId}, Title: "${title}", Description: "${description}", Language: ${preferredLanguage}, Analysis Items: ${analysisItemsArray.length}`
    )

    // Construct enhanced prompt with problem analysis context for AI processing
    let enhancedPromptForAI = description
    if (analysisItemsArray && analysisItemsArray.length > 0) {
      const analysisContext = analysisItemsArray
        .map((item: string, index: number) => `${index + 1}. ${item}`)
        .join('\n')

      enhancedPromptForAI = `${description}

PROBLEM ANALYSIS CONTEXT:
The following analysis points should be considered during clarification:
${analysisContext}

Please use this analysis context to generate more targeted clarification questions.`
    }

    // Enforce per-tier drag tree creation limit
    const session = await auth()
    const userId = session?.user?.id || authResult.userId
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { subscription_tier: true },
    })
    const tier = user?.subscription_tier || SubscriptionTier.FREE
    const { maxDragTrees } = getTierPermissions(tier)
    const existingCount = await prisma.dragTree.count({
      where: { user_id: userId },
    })

    if (existingCount >= maxDragTrees) {
      return NextResponse.json(
        {
          error:
            tier === SubscriptionTier.FREE
              ? 'Free plan limit reached: You can create up to 3 drag trees. Upgrade to PRO to create more.'
              : 'You have reached the maximum number of drag trees for your plan.',
          code: 'DRAGTREE_LIMIT_REACHED',
          limit: maxDragTrees,
        },
        { status: 403 }
      )
    }

    // Create initial drag tree in INITIALIZED state (no content generation yet)
    const createResult = await createDragTree({
      userId: authResult.userId,
      title: title,
      userPrompt: description, // Keep clean - just the original user question
      preferredLanguage,
      metadata: {
        originalUserPrompt: description,
        problemAnalysisItems: structuredAnalysis, // Store structured format - can reconstruct everything from this
        enhancedPromptForAI, // Store the enhanced prompt for AI processing
      },
    })

    if (!createResult.success || !createResult.data) {
      console.error(
        '❌ [Drag Tree Initialize] Failed to create drag tree:',
        createResult.error
      )
      return NextResponse.json(
        { error: createResult.error || 'Failed to create drag tree' },
        { status: 500 }
      )
    }

    const dragTree = createResult.data
    console.log(
      `✅ [Drag Tree Initialize] Created drag tree: ${dragTree.id} (status: ${dragTree.status}) with ${analysisItemsArray.length} analysis items`
    )

    return createSuccessResponse({
      dragTreeId: dragTree.id,
      status: dragTree.status,
      title: title,
      description: description,
      message: 'Drag tree created successfully. Ready for content generation.',
    })
  } catch (error) {
    console.error('💥 [Drag Tree Initialize] Unexpected error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
