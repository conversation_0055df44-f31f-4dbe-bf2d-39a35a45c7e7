import { AnimatePresence, motion } from 'framer-motion'
import { useEffect, useState } from 'react'

const text = [
  'Ever struggled to express your thoughts clearly?',
  'Ever felt clueless with ambiguous problems?',
  'Ever wished you had a smart partner for brainstorming?',
  'Ever randomly doodled to make sense of your ideas?',
]

const TextRotate = () => {
  const [index, setIndex] = useState(0)

  useEffect(() => {
    const id = setInterval(() => {
      setIndex(state => {
        if (state >= text.length - 1) return 0
        return state + 1
      })
    }, 4000)
    return () => clearInterval(id)
  }, [])

  return (
    <div className="relative flex w-full items-center justify-center py-4 text-center">
      <AnimatePresence>
        <motion.div
          style={{
            position: 'absolute',
            cursor: 'pointer',
            fontSize: '1.5rem',
            fontWeight: '600',
            letterSpacing: '-0.025em',
            color: '#fde047',
          }}
          key={index}
          initial={{ y: 20, opacity: 0, scale: 0.8 }}
          animate={{ y: 0, opacity: 1, scale: 1 }}
          exit={{ y: -20, opacity: 0, scale: 0.8 }}
          transition={{ ease: 'easeInOut', delay: 0.2, duration: 0.5 }}
          //   whileHover={{ scale: 1.1 }}
        >
          {text[index]}
        </motion.div>
      </AnimatePresence>
    </div>
  )
}

export default TextRotate
