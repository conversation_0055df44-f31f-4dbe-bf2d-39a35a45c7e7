import React from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { X, Home, Zap, MessageCircle, Search } from 'lucide-react'
import { cn } from '@/lib/utils'
import type { Tab } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useTabStore'

interface TabButtonProps {
  tab: Tab
  isActive: boolean
  onSelect: (tabId: string) => void
  onClose: (tabId: string) => void
  isDragging?: boolean
}

/**
 * Individual tab button component
 * Handles tab selection, closing, and drag interactions
 */
const TabButton: React.FC<TabButtonProps> = ({
  tab,
  isActive,
  onSelect,
  onClose,
  isDragging = false,
}) => {
  const handleSelect = (e: React.MouseEvent) => {
    if (
      e.currentTarget === e.target ||
      (e.target as HTMLElement).closest('.tab-content')
    ) {
      onSelect(tab.id)
    }
  }

  const handleClose = (e: React.MouseEvent) => {
    e.stopPropagation()
    onClose(tab.id)
  }

  // Get appropriate icon based on tab type
  const getTabIcon = () => {
    switch (tab.type) {
      case 'main':
        return <Home className="h-3 w-3 text-gray-500" />
      case 'generate':
        return <Zap className="h-3 w-3 text-blue-500" />
      case 'chat':
        return <MessageCircle className="h-3 w-3 text-green-500" />
      case 'research':
        return <Search className="h-3 w-3 text-purple-500" />
      default:
        return null
    }
  }

  return (
    <div
      className={cn(
        'group flex items-center gap-1 px-2 py-1 rounded-t-lg border-b-2 transition-all duration-200 cursor-pointer',
        'hover:bg-gray-50',
        {
          'bg-white border-blue-500 shadow-sm': isActive,
          'bg-gray-100 border-transparent': !isActive,
          'opacity-50': isDragging,
        }
      )}
      onClick={handleSelect}
    >
      {/* Tab content */}
      <div className="tab-content flex-1 min-w-0 flex items-center space-x-1">
        {getTabIcon()}
        <div className="text-sm font-medium text-gray-700 truncate">
          {tab.title}
        </div>
      </div>

      {/* Close button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={handleClose}
        className="h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-gray-200"
      >
        <X className="h-3 w-3" />
      </Button>
    </div>
  )
}

// Memoize to prevent unnecessary re-renders when props haven't changed
export default React.memo(TabButton)
