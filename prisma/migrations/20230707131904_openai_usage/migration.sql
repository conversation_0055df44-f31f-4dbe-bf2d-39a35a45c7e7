-- CreateEnum
CREATE TYPE "OpenAIUsageType" AS ENUM ('EMAIL_ANALYSIS', 'CHAT');

-- CreateTable
CREATE TABLE "openai_usage" (
    "id" TEXT NOT NULL,
    "open_ai_usage_type" "OpenAIUsageType" NOT NULL DEFAULT 'CHAT',
    "model_name" TEXT NOT NULL,
    "input_text" TEXT NOT NULL,
    "output_text" TEXT NOT NULL,
    "input_token_est" INTEGER NOT NULL,
    "output_token_est" INTEGER NOT NULL,
    "config" JSONB NOT NULL DEFAULT '{}',
    "user_id" TEXT,
    "conversation_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "openai_usage_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "openai_usage_id_idx" ON "openai_usage" USING HASH ("id");

-- CreateIndex
CREATE INDEX "openai_usage_conversation_id_idx" ON "openai_usage" USING HASH ("conversation_id");

-- CreateIndex
CREATE INDEX "openai_usage_user_id_idx" ON "openai_usage" USING HASH ("user_id");

-- CreateIndex
CREATE INDEX "openai_usage_model_name_idx" ON "openai_usage" USING HASH ("model_name");

-- CreateIndex
CREATE INDEX "openai_usage_created_at_idx" ON "openai_usage"("created_at");

-- AddForeignKey
ALTER TABLE "openai_usage" ADD CONSTRAINT "openai_usage_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "openai_usage" ADD CONSTRAINT "openai_usage_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "conversations"("id") ON DELETE SET NULL ON UPDATE CASCADE;
