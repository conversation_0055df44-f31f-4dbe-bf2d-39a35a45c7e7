# Enhanced Metadata Usage Examples

This document shows how to use the new structured metadata system for AI chat execution steps.

## Overview

The metadata system now provides structured, type-safe metadata for different execution step types. The metadata is automatically persisted to the database's `AiExecutionStep.metadata` JSON column.

## Database Storage

**No schema changes needed!** The existing `AiExecutionStep` table already has a `metadata Json` column that stores all this data:

```sql
-- Existing schema (no changes needed)
CREATE TABLE ai_execution_steps (
  id VARCHAR PRIMARY KEY,
  message_id VARCHAR NOT NULL,
  step_order INTEGER NOT NULL,
  type VARCHAR NOT NULL, -- 'THOUGHT', 'TOOL_CALL', 'TOOL_RESULT', etc.
  metadata JSON,         -- <-- All structured metadata goes here
  parallel_key VARCHAR,
  parent_step_id VARCHAR,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

## Usage Examples

### 1. Enhanced Tool Calls

```typescript
import { ExecutionStepCollector } from '@/app/server-actions/ai-chat/execution-step-collector'

const collector = new ExecutionStepCollector()

// Old way (still works)
collector.addToolCall('web_search', { query: 'AI trends 2024' })

// New enhanced way
collector.addToolCall(
  'web_search',
  { query: 'AI trends 2024' },
  {
    priority: 'high',
    expectedResultType: 'search_results',
    retryCount: 0,
  }
)
```

**Database storage:**

```json
{
  "toolName": "web_search",
  "args": { "query": "AI trends 2024" },
  "timestamp": 1703123456789,
  "priority": "high",
  "expectedResultType": "search_results",
  "retryCount": 0
}
```

### 2. Enhanced Tool Results with Context

```typescript
// Enhanced tool result with AI context analysis
collector.addToolResult('web_search', searchResults, {
  contextProvided: {
    summary:
      'Found 10 articles about AI trends, focusing on LLMs and automation',
    keyPoints: [
      'Large Language Models are becoming more efficient',
      'AI automation is expanding to new industries',
      'Ethical AI considerations are increasing',
    ],
    relevanceScore: 0.92,
    sourceCount: 10,
  },
  resultSize: 15420,
  resultType: 'SearchResults',
})
```

**Database storage:**

```json
{
  "toolName": "web_search",
  "result": {
    /* search results */
  },
  "timestamp": 1703123456789,
  "success": true,
  "resultSize": 15420,
  "resultType": "SearchResults",
  "contextProvided": {
    "summary": "Found 10 articles about AI trends...",
    "keyPoints": ["Large Language Models...", "AI automation..."],
    "relevanceScore": 0.92,
    "sourceCount": 10
  }
}
```

### 3. Enhanced Reasoning Steps

```typescript
// Enhanced thought with complexity and category
collector.addThought(
  'I need to search for recent AI trends to provide accurate information',
  {
    complexity: 'moderate',
    category: 'planning',
    thinkingTime: 2.5,
    confidence: 0.85,
  }
)

// Enhanced reasoning summary
collector.addReasoningSummary(
  'Based on the search results, I can provide comprehensive information about AI trends',
  {
    keyDecisions: [
      'Focus on LLM developments',
      'Include practical applications',
      'Mention ethical considerations',
    ],
    confidenceLevel: 0.9,
    alternativesConsidered: [
      'Could focus only on technical aspects',
      'Could include more historical context',
    ],
  }
)
```

## UI Display Benefits

The enhanced metadata automatically improves the chat interface:

### Tool Call Display

- Shows priority levels with color coding
- Displays expected result types
- Shows retry attempts if any

### Tool Result Display

- Shows success/failure status clearly
- Displays AI's context analysis
- Shows relevance scores and source counts
- Highlights key insights extracted

### Reasoning Display

- Shows complexity levels (simple/moderate/complex)
- Displays thinking categories (analysis/planning/evaluation/synthesis)
- Shows confidence levels with visual indicators
- Lists key decisions and alternatives considered

## Backward Compatibility

The system is fully backward compatible:

```typescript
// Old metadata format still works
const oldMetadata = {
  toolName: 'web_search',
  args: { query: 'test' },
  timestamp: Date.now(),
}

// New UI components safely handle both formats
const toolCallMeta = getTypedStepMetadata<ToolCallStepMetadata>(
  oldMetadata,
  'TOOL_CALL'
)
// Returns typed metadata if valid, null if not - no errors!
```

## Integration Points

### 1. API Routes

Use the enhanced collector in your chat API:

```typescript
// In /api/aipane/chat/route.ts
const stepCollector = createExecutionStepCollector()

// During tool execution
stepCollector.addToolCall('web_search', { query }, { priority: 'high' })
const results = await searchTool.execute(query)
stepCollector.addToolResult('web_search', results, {
  contextProvided: {
    summary: 'Analyzed search results...',
    keyPoints: extractKeyPoints(results),
    relevanceScore: calculateRelevance(results, query),
  },
})
```

### 2. Streaming Updates

The metadata flows through the existing streaming system automatically - no changes needed to the streaming infrastructure.

### 3. Database Queries

Query metadata using JSON operators:

```sql
-- Find high-priority tool calls
SELECT * FROM ai_execution_steps
WHERE type = 'TOOL_CALL'
AND metadata->>'priority' = 'high';

-- Find tool results with high relevance
SELECT * FROM ai_execution_steps
WHERE type = 'TOOL_RESULT'
AND CAST(metadata->'contextProvided'->>'relevanceScore' AS FLOAT) > 0.8;
```

## Summary

✅ **No database migrations needed** - uses existing JSON column  
✅ **Fully backward compatible** - old metadata still works  
✅ **Type-safe** - TypeScript ensures correct metadata structure  
✅ **Enhanced UI** - automatically improves chat interface display  
✅ **Extensible** - easy to add new metadata fields

The enhanced metadata system provides a much richer, more transparent AI chat experience while maintaining full compatibility with existing data and code.
