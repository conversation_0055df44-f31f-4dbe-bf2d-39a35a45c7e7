/* CSS for tiptap editor */

.ProseMirror .is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af; /* Neutral gray for subtle hint */
  pointer-events: none;
  height: 0;
}
.ProseMirror .is-empty::before {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af; /* Neutral gray for subtle hint */
  pointer-events: none;
  height: 0;
}

/* Custom image styles */

.ProseMirror img {
  transition: filter 0.1s ease-in-out;
}

.ProseMirror img:hover {
  cursor: pointer;
  filter: brightness(90%);
}

.ProseMirror img.ProseMirror-selectednode {
  outline: 3px solid #5abbf7;
  filter: brightness(90%);
}

.img-placeholder {
  position: relative;
}

.img-placeholder:before {
  content: '';
  box-sizing: border-box;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: 3px solid rgb(231 229 228);
  border-top-color: rgb(41 37 36);
  animation: spinning 0.6s linear infinite;
}

@keyframes spinning {
  to {
    transform: rotate(360deg);
  }
}

/* Custom TODO list checkboxes – shoutout to this awesome tutorial: https://moderncss.dev/pure-css-custom-checkbox-style/ */

ul[data-type='taskList'] li > label {
  margin-right: 0.2rem;
  user-select: none;
}

@media screen and (max-width: 768px) {
  ul[data-type='taskList'] li > label {
    margin-right: 0.5rem;
  }
}

ul[data-type='taskList'] li > label input[type='checkbox'] {
  -webkit-appearance: none;
  appearance: none;
  background-color: rgb(255 255 255);
  margin: 0;
  cursor: pointer;
  width: 1.2em;
  height: 1.2em;
  position: relative;
  top: 5px;
  border: 2px solid rgb(28 25 23);
  margin-right: 0.3rem;
  display: grid;
  place-content: center;
}

ul[data-type='taskList'] li > label input[type='checkbox']:hover {
  background-color: rgb(250 250 249);
}

ul[data-type='taskList'] li > label input[type='checkbox']:active {
  background-color: rgb(231 229 228);
}

ul[data-type='taskList'] li > label input[type='checkbox']::before {
  content: '';
  width: 0.65em;
  height: 0.65em;
  transform: scale(0);
  transition: 120ms transform ease-in-out;
  box-shadow: inset 1em 1em;
  transform-origin: center;
  clip-path: polygon(14% 44%, 0 65%, 50% 100%, 100% 16%, 80% 0%, 43% 62%);
}

ul[data-type='taskList'] li > label input[type='checkbox']:checked::before {
  transform: scale(1);
}

ul[data-type='taskList'] li[data-checked='true'] > div > p {
  color: rgb(168 162 158);
  text-decoration: line-through;
  text-decoration-thickness: 2px;
}

/* Table styles */

.ProseMirror table {
  border-collapse: collapse;
  margin: 0;
  overflow: hidden;
  table-layout: fixed;
  width: 100%;
}

.ProseMirror table td,
.ProseMirror table th {
  border: 2px solid #ced4da;
  box-sizing: border-box;
  min-width: 1em;
  min-height: 1.5em;
  padding: 3px 5px;
  position: relative;
  vertical-align: top;
}

.ProseMirror table .table-cell,
.ProseMirror table .table-header-cell {
  /* Ensure proper layout for empty cells */
  min-height: 1.5em;
  line-height: 1.5;
}

.ProseMirror table .table-cell:empty:before,
.ProseMirror table .table-header-cell:empty:before {
  content: '\00A0'; /* Non-breaking space fallback */
  opacity: 0;
}

.ProseMirror table td > *,
.ProseMirror table th > * {
  margin-bottom: 0;
}

.ProseMirror table th {
  background-color: #f1f3f4;
  font-weight: bold;
  text-align: left;
}

.ProseMirror table .selectedCell:after {
  background: rgba(200, 200, 255, 0.4);
  content: '';
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  pointer-events: none;
  position: absolute;
  z-index: 2;
}

.ProseMirror table .column-resize-handle {
  background-color: #adf;
  bottom: -2px;
  position: absolute;
  right: -2px;
  pointer-events: none;
  top: 0;
  width: 4px;
}

.ProseMirror table p {
  margin: 0;
}

/* Overwrite tippy-box original max-width */

.tippy-box {
  max-width: 400px !important;
}
