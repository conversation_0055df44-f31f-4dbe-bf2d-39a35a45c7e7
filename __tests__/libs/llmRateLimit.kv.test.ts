jest.mock('next/server', () => ({
  NextResponse: {
    json: (_body: any, init?: any) => ({ status: init?.status }),
  },
}))
import {
  _resetRateLimitCounters,
  enforceRateLimit,
} from '@/app/libs/llmRateLimit'
import type { Session } from 'next-auth'

const mockSession = (id: string): Session =>
  ({ user: { id, subscription_tier: 'FREE' } }) as any

describe('KV-backed rate limiter', () => {
  const OLD_URL = process.env.KV_REST_API_URL
  const OLD_TOKEN = process.env.KV_REST_API_TOKEN

  beforeEach(() => {
    _resetRateLimitCounters()
    process.env.KV_REST_API_URL = 'https://fake-kv.upstash.io'
    process.env.KV_REST_API_TOKEN = 'test-token'
  })

  afterEach(() => {
    jest.resetModules()
    jest.clearAllMocks()
    process.env.KV_REST_API_URL = OLD_URL
    process.env.KV_REST_API_TOKEN = OLD_TOKEN
  })

  it('blocks when KV counter exceeds max', async () => {
    let incrCount = 0
    // Mock global fetch for Upstash REST calls
    global.fetch = jest.fn().mockImplementation((url: string) => {
      if (url.includes('/incr/')) {
        incrCount += 1
        // return hits as text
        return Promise.resolve({
          text: () => Promise.resolve(String(incrCount)),
        })
      }
      if (url.includes('/expire/')) {
        return Promise.resolve({ text: () => Promise.resolve('OK') })
      }
      return Promise.reject(new Error('unexpected fetch call'))
    }) as any

    const session = mockSession('user-kv')

    // First request allowed
    const res1 = await enforceRateLimit(session, 'screening_rephrase')
    expect(res1).toBeNull()

    // Second request should be blocked because maxRequests for FREE tier screening_rephrase is 1 (default)
    const res2 = await enforceRateLimit(session, 'screening_rephrase')
    expect(res2?.status).toBe(429)
  })
})
