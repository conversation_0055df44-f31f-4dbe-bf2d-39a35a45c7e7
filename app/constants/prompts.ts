// Don't introduce breaking changes to existing prompt panel, hence clone this file
// If we stay with this approach, we should use this file at the end

export const promptSuffix = `
Original ask:
{{ORIGINAL_ASK}}

We have completed interviews with their core teams and clarified some questions. Below are their responses to our inquiries:
{{WORKING_CONTEXT}}

Please note that the contexts are not exhaustive and may have blind spots. Your analysis will actively think of new insights outside of existing contexts

Those numbers with EXAMPLE NUMBER as a suffix may not be fact-based numbers, you should conduct your own search if needed

Please ensure you are doing what we asked, distinguish b/w requests and contexts
`

const enum PromptTag {
  // Domain-specific tags
  General = 'General',
  Business = 'Business',
  Engineering = 'Engineering',
  Product = 'Product',
  // Prompt-specific tags
  AssignPersona = 'Assign Persona/Role',
  ZeroShot = 'Zero-shot Learning',
  OneShot = 'One/Few-shot Learning',
  TipsOrThreat = 'Tips or Threat',
  StepByStep = 'Step-by-Step alike',

  // Context
  StructuredOutput = 'Provide Output Flow',

  // Capability
  Multilingual = 'Multilingual',
}

type Prompt = {
  title: string
  description: string
  tags: PromptTag[]
}

export const prompts: Prompt[] = [
  {
    title: 'Explain to layman',
    description: `We are writing an industry report targeting executives who are NOT in this domain. Based on the context below, carefully think and organize the materials. Your objective is to help people to understand this domain ASAP, you focus on the first principles of the industry and also the easy-to-read communication.  You use examples, illustrations, and analogies to help people build mental models for this domain and enable them to decide how/whether to enter the market, try to avoid jargon, and use plain English.

The outputs, should not be in point form, they should be integrated and explained with a logical flow [similar to slide presentation, you shouldn't jump b/w points, you need to connect the dots]

Remember, you are not rephrasing the context, you need to tell a consistent, clear, and easy-to-understand analogy/story to assist the beginner to aware of the situation
`,
    tags: [PromptTag.Business, PromptTag.ZeroShot, PromptTag.AssignPersona],
  },
  {
    title: 'Criticize constructively',
    description: `As a successful VC investor, based on the context below, carefully think and criticize the project, your goal is to find the risks asap, you have to think more and cover the spots that I have not considered before. Since we are all one team and love constructive feedback, you are straight and direct, or even mean, but still can well explain your rationale clearly, try to use a mean and picky tone
`,
    tags: [PromptTag.General, PromptTag.AssignPersona],
  },
  {
    title: 'Give recommendations',
    description: `As a pragmatic and efficient consultant, you need to answer the user's original ask, your response must be actionable and tell so-what. You get paid for insights and recommendations, not producing slides we can google, adopt the following structure for various problems:

# 1. Problem statement
You will first rephrase the problem with information to ensure users are aligned with us.
eg: when asked to analyze churn rate
You need to state the question unambiguously and include as specific information as possible. eg: the entity [specific business/industry], goals [churn rate, define exact metrics, magnitude], hidden conditions [prob they want certain segments only, not including all customers]. Also the dimensions, eg: time [monthly, yearly, quarterly], geolocation [specific city, region, country, global], please elaborate more based on user input

This could be an example:
{
## Problem statement
To conduct a comprehensive analysis of member churn. Our goal is to identify underlying patterns and insights from our churn data that will enable the development of targeted, data-driven retention strategies. These strategies aim to reduce customer attrition significantly and enhance the division's long-term viability.
}

It is fine sometimes you don't know every detail or need to make assumptions, please list as many as possible out:
{
## Uncertainties & Risks
- Data Completeness: The churn data might not capture all relevant variables that influence churn, such as detailed member satisfaction metrics or external economic factors.
- Model Accuracy: Predictive models might not fully account for complex, nonlinear relationships or unexpected external shocks (e.g., a global pandemic).
- Change in Member Behavior: Member behavior and preferences may evolve, making historical data less predictive over time.
- Competitive Dynamics: Changes in the competitive landscape could unexpectedly alter churn, independent of internal strategy effectiveness.

## Assumptions
- Data Integrity: We assume that the data collected in the member churn table is accurate and reliably updated.
- Stable Market Conditions: The analysis assumes relatively stable healthcare market conditions, without dramatic changes in regulation or market entry by competitors.
- Member Engagement: We assume that members will respond positively to targeted interventions based on historical interaction patterns.
}

# 2. Breakdown and recommendations
You want to identify several key aspects of the problem and assign priority with reasons, for example, to analyze the churn problem:
{
## Understanding Churn Rate Dynamics
- Problem Clarification: Assess the current 15% annual churn rate against industry benchmarks of 12%, focusing on why our rates are higher and what factors contribute to this variance.
- Actionable Insight: Investigate specific drivers of churn unique to our member base, such as service dissatisfaction or competitive offerings.
- Recommendation: Implement targeted communication strategies to address specific dissatisfaction points uncovered during the analysis.

## Data-Driven Insight Generation
- Problem Clarification: Utilize the extensive data points available in the member churn table, such as demographics, medical conditions, and interaction histories, to build a detailed understanding of churn patterns.
- Actionable Insight: Employ advanced analytics and machine learning models like logistic regression and Random Forest to predict potential churn and understand key factors influencing member decisions.
- Recommendation: Develop predictive models that integrate real-time data to provide ongoing assessments of churn risk, allowing for proactive engagement with at-risk members.

## Personalization of Retention Strategies
- Problem Clarification: With churn drivers identified and predictive models in place, the next step is crafting personalized retention strategies that cater to the specific needs and behaviors of different member segments.
- Actionable Insight: Use insights from data analysis to segment the member base more effectively and tailor interventions, such as health management programs for members with chronic conditions or loyalty rewards for long-term members.
- Recommendation: Roll out pilot programs in segments with the highest churn rates, measure effectiveness, and scale successful strategies across the division.

## Performance Tracking and Optimization
- Problem Clarification: Establish robust mechanisms to monitor the effectiveness of implemented strategies and adapt them based on real-time feedback and emerging trends.
- Actionable Insight: Use advanced data visualization tools and business intelligence platforms to monitor key performance indicators like churn rate, member satisfaction, and service utilization.
- Recommendation: Set up a dashboard for continuous monitoring and implement a feedback loop that includes regular member surveys and feedback sessions to refine strategies.

## Strategic Implementation Timeline
- Problem Clarification: Define a clear timeline for each stage of the strategy from data analysis to the full-scale implementation of retention programs.
- Actionable Insight: Break down the project into phased milestones, starting with data assessment, followed by model development, strategy testing, and widespread deployment.
- Recommendation: Prioritize quick wins by implementing strategies in segments that require minimal adjustments but promise substantial impacts on retention rates.
}
`,
    tags: [
      PromptTag.Business,
      PromptTag.AssignPersona,
      PromptTag.OneShot,
      PromptTag.StructuredOutput,
    ],
  },
  {
    title: 'Think out of the box',
    description: `The client is almost out of funding, we cannot take the traditional approach because it will not work out given limited resources! This is the time to be visionary and bold, try to think out of the box, brainstorm what approaches we haven't considered yet, and what are some risky approaches we can take with huge potential upside [state the upside briefly], be bold and make aggressive moves
`,
    tags: [PromptTag.Business, PromptTag.ZeroShot, PromptTag.TipsOrThreat],
  },
  {
    title: 'Summarize and translate',
    description: `As the top executive assistant, please read the information and provide a summary of the key findings with important details in English. Then, translate that summary into Chinese, Spanish, and Japanese.
`,
    tags: [
      PromptTag.General,
      PromptTag.AssignPersona,
      PromptTag.ZeroShot,
      PromptTag.Multilingual,
    ],
  },
  {
    title: 'Draft the action plan',
    description: `I need to prioritize this urgent task immediately. Could you quickly devise a 30-day beginner-friendly plan to complete the project? Please consider each step carefully. There could be a bonus for you if we finish ahead of schedule.
`,
    tags: [PromptTag.Business, PromptTag.ZeroShot, PromptTag.TipsOrThreat],
  },
  {
    title: 'Do a SWOT analysis',
    description: `As a seasoned business analyst, please conduct a SWOT analysis based on the provided context. For each point, include your rationale and as much relevant information as possible about the topic, to aid in decision-making.
`,
    tags: [PromptTag.Business, PromptTag.AssignPersona, PromptTag.ZeroShot],
  },
  {
    title: 'Form the team',
    description: `You are the project lead and we are out of hand, now to accomplish this task, we need to leverage consulting firms. To help us identify the right team, we need to state our needs/goals clearly, and then list out the key talent that we need, such as capability and role. Treat this like job descriptions that are used to send to consulting firms, please make sure the requirements are very concrete, not vague skills.
eg:
- Vague: web development; Concrete: Next JS + React + TypeScript
- Vague: machine learning; Concrete: finetune LLM for voice command understanding
- Vague: leadership; Concrete: lead team of 3+ to refactor a broken app
- Vague: good communication: Concrete: lead client-facing negotiation and software presales
`,
    tags: [PromptTag.Business, PromptTag.AssignPersona, PromptTag.OneShot],
  },
  {
    title: 'Draft slack messages to the team',
    description: `Create 3 versions of slack to send to our stakeholders explaining them we are working on this. Use an informal tone and some emojis.
`,
    tags: [PromptTag.General, PromptTag.ZeroShot],
  },
  {
    title: 'Summarize to executive summary',
    description: `Summarize this context into an executive summary of about 300 characters
`,
    tags: [PromptTag.General, PromptTag.ZeroShot],
  },
  {
    title: 'My own prompt',
    description: 'Paste your prompt here, at least 100 characters',
    tags: [PromptTag.General],
  },
]
