import { NextResponse } from 'next/server'

/**
 * Standard error response format for AI Pane APIs
 */
export type StandardError = {
  error: {
    message: string
    code: string
    details?: Record<string, any>
  }
}

/**
 * Common error codes for AI Pane APIs
 */
export const ERROR_CODES = {
  // Authentication & Authorization
  AUTH_REQUIRED: 'AUTH_REQUIRED',
  ACCESS_DENIED: 'ACCESS_DENIED',

  // Rate Limiting
  RATE_LIMITED: 'RATE_LIMITED',

  // Validation
  INVALID_JSON: 'INVALID_JSON',
  MISSING_BODY: 'MISSING_BODY',
  INVALID_MESSAGES: 'INVALID_MESSAGES',
  INVALID_ID_FORMAT: 'INVALID_ID_FORMAT',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',

  // Resources
  NOT_FOUND: 'NOT_FOUND',
  CONVERSATION_NOT_FOUND: 'CONVERSATION_NOT_FOUND',
  MESSAGE_NOT_FOUND: 'MESSAGE_NOT_FOUND',

  // Server Errors
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  AI_SERVICE_ERROR: 'AI_SERVICE_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  PERSISTENCE_FAILED: 'PERSISTENCE_FAILED',
} as const

/**
 * Creates a standardized error response
 */
export function standardError(
  message: string,
  code: keyof typeof ERROR_CODES,
  status: number,
  details?: Record<string, any>,
  headers?: Record<string, string>
): NextResponse {
  const errorResponse: StandardError = {
    error: {
      message,
      code: ERROR_CODES[code],
      ...(details && { details }),
    },
  }

  return NextResponse.json(errorResponse, {
    status,
    headers,
  })
}

/**
 * Convenience functions for common error types
 */
export const standardErrors = {
  unauthorized: (message = 'Authentication required') =>
    standardError(message, 'AUTH_REQUIRED', 401),

  forbidden: (message = 'Access denied') =>
    standardError(message, 'ACCESS_DENIED', 403),

  notFound: (message = 'Resource not found') =>
    standardError(message, 'NOT_FOUND', 404),

  conversationNotFound: (conversationId?: string) =>
    standardError(
      'Conversation not found',
      'CONVERSATION_NOT_FOUND',
      404,
      conversationId ? { conversationId } : undefined
    ),

  messageNotFound: (messageId?: string) =>
    standardError(
      'Message not found',
      'MESSAGE_NOT_FOUND',
      404,
      messageId ? { messageId } : undefined
    ),

  invalidJson: (message = 'Invalid JSON in request body') =>
    standardError(message, 'INVALID_JSON', 400),

  missingBody: (message = 'Request body is required') =>
    standardError(message, 'MISSING_BODY', 400),

  invalidMessages: (message = 'Messages array is required and must be valid') =>
    standardError(message, 'INVALID_MESSAGES', 400),

  invalidIdFormat: (field: string, expected: string) =>
    standardError(`Invalid ${field} format`, 'INVALID_ID_FORMAT', 400, {
      field,
      expected,
    }),

  rateLimited: (retryAfter: number, limit: string) =>
    standardError(
      'Too many requests',
      'RATE_LIMITED',
      429,
      { limit },
      {
        'Retry-After': retryAfter.toString(),
        'X-RateLimit-Limit': limit,
        'X-RateLimit-Remaining': '0',
      }
    ),

  internalError: (
    message = 'Internal server error',
    details?: Record<string, any>
  ) => standardError(message, 'INTERNAL_ERROR', 500, details),

  persistenceFailed: (context?: Record<string, any>) =>
    standardError('Failed to save data', 'PERSISTENCE_FAILED', 500, context),
}
