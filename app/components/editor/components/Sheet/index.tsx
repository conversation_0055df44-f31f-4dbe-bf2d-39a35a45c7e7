import { Sheet, SheetContent } from "@/components/ui/sheet";
import { Draft } from "@prisma/client";
import { toast } from "react-hot-toast";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { MetadataTabContent } from "./MetadataTabContent";
import { PromptTabContent } from "./PromptTabContent";
import mixpanel from "@/app/libs/mixpanel";

export type PartialIssueTreeNode = {
  id: string;
  data: {
    label: string;
    example?: string;
    resolved?: boolean;
    skipped?: boolean;
  };
  type: string;
};
export interface PartialIssueTree {
  id: string;
  conversation_id: string;
  nodes: PartialIssueTreeNode[];
}

interface ExpandSheetProps {
  showSheet: boolean;
  closeSheet: () => void;
  modal?: boolean;
  draft?: Draft;
  editorText?: string;
  issueTrees?: PartialIssueTree[] | null;
}

export default function ExpandSheet({
  showSheet,
  closeSheet,
  modal,
  draft,
  editorText,
  issueTrees,
}: ExpandSheetProps) {
  console.log("expand sheet issue tree", issueTrees);
  // In getDraftById, we have replaced exact_prompt w/ summary_context
  const displaySummaryContext =
    draft?.exact_prompt ||
    "THIS SHOULD SHOW THE CONTEXT TO GENERATE THIS DRAFT, if you see this and want to know the context, refreshing the page usually works";

  const filterAndTransformIssueTrees = (
    issueTrees: PartialIssueTree[] | null | undefined
  ) => {
    if (!issueTrees) return null;

    return issueTrees.map((issueTree) => {
      // Explicitly specify that nodes is an array of PartialIssueTreeNode
      const nodes = JSON.parse(
        issueTree.nodes as unknown as string
      ) as PartialIssueTreeNode[];

      const filteredNodes = nodes.filter(
        (node: PartialIssueTreeNode) =>
          node.type === "customLeafNode" && !node.data.skipped
      );

      return {
        ...issueTree,
        nodes: filteredNodes,
      };
    });
  };

  const transformedIssueTrees = filterAndTransformIssueTrees(issueTrees);

  let qaString = "";
  transformedIssueTrees?.forEach((issueTree) => {
    issueTree.nodes.forEach((node) => {
      qaString += `Q: ${node.data.label}\nA: ${node.data.example}\n\n`;
    });
  });

  const handleCopyText = (text: string) => {
    if (draft) {
      navigator.clipboard.writeText(text).then(
        () => {
          toast.success("Copied to clipboard!");
        },
        (err) => {
          toast.error("Could not copy text: ", err);
        }
      );
    }
  };

  const handleCopySummaryContext = () => {
    mixpanel.track("copy_summary_context_click", { location: "metadata_tab" });
    handleCopyText(displaySummaryContext);
  };

  const handleCopyQA = () => {
    mixpanel.track("copy_qa_click", { location: "metadata_tab" });
    handleCopyText(qaString);
  };

  return (
    <Sheet key={"top"} open={showSheet} onOpenChange={closeSheet} modal={modal}>
      <SheetContent>
        <Tabs defaultValue="prompt">
          <TabsList>
            <TabsTrigger value="prompt">Experimental features</TabsTrigger>
            <TabsTrigger value="meta-data">Metadata</TabsTrigger>
          </TabsList>
          <TabsContent value="meta-data">
            <MetadataTabContent
              displaySummaryContext={displaySummaryContext}
              draft={draft}
              handleCopySummaryContext={handleCopySummaryContext}
              handleCopyQA={handleCopyQA}
              issueTrees={issueTrees}
              qaString={qaString}
            />
          </TabsContent>
          <TabsContent value="prompt">
            <PromptTabContent
              handleCopyText={handleCopyText}
              editorText={editorText}
            />
          </TabsContent>
        </Tabs>
      </SheetContent>
    </Sheet>
  );
}
