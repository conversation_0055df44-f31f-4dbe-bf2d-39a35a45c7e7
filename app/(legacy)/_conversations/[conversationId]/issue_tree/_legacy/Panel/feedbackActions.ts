'use server'

import prisma from '@/app/libs/prismadb'

export const fetchFeedback = async (conversationId: string) => {
  const feedback = await prisma.feedback.findFirst({
    where: { conversation_id: conversationId },
    orderBy: { created_at: 'desc' },
  })
  const feedbackData =
    typeof feedback?.data === 'string'
      ? JSON.parse(feedback?.data)
      : feedback?.data

  return feedback ? feedbackData : null
}

export const updateFeedback = async (
  conversationId: string,
  promptPanelFeedback: string
) => {
  // Fetch the existing feedback
  const feedback = await prisma.feedback.findFirst({
    where: { conversation_id: conversationId },
    orderBy: { created_at: 'desc' },
  })

  if (!feedback) {
    throw new Error(`No feedback found for conversation ID ${conversationId}`)
  }

  // Parse the feedback data if it's a string
  const feedbackData =
    typeof feedback.data === 'string'
      ? JSON.parse(feedback.data)
      : feedback.data

  // Update the prompt_panel_feedback field
  feedbackData.prompt_panel_feedback = promptPanelFeedback

  // Update the feedback in the database
  const updatedFeedback = await prisma.feedback.update({
    where: { id: feedback.id },
    data: { data: feedbackData },
  })

  return updatedFeedback
}
