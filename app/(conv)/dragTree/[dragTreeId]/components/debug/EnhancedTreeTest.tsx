// "use client";

// import React, { useEffect } from "react";
// import { useSession } from "next-auth/react";
// import { Button } from "@/components/ui/button";
// import { Badge } from "@/components/ui/badge";
// import { useDragTreeStoreEnhanced } from "@/app/stores/dragtree_store";
// import { TreeNode } from "@/app/types";

// type EnhancedTreeTestProps = {
//   dragTreeId: string;
// };

// export default function EnhancedTreeTest({
//   dragTreeId,
// }: EnhancedTreeTestProps) {
//   const { data: session } = useSession();

//   const {
//     // State
//     treeData,
//     syncStatus,
//     isLoading,
//     autoSyncEnabled,

//     // Sync methods
//     initializeSync,
//     loadFromDatabase,
//     saveToDatabase,
//     forceSyncToDatabase,

//     // Tree methods (now with auto-sync)
//     markNodeAsInterested,
//     editNode,
//     deleteNode,
//     reorderNode,
//     getInterestedNodes,

//     // Sync control
//     enableAutoSync,
//     disableAutoSync,
//   } = useDragTreeStoreEnhanced();

//   // Initialize sync on mount
//   useEffect(() => {
//     if (session?.user?.id && dragTreeId) {
//       console.log("🚀 Initializing sync...");
//       initializeSync(dragTreeId, session.user.id, true); // Enable auto-sync

//       // Load initial data
//       setTimeout(() => {
//         loadFromDatabase();
//       }, 100);
//     }
//   }, [session?.user?.id, dragTreeId, initializeSync, loadFromDatabase]);

//   // Demo functions
//   const handleMarkInterested = (nodeId: string) => {
//     console.log("📌 Marking node as interested:", nodeId);
//     markNodeAsInterested(nodeId); // This will auto-sync!
//   };

//   const handleEditLabel = (nodeId: string, newLabel: string) => {
//     console.log("✏️ Editing node label:", nodeId, "->", newLabel);
//     editNode(nodeId, newLabel); // This will auto-sync!
//   };

//   const handleDeleteNode = (nodeId: string) => {
//     if (confirm("Are you sure you want to delete this node?")) {
//       console.log("🗑️ Deleting node:", nodeId);
//       deleteNode(nodeId, "node"); // This will auto-sync!
//     }
//   };

//   const handleManualSave = () => {
//     console.log("💾 Manual save triggered");
//     saveToDatabase();
//   };

//   const handleForceSync = () => {
//     console.log("⚡ Force sync triggered");
//     forceSyncToDatabase();
//   };

//   const toggleAutoSync = () => {
//     if (autoSyncEnabled) {
//       disableAutoSync();
//     } else {
//       enableAutoSync();
//     }
//   };

//   // Render tree recursively
//   const renderTreeNode = (
//     node: TreeNode,
//     depth: number = 0
//   ): React.ReactNode => {
//     const indent = depth * 20;
//     const isInterested = node.isInterestedIn;

//     return (
//       <div key={node.id} style={{ marginLeft: `${indent}px` }} className="py-1">
//         <div className="flex items-center gap-2 p-3 border rounded-lg bg-white shadow-sm">
//           <Badge variant={node.type === "category" ? "default" : "secondary"}>
//             {node.type}
//           </Badge>

//           <span className="flex-1 font-medium">{node.label}</span>

//           {isInterested && (
//             <Badge variant="outline" className="bg-green-50 text-green-700">
//               ❤️ Interested
//             </Badge>
//           )}

//           <div className="flex gap-1">
//             <Button
//               size="sm"
//               variant="outline"
//               onClick={() => handleMarkInterested(node.id)}
//               disabled={isInterested}
//             >
//               {isInterested ? "✓" : "♡"}
//             </Button>

//             <Button
//               size="sm"
//               variant="outline"
//               onClick={() => {
//                 const newLabel = prompt("Enter new label:", node.label);
//                 if (newLabel && newLabel !== node.label) {
//                   handleEditLabel(node.id, newLabel);
//                 }
//               }}
//             >
//               ✏️
//             </Button>

//             <Button
//               size="sm"
//               variant="destructive"
//               onClick={() => handleDeleteNode(node.id)}
//             >
//               🗑️
//             </Button>
//           </div>
//         </div>

//         {node.children.map((child) => renderTreeNode(child, depth + 1))}
//       </div>
//     );
//   };

//   const getSyncStatusBadge = () => {
//     switch (syncStatus) {
//       case "idle":
//         return <Badge variant="secondary">⏸️ Idle</Badge>;
//       case "syncing":
//         return (
//           <Badge className="bg-blue-100 text-blue-800">🔄 Syncing...</Badge>
//         );
//       case "success":
//         return <Badge className="bg-green-100 text-green-800">✅ Synced</Badge>;
//       case "error":
//         return <Badge variant="destructive">❌ Error</Badge>;
//       default:
//         return <Badge variant="secondary">❓ Unknown</Badge>;
//     }
//   };

//   const interestedCount = getInterestedNodes().length;

//   return (
//     <div className="max-w-5xl mx-auto p-6 space-y-6">
//       {/* Header */}
//       <div className="bg-white rounded-lg shadow-sm border p-6">
//         <h2 className="text-2xl font-bold mb-4">
//           Enhanced Drag Tree Store Test
//         </h2>

//         {/* Status Panel */}
//         <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
//           <div>
//             <div className="font-medium text-sm text-gray-600">Sync Status</div>
//             {getSyncStatusBadge()}
//           </div>
//           <div>
//             <div className="font-medium text-sm text-gray-600">Auto-Sync</div>
//             <Badge variant={autoSyncEnabled ? "default" : "secondary"}>
//               {autoSyncEnabled ? "🔄 ON" : "⏸️ OFF"}
//             </Badge>
//           </div>
//           <div>
//             <div className="font-medium text-sm text-gray-600">Loading</div>
//             <Badge variant={isLoading ? "default" : "secondary"}>
//               {isLoading ? "📥 Loading..." : "✅ Ready"}
//             </Badge>
//           </div>
//           <div>
//             <div className="font-medium text-sm text-gray-600">
//               Interested Nodes
//             </div>
//             <Badge variant="outline">{interestedCount} nodes</Badge>
//           </div>
//         </div>

//         {/* Control Panel */}
//         <div className="space-y-3">
//           <h3 className="font-semibold">Controls</h3>
//           <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
//             <Button
//               onClick={loadFromDatabase}
//               disabled={isLoading}
//               variant="outline"
//             >
//               📥 Load DB
//             </Button>
//             <Button
//               onClick={handleManualSave}
//               disabled={isLoading}
//               variant="outline"
//             >
//               💾 Save DB
//             </Button>
//             <Button
//               onClick={handleForceSync}
//               disabled={isLoading}
//               variant="outline"
//             >
//               ⚡ Force Sync
//             </Button>
//             <Button
//               onClick={toggleAutoSync}
//               variant={autoSyncEnabled ? "default" : "secondary"}
//             >
//               {autoSyncEnabled ? "⏸️ Disable Auto" : "🔄 Enable Auto"}
//             </Button>
//           </div>
//         </div>
//       </div>

//       {/* Instructions */}
//       <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
//         <h3 className="font-semibold text-blue-800 mb-2">How to Test:</h3>
//         <ul className="text-sm text-blue-700 space-y-1">
//           <li>
//             • <strong>Mark Interested</strong>: Click ♡ button - should
//             auto-sync to database
//           </li>
//           <li>
//             • <strong>Edit Labels</strong>: Click ✏️ button - should auto-sync
//             changes
//           </li>
//           <li>
//             • <strong>Delete Nodes</strong>: Click 🗑️ button - should remove and
//             sync
//           </li>
//           <li>
//             • <strong>Auto-Sync</strong>: Changes save automatically after 1.5
//             seconds
//           </li>
//           <li>
//             • <strong>Status</strong>: Watch sync status change from Idle →
//             Syncing → Synced
//           </li>
//         </ul>
//       </div>

//       {/* Tree Display */}
//       <div className="bg-white border rounded-lg p-6">
//         <h3 className="font-semibold mb-4">Tree Structure</h3>
//         {treeData ? (
//           <div className="space-y-3">{renderTreeNode(treeData)}</div>
//         ) : (
//           <div className="text-center py-12 text-gray-500">
//             <div className="text-lg mb-2">No tree data available</div>
//             <div className="text-sm mb-4">
//               {isLoading
//                 ? "Loading from database..."
//                 : "Click 'Load DB' to fetch tree data"}
//             </div>
//             {!isLoading && (
//               <Button onClick={loadFromDatabase} variant="outline">
//                 📥 Load Tree Data
//               </Button>
//             )}
//           </div>
//         )}
//       </div>

//       {/* Debug Info */}
//       <div className="bg-gray-50 border rounded-lg p-4 text-xs">
//         <h4 className="font-semibold mb-2">Debug Info:</h4>
//         <div className="grid grid-cols-2 gap-4">
//           <div>
//             <strong>Drag Tree ID:</strong> {dragTreeId}
//           </div>
//           <div>
//             <strong>User ID:</strong> {session?.user?.id || "Not logged in"}
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// }
