// Legacy Mixpanel integration - DEPRECATED
// This file has been replaced by the centralized logging system in app/libs/logging.ts
// Keeping this file for backward compatibility during migration

console.warn(
  'Mixpanel integration is deprecated. Please use the centralized logging system from app/libs/logging.ts'
)

// Stub implementation to prevent errors during migration
const mixpanelStub = {
  track: (eventName: string, properties?: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[DEPRECATED MIXPANEL] ${eventName}:`, properties)
    }
  },
  identify: (userId: string, properties?: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[DEPRECATED MIXPANEL] Identify: ${userId}`, properties)
    }
  },
}

export default mixpanelStub
