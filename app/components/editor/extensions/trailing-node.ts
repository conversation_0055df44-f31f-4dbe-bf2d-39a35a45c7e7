import { Extension } from '@tiptap/core'
import { Plug<PERSON>, Plugin<PERSON><PERSON> } from 'prosemirror-state'

/**
 * TrailingNode Extension
 * Ensures the document always ends with at least one empty paragraph.
 * That allows the cursor to escape block nodes (e.g. tables) placed at the end.
 */
const TrailingNode = Extension.create({
  name: 'trailingNode',

  addOptions() {
    return {
      nodeType: 'paragraph' as string,
    }
  },

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('trailingNode'),
        appendTransaction: (_transactions, _oldState, newState) => {
          const { doc, schema } = newState
          const nodeType = schema.nodes[this.options.nodeType]
          if (!nodeType) return

          const lastChild = doc.lastChild
          const isMissingTrailing = !lastChild || lastChild.type !== nodeType

          if (isMissingTrailing) {
            // Insert a new paragraph at the end of the document
            const tr = newState.tr.insert(doc.content.size, nodeType.create())
            return tr
          }
          return null
        },
      }),
    ]
  },
})

export default TrailingNode
