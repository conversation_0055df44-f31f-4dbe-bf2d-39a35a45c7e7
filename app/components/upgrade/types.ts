/**
 * Unified upgrade component system types
 * Provides consistent typing for all upgrade-related components
 */

import { SubscriptionTier } from '@prisma/client'

export type UpgradeContext =
  | 'sidebar'
  | 'profile'
  | 'feature-gate'
  | 'export'
  | 'quota'
  | 'screening'
  | 'research'
  | 'chat'
  | 'generation'
  | 'banner'

export type UpgradeSize = 'sm' | 'md' | 'lg'

export type UpgradeVariant = 'primary' | 'outline' | 'ghost'

export type UpgradeButtonProps = {
  /** Custom text for the button (default: "Upgrade to Pro") */
  text?: string
  /** Size variant */
  size?: UpgradeSize
  /** Visual variant */
  variant?: UpgradeVariant
  /** Custom icon (default: FiZap) */
  icon?: React.ComponentType<{ size?: number; className?: string }>
  /** Context for analytics tracking */
  context: UpgradeContext
  /** Current user tier for analytics */
  currentTier?: SubscriptionTier
  /** Custom click handler (default: navigate to /subscription) */
  onClick?: () => void
  /** Additional CSS classes */
  className?: string
  /** Whether button is disabled */
  disabled?: boolean
  /** Loading state */
  loading?: boolean
  /** Full width button */
  fullWidth?: boolean
}

export type UpgradeHintProps = {
  /** Message to display */
  message: string
  /** Context for analytics tracking */
  context: UpgradeContext
  /** Current user tier for analytics */
  currentTier?: SubscriptionTier
  /** Custom click handler (default: navigate to /subscription) */
  onClick?: () => void
  /** Additional CSS classes */
  className?: string
  /** Show upgrade button inline (default: true) */
  showButton?: boolean
  /** Custom button text (default: "Upgrade") */
  buttonText?: string
}

export type UpgradeBannerProps = {
  /** Title for the banner */
  title?: string
  /** Description message */
  message: string
  /** Context for analytics tracking */
  context: UpgradeContext
  /** Current user tier for analytics */
  currentTier?: SubscriptionTier
  /** Custom click handler (default: navigate to /subscription) */
  onClick?: () => void
  /** Additional CSS classes */
  className?: string
  /** Whether banner can be dismissed */
  dismissible?: boolean
  /** Custom button text (default: "Upgrade to Pro") */
  buttonText?: string
}
