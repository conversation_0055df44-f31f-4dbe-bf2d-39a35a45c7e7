/**
 * AI SDK 5 Type Definitions
 *
 * This file contains unified type definitions for AI SDK 5 migration.
 * Following the migration strategy to use UIMessage<any, any, any> as the base type
 * and define data part types for titles, citations, and status updates.
 */

import { UIMessage } from 'ai'

// Unified message type as recommended in migration document
export type Msg = UIMessage<any, any, any>

// Data part type definitions for AI SDK 5 streaming
export type DataPartType =
  | 'data-title'
  | 'data-citations'
  | 'data-status'
  | 'data-execution-step'
  | 'data-stream-start'
  | 'data-stream-finish'

// Title data part
export interface TitleDataPart {
  type: 'data-title'
  data: {
    title: string
  }
}

// Citations data part
export interface CitationsDataPart {
  type: 'data-citations'
  data: {
    list: Citation[]
  }
}

// Status update data part
export interface StatusDataPart {
  type: 'data-status'
  data: {
    message: string
    timestamp?: number
  }
}

// Execution step data part for real-time step streaming
export interface ExecutionStepDataPart {
  type: 'data-execution-step'
  data: {
    step: {
      type: 'TOOL_CALL' | 'TOOL_RESULT' | 'THOUGHT' | 'REASONING_SUMMARY'
      toolName?: string
      args?: any
      result?: any
      toolCallId?: string
      timestamp: number
    }
  }
}

// Stream control data parts
export interface StreamStartDataPart {
  type: 'data-stream-start'
  data: {
    conversationId: string
    timestamp: number
  }
}

export interface StreamFinishDataPart {
  type: 'data-stream-finish'
  data: {
    timestamp: number
  }
}

// Union type for all data parts
export type DataPart =
  | TitleDataPart
  | CitationsDataPart
  | StatusDataPart
  | ExecutionStepDataPart
  | StreamStartDataPart
  | StreamFinishDataPart

// Citation type (reused from existing types)
export interface Citation {
  url: string
  snippet: string
  metadata?: {
    title?: string
    description?: string
    domain?: string
  }
}

// Helper type for extracting data parts from messages
export type MessageDataParts<T extends DataPartType> = Extract<
  DataPart,
  { type: T }
>

// Helper functions for working with data parts
export function isDataPart(part: any): part is DataPart {
  return (
    part &&
    typeof part === 'object' &&
    'type' in part &&
    typeof part.type === 'string'
  )
}

export function isTitleDataPart(part: any): part is TitleDataPart {
  return isDataPart(part) && part.type === 'data-title'
}

export function isCitationsDataPart(part: any): part is CitationsDataPart {
  return isDataPart(part) && part.type === 'data-citations'
}

export function isStatusDataPart(part: any): part is StatusDataPart {
  return isDataPart(part) && part.type === 'data-status'
}

export function isExecutionStepDataPart(
  part: any
): part is ExecutionStepDataPart {
  return isDataPart(part) && part.type === 'data-execution-step'
}

export function isStreamStartDataPart(part: any): part is StreamStartDataPart {
  return isDataPart(part) && part.type === 'data-stream-start'
}

export function isStreamFinishDataPart(
  part: any
): part is StreamFinishDataPart {
  return isDataPart(part) && part.type === 'data-stream-finish'
}

// Helper function to extract data parts of a specific type from a message
export function getDataParts<T extends DataPartType>(
  message: Msg,
  type: T
): MessageDataParts<T>[] {
  if (!message.parts) return []

  return message.parts.filter(
    (part): part is MessageDataParts<T> =>
      part && typeof part === 'object' && 'type' in part && part.type === type
  ) as MessageDataParts<T>[]
}

// Helper function to get the latest data part of a specific type from a message
export function getLatestDataPart<T extends DataPartType>(
  message: Msg,
  type: T
): MessageDataParts<T> | undefined {
  const parts = getDataParts(message, type)
  return parts[parts.length - 1]
}

// Helper function to extract title from message data parts
export function extractTitleFromMessage(message: Msg): string | undefined {
  const titlePart = getLatestDataPart(message, 'data-title')
  return titlePart?.data.title
}

// Helper function to extract citations from message data parts
export function extractCitationsFromMessage(message: Msg): Citation[] {
  const citationsPart = getLatestDataPart(message, 'data-citations')
  return citationsPart?.data.list || []
}

// Helper function to extract execution steps from message data parts
export function extractExecutionStepsFromMessage(
  message: Msg
): ExecutionStepDataPart['data']['step'][] {
  const stepParts = getDataParts(message, 'data-execution-step')
  return stepParts.map(part => part.data.step)
}

// Helper function to extract text content from UIMessage parts with AI SDK 5 optimizations
export function extractTextContent(message: Msg): string {
  // --- NEW: Handle multiple message formats gracefully ---
  // 1. Preferred path: parts array with { type: 'text' } and streaming { type: 'text-delta' }
  if (Array.isArray(message.parts) && message.parts.length > 0) {
    // PRIORITY 1: Extract actual text content (LLM responses)
    const textParts = message.parts
      .filter(part => {
        // Support traditional text and text-delta parts
        return (
          ((part as any).type === 'text' &&
            typeof (part as any).text === 'string') ||
          ((part as any).type === 'text-delta' &&
            typeof (part as any).textDelta === 'string')
        )
      })
      .map(part =>
        (part as any).type === 'text-delta'
          ? ((part as any).textDelta as string)
          : ((part as any).text as string)
      )
      .join('')

    // If we have text content, return it (this is the LLM's actual response)
    if (textParts.trim()) {
      return textParts
    }

    // PRIORITY 2: Fallback to tool outputs only if no text content exists
    const toolOutputs = message.parts
      .filter(part => {
        // Support tool result parts that contain search results
        return (
          (part as any).type === 'tool-webSearch' &&
          typeof (part as any).output === 'string'
        )
      })
      .map(part => (part as any).output as string)
      .join('')

    return toolOutputs
  }

  // 2. Fallback: vanilla `content` property (string)
  if (typeof (message as any).content === 'string') {
    return (message as any).content
  }

  // 3. Fallback: `.text` property sometimes exposed by SDK helpers
  if (typeof (message as any).text === 'string') {
    return (message as any).text
  }

  return ''
}

/**
 * Helper functions for raw provider data migration
 */

/**
 * Extracts the UIMessage from persisted metadata
 */
export const getUiMessage = (json: any): Msg | undefined => {
  return json?.uiMessage as Msg | undefined
}

/**
 * Extracts the raw step data from execution step metadata
 */
export const getRawStep = (step: { metadata: any }): any => {
  return step.metadata?.raw
}

/**
 * Creates metadata object for message persistence with full UIMessage
 */
export const createMessageMetadata = (
  responseMessage: Msg,
  tokenUsage?: any,
  additionalMetadata?: Record<string, any>
) => {
  return {
    uiMessage: responseMessage, // Full UIMessage object for perfect replay
    tokenUsage,
    ...additionalMetadata,
  }
}
