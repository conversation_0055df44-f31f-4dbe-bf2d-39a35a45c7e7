import React, { memo } from 'react'
import { Handle, Position } from 'reactflow'
import { Textarea } from '@/components/ui/textarea'

interface SubtreeLeafNodeProps {
  data: {
    label: string
    example?: string
    resolved?: boolean
    skipped?: boolean
  }
  selected: boolean
}

const SubtreeLeafNode = memo(({ data, selected }: SubtreeLeafNodeProps) => {
  const text = data.example || ''

  return (
    <div
      className={`flex p-2 bg-blue-200 w-auto max-w-3xl h-auto h-max-28 rounded-lg ${
        selected ? 'border-2 border-blue-500' : ''
      }`}
    >
      <div className="flex-grow">
        <div className="flex items-center">
          {/* Connect handle */}
          <Handle
            type="target"
            position={Position.Left}
            style={{ background: '#555' }}
            onConnect={params => console.log('handle onConnect', params)}
            isConnectable={false}
          />
          {/* Question */}
          <div>
            <strong>{data.label}</strong>
          </div>
        </div>

        {/* Textarea */}
        <Textarea
          value={text}
          className={`w-full h-16 text-sm p-1 rounded-md transition-shadow overflow-auto bg-gray-100 border-gray-300 cursor-not-allowed`}
          placeholder="Type here..."
          readOnly
        />
      </div>
    </div>
  )
})

SubtreeLeafNode.displayName = 'SubtreeLeafNode'

export default SubtreeLeafNode
