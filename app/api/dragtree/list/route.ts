import { NextResponse } from 'next/server'
import { auth } from '@/auth'
import { getDragTreesByUserId } from '@/app/server-actions/drag-tree'
import { UserStatus } from '@prisma/client'
import prisma from '@/app/libs/prismadb'
import { isRateLimited, getRetryAfterSeconds } from '@/app/libs/rateLimiter'

export async function GET() {
  try {
    // Check if user is authenticated using getServerSession
    const session = await auth()

    if (!session?.user?.id) {
      return NextResponse.json('Unauthorized', { status: 401 })
    }

    // Optional: Check user status if needed for security
    // You can remove this if you don't need the UserStatus check
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { status: true },
    })

    if (user?.status !== UserStatus.ACTIVE) {
      return NextResponse.json('User account not active', { status: 401 })
    }

    // Prevent accidental rapid-fire requests (e.g. Safari reload loops)
    const rateLimitKey = `${session.user.id}:dragtree:list`
    if (isRateLimited(rateLimitKey, 5000)) {
      const retryAfter = getRetryAfterSeconds(rateLimitKey, 5000)
      return NextResponse.json(
        { error: 'Too many requests – please wait a moment.' },
        {
          status: 429,
          headers: { 'Retry-After': retryAfter.toString() },
        }
      )
    }

    // Fetch drag trees for the user
    const result = await getDragTreesByUserId(session.user.id)

    if (result.success) {
      return NextResponse.json(result.data)
    } else {
      return NextResponse.json('Failed to fetch drag trees', { status: 500 })
    }
  } catch (error) {
    console.error('Error fetching drag trees:', error)
    return NextResponse.json('Internal Error', { status: 500 })
  }
}
