#!/bin/bash
# Comprehensive setup and validation script for portable branch-based Telegram notifications

set -e  # Exit on any error

echo "🚀 Comprehensive Telegram Forum Setup & Validation"
echo "=================================================="
echo ""

# Global validation status
VALIDATION_ERRORS=0
VALIDATION_WARNINGS=0

# Function to log validation results
log_success() {
    echo "✅ $1"
}

log_warning() {
    echo "⚠️  $1"
    ((VALIDATION_WARNINGS++))
}

log_error() {
    echo "❌ $1"
    ((VALIDATION_ERRORS++))
}

log_info() {
    echo "ℹ️  $1"
}

# Step 1: Validate .env file and environment variables
echo "📋 Step 1: Validating Environment Configuration"
echo "----------------------------------------------"

if [ ! -f ".env" ]; then
    log_error ".env file not found in current directory"
    echo "   Create .env file with:"
    echo "   TG_TOKEN=your_bot_token_here"
    echo "   TG_CHAT=your_group_chat_id_here"
else
    log_success ".env file exists"
fi

# Source environment variables
if [ -f ".env" ]; then
    set -a
    source .env
    set +a
fi

# Validate required environment variables
if [ -z "$TG_TOKEN" ]; then
    log_error "TG_TOKEN not set in .env file"
else
    if [[ "$TG_TOKEN" =~ ^[0-9]+:[A-Za-z0-9_-]+$ ]]; then
        log_success "TG_TOKEN format appears valid"
        echo "   Token: ${TG_TOKEN:0:10}...${TG_TOKEN: -10}"
    else
        log_error "TG_TOKEN format appears invalid (should be: 123456789:ABC-DEF1234ghIkl-zyx57W2v1u123ew11)"
    fi
fi

if [ -z "$TG_CHAT" ]; then
    log_error "TG_CHAT not set in .env file"
else
    if [[ "$TG_CHAT" =~ ^-?[0-9]+$ ]]; then
        log_success "TG_CHAT format appears valid"
        echo "   Chat ID: $TG_CHAT"
    else
        log_error "TG_CHAT format appears invalid (should be numeric, e.g., -1001234567890)"
    fi
fi

echo ""

# Step 2: Test Telegram Bot Connectivity
echo "📡 Step 2: Testing Telegram Bot Connectivity"
echo "--------------------------------------------"

if [ -n "$TG_TOKEN" ]; then
    BOT_INFO=$(curl -s -X POST "https://api.telegram.org/bot$TG_TOKEN/getMe")

    if echo "$BOT_INFO" | grep -q '"ok":true'; then
        BOT_USERNAME=$(echo "$BOT_INFO" | grep -o '"username":"[^"]*"' | cut -d'"' -f4)
        BOT_NAME=$(echo "$BOT_INFO" | grep -o '"first_name":"[^"]*"' | cut -d'"' -f4)
        log_success "Bot connectivity verified"
        echo "   Bot Name: $BOT_NAME"
        echo "   Bot Username: @$BOT_USERNAME"
    else
        log_error "Failed to connect to Telegram bot"
        echo "   Response: $BOT_INFO"
        echo "   Check your TG_TOKEN is correct"
    fi
else
    log_error "Cannot test bot connectivity - TG_TOKEN not set"
fi

echo ""

# Step 3: Validate Chat Access and Forum Status
echo "🏠 Step 3: Validating Chat Access and Forum Status"
echo "--------------------------------------------------"

if [ -n "$TG_TOKEN" ] && [ -n "$TG_CHAT" ]; then
    CHAT_INFO=$(curl -s -X POST "https://api.telegram.org/bot$TG_TOKEN/getChat" \
        -H "Content-Type: application/json" \
        -d "{\"chat_id\": \"$TG_CHAT\"}")

    if echo "$CHAT_INFO" | grep -q '"ok":true'; then
        CHAT_TITLE=$(echo "$CHAT_INFO" | grep -o '"title":"[^"]*"' | cut -d'"' -f4)
        CHAT_TYPE=$(echo "$CHAT_INFO" | grep -o '"type":"[^"]*"' | cut -d'"' -f4)
        IS_FORUM=$(echo "$CHAT_INFO" | grep -o '"is_forum":true')

        log_success "Chat access verified"
        echo "   Chat Title: $CHAT_TITLE"
        echo "   Chat Type: $CHAT_TYPE"

        if [ -n "$IS_FORUM" ]; then
            log_success "Chat is a forum group - ready for topics!"
        else
            log_error "Chat is not a forum group"
            echo "   📋 To enable forum mode:"
            echo "      1. Open your Telegram group: $CHAT_TITLE"
            echo "      2. Go to Group Settings"
            echo "      3. Enable 'Topics' feature"
            echo "      4. Run this script again"
        fi
    else
        log_error "Failed to access chat"
        echo "   Response: $CHAT_INFO"
        echo "   Possible issues:"
        echo "   - Bot is not added to the group"
        echo "   - Chat ID is incorrect"
        echo "   - Bot was removed from the group"
    fi
else
    log_error "Cannot test chat access - missing TG_TOKEN or TG_CHAT"
fi

echo ""

# Step 4: Test Topic Creation Permissions
echo "🔧 Step 4: Testing Topic Creation Permissions"
echo "---------------------------------------------"

if [ -n "$TG_TOKEN" ] && [ -n "$TG_CHAT" ]; then
    # Get repository name for test topic
    REPO_NAME=""
    if git remote get-url origin >/dev/null 2>&1; then
        REPO_NAME=$(git remote get-url origin | sed 's/.*\///' | sed 's/\.git$//')
    else
        REPO_NAME=$(basename "$(git rev-parse --show-toplevel 2>/dev/null || pwd)")
    fi

    TEST_TOPIC_NAME="${REPO_NAME}:branch:setup-test"
    log_info "Testing topic creation with name: $TEST_TOPIC_NAME"

    TEST_RESPONSE=$(curl -s -X POST "https://api.telegram.org/bot$TG_TOKEN/createForumTopic" \
        -H "Content-Type: application/json" \
        -d "{
            \"chat_id\": \"$TG_CHAT\",
            \"name\": \"$TEST_TOPIC_NAME\",
            \"icon_color\": 0x6FB9F0
        }")

    if echo "$TEST_RESPONSE" | grep -q '"ok":true'; then
        TOPIC_ID=$(echo "$TEST_RESPONSE" | grep -o '"message_thread_id":[0-9]*' | cut -d: -f2)
        log_success "Topic creation successful!"
        echo "   Topic ID: $TOPIC_ID"
        echo "   Topic Name: $TEST_TOPIC_NAME"

        # Clean up: Close the test topic
        if [ -n "$TOPIC_ID" ]; then
            curl -s -X POST "https://api.telegram.org/bot$TG_TOKEN/closeForumTopic" \
                -H "Content-Type: application/json" \
                -d "{\"chat_id\": \"$TG_CHAT\", \"message_thread_id\": $TOPIC_ID}" > /dev/null
            log_info "Test topic closed and cleaned up"
        fi
    else
        log_error "Topic creation failed"
        echo "   Response: $TEST_RESPONSE"
        echo "   Possible issues:"
        echo "   - Bot lacks 'Manage Topics' permission"
        echo "   - Bot is not an administrator"
        echo "   - Chat is not a forum group"
    fi
else
    log_error "Cannot test topic creation - missing TG_TOKEN or TG_CHAT"
fi

echo ""

# Step 5: Validate Claude Hook Configuration
echo "⚙️  Step 5: Validating Claude Hook Configuration"
echo "-----------------------------------------------"

CLAUDE_CONFIG=".claude/settings.local.json"

if [ ! -f "$CLAUDE_CONFIG" ]; then
    log_error "Claude configuration file not found: $CLAUDE_CONFIG"
    echo "   Create the file with proper hook configuration"
else
    log_success "Claude configuration file exists"

    # Check if hooks are configured
    if grep -q '"hooks"' "$CLAUDE_CONFIG"; then
        log_success "Hooks section found in configuration"

        # Check for Notification hook
        if grep -q '"Notification"' "$CLAUDE_CONFIG"; then
            if grep -q 'notify_telegram.sh' "$CLAUDE_CONFIG"; then
                log_success "Notification hook configured with notify_telegram.sh"
            else
                log_warning "Notification hook exists but may not use notify_telegram.sh"
            fi
        else
            log_warning "Notification hook not configured"
        fi

        # Check for Stop hook
        if grep -q '"Stop"' "$CLAUDE_CONFIG"; then
            if grep -q 'notify_telegram.sh' "$CLAUDE_CONFIG"; then
                log_success "Stop hook configured with notify_telegram.sh"
            else
                log_warning "Stop hook exists but may not use notify_telegram.sh"
            fi
        else
            log_warning "Stop hook not configured"
        fi
    else
        log_error "No hooks section found in Claude configuration"
        echo "   Add hooks configuration to enable notifications"
    fi
fi

echo ""

# Step 6: Test End-to-End Notification Flow
echo "🧪 Step 6: Testing End-to-End Notification Flow"
echo "-----------------------------------------------"

if [ $VALIDATION_ERRORS -eq 0 ]; then
    CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "setup-test")

    log_info "Testing notification system with current branch: $CURRENT_BRANCH"

    # Test the notification script directly
    TEST_OUTPUT=$(bash ./scripts/notify_telegram.sh "🧪 End-to-end setup validation test" "SetupValidation" 2>&1)

    if [ $? -eq 0 ]; then
        log_success "Notification script executed successfully"
        echo "   Branch: $CURRENT_BRANCH"
        echo "   Check your Telegram forum group for the test message"

        # Check if topic was created
        if echo "$TEST_OUTPUT" | grep -q "Using topic"; then
            TOPIC_ID=$(echo "$TEST_OUTPUT" | grep -o "Using topic [0-9]*" | grep -o "[0-9]*")
            log_success "Topic automatically created/found (ID: $TOPIC_ID)"
        fi
    else
        log_error "Notification script failed to execute"
        echo "   Output: $TEST_OUTPUT"
    fi
else
    log_warning "Skipping end-to-end test due to previous validation errors"
fi

echo ""

# Step 7: Validation Summary and Recommendations
echo "📊 Step 7: Validation Summary"
echo "-----------------------------"

echo ""
echo "🎯 VALIDATION RESULTS:"
echo "====================="

if [ $VALIDATION_ERRORS -eq 0 ] && [ $VALIDATION_WARNINGS -eq 0 ]; then
    echo "🎉 ALL VALIDATIONS PASSED!"
    echo ""
    echo "✅ Your system is fully configured and ready to use!"
    echo "✅ No local state files required - fully portable!"
    echo "✅ Works on any computer with proper .env configuration!"

elif [ $VALIDATION_ERRORS -eq 0 ]; then
    echo "⚠️  SETUP COMPLETE WITH WARNINGS"
    echo ""
    echo "✅ Core functionality is working"
    echo "⚠️  $VALIDATION_WARNINGS warning(s) found - review above for details"

else
    echo "❌ SETUP INCOMPLETE"
    echo ""
    echo "❌ $VALIDATION_ERRORS error(s) must be fixed before system will work"
    if [ $VALIDATION_WARNINGS -gt 0 ]; then
        echo "⚠️  $VALIDATION_WARNINGS warning(s) should also be addressed"
    fi
    echo ""
    echo "🔧 REQUIRED ACTIONS:"
    echo "   1. Fix all errors listed above"
    echo "   2. Run this script again to re-validate"
    exit 1
fi

echo ""
echo "🚀 SYSTEM FEATURES:"
echo "=================="
echo "✅ Dynamic topic discovery (no local files needed)"
echo "✅ Automatic topic creation for new branches"
echo "✅ Repository name included in all messages"
echo "✅ Fully portable across computers"
echo "✅ Real-time Telegram API integration"

echo ""
echo "📋 USAGE:"
echo "========"
echo "• Switch git branches and trigger Claude operations"
echo "• Topics are created automatically: REPO:branch:BRANCH_NAME"
echo "• Each branch gets isolated notification thread"
echo "• System works immediately on any computer with .env configured"

echo ""
echo "🎯 PORTABILITY GUARANTEE:"
echo "========================"
echo "This system requires ONLY:"
echo "• .env file with TG_TOKEN and TG_CHAT"
echo "• .claude/settings.local.json with hook configuration"
echo "• NO local state files or computer-specific setup needed!"

if [ $VALIDATION_ERRORS -eq 0 ]; then
    echo ""
    echo "🎉 Setup validation completed successfully!"
    echo "Your branch-based Telegram notification system is ready to use!"
fi
