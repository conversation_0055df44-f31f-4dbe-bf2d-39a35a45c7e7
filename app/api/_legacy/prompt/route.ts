// import { NextResponse } from "next/server";
// import prisma from "@/app/libs/prismadb";

// // Define the GET request handler function
// export async function POST(request: Request) {
//   try {
//     // Parse the request body as JSON
//     const body = await request.json();

//     // Extract the necessary fields from the request body
//     const { currentUser, id } = body;
//     console.log("api/prompt: ", body);

//     // Check if the user is authorized
//     if (!currentUser?.id || !currentUser?.email) {
//       return new NextResponse("Unauthorized", { status: 401 });
//     }
//     // Create a new message in the database using Prisma
//     const prompt = await prisma.prompt.findUnique({
//       where: { id },
//     });

//     console.log(prompt);

//     // Return the newly created message as a JSON response
//     return NextResponse.json(prompt);
//   } catch (error) {
//     console.error(error);

//     // If an error occurs, return a 500 status code with an "Error" message
//     return new NextResponse("Error", { status: 500 });
//   }
// }
