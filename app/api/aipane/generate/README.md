# AI Pane Generate Endpoint

**Path:** `/api/aipane/generate`

Streaming content generation for DragTree _documents_ (`AIGeneration` records). Designed for long-running generations with server-authoritative IDs and atomic updates.

## Key Concepts

| Concept                  | Details                                                                                            |
| ------------------------ | -------------------------------------------------------------------------------------------------- |
| **Placeholder → Active** | We create an `AIGeneration` row with `status = GENERATING`, then update to `ACTIVE` in `onFinish`. |
| **Server IDs**           | IDs follow `doc_${uuid}` pattern to avoid client collisions.                                       |
| **Atomic Update**        | Final `prisma.aIGeneration.update` is now retried up to 3× for reliability.                        |
| **Rate Limits**          | Controlled indirectly via auth & DragTree ownership validation.                                    |

## Request Shape

```ts
export interface AIPaneGenerateRequest {
  prompt: string
  model: string
  settings?: Record<string, any>
  dragTreeId: string
  entityType?: string // default: 'drag_tree_v1'
  promptTemplate?: string // optional – used for smart titles
}
```

## Response

`DataStreamResponse` (text/event-stream) – same contract as `/chat`. A custom header `X-Generation-Id` returns the server-generated ID.

## Persistence Flow

1. **Create placeholder** – `status: GENERATING` before streaming.
2. **Stream tokens** – via `streamText` (Azure OpenAI).
3. **onFinish** –
   1. Generate smart title (slugified template name + date).
   2. Retry `prisma.aIGeneration.update` up to 3×.
4. **Failure Handling** – If final update fails, we mark the record `INACTIVE`.

## Failure Modes

- **Update failure** – Marked `INACTIVE` _and_ surfaced via console.warn after retries.
- **Ownership check** – `validateDragTreeOwnership` returns 403 if user doesn’t own tree.

## Useful Links

- Prisma model: `AIGeneration` in `prisma/schema.prisma`.
- Smart title util: `generateSmartTitle()` (bottom of route file).
- Frontend hook: `useAiGeneration` (see `components/chat/GenerateTabContent.tsx`).

---

_Last updated: 2025-07-17_
