'use client'

import React, { useState } from 'react'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { Button } from '@/components/ui/button'
import { TreeNode } from '@/app/types'

const TestDeleteNode: React.FC = () => {
  // Optimized: Use individual selectors to prevent unnecessary re-renders
  const frontendTreeStructure = useDragTreeStore(
    state => state.frontendTreeStructure
  )
  const deleteNode = useDragTreeStore(state => state.deleteNode)
  const nodeMap = useDragTreeStore(state => state.nodeMap)
  const [selectedNodeId, setSelectedNodeId] = useState<string>('')

  // Get all nodes for selection
  const getAllNodes = (node: TreeNode): TreeNode[] => {
    let nodes = [node]
    node.children.forEach(child => {
      nodes = nodes.concat(getAllNodes(child))
    })
    return nodes
  }

  const allNodes = frontendTreeStructure
    ? getAllNodes(frontendTreeStructure)
    : []

  const handleDelete = () => {
    if (!selectedNodeId) return

    const nodeToDelete = nodeMap.get(selectedNodeId)
    if (nodeToDelete) {
      deleteNode(selectedNodeId, nodeToDelete.type)
    }
  }

  const getNodeInfo = (nodeId: string) => {
    const node = nodeMap.get(nodeId)
    if (!node) return 'Unknown'

    const childCount = node.children.length
    return `${node.type}: "${node.label}" (${childCount} children)`
  }

  return (
    <div className="p-4 border rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold mb-4">🗑️ Test Delete Node</h3>

      {frontendTreeStructure ? (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              Select Node to Delete:
            </label>
            <select
              value={selectedNodeId}
              onChange={e => setSelectedNodeId(e.target.value)}
              className="w-full p-2 border rounded-md"
            >
              <option value="">-- Select a node --</option>
              {allNodes.map(node => (
                <option key={node.id} value={node.id}>
                  {getNodeInfo(node.id)}
                </option>
              ))}
            </select>
          </div>

          <Button
            onClick={handleDelete}
            variant="destructive"
            disabled={!selectedNodeId}
            className="w-full"
          >
            Delete Selected Node
          </Button>

          <div className="text-sm text-gray-600 space-y-1">
            <div>
              <strong>Total Nodes:</strong> {allNodes.length}
            </div>
            <div>
              <strong>Root Node:</strong> {frontendTreeStructure.label}
            </div>
            <div>
              <strong>Selected:</strong>{' '}
              {selectedNodeId ? getNodeInfo(selectedNodeId) : 'None'}
            </div>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 p-3 rounded-md">
            <h4 className="font-medium text-yellow-800 mb-2">
              🔍 Validation Rules:
            </h4>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>✅ Categories with children show confirmation dialog</li>
              <li>✅ Cannot delete last child under a category</li>
              <li>✅ Nodes marked as INACTIVE in database</li>
              <li>✅ Tree structure updated properly</li>
              <li>✅ Root node cannot be deleted</li>
            </ul>
          </div>
        </div>
      ) : (
        <p className="text-gray-500">No tree data available</p>
      )}
    </div>
  )
}

export default TestDeleteNode
