import Stripe from 'stripe'
import prisma from '@/app/libs/prismadb'
import {
  Prisma,
  PricingPlanInterval,
  PricingType,
  SubscriptionService,
  SubscriptionTier,
} from '@prisma/client'

import { stripe } from '@/lib/stripe/config'

// Helper function to convert seconds to DateTime
export const toDateTime = (secs: number) => {
  const t = new Date(0) // Unix epoch start
  t.setSeconds(secs)
  return t
}

// Change to control trial period length
const TRIAL_PERIOD_DAYS = 0

// Product related functions
const createProductRecord = async (product: Stripe.Product) => {
  const productData: Prisma.ProductCreateInput = {
    id: product.id,
    active: product.active,
    name: product.name,
    description: product.description ?? null,
    image: product.images?.[0] ?? null,
    metadata: product.metadata ? JSON.stringify(product.metadata) : null,
  }

  await prisma.product.create({ data: productData })
}

const updateProductRecord = async (product: Stripe.Product) => {
  const existingProduct = await prisma.product.findUnique({
    where: { id: product.id },
  })

  const productData: Prisma.ProductUpdateInput = {
    active: product.active,
    name: product.name,
    description: product.description ?? null,
    image: product.images?.[0] ?? null,
    metadata: product.metadata ? JSON.stringify(product.metadata) : null,
  }

  if (existingProduct) {
    await prisma.product.update({
      where: { id: product.id },
      data: productData,
    })
  } else {
    await createProductRecord(product)
  }
}

// Price related functions
const createPriceRecord = async (price: Stripe.Price) => {
  const priceData: Prisma.PriceCreateInput = {
    id: price.id,
    active: price.active,
    currency: price.currency,
    type: price.type as PricingType,
    unit_amount: price.unit_amount || -1,
    interval: price.recurring?.interval as PricingPlanInterval,
    interval_count: price.recurring?.interval_count || -1,
    trial_period_days: price.recurring?.trial_period_days ?? TRIAL_PERIOD_DAYS,
    product: { connect: { id: price.product as string } },
  }

  await prisma.price.create({ data: priceData })
}

const updatePriceRecord = async (price: Stripe.Price) => {
  const existingPrice = await prisma.price.findUnique({
    where: { id: price.id },
  })

  const priceData: Prisma.PriceUpdateInput = {
    active: price.active,
    currency: price.currency,
    type: price.type,
    unit_amount: price.unit_amount || -1,
    interval: price.recurring?.interval as PricingPlanInterval,
    interval_count: price.recurring?.interval_count || -1,
    trial_period_days: price.recurring?.trial_period_days ?? TRIAL_PERIOD_DAYS,
  }

  if (existingPrice) {
    await prisma.price.update({
      where: { id: price.id },
      data: priceData,
    })
  } else {
    await createPriceRecord(price)
  }
}

// Subscription related functions
enum SubscriptionEventType {
  Created = 'created',
  Updated = 'updated',
}

// Update user record with latest subscription info
const updateUserRecord = async (subscription: Stripe.Subscription) => {
  const updateData: any = {
    subscription_service: SubscriptionService.STRIPE,
    subscription_customer_id: subscription.customer as string,
    subscription_id: subscription.id,
    subscription_end_date: toDateTime(
      subscription.current_period_end
    ).toISOString(),
    subscription_tier: SubscriptionTier.PRO, // Set tier to PRO for active subscriptions
    subscription_cancel_pending: subscription.cancel_at_period_end, // Track cancellation status
  }

  await prisma.user.update({
    where: { id: subscription.metadata.user_id },
    data: updateData,
  })
}

const manageSubscriptionRecord = async (
  subscription: Stripe.Subscription,
  _eventType: SubscriptionEventType
) => {
  if (!subscription.metadata?.user_id) {
    console.error('Subscription is missing user_id')
    return
  }

  if (!subscription.items.data[0]?.price) {
    console.error('Subscription is missing price data')
    return
  }

  const subscriptionData = {
    id: subscription.id,
    customer_id: subscription.customer as string,
    status: subscription.status as Stripe.Subscription.Status,
    quantity: 1,
    cancel_at_period_end: subscription.cancel_at_period_end,
    current_period_start: toDateTime(
      subscription.current_period_start
    ).toISOString(),
    current_period_end: toDateTime(
      subscription.current_period_end
    ).toISOString(),
    ended_at: subscription.ended_at
      ? toDateTime(subscription.ended_at).toISOString()
      : null,
    cancel_at: subscription.cancel_at
      ? toDateTime(subscription.cancel_at).toISOString()
      : null,
    canceled_at: subscription.canceled_at
      ? toDateTime(subscription.canceled_at).toISOString()
      : null,
    trial_start: subscription.trial_start
      ? toDateTime(subscription.trial_start).toISOString()
      : null,
    trial_end: subscription.trial_end
      ? toDateTime(subscription.trial_end).toISOString()
      : null,
    user: { connect: { id: subscription.metadata.user_id } },
    price: { connect: { id: subscription.items.data[0].price.id } },
    product: {
      connect: { id: subscription.items.data[0].price.product as string },
    },
  }

  const existingSubscription = await prisma.subscription.findUnique({
    where: { id: subscription.id },
  })

  if (existingSubscription) {
    await prisma.subscription.update({
      where: { id: subscription.id },
      data: subscriptionData,
    })
  } else {
    await prisma.subscription.create({ data: subscriptionData })
  }

  await updateUserRecord(subscription)
}

// Handle subscription deletion - downgrade user to FREE tier
const handleSubscriptionDeleted = async (subscription: Stripe.Subscription) => {
  if (!subscription.metadata?.user_id) {
    console.error('Subscription deletion: missing user_id in metadata')
    return
  }

  console.log(
    `🔄 Processing subscription deletion for user: ${subscription.metadata.user_id}`
  )

  try {
    // Update user record to downgrade to FREE tier
    await prisma.user.update({
      where: { id: subscription.metadata.user_id },
      data: {
        subscription_tier: SubscriptionTier.FREE,
        subscription_end_date: null,
        subscription_cancel_pending: false,
        subscription_id: null,
        subscription_customer_id: null,
        subscription_service: null,
      },
    })

    // Update subscription record status
    await prisma.subscription.updateMany({
      where: {
        id: subscription.id,
        user_id: subscription.metadata.user_id,
      },
      data: {
        status: 'canceled',
        ended_at: subscription.ended_at
          ? toDateTime(subscription.ended_at).toISOString()
          : new Date().toISOString(),
        canceled_at: subscription.canceled_at
          ? toDateTime(subscription.canceled_at).toISOString()
          : new Date().toISOString(),
      },
    })

    console.log(
      `✅ Successfully downgraded user ${subscription.metadata.user_id} to FREE tier`
    )
  } catch (error) {
    console.error(`❌ Error handling subscription deletion:`, error)
    throw error
  }
}

// Handle completed checkout session
const handleCheckoutSessionCompleted = async (
  checkoutSession: Stripe.Checkout.Session
) => {
  const { metadata } = checkoutSession

  if (checkoutSession.mode === 'subscription' && checkoutSession.subscription) {
    const subscriptionId = checkoutSession.subscription as string
    try {
      // Update subscription's metadata
      const subscription = await stripe.subscriptions.retrieve(subscriptionId)
      await stripe.subscriptions.update(subscriptionId, {
        metadata: { ...subscription.metadata, ...metadata },
      })
      console.log('Subscription metadata updated successfully')

      // Create subscription record in case webhook is not triggered
      await manageSubscriptionRecord(
        subscription,
        SubscriptionEventType.Created
      )
      console.log('Subscription record created successfully')
    } catch (error) {
      console.error('Error updating subscription metadata:', error)
      throw error
    }
  } else {
    console.error('Unhandled checkout session mode!')
    throw new Error('Unhandled checkout session mode!')
  }
}

export {
  createProductRecord,
  updateProductRecord,
  createPriceRecord,
  updatePriceRecord,
  manageSubscriptionRecord,
  handleCheckoutSessionCompleted,
  handleSubscriptionDeleted,
  SubscriptionEventType,
}
