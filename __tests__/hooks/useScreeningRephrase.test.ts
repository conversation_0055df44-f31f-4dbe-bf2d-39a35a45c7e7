import { renderHook, act } from '@testing-library/react'
import { useScreeningRephrase } from '@/app/(conv)/screening/hooks/useScreeningRephrase'
import { useSession } from 'next-auth/react'
import { toast } from 'react-hot-toast'

// Mock dependencies - using central mock for @ai-sdk/react
jest.mock('next-auth/react')
jest.mock('react-hot-toast')

// Get the mock from the central mock file
const { experimental_useObject } = require('@ai-sdk/react')
const useObjectMock = experimental_useObject as jest.Mock
const useSessionMock = useSession as jest.Mock

describe('useScreeningRephrase', () => {
  let mockSubmit: jest.Mock
  let onRephrasedUpdate: jest.Mock

  const defaultProps = {
    description: 'test description',
    selectedLanguage: 'en' as const,
    onRephrasedUpdate: jest.fn(),
  }

  beforeEach(() => {
    jest.clearAllMocks()
    mockSubmit = jest.fn()
    onRephrasedUpdate = jest.fn()

    // Mock the object hook that experimental_useObject returns
    useObjectMock.mockReturnValue({
      object: null,
      submit: mockSubmit,
      isLoading: false,
    })
    useSessionMock.mockReturnValue({ data: { user: { id: 'test-user' } } })
  })

  it('should call append when startRephrase is called', () => {
    const { result } = renderHook(() =>
      useScreeningRephrase({ ...defaultProps, onRephrasedUpdate })
    )

    act(() => {
      result.current.startRephrase()
    })

    expect(mockSubmit).toHaveBeenCalledWith({
      userId: 'test-user',
      description: 'test description',
      preferredLanguage: 'en',
    })
  })

  it('should show an error if user is not authenticated', () => {
    useSessionMock.mockReturnValue({ data: null })
    const { result } = renderHook(() =>
      useScreeningRephrase({ ...defaultProps, onRephrasedUpdate })
    )

    act(() => {
      result.current.startRephrase()
    })

    expect(toast.error).toHaveBeenCalledWith(
      'Please log in to use the real API.'
    )
    expect(mockSubmit).not.toHaveBeenCalled()
  })

  it('should call onRephrasedUpdate with parsed suggestions', () => {
    const newObject = {
      rephrases: ['First suggestion', 'Second suggestion', 'Third suggestion'],
    }

    useObjectMock.mockReturnValue({
      object: newObject,
      submit: mockSubmit,
      isLoading: false,
    })

    const { rerender } = renderHook(() =>
      useScreeningRephrase({ ...defaultProps, onRephrasedUpdate })
    )

    rerender({})

    expect(onRephrasedUpdate).toHaveBeenCalledWith([
      'First suggestion',
      'Second suggestion',
      'Third suggestion',
    ])
  })

  it('should map isLoading to isStreaming', () => {
    useObjectMock.mockReturnValue({
      object: null,
      submit: mockSubmit,
      isLoading: true,
    })

    const { result } = renderHook(() =>
      useScreeningRephrase({ ...defaultProps, onRephrasedUpdate })
    )
    expect(result.current.isStreaming).toBe(true)
  })
})
