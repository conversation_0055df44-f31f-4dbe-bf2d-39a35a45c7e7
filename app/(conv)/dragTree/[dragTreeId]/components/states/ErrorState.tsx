'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import { Session } from 'next-auth'
import Header from '@/app/(conv)/dragTree/[dragTreeId]/components/layout/Header'
import { Button } from '@/components/ui/button'
import { AlertTriangle, RefreshCw } from 'lucide-react'

type ErrorStateProps = {
  session: Session | null
  errorMessage: string
  showSyncTest: boolean
  onToggleSyncTest: () => void
  onClearError: () => void
}

/**
 * Error State Component
 *
 * Displays error states with optional retry functionality.
 * Used across the application for consistent error handling.
 */
const ErrorState: React.FC<ErrorStateProps> = ({
  session,
  errorMessage,
  showSyncTest,
  onToggleSyncTest,
  onClearError,
}) => {
  const router = useRouter()

  const handleRetry = () => {
    onClearError()
    router.refresh() // Next.js best practice for refresh
  }

  return (
    <div className="flex flex-col h-screen w-full bg-white overflow-hidden animate-in fade-in duration-300">
      <div className="flex-shrink-0 border-b">
        <Header
          session={session}
          showSyncTest={showSyncTest}
          onToggleSyncTest={onToggleSyncTest}
        />
      </div>
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto animate-in slide-in-from-bottom-4 duration-500 delay-100">
          <div className="text-red-500 mb-4">
            <AlertTriangle className="h-12 w-12 mx-auto mb-4" />
          </div>
          <h2 className="text-xl font-semibold text-gray-700 mb-2">
            Unable to Load Clarification Tree
          </h2>
          <p className="text-gray-500 mb-6">{errorMessage}</p>
          <div className="space-y-2">
            <button
              onClick={() => router.push('/screening')}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Go to Screening Page
            </button>
            <Button
              onClick={handleRetry}
              variant="outline"
              className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Retry Loading
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

// Memoize to prevent unnecessary re-renders when props haven't changed
export default React.memo(ErrorState)
