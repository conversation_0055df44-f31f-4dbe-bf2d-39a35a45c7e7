// Export the main store
export { useDragTreeStore } from './store'

// Export utility types for consumers
export type { DatabaseDragTree } from './utils/database-utils'
export type { NodeContentItem } from './store'

// Export utility functions for potential external use
export {
  reconstructTreeFromDatabase,
  convertTreeToDbStructure,
} from './utils/database-utils'

export {
  calculateNodeLevel,
  findNodeById,
  buildNodeMap,
  collectInterestedNodes,
  validateTreeConstraints,
} from './utils/tree-utils'

export { createNodeMetadata, createNewTreeNode } from './utils/node-utils'

export { debouncedDbSync } from './utils/sync-utils'

// Performance-optimized selectors to prevent unnecessary re-renders
// Use these instead of accessing the full store state
export const dragTreeSelectors = {
  // Core tree data
  selectTreeStructure: (state: any) => state.frontendTreeStructure,
  selectScreeningQuestion: (state: any) => state.screeningQuestion,
  selectDragTreeId: (state: any) => state.dragTreeId,
  selectDragTreeTitle: (state: any) => state.dragTreeTitle,

  // Content management
  selectNodeContent: (state: any) => state.nodeContent,
  selectResearchedNodeIds: (state: any) => state.researchedNodeIds,
  selectContentFetchInProgress: (state: any) => state.contentFetchInProgress,

  // Performance optimizations
  selectNodeMap: (state: any) => state.nodeMap,
  selectPendingOperations: (state: any) => state.pendingDatabaseOperations,

  // Specific node content selector (prevents re-renders when other nodes change)
  selectNodeContentById: (nodeId: string) => (state: any) =>
    state.nodeContent.get(nodeId),

  // Check if node has research content
  selectHasNodeContent: (nodeId: string) => (state: any) =>
    state.nodeContent.has(nodeId) && state.nodeContent.get(nodeId)?.size > 0,

  // Get node from nodeMap (O(1) lookup)
  selectNodeById: (nodeId: string) => (state: any) => state.nodeMap.get(nodeId),
}
