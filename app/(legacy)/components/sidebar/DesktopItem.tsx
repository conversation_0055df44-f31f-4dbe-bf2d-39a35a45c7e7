import clsx from 'clsx'
import Link from 'next/link'

interface DesktopItemProps {
  label: string // The label to be displayed on the DesktopItem
  icon: any // The icon to be displayed on the DesktopItem
  href: string // The href for the Link component
  onClick?: () => void // Optional onClick function
  active?: boolean // Whether the DesktopItem is active or not
}

const DesktopItem: React.FC<DesktopItemProps> = ({
  label,
  href,
  icon: Icon,
  active,
  onClick,
}) => {
  // Handle the onClick event
  const handleClick = () => {
    if (onClick) {
      return onClick()
    }
  }

  // Render the DesktopItem component
  return (
    <li onClick={handleClick} key={label}>
      <Link
        href={href}
        className={clsx(
          `
                        group
                        flex
                        gap-x-3
                        rounded-md
                        p-3
                        text-sm
                        leading-6
                        font-semibold
                        text-gray-500
                        hover:text-black
                        hover:bg-gray-100
                    `,
          active && 'bg-gray-100 text-black'
        )}
      >
        <Icon className="h-6 w-6 shrink-0" aria-hidden="true" />
        <span className="sr-only">{label}</span>
      </Link>
    </li>
  )
}

export default DesktopItem
