# Legacy Server Actions

This folder contains server actions that are no longer part of the main application flow but are kept for legacy route support and potential future cleanup.

## Files Moved Here

### Unused Server Actions

- `feedback.ts` - Legacy feedback functionality (unused)
- `issue-tree.ts` - Legacy issue tree operations (unused)
- `log_openai_usage.ts` - Legacy OpenAI usage logging (replaced by `log_ai_usage.ts`)
- `product.ts` - Legacy product operations (unused)
- `rag.ts` - Legacy RAG functionality (unused)
- `search.ts` - Legacy search operations (unused)
- `subtree.ts` - Legacy subtree operations (unused)
- `user.ts` - Legacy user operations (minimal usage)

### Duplicate Server Actions

These existed in both `/app/server-actions/` and `/app/(legacy)/_actions/` as duplicates:

- `getConversationById.ts` - Get conversation by ID (legacy)
- `getConversations.ts` - Get conversations list (legacy)
- `getIssueTreeById.ts` - Get issue tree by ID (legacy)
- `getSession.ts` - Get user session (legacy)
- `getUsers.ts` - Get users list (legacy)

## Current Usage

Some of these files are still imported by:

- Legacy route handlers in `/app/api/_legacy/`
- Legacy components in `/app/(legacy)/`
- A few bridge components that support legacy functionality

## Cleanup Strategy

These files can be safely removed once:

1. All legacy routes are deprecated
2. Legacy conversation system is fully replaced
3. All imports are verified as unused

## Active Alternatives

The main application now uses:

- `/app/server-actions/drag-tree/` - Modern drag tree operations
- `/app/server-actions/ai-chat/` - Modern AI chat functionality
- `/app/server-actions/getCurrentUser.ts` - Modern user session
- `/app/server-actions/log_ai_usage.ts` - Modern AI usage logging
- `/app/server-actions/user-feedback.ts` - Modern user feedback
