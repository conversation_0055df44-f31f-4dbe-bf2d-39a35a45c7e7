name: Supabase nightly backup (dated file)

on:
  schedule:
    - cron: '15 2 * * 6' # At 2:15am every Saturday UTC
  workflow_dispatch: # manual “Run workflow” button

jobs:
  backup:
    runs-on: ubuntu-latest
    env:
      SUPABASE_DB_URL: ${{ secrets.SUPABASE_DB_URL }}
      SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
      SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}

    steps:
      - name: Install pg client
        run: sudo apt-get update && sudo apt-get -y install postgresql-client

      - name: Dump the database
        run: |
          pg_dump --dbname="$SUPABASE_DB_URL" \
                  --format=custom --compress=9 \
                  --file=db.dump

      - name: Upload to Supabase Storage
        run: |
          DATE=$(date +%Y%m%d)
          FILENAME="latest_${DATE}.dump"
          curl -X POST "$SUPABASE_URL/storage/v1/object/db-backups/$FILENAME" \
               -H "Authorization: Bearer $SUPABASE_SERVICE_ROLE_KEY" \
               -H "Content-Type: application/octet-stream" \
               -H "x-upsert: true" \     # overwrite if today file already exists
               --data-binary @db.dump    # <-- body of the upload
      - name: Clean up runner workspace
        run: rm db.dump
