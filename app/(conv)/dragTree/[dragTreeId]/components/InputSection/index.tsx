"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

type InputSectionProps = {
  onSubmit: (value: string) => void;
  disabled?: boolean;
};

export const InputSection: React.FC<InputSectionProps> = ({
  onSubmit,
  disabled = false,
}) => {
  const [inputValue, setInputValue] = useState<string>("");

  const handleSubmit = () => {
    if (inputValue.trim() && !disabled) {
      onSubmit(inputValue.trim());
      setInputValue("");
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  return (
    <div className="flex gap-1 py-2">
      <Input
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        onKeyPress={handleKeyPress}
        placeholder="Enter your prompt to generate content..."
        size={1}
        className="flex-1"
        disabled={disabled}
      />
      <Button
        onClick={handleSubmit}
        size="sm"
        // disabled={disabled || !inputValue.trim()}
        disabled={disabled}
      >
        {disabled ? "Generating..." : "Submit"}
      </Button>
    </div>
  );
};
