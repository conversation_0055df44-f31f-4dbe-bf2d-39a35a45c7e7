// // Import necessary modules
// import { Configuration, OpenAIApi } from "openai-edge";
// import { OpenAIStream, StreamingTextResponse } from "ai";
// import { NextResponse } from "next/server";
// import { Message } from "@prisma/client";
// import { LogAPIUsageDataType } from "@/app/types";
// import { OpenAIUsageType } from "@prisma/client";
// import { logAPIUsage } from "@/app/api/utils";

// // Create an OpenAI API client (that's edge friendly!)
// const config = new Configuration({
//   apiKey: process.env.OPENAI_API_KEY,
// });
// const openai = new OpenAIApi(config);

// // IMPORTANT! Set the runtime to edge
// export const runtime = "edge";

// // Define the POST function
// export async function POST(req: Request) {
//   try {
//     // Extract the `messages`, `conversationId`, and `currentUser` from the body of the request
//     const { messages, conversationId, currentUser } = await req.json();

//     const baseUrl =
//       process.env.NODE_ENV === "development"
//         ? "http://localhost:3000"
//         : process.env.NEXT_PUBLIC_SERVER_URL;

//     console.log("api/chat: ", messages, conversationId, currentUser);

//     // // // // // Helper functions // // // // //
//     // Define a function to save a message to the database
//     const saveMessage = async (data: any) => {
//       try {
//         const response = await fetch(`${baseUrl}/api/messages`, {
//           method: "POST",
//           headers: {
//             "Content-Type": "application/json",
//           },
//           body: JSON.stringify({
//             content: data.content,
//             conversationId,
//             role: data.role,
//             currentUser,
//           }),
//         });

//         if (!response.ok) {
//           throw new Error("Server response: " + response.status);
//         }

//         const result = await response.json();
//         console.log("Save message response:", result);
//       } catch (error) {
//         console.error("Error in saveMessage:", error);
//       }
//     };

//     // // Define a function to log Open AI API usage to the database
//     // const logAPIUsage = async (data: LogAPIUsageDataType) => {
//     //   try {
//     //     const response = await fetch(`${baseUrl}/api/record`, {
//     //       method: "POST",
//     //       headers: {
//     //         "Content-Type": "application/json",
//     //       },
//     //       body: JSON.stringify({
//     //         open_ai_usage_type: data.open_ai_usage_type,
//     //         model_name: data.model_name,
//     //         input_text: data.input_text,
//     //         output_text: data.output_text,
//     //         currentUser: currentUser,
//     //         conversationId: conversationId,
//     //       }),
//     //     });

//     //     if (!response.ok) {
//     //       throw new Error("Server response: " + response.status);
//     //     }

//     //     const result = await response.json();
//     //     console.log("Log API usage response:", result);
//     //   } catch (error) {
//     //     console.error("Error in logAPIUsage:", error);
//     //   }
//     // };

//     // Try to extract useful snippet from assistant message using this pattern
//     const extractRequirementSummary = (text: string): string | null => {
//       // make text same case and remove all colons
//       const cleanedText = text.replace(/:/g, "").toLowerCase();

//       // Define start and end patterns
//       const startPattern = "**requirement summary**";
//       const endPattern = "**analysis**";

//       const startIndex = cleanedText.indexOf(startPattern);
//       const endIndex = cleanedText.indexOf(endPattern, startIndex);

//       if (startIndex !== -1 && endIndex !== -1 && startIndex < endIndex) {
//         const summary = text
//           // remove all colons, ensure the index is aligned with cleanedText
//           .replace(/:/g, "")
//           .substring(startIndex + startPattern.length, endIndex)
//           .trim();
//         return summary;
//       }

//       return null;
//     };

//     // // // // // Construct the messages array to send to OpenAI // // // // //
//     let messagesArr = messages;

//     // Extract the last "available" requirement summary from the assistant message
//     const assistantMessages = messagesArr.filter(
//       (message: any) => message.role === "assistant"
//     );
//     console.log("assistantMessages", assistantMessages);

//     // This is to prevent the last assistant message doesn't contain the pattern, eg: user just type "hi"
//     // So we need to loop through the assistant messages from the last one
//     let extractedLastRequirementSummary = null;
//     for (let i = assistantMessages.length - 1; i >= 0; i--) {
//       const summary = extractRequirementSummary(assistantMessages[i].content);
//       if (summary) {
//         extractedLastRequirementSummary = summary;
//         break;
//       }
//     }
//     console.log(
//       "extractedLastRequirementSummary",
//       extractedLastRequirementSummary
//     );

//     // Get the system prompt message from the database to avoid leaking the prompt
//     const systemPromptMessageFetch = fetch(`${baseUrl}/api/messages/system`, {
//       method: "POST",
//       headers: {
//         "Content-Type": "application/json",
//       },
//       body: JSON.stringify({
//         currentUser: currentUser,
//         conversationId: conversationId,
//       }),
//     })
//       .then((res) => {
//         if (!res.ok) {
//           throw new Error(`Server responded with status ${res.status}`);
//         }
//         return res.json();
//       })
//       .then((data) => {
//         // handle the data returned from the server
//         const systemPromptMessage = data as Message;
//         let formattedSystemPromptMessage = null;
//         if (systemPromptMessage && "content" in systemPromptMessage) {
//           formattedSystemPromptMessage = {
//             content: systemPromptMessage.content,
//             role: "system",
//           };
//         }
//         return formattedSystemPromptMessage;
//       })
//       .catch((error) => {
//         // handle any errors
//         console.error(
//           "Error api/chat/systemPromptMessageFetch:",
//           error,
//           formattedSystemPromptMessage
//         );
//       });

//     // Get the system prompt message from the database
//     const systemPromptMessage = await systemPromptMessageFetch;
//     console.log("api/chat, systemPromptMessage: ", systemPromptMessage);

//     // Modify the system prompt message to include the last requirement summar if any
//     const formattedSystemPromptMessage = {
//       role: systemPromptMessage?.role || "system",
//       content: extractedLastRequirementSummary
//         ? systemPromptMessage?.content +
//           `\n\n!!!This is the last requirement summary we have, based on that to generate your next requirement summary which includes Every! detail below: ${extractedLastRequirementSummary}` +
//           "\n\nFirst line will be **Requirement Summary**; One text paragraph only and no markdown in **Analysis**; always come with examples and prioritize business scenario in **Questions**"
//         : systemPromptMessage?.content +
//           "\n\nFirst line will be **Requirement Summary**; One text paragraph only and no markdown in **Analysis**; always come with examples and prioritize business scenario in **Questions**",
//     };

//     console.log("api/chat: ", formattedSystemPromptMessage);

//     // Get the last user message from the `messages` array
//     const lastUserMessage = messagesArr
//       .filter((message: any) => message.role === "user")
//       .pop();

//     // If lastUserMessage exists, normal flow, save user message to the database
//     if (lastUserMessage) {
//       console.log("api/chat: lastUserMessage:", lastUserMessage);
//       // Save the last user message to the database
//       await saveMessage({
//         content: lastUserMessage.content,
//         role: "user",
//         conversationId,
//       });
//     }

//     // combine with system prompt to construct the messages array to send to OpenAI
//     if (formattedSystemPromptMessage) {
//       if (lastUserMessage) {
//         // TODO: paramaterize this as part of the system config, control by DB
//         // Save the tokens by loading only the last 4 messages and system prompt to API
//         // This should be fine because the assistant response contains the summary part
//         // Have one more extra round [2 more messages] as buffer
//         const messageToInclude = -4;
//         messagesArr = messagesArr
//           .filter((message: { role: string }) => message.role !== "system")
//           .slice(messageToInclude);
//         messagesArr = [formattedSystemPromptMessage, ...messagesArr];
//       } else if (!lastUserMessage) {
//         // if there is no lastUserMessage, just use the system prompt
//         // This is the scenario when the user just starts the conversation
//         messagesArr = [formattedSystemPromptMessage];
//       }
//     }

//     // Use more wider model if the input tokens is more than 3000 tokens/ ~12000 characters
//     const model_name =
//       JSON.stringify(messagesArr).length > 12000
//         ? "gpt-3.5-turbo-16k"
//         : "gpt-3.5-turbo";

//     // Ask OpenAI for a streaming chat completion given the prompt
//     console.log(
//       "api/chat: Number of Messages that go to OpenAI:",
//       messagesArr.length
//     );
//     const response = await openai.createChatCompletion({
//       model: model_name,
//       stream: true,
//       messages: messagesArr.map((message: any) => ({
//         content: message.content,
//         role: message.role,
//       })),
//     });

//     // Convert the response into a friendly text-stream
//     const stream = OpenAIStream(response, {
//       // Save the response
//       async onCompletion(completion) {
//         console.log("api/chat: OpenAIStream:onCompletion", completion);

//         await Promise.all([
//           // record the API usage to the database as audit log
//           logAPIUsage({
//             open_ai_usage_type: OpenAIUsageType.CHAT,
//             model_name: model_name,
//             input_text: JSON.stringify(messagesArr),
//             output_text: completion,
//             currentUser: currentUser,
//             conversationId: conversationId,
//           }),
//           // Save the assistant's response to the database
//           saveMessage({
//             content: completion,
//             role: "assistant",
//             conversationId,
//           }),
//         ]);
//       },
//     });

//     // Respond with the stream
//     return new StreamingTextResponse(stream);
//   } catch (error) {
//     // Log any errors to the console and return a 500 error response
//     console.log("api/chat: error:", error);
//     return new NextResponse("Error", { status: 500 });
//   }
// }
