'use client'

import React from 'react'
import { cn } from '@/lib/utils'

type LoadingOverlayProps = {
  isVisible: boolean
  title?: string
  subtitle?: string
  className?: string
}

/**
 * Reusable loading overlay component with backdrop blur and spinner
 * Used during AI generation processes and other loading states
 */
export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isVisible,
  title = 'AI Processing',
  subtitle = 'Generating content...',
  className = '',
}) => {
  if (!isVisible) return null

  return (
    <section
      className={cn(
        'absolute inset-0 bg-white/60 backdrop-blur-sm flex items-center justify-center z-50',
        className
      )}
      role="status"
      aria-live="polite"
      aria-label="Loading content"
    >
      <div className="bg-white rounded-xl shadow-xl border-2 border-amber-200 p-6 flex items-center gap-4">
        <div
          className="animate-spin h-8 w-8 border-4 border-amber-500 border-t-transparent rounded-full"
          role="progressbar"
          aria-label="Loading spinner"
        />
        <div className="text-center">
          <h3 className="font-semibold text-slate-800">{title}</h3>
          <p className="text-sm text-slate-600">{subtitle}</p>
        </div>
      </div>
    </section>
  )
}
