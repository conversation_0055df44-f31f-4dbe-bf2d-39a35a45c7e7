'use client'

import useConversation from '@/app/(legacy)/_hooks/useConversation'
import useRoutes from '@/app/(legacy)/_hooks/useRoutes'
import MobileItem from './MobileItem'

// This component is responsible for rendering the mobile footer of the app.
const MobileFooter = () => {
  const routes = useRoutes()
  const { isOpen } = useConversation()

  // If a conversation is open, do not render the footer.
  if (isOpen) {
    return null
  }

  // Render the mobile footer with the routes provided by useRoutes hook.
  return (
    // The following CSS classes are applied to the mobile footer:
    // - fixed: The footer is fixed to the bottom of the screen.
    // - justify-between: The items inside the footer are evenly distributed with space between them.
    // - w-full: The footer takes up the full width of the screen.
    // - bottom-0: The footer is positioned at the bottom of the screen.
    // - z-40: The footer has a z-index of 40, which places it above other elements on the page.
    // - flex: The items inside the footer are displayed as a flex container.
    // - items-center: The items inside the footer are vertically centered.
    // - bg-white: The background color of the footer is white.
    // - border-t-[1px]: The footer has a 1 pixel border at the top.
    // - lg:hidden: The footer is hidden on large screens.
    <div
      className="
                fixed
                justify-between
                w-full
                bottom-0
                z-40
                flex
                items-center
                bg-white
                border-t-[1px]
                lg:hidden
            "
    >
      {routes.map(route => (
        <MobileItem
          key={route.href}
          href={route.href}
          active={route.active}
          icon={route.icon}
          onClick={route.onClick}
        />
      ))}
    </div>
  )
}

export default MobileFooter
