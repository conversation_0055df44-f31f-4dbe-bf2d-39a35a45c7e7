'use client'

import React from 'react'
import { Badge } from '@/components/ui/badge'
import { Spa<PERSON>les, Zap } from 'lucide-react'
import { SubscriptionTier } from '@prisma/client'
import { hasPaidFeatures } from '@/app/configs/tier-permissions'

type ModernSubscriptionHeaderProps = {
  userTier?: SubscriptionTier
  userName?: string
}

export const ModernSubscriptionHeader: React.FC<
  ModernSubscriptionHeaderProps
> = ({ userTier = SubscriptionTier.FREE, userName }) => {
  const isPaidUser = hasPaidFeatures(userTier)

  return (
    <div className="text-center py-12 px-4">
      <div className="max-w-3xl mx-auto">
        {/* Status Badge - Only show for paid users */}
        {isPaidUser && (
          <div className="flex items-center justify-center mb-6">
            <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-2 text-sm font-medium">
              <Sparkles className="w-4 h-4 mr-2" />
              {userTier === SubscriptionTier.GUEST
                ? 'Guest Access'
                : 'Pro Member'}
            </Badge>
          </div>
        )}

        {/* Main Heading */}
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
          {isPaidUser ? (
            <>
              Welcome back{userName ? `, ${userName.split(' ')[0]}` : ''}!
              <span className="block text-purple-600 mt-2">
                You&apos;re all set with Pro features
              </span>
            </>
          ) : (
            <>
              Unlock the Full Power of
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600 mt-2">
                AI-Powered Research
              </span>
            </>
          )}
        </h1>

        {/* Subtitle */}
        <p className="text-xl text-gray-600 mb-8 leading-relaxed">
          {isPaidUser
            ? userTier === SubscriptionTier.GUEST
              ? 'You have complimentary access to all Pro features. Enjoy the full experience!'
              : 'You have access to all Pro features including advanced AI capabilities, priority support, and more.'
            : 'Transform your research workflow with advanced AI features, unlimited drag trees, and priority support.'}
        </p>

        {/* Feature Highlights */}
        {!isPaidUser && (
          <div className="flex flex-wrap justify-center gap-6 text-sm text-gray-600">
            <div className="flex items-center">
              <Zap className="w-4 h-4 text-purple-500 mr-2" />
              <span>Unlimited Drag Trees</span>
            </div>
            <div className="flex items-center">
              <Zap className="w-4 h-4 text-purple-500 mr-2" />
              <span>Quick Research</span>
            </div>
            <div className="flex items-center">
              <Zap className="w-4 h-4 text-purple-500 mr-2" />
              <span>Advanced AI Features</span>
            </div>
            <div className="flex items-center">
              <Zap className="w-4 h-4 text-purple-500 mr-2" />
              <span>Priority Support</span>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
