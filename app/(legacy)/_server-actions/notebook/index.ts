'use server'

import prisma from '@/app/libs/prismadb'
import { Notebook, NotebookStatus, NotebookType, Prisma } from '@prisma/client'

export type createNotebookType = {
  userId: string
  issueTreeId: string
  conversationId: string
  title: string
  full_prompt: string
  config: Prisma.JsonValue
  remark: string
}

export const createNotebook = async ({
  userId,
  issueTreeId,
  conversationId,
  title,
  full_prompt,
  config,
  remark,
}: createNotebookType) => {
  if (
    !userId ||
    !issueTreeId ||
    !conversationId ||
    !title ||
    !full_prompt ||
    !remark ||
    !config
  ) {
    throw new Error('Invalid input')
  }
  try {
    const newNotebook = await prisma.notebook.create({
      data: {
        user_id: userId,
        conversation_id: conversationId,
        issue_tree_id: issueTreeId,
        status: NotebookStatus.INITIALIZED,
        type: NotebookType.BASE,
        title: title,
        generation_input: full_prompt,
        remark: remark,
        config: config,
      },
    })
    return newNotebook
  } catch (error) {
    console.error('Error creating notebook:', error)
    throw error
  }
}

export const getNotebookById = async (notebookId: string) => {
  if (!notebookId) {
    throw new Error('Invalid input')
  }
  try {
    const notebook = await prisma.notebook.findUnique({
      where: { id: notebookId },
    })
    return notebook
  } catch (error) {
    console.error('Error getting notebook by id:', error)
    throw error
  }
}

export const getNotebookStatusById = async (notebookId: string) => {
  if (!notebookId) {
    throw new Error('Invalid input')
  }
  try {
    const notebook = await prisma.notebook.findUnique({
      where: { id: notebookId },
      select: {
        id: true,
        status: true,
        content: true,
        generation_output: true,
      },
    })
    return notebook
  } catch (error) {
    console.error('Error getting notebook status by id:', error)
    throw error
  }
}

export const getNotebookTitleById = async (notebookId: string) => {
  if (!notebookId) {
    throw new Error('Invalid input')
  }
  try {
    const notebook = await prisma.notebook.findUnique({
      where: { id: notebookId },
      select: { id: true, title: true },
    })
    return notebook
  } catch (error) {
    console.error('Error getting notebook title by id:', error)
    throw error
  }
}
export const updateNotebook = async (
  notebookId: string,
  data: Partial<Omit<Notebook, 'config'>> & { config?: Prisma.JsonValue }
) => {
  if (!notebookId) {
    throw new Error('Invalid input')
  }
  try {
    const { config, ...otherData } = data

    const updateData: Prisma.NotebookUpdateInput = {
      ...otherData,
      ...(config && { config: JSON.parse(JSON.stringify(config)) }),
    }

    console.log(
      `updateNotebook server action ${notebookId}`,
      JSON.stringify(updateData).length
    )

    const updatedNotebook = await prisma.notebook.update({
      where: { id: notebookId },
      data: updateData,
    })
    return updatedNotebook
  } catch (error) {
    console.error('Error updating notebook:', error)
    throw error
  }
}

export const fetchNotebooks = async (conversationId: string) => {
  if (!conversationId) {
    throw new Error('Invalid input')
  }
  try {
    const notebooks = await prisma.notebook.findMany({
      where: { conversation_id: conversationId },
      select: {
        id: true,
        title: true,
        status: true,
        updated_at: true,
      },
      orderBy: { updated_at: 'desc' },
    })
    return notebooks
  } catch (error) {
    console.error('Error getting notebooks by conversation id:', error)
    throw error
  }
}
