import { NextResponse } from 'next/server'
import prisma from '@/app/libs/prismadb'

export async function POST(req: Request) {
  try {
    const {
      issueTreeId,
      currentUser: _currentUser,
      ...updateData
    } = await req.json()
    console.log('updateData', issueTreeId, updateData)

    // TODO: Uncomment and implement user authentication if needed
    // if (!isValidUser(currentUser)) {
    //   return new NextResponse("Unauthorized", { status: 400 });
    // }
    // if (!isActiveUser(currentUser)) {
    //   return new NextResponse("Unauthorized", { status: 401 });
    // }

    const updatedIssueTree = await updateIssueTree(issueTreeId, updateData)
    return NextResponse.json(updatedIssueTree)
  } catch (error) {
    console.error('Error in api/issuetree/update:', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}

// Helper functions

async function updateIssueTree(issueTreeId: string, updateData: any) {
  return prisma.issueTree.update({
    where: { id: issueTreeId },
    data: updateData,
  })
}

// Uncomment and implement these functions if user authentication is needed
// function isValidUser(user: any): boolean {
//   return user?.id && user?.email;
// }

// function isActiveUser(user: any): boolean {
//   return user?.status === UserStatus.ACTIVE;
// }
