'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'

import { youtubeEmbedUrlId } from '@/app/configs'
import LoadingPage from '@/app/components/LoadingPage'
import {
  <PERSON><PERSON>,
  <PERSON>,
  Footer,
  StaticLogoCloud,
} from '@/app/(landing)/components/LandingComponents'

import dynamic from 'next/dynamic'

// Dynamically import the below-the-fold sections to reduce the initial JS bundle.
// These components contain heavier animations and are not required for the very first paint.
// They are still server-rendered (ssr: true) to preserve SEO while splitting the client bundle.
const HowItWorks = dynamic(
  () =>
    import('@/app/(landing)/components/LandingComponents').then(
      mod => mod.HowItWorks
    ),
  {
    ssr: false,
    loading: () => (
      <div className="py-20 text-center text-gray-400">
        Loading How It Works...
      </div>
    ),
  }
)

const Benefits = dynamic(
  () =>
    import('@/app/(landing)/components/LandingComponents').then(
      mod => mod.Benefits
    ),
  {
    ssr: false,
    loading: () => (
      <div className="py-20 text-center text-gray-400">Loading Benefits...</div>
    ),
  }
)

const FAQ = dynamic(
  () =>
    import('@/app/(landing)/components/LandingComponents').then(mod => mod.FAQ),
  {
    ssr: false,
    loading: () => (
      <div className="py-20 text-center text-gray-400">Loading FAQ...</div>
    ),
  }
)

export default function LandingPage() {
  const { status } = useSession()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [scrollY, setScrollY] = useState<number>(0)

  useEffect(() => {
    if (status === 'loading') {
      setIsLoading(true)
    } else if (status === 'authenticated') {
      // Use replace to avoid navigating back to landing page
      router.replace('/screening')
    } else {
      setIsLoading(false)
    }
  }, [status, router])

  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY)
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  useEffect(() => {
    window.scrollTo(0, 0)
  }, [])

  if (isLoading) {
    return <LoadingPage />
  }

  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-br from-gray-900 to-blue-900 text-white overflow-hidden">
      <div className="relative z-10">
        <Header />
        <main className="flex-grow pt-16">
          <Hero youtubeEmbedUrlId={youtubeEmbedUrlId} scrollY={scrollY} />
          <StaticLogoCloud />
          <HowItWorks />
          <Benefits />
          <FAQ />
        </main>
        <Footer />
      </div>
    </div>
  )
}
