/*
  Web Worker for layout computation (Dagre / EL<PERSON> Radial)
  -----------------------------------------------------
  This worker receives a message containing:
    { id: string, nodes: Node[], edges: Edge[], layoutMode: 'linear' | 'radial' }

  and returns a message:
    { id: string, nodes: Node[], edges: Edge[] }
*/

// NOTE: We deliberately only import *types* from `reactflow` so that the heavy React Flow
// runtime is not bundled into the worker. The core layout utilities already import the
// required runtime for MarkerType, etc. If this causes issues, consider refactoring the
// utils to avoid that dependency.
import type { Node, Edge } from 'reactflow'

import { getDagreLayoutedElements } from '../utils/dagreAutoLayout'
import { getRadialLayoutedElements } from '../utils/radialAutoLayout'

// eslint-disable-next-line no-restricted-globals
self.addEventListener('message', async (event: MessageEvent) => {
  const { id, nodes, edges, layoutMode } = event.data as {
    id: string
    nodes: Node[]
    edges: Edge[]
    layoutMode: 'linear' | 'radial'
  }

  try {
    let result: { nodes: Node[]; edges: Edge[] }
    if (layoutMode === 'radial') {
      result = await getRadialLayoutedElements(nodes, edges)
    } else {
      result = getDagreLayoutedElements(nodes, edges)
    }

    // eslint-disable-next-line no-restricted-globals
    ;(self as any).postMessage({ id, ...result })
  } catch (error: any) {
    console.error('[layoutWorker] Layout computation failed:', error)
    // eslint-disable-next-line no-restricted-globals
    ;(self as any).postMessage({
      id,
      error: error?.message ?? 'Unknown error',
    })
  }
})
