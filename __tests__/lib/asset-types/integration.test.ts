import { describe, it, expect, beforeEach } from '@jest/globals'
import React from 'react'
import { FiFile, FiImage } from 'react-icons/fi'
import {
  assetTypeRegistry,
  registerAssetType,
  getAssetType,
  isValidAssetType,
  type AssetTypeDefinition,
} from '@/lib/asset-types/registry'
import { generateAssetType, chatAssetType } from '@/lib/asset-types'

// Mock component for testing
const MockTabComponent: React.FC<any> = () => {
  return React.createElement('div', {}, 'Mock Component')
}

// Test asset type definition
const documentAssetType: AssetTypeDefinition = {
  id: 'document',
  displayName: 'Document',
  icon: FiFile,
  tabComponent: MockTabComponent,
  getAssetTypeColor: () => 'text-purple-600',
  createTabTitle: (asset: any) => `Document - ${asset.title}`,
  isAssetType: (type: string) => type === 'document',
  createAssetTabData: (asset: any) => ({
    type: 'document',
    model: asset.model,
    prompt: asset.prompt,
    contextIds: asset.contextIds || [],
    settings: {},
    documentType: asset.documentType || 'general',
  }),
}

describe('Asset Type System Integration', () => {
  beforeEach(() => {
    // Clear registry and register built-in types
    assetTypeRegistry.clear()
    registerAssetType(generateAssetType)
    registerAssetType(chatAssetType)
  })

  describe('Built-in Asset Types', () => {
    it('should have generate asset type registered', () => {
      expect(isValidAssetType('generate')).toBe(true)

      const generateType = getAssetType('generate')
      expect(generateType?.id).toBe('generate')
      expect(generateType?.displayName).toBe('Generate')
      expect(generateType?.getAssetTypeColor()).toBe('text-blue-600')
    })

    it('should have chat asset type registered', () => {
      expect(isValidAssetType('chat')).toBe(true)

      const chatType = getAssetType('chat')
      expect(chatType?.id).toBe('chat')
      expect(chatType?.displayName).toBe('Chat')
      expect(chatType?.getAssetTypeColor()).toBe('text-green-600')
    })

    it('should create proper tab titles for built-in types', () => {
      const generateType = getAssetType('generate')
      const chatType = getAssetType('chat')

      const mockAsset = { title: 'Test Asset' }

      expect(generateType?.createTabTitle(mockAsset)).toBe(
        'Generate - Test Asset'
      )
      expect(chatType?.createTabTitle(mockAsset)).toBe('Test Asset')
    })

    it('should create proper tab data for built-in types', () => {
      const generateType = getAssetType('generate')
      const chatType = getAssetType('chat')

      const mockAsset = {
        id: 'asset-123',
        model: 'gpt-4',
        prompt: 'Test prompt',
        contextIds: ['ctx-1'],
        content: 'Test content',
        messages: [{ role: 'user', content: 'Hello' }],
      }

      const generateTabData = generateType?.createAssetTabData?.(mockAsset)
      expect(generateTabData?.type).toBe('generate')
      expect(generateTabData?.generationPhase).toBe('completed')
      expect(generateTabData?.assetContent).toBe('Test content')

      const chatTabData = chatType?.createAssetTabData?.(mockAsset)
      expect(chatTabData?.type).toBe('chat')
      expect(chatTabData?.conversationId).toBe('asset-123')
      expect(chatTabData?.assetMessages).toEqual(mockAsset.messages)
    })
  })

  describe('Extensibility', () => {
    it('should allow registering new asset types', () => {
      // Initially document type should not exist
      expect(isValidAssetType('document')).toBe(false)

      // Register new type
      registerAssetType(documentAssetType)

      // Now it should be available
      expect(isValidAssetType('document')).toBe(true)
      expect(getAssetType('document')).toBe(documentAssetType)
    })

    it('should support multiple custom asset types', () => {
      const presentationType: AssetTypeDefinition = {
        id: 'presentation',
        displayName: 'Presentation',
        icon: FiImage,
        tabComponent: MockTabComponent,
        getAssetTypeColor: () => 'text-orange-600',
        createTabTitle: (asset: any) => `Presentation - ${asset.title}`,
        isAssetType: (type: string) => type === 'presentation',
      }

      // Register multiple types
      registerAssetType(documentAssetType)
      registerAssetType(presentationType)

      // All should be available
      expect(isValidAssetType('generate')).toBe(true)
      expect(isValidAssetType('chat')).toBe(true)
      expect(isValidAssetType('document')).toBe(true)
      expect(isValidAssetType('presentation')).toBe(true)
      expect(isValidAssetType('unknown')).toBe(false)

      // Should have correct properties
      expect(getAssetType('document')?.displayName).toBe('Document')
      expect(getAssetType('presentation')?.displayName).toBe('Presentation')
    })

    it('should handle asset type overwriting', () => {
      registerAssetType(documentAssetType)

      const updatedDocumentType: AssetTypeDefinition = {
        ...documentAssetType,
        displayName: 'Updated Document',
        getAssetTypeColor: () => 'text-red-600',
      }

      registerAssetType(updatedDocumentType)

      const retrievedType = getAssetType('document')
      expect(retrievedType?.displayName).toBe('Updated Document')
      expect(retrievedType?.getAssetTypeColor()).toBe('text-red-600')
    })
  })

  describe('Type Validation', () => {
    it('should validate known types correctly', () => {
      registerAssetType(documentAssetType)

      // Core types should not be valid asset types
      expect(isValidAssetType('main')).toBe(false)
      expect(isValidAssetType('research')).toBe(false)

      // Registered asset types should be valid
      expect(isValidAssetType('generate')).toBe(true)
      expect(isValidAssetType('chat')).toBe(true)
      expect(isValidAssetType('document')).toBe(true)

      // Unknown types should be invalid
      expect(isValidAssetType('unknown')).toBe(false)
      expect(isValidAssetType('')).toBe(false)
    })

    it('should handle edge cases', () => {
      expect(isValidAssetType(null as any)).toBe(false)
      expect(isValidAssetType(undefined as any)).toBe(false)
      expect(getAssetType('non-existent')).toBeUndefined()
    })
  })

  describe('Asset Type Properties', () => {
    beforeEach(() => {
      registerAssetType(documentAssetType)
    })

    it('should provide correct type identification', () => {
      const documentType = getAssetType('document')

      expect(documentType?.isAssetType('document')).toBe(true)
      expect(documentType?.isAssetType('generate')).toBe(false)
      expect(documentType?.isAssetType('chat')).toBe(false)
    })

    it('should create custom tab data correctly', () => {
      const documentType = getAssetType('document')

      const mockAsset = {
        id: 'doc-123',
        title: 'My Document',
        model: 'gpt-4',
        prompt: 'Create document',
        contextIds: ['ctx-1'],
        documentType: 'report',
      }

      const tabData = documentType?.createAssetTabData?.(mockAsset)

      expect(tabData).toEqual({
        type: 'document',
        model: 'gpt-4',
        prompt: 'Create document',
        contextIds: ['ctx-1'],
        settings: {},
        documentType: 'report',
      })
    })

    it('should handle missing optional fields', () => {
      const documentType = getAssetType('document')

      const minimalAsset = {
        id: 'doc-123',
        title: 'My Document',
        model: 'gpt-4',
        prompt: 'Create document',
      }

      const tabData = documentType?.createAssetTabData?.(minimalAsset)

      expect(tabData?.contextIds).toEqual([])
      expect((tabData as any)?.documentType).toBe('general')
    })
  })

  describe('Registry State Management', () => {
    it('should maintain registry state correctly', () => {
      expect(assetTypeRegistry.getRegisteredTypes()).toContain('generate')
      expect(assetTypeRegistry.getRegisteredTypes()).toContain('chat')

      registerAssetType(documentAssetType)

      const types = assetTypeRegistry.getRegisteredTypes()
      expect(types).toContain('generate')
      expect(types).toContain('chat')
      expect(types).toContain('document')
      expect(types).toHaveLength(3)
    })

    it('should provide all definitions', () => {
      registerAssetType(documentAssetType)

      const definitions = assetTypeRegistry.getAllDefinitions()
      expect(definitions).toHaveLength(3)
      expect(definitions.map(d => d.id)).toEqual([
        'generate',
        'chat',
        'document',
      ])
    })

    it('should support registry cleanup', () => {
      registerAssetType(documentAssetType)
      expect(assetTypeRegistry.getRegisteredTypes()).toHaveLength(3)

      assetTypeRegistry.clear()
      expect(assetTypeRegistry.getRegisteredTypes()).toHaveLength(0)
      expect(isValidAssetType('generate')).toBe(false)
      expect(isValidAssetType('document')).toBe(false)
    })
  })
})
