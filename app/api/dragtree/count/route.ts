import { NextResponse } from 'next/server'
import prisma from '@/app/libs/prismadb'
import { auth } from '@/auth'

export async function GET() {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ count: 0 })
    }
    const count = await prisma.dragTree.count({
      where: { user_id: session.user.id },
    })
    return NextResponse.json({ count })
  } catch (err) {
    return NextResponse.json({ count: 0 })
  }
}
