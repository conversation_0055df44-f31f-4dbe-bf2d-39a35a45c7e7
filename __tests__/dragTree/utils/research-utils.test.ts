/**
 * Tests for Research Data Logic and Utilities
 *
 * Tests the research-related utility functions including:
 * - Search results validation
 * - Metadata processing
 * - Type guards
 * - Data transformation functions
 */

import {
  hasSearchResults,
  isValidSearchResult,
  type SearchMetadata,
} from '@/app/(conv)/dragTree/[dragTreeId]/components/SearchResultsDisplay'

describe('Research Data Utilities', () => {
  describe('hasSearchResults', () => {
    const validSearchResult: SearchMetadata = {
      keyword: 'test query',
      url: 'https://example.com',
      icon: 'https://example.com/icon.png',
      snippets: ['Test snippet 1', 'Test snippet 2'],
      timestamp: '2024-01-01T00:00:00Z',
      source: 'brave_search',
    }

    it('should return true for valid metadata with search results', () => {
      const metadata = {
        searchResults: [validSearchResult],
        otherData: 'some value',
      }

      expect(hasSearchResults(metadata)).toBe(true)
    })

    it('should return falsy for null metadata', () => {
      expect(hasSearchResults(null)).toBeFalsy()
    })

    it('should return falsy for undefined metadata', () => {
      expect(hasSearchResults(undefined)).toBeFalsy()
    })

    it('should return false for non-object metadata', () => {
      expect(hasSearchResults('string')).toBe(false)
      expect(hasSearchResults(123)).toBe(false)
      expect(hasSearchResults(true)).toBe(false)
    })

    it('should return false when searchResults is missing', () => {
      const metadata = {
        otherData: 'some value',
      }

      expect(hasSearchResults(metadata)).toBe(false)
    })

    it('should return false when searchResults is not an array', () => {
      const metadata = {
        searchResults: 'not an array',
      }

      expect(hasSearchResults(metadata)).toBe(false)
    })

    it('should return false when searchResults is empty array', () => {
      const metadata = {
        searchResults: [],
      }

      expect(hasSearchResults(metadata)).toBe(false)
    })

    it('should return false when searchResults contains invalid items', () => {
      const metadata = {
        searchResults: [
          {
            // Missing required fields
            keyword: 'test',
          },
        ],
      }

      expect(hasSearchResults(metadata)).toBe(false)
    })

    it('should return false when some search results are invalid', () => {
      const metadata = {
        searchResults: [
          validSearchResult,
          {
            // Invalid item
            keyword: 'test',
            // missing url and snippets
          },
        ],
      }

      expect(hasSearchResults(metadata)).toBe(false)
    })

    it('should return true when all search results are valid', () => {
      const metadata = {
        searchResults: [
          validSearchResult,
          {
            keyword: 'another query',
            url: 'https://another.com',
            snippets: ['Another snippet'],
            timestamp: '2024-01-02T00:00:00Z',
            source: 'brave_search' as const,
          },
        ],
      }

      expect(hasSearchResults(metadata)).toBe(true)
    })

    it('should handle search results without optional fields', () => {
      const minimalResult = {
        keyword: 'test query',
        url: 'https://example.com',
        snippets: ['Test snippet'],
        timestamp: '2024-01-01T00:00:00Z',
        source: 'brave_search' as const,
        // icon is optional
      }

      const metadata = {
        searchResults: [minimalResult],
      }

      expect(hasSearchResults(metadata)).toBe(true)
    })
  })

  describe('isValidSearchResult', () => {
    it('should return true for valid search result', () => {
      const validResult: SearchMetadata = {
        keyword: 'test query',
        url: 'https://example.com',
        icon: 'https://example.com/icon.png',
        snippets: ['Test snippet'],
        timestamp: '2024-01-01T00:00:00Z',
        source: 'brave_search',
      }

      expect(isValidSearchResult(validResult)).toBe(true)
    })

    it('should return falsy for null or undefined', () => {
      expect(isValidSearchResult(null)).toBeFalsy()
      expect(isValidSearchResult(undefined)).toBeFalsy()
    })

    it('should return false for non-object types', () => {
      expect(isValidSearchResult('string')).toBe(false)
      expect(isValidSearchResult(123)).toBe(false)
      expect(isValidSearchResult(true)).toBe(false)
      expect(isValidSearchResult([])).toBe(false)
    })

    it('should return false when required fields are missing', () => {
      expect(
        isValidSearchResult({
          // Missing all required fields
        })
      ).toBe(false)

      expect(
        isValidSearchResult({
          keyword: 'test',
          // Missing url, snippets
        })
      ).toBe(false)

      expect(
        isValidSearchResult({
          keyword: 'test',
          url: 'https://example.com',
          // Missing snippets
        })
      ).toBe(false)
    })

    it('should return false when required fields have wrong types', () => {
      expect(
        isValidSearchResult({
          keyword: 123, // Should be string
          url: 'https://example.com',
          snippets: ['test'],
        })
      ).toBe(false)

      expect(
        isValidSearchResult({
          keyword: 'test',
          url: 123, // Should be string
          snippets: ['test'],
        })
      ).toBe(false)

      expect(
        isValidSearchResult({
          keyword: 'test',
          url: 'https://example.com',
          snippets: 'not an array', // Should be array
        })
      ).toBe(false)
    })

    it('should handle optional fields correctly', () => {
      // Valid without optional fields
      expect(
        isValidSearchResult({
          keyword: 'test',
          url: 'https://example.com',
          snippets: ['test'],
          timestamp: '2024-01-01T00:00:00Z',
          source: 'brave_search',
        })
      ).toBe(true)

      // Valid with optional icon field
      expect(
        isValidSearchResult({
          keyword: 'test',
          url: 'https://example.com',
          snippets: ['test'],
          timestamp: '2024-01-01T00:00:00Z',
          source: 'brave_search',
          icon: 'https://example.com/icon.png',
        })
      ).toBe(true)
    })

    it('should validate snippets array content', () => {
      // Empty snippets array should be valid
      expect(
        isValidSearchResult({
          keyword: 'test',
          url: 'https://example.com',
          snippets: [],
          timestamp: '2024-01-01T00:00:00Z',
          source: 'brave_search',
        })
      ).toBe(true)

      // Array with string elements should be valid
      expect(
        isValidSearchResult({
          keyword: 'test',
          url: 'https://example.com',
          snippets: ['snippet 1', 'snippet 2'],
          timestamp: '2024-01-01T00:00:00Z',
          source: 'brave_search',
        })
      ).toBe(true)
    })
  })

  describe('Edge Cases and Data Integrity', () => {
    it('should handle deeply nested invalid data', () => {
      const deeplyNestedInvalid = {
        searchResults: [
          {
            keyword: 'test',
            url: 'https://example.com',
            snippets: [
              'valid snippet',
              null, // Invalid snippet
              'another valid snippet',
            ],
          },
        ],
      }

      // The type guard should still work even with invalid nested data
      // as it only checks the basic structure
      expect(hasSearchResults(deeplyNestedInvalid)).toBe(true)
    })

    it('should handle circular references gracefully', () => {
      const circular: any = {
        keyword: 'test',
        url: 'https://example.com',
        snippets: ['test'],
      }
      circular.self = circular

      const metadata = {
        searchResults: [circular],
      }

      // Should not throw an error
      expect(() => hasSearchResults(metadata)).not.toThrow()
    })

    it('should handle very large datasets', () => {
      const largeSearchResults = Array.from({ length: 1000 }, (_, i) => ({
        keyword: `query ${i}`,
        url: `https://example${i}.com`,
        snippets: [`snippet ${i}`],
        timestamp: '2024-01-01T00:00:00Z',
        source: 'brave_search' as const,
      }))

      const metadata = {
        searchResults: largeSearchResults,
      }

      expect(hasSearchResults(metadata)).toBe(true)
    })

    it('should validate URL formats correctly', () => {
      const invalidUrlResult = {
        keyword: 'test',
        url: 'not-a-valid-url',
        snippets: ['test'],
        timestamp: '2024-01-01T00:00:00Z',
        source: 'brave_search' as const,
      }

      // Type guard only checks for string type, not URL validity
      expect(isValidSearchResult(invalidUrlResult)).toBe(true)
    })

    it('should handle mixed valid and invalid results in validation', () => {
      const mixedResults = [
        {
          keyword: 'valid',
          url: 'https://valid.com',
          snippets: ['valid snippet'],
          timestamp: '2024-01-01T00:00:00Z',
          source: 'brave_search' as const,
        },
        {
          keyword: 'invalid',
          // Missing required fields
        },
        {
          keyword: 'another valid',
          url: 'https://another-valid.com',
          snippets: ['another snippet'],
          timestamp: '2024-01-01T00:00:00Z',
          source: 'brave_search' as const,
        },
      ]

      const metadata = {
        searchResults: mixedResults,
      }

      // Should return false because not all results are valid
      expect(hasSearchResults(metadata)).toBe(false)
    })
  })

  describe('Type Guard Accuracy', () => {
    it('should narrow types correctly when hasSearchResults returns true', () => {
      const metadata: unknown = {
        searchResults: [
          {
            keyword: 'test',
            url: 'https://example.com',
            snippets: ['test'],
            timestamp: '2024-01-01T00:00:00Z',
            source: 'brave_search',
          },
        ],
      }

      if (hasSearchResults(metadata)) {
        // TypeScript should narrow the type here
        expect(Array.isArray(metadata.searchResults)).toBe(true)
        expect(metadata.searchResults[0].keyword).toBe('test')
        expect(metadata.searchResults[0].url).toBe('https://example.com')
      }
    })

    it('should work as a filter predicate', () => {
      const metadataArray = [
        {
          searchResults: [
            {
              keyword: 'test',
              url: 'https://example.com',
              snippets: ['test'],
              timestamp: '2024-01-01T00:00:00Z',
              source: 'brave_search' as const,
            },
          ],
        },
        { otherData: 'no search results' },
        { searchResults: [] },
        {
          searchResults: [
            {
              keyword: 'test2',
              url: 'https://example2.com',
              snippets: ['test2'],
              timestamp: '2024-01-01T00:00:00Z',
              source: 'brave_search' as const,
            },
          ],
        },
      ]

      const withSearchResults = metadataArray.filter(hasSearchResults)

      expect(withSearchResults).toHaveLength(2)
      expect(withSearchResults[0].searchResults[0].keyword).toBe('test')
      expect(withSearchResults[1].searchResults[0].keyword).toBe('test2')
    })
  })
})
