// This is to create the new conversation, this time not chat, but issue tree

import getCurrentUser from '@/app/server-actions/_legacy/getCurrentUser'
import { NextResponse } from 'next/server'

import prisma from '@/app/libs/prismadb'
import { UserStatus } from '@prisma/client'

export async function POST(request: Request) {
  try {
    const currentUser = await getCurrentUser()
    const body = await request.json()
    const { conversationId } = body

    if (!currentUser?.id || !currentUser?.email) {
      return new NextResponse('Unauthorized', { status: 400 })
    }

    // Block them from accessing API if they are not active
    if (currentUser?.status !== UserStatus.ACTIVE) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    const allFeedbacks = await prisma.feedback.findMany({
      where: {
        conversation_id: conversationId,
      },
    })

    return NextResponse.json(allFeedbacks)
  } catch {
    return new NextResponse('Internal Error', { status: 500 })
  }
}
