'use client'

import React, { useEffect, useRef, useState, useCallback } from 'react'
import {
  FiSearch,
  <PERSON>Loader,
  FiCopy,
  FiCpu,
  FiChevronDown,
  FiChevronUp,
  FiArrowDown,
} from 'react-icons/fi'
import { formatChatTimestamp } from '@/lib/utils'
import ReadOnlyTiptapEditor from '@/app/components/editor/ReadOnlyTiptapEditor'
import { ReasoningTimeline } from './ReasoningTimeline'
import toast from 'react-hot-toast'

type Message = {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  createdAt?: string | Date
  isStreaming?: boolean
  toolCalls?: ToolCall[]
  stepCount?: number
  steps?: any[]
  cleanContent?: string
  timestamp?: Date
}

type ToolCall = {
  id: string
  type: 'function'
  function: {
    name: string
    arguments: string
  }
  result?: any
}

type SimpleMessageListProps = {
  messages: Message[]
  isLoading?: boolean
  className?: string
}

// Tool call display component
const ToolCallDisplay: React.FC<{ tool: ToolCall }> = ({ tool }) => {
  const toolName = tool.function.name
  const isWebSearch = toolName === 'web_search'

  if (isWebSearch) {
    let query = ''
    try {
      const args = JSON.parse(tool.function.arguments)
      query = args.query || ''
    } catch (_e) {
      query = 'Invalid query'
    }

    const hasResult = tool.result !== undefined
    const resultCount = tool.result?.resultCount || tool.result?.results?.length

    return (
      <div className="flex items-center gap-2 text-xs text-gray-600 my-2 p-2 bg-gray-50 rounded-lg border">
        <FiSearch className="w-3 h-3 flex-shrink-0" />
        {!hasResult ? (
          <>
            <FiLoader className="w-3 h-3 animate-spin flex-shrink-0" />
            <span>
              Searching:{' '}
              <span className="font-mono text-blue-600">{query}</span>
            </span>
          </>
        ) : (
          <>
            <span>
              Found{' '}
              <span className="font-semibold text-green-600">
                {resultCount ?? '?'}
              </span>{' '}
              results for{' '}
              <span className="font-mono text-blue-600">{query}</span>
            </span>
          </>
        )}
      </div>
    )
  }

  // Generic tool call display
  return (
    <div className="flex items-center gap-2 text-xs text-gray-600 my-2 p-2 bg-gray-50 rounded-lg border">
      <FiCpu className="w-3 h-3 flex-shrink-0" />
      <span>
        Tool: <span className="font-mono">{toolName}</span>
      </span>
      {!tool.result && <FiLoader className="w-3 h-3 animate-spin" />}
    </div>
  )
}

// Streaming indicator
const StreamingIndicator: React.FC = () => (
  <div className="flex items-center gap-2 text-xs text-gray-500 mt-2">
    <div className="flex space-x-1">
      <div
        className="w-1 h-1 bg-gray-400 rounded-full animate-bounce"
        style={{ animationDelay: '0ms' }}
      ></div>
      <div
        className="w-1 h-1 bg-gray-400 rounded-full animate-bounce"
        style={{ animationDelay: '150ms' }}
      ></div>
      <div
        className="w-1 h-1 bg-gray-400 rounded-full animate-bounce"
        style={{ animationDelay: '300ms' }}
      ></div>
    </div>
    <span>AI is typing...</span>
  </div>
)

// Individual message component
const MessageItem: React.FC<{ message: Message }> = ({ message }) => {
  const isUser = message.role === 'user'
  const isSystem = message.role === 'system'

  // Determine if message is long (heuristic by character count)
  const LONG_THRESHOLD = 800
  const charCount = message.content?.length || 0
  const isLong = charCount > LONG_THRESHOLD
  const [collapsed, setCollapsed] = useState<boolean>(isLong)

  // Don't render system messages
  if (isSystem) return null

  // Check if this message has step count (indicating reasoning/tool usage)
  const hasSteps =
    message.role === 'assistant' && !!message.stepCount && message.stepCount > 0

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(message.content)
      toast.success('Message copied')
    } catch (_e) {
      toast.error('Failed to copy')
    }
  }

  const timestamp = message.createdAt
    ? formatChatTimestamp(message.createdAt)
    : ''

  return (
    <div className={`mb-4 ${isUser ? 'text-right' : 'text-left'}`}>
      <div
        className={`chat-bubble ${isUser ? 'user-bubble' : 'assistant-bubble'} group`}
      >
        {/* Copy icon */}
        <button
          onClick={handleCopy}
          className="copy-button"
          title="Copy message"
        >
          <FiCopy className="w-3 h-3" />
        </button>

        {/* Message content with optional collapse */}
        <div className="whitespace-pre-wrap break-words">
          {collapsed && isLong ? (
            <>
              <div className="overflow-hidden max-h-60">
                {isUser ? (
                  message.content
                ) : message.isStreaming ? (
                  // Use simple textarea during streaming to prevent flickering
                  <div className="prose prose-sm max-w-none">
                    <div className="whitespace-pre-wrap text-sm leading-relaxed">
                      {message.content}
                    </div>
                  </div>
                ) : (
                  <ReadOnlyTiptapEditor content={message.content} />
                )}
              </div>
              {/* Gradient overlay */}
              <div className="absolute bottom-0 left-0 w-full h-10 bg-gradient-to-b from-transparent to-inherit pointer-events-none rounded-b-lg" />
              <button
                onClick={() => setCollapsed(false)}
                className="expand-button"
              >
                Show more <FiChevronDown className="w-3 h-3" />
              </button>
            </>
          ) : (
            <>
              {isUser ? (
                message.content
              ) : message.isStreaming ? (
                // Use simple textarea during streaming to prevent flickering
                <div className="prose prose-sm max-w-none">
                  <div className="whitespace-pre-wrap text-sm leading-relaxed">
                    {message.content}
                  </div>
                </div>
              ) : (
                <ReadOnlyTiptapEditor content={message.content} />
              )}
              {isLong && (
                <button
                  onClick={() => setCollapsed(true)}
                  className="expand-button"
                >
                  Show less <FiChevronUp className="w-3 h-3" />
                </button>
              )}
            </>
          )}

          {/* Tool calls */}
          {message.toolCalls?.map(tool => (
            <ToolCallDisplay key={tool.id} tool={tool} />
          ))}

          {/* Streaming indicator */}
          {message.isStreaming && <StreamingIndicator />}
        </div>
      </div>

      {/* Timestamp */}
      {timestamp && (
        <div
          className={`text-xs text-gray-500 mt-1 ${isUser ? 'text-right mr-3' : 'text-left ml-3'}`}
        >
          {timestamp}
        </div>
      )}

      {/* Show reasoning timeline for assistant messages with steps */}
      {hasSteps && (
        <div className="mt-3">
          <ReasoningTimeline
            messageId={message.id}
            stepCount={message.stepCount || 0}
            isStreaming={message.isStreaming || false}
            liveSteps={message.steps}
          />
        </div>
      )}
    </div>
  )
}

// Main message list component
export default function SimpleMessageList({
  messages,
  isLoading = false,
  className = '',
}: SimpleMessageListProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const [showScrollButton, setShowScrollButton] = useState(false)
  const isInitialLoad = useRef(true)

  // Auto-scroll to bottom when new messages arrive or on initial load
  useEffect(() => {
    const container = scrollContainerRef.current
    const messagesEnd = messagesEndRef.current

    if (!messagesEnd) return

    const scrollToBottom = () => {
      if (container) {
        // Check if user is near bottom before auto-scrolling
        const { scrollTop, scrollHeight, clientHeight } = container
        const isNearBottom = scrollHeight - scrollTop - clientHeight < 100

        if (isNearBottom || isInitialLoad.current) {
          container.scrollTop = container.scrollHeight
        }
      }
    }

    // For initial load with existing messages, scroll immediately to bottom
    if (isInitialLoad.current && messages.length > 0) {
      // Use requestAnimationFrame to ensure DOM is fully rendered
      requestAnimationFrame(() => {
        scrollToBottom()
        isInitialLoad.current = false
      })
      return
    }

    // For subsequent message updates, wait for content to render then scroll
    if (!isInitialLoad.current && messages.length > 0) {
      // Use requestAnimationFrame to wait for DOM updates, then setTimeout for content rendering
      requestAnimationFrame(() => {
        setTimeout(() => {
          scrollToBottom()
        }, 50) // Small delay to ensure content is fully rendered
      })
    }
  }, [messages])

  // Check if user has scrolled up to show scroll-to-bottom button
  const handleScroll = useCallback(() => {
    const container = scrollContainerRef.current
    if (container) {
      const { scrollTop, scrollHeight, clientHeight } = container
      const isNearBottom = scrollHeight - scrollTop - clientHeight < 100
      setShowScrollButton(!isNearBottom)
    }
  }, [])

  // Scroll to bottom function
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [])

  // Sort messages by timestamp to ensure correct order (oldest first)
  const sortedMessages = [...messages].sort((a, b) => {
    const timeA = new Date(a.createdAt || 0).getTime()
    const timeB = new Date(b.createdAt || 0).getTime()
    return timeA - timeB
  })

  return (
    <div className={`flex flex-col h-full relative ${className}`}>
      <div
        ref={scrollContainerRef}
        onScroll={handleScroll}
        className="flex-1 overflow-y-auto p-4 space-y-4"
      >
        {sortedMessages.length === 0 ? (
          <div className="flex items-center justify-center h-full text-gray-500">
            <div className="text-center">
              <FiCpu className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p className="text-lg font-medium">Start a conversation</p>
              <p className="text-sm">
                Send a message to begin chatting with AI
              </p>
            </div>
          </div>
        ) : (
          <>
            {sortedMessages.map(message => (
              <MessageItem key={message.id} message={message} />
            ))}
            {isLoading && (
              <div className="flex gap-3 mb-6">
                <div className="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                  <FiCpu className="w-4 h-4 text-gray-600" />
                </div>
                <div className="bg-gray-100 rounded-lg px-4 py-3">
                  <StreamingIndicator />
                </div>
              </div>
            )}
          </>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Scroll to bottom button */}
      {showScrollButton && (
        <button
          onClick={scrollToBottom}
          className="absolute bottom-4 right-4 bg-blue-600 hover:bg-blue-700 text-white rounded-full p-2 shadow-lg transition-all duration-200 z-10"
          title="Scroll to bottom"
        >
          <FiArrowDown className="w-4 h-4" />
        </button>
      )}
    </div>
  )
}
