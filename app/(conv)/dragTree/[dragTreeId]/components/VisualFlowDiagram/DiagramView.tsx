'use client'
import React, { Suspense, lazy } from 'react'

// Lightweight wrapper that lazy-loads the heavy ReactFlow canvas
const ReactFlowCanvas = lazy(() =>
  import('./ReactFlowCanvas').then(m => ({ default: m.default }))
)

const ReactFlowSkeleton: React.FC = () => (
  <div className="w-full h-full flex items-center justify-center bg-gray-50 rounded-lg">
    <div className="text-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
      <p className="text-gray-600 text-sm">Loading diagram...</p>
    </div>
  </div>
)

type DiagramViewProps = {
  layoutMode: 'linear' | 'radial'
  setLayoutMode: (mode: 'linear' | 'radial') => void
}

export const DiagramView: React.FC<DiagramViewProps> = React.memo(
  function DiagramView({ layoutMode, setLayoutMode }) {
    return (
      <Suspense fallback={<ReactFlowSkeleton />}>
        <ReactFlowCanvas
          layoutMode={layoutMode}
          setLayoutMode={setLayoutMode}
        />
      </Suspense>
    )
  }
)
