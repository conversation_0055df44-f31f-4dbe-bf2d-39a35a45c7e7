var dagre = require('dagre')
import { Node, Edge, Position, MarkerType } from 'reactflow'

export const getDagreLayoutedElements = (
  nodes: Node[],
  edges: Edge[],
  direction = 'LR'
) => {
  // Validate input data
  if (!nodes || nodes.length === 0) {
    console.warn('getDagreLayoutedElements: No nodes provided')
    return { nodes: [], edges: [] }
  }

  try {
    const dagreGraph = new dagre.graphlib.Graph()
    dagreGraph.setDefaultEdgeLabel(() => ({}))
    dagreGraph.setGraph({
      rankdir: direction,
      ranksep: 120, // Spacing between levels (horizontal distance)
      nodesep: 80, // Spacing between nodes in same level (vertical distance)
      edgesep: 20, // Spacing between edges
    })

    nodes.forEach(node => {
      const width = (node.data as any)?.width ?? 350
      const height = (node.data as any)?.height ?? 80
      dagreGraph.setNode(node.id, { width, height })
    })

    edges.forEach(edge => {
      dagreGraph.setEdge(edge.source, edge.target)
    })

    dagre.layout(dagreGraph)

    const layoutedNodes = nodes.map(node => {
      const nodeWithPosition = dagreGraph.node(node.id)

      // Check if position exists from dagre
      if (!nodeWithPosition) {
        console.warn(
          `getDagreLayoutedElements: No position found for node ${node.id}`
        )
        return {
          ...node,
          targetPosition: Position.Left,
          sourcePosition: Position.Right,
          position: node.position || { x: 0, y: 0 },
        }
      }

      // Ensure position values are finite numbers to prevent zoom issues
      const x = nodeWithPosition.x
      const y = nodeWithPosition.y
      const width = nodeWithPosition.width || 350
      const height = nodeWithPosition.height || 80

      const position = {
        x: isNaN(x) || !isFinite(x) ? 0 : x - width / 2,
        y: isNaN(y) || !isFinite(y) ? 0 : y - height / 2,
      }

      return {
        ...node,
        targetPosition: Position.Left,
        sourcePosition: Position.Right,
        position,
      }
    })

    const layoutedEdges = edges.map(edge => ({
      ...edge,
      sourceHandle: 'right',
      targetHandle: 'left',
      // Preserve edge styling
      type: edge.type || 'smoothstep',
      style: edge.style || {
        stroke: '#64748b',
        strokeWidth: 2,
      },
      markerEnd: edge.markerEnd || {
        type: MarkerType.ArrowClosed,
        color: '#64748b',
      },
    }))

    return { nodes: layoutedNodes, edges: layoutedEdges }
  } catch (error) {
    console.error('Dagre layout failed:', error)
    // Return original nodes and edges as fallback
    return { nodes, edges }
  }
}
