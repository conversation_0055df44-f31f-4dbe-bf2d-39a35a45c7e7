'use client'

import { useEffect, useRef, useState } from 'react'
import React from 'react'
import { useEditor, EditorContent } from '@tiptap/react'
import { TiptapEditorProps } from '../props'
import { TiptapExtensions } from '../extensions'
import { useDebouncedCallback } from 'use-debounce'
import { useCompletion } from '@ai-sdk/react'
import { toast } from 'react-hot-toast'
import DEFAULT_EDITOR_CONTENT from '../default-content'
import { EditorBubbleMenu } from '../components'
import { Draft } from '@prisma/client'
import ExpandSheet from '../components/Sheet'
import Link from 'next/link'
import axios from 'axios'
import { ConversationStatus } from '@prisma/client'
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import mixpanel from '@/app/libs/mixpanel'

interface IssueTree {
  id: string
  conversation_id: string
  nodes: any
}
interface EditorProps {
  draft?: Draft
  currentUser?: any
  conversationStatus?: string
  issueTrees?: IssueTree[] | null
}

export default function Editor({
  draft,
  currentUser,
  conversationStatus,
  issueTrees,
}: EditorProps) {
  const output = draft ? JSON.parse(draft?.content!) : DEFAULT_EDITOR_CONTENT

  // setContent is not that helpful since editor handles its own state
  // But original localStorage code uses it, just lazy and replace that with useSate
  const [content, setContent] = useState(output)

  const [saveStatus, setSaveStatus] = useState('Saved')

  const [hydrated, setHydrated] = useState(false)

  // Control the Expand sheet state
  const [showSheet, setShowSheet] = useState(false)
  const openSheet = () => {
    setShowSheet(true)
    mixpanel.track('open_experimental_features_click')
  }
  const closeSheet = () => setShowSheet(false)

  const debouncedUpdates = useDebouncedCallback(async ({ editor }) => {
    const json = editor.getJSON()
    setContent(json)
    setSaveStatus('Saving...')
    axios
      .post(`/api/drafts/${draft?.id}`, { content: json })
      .then(() => {
        setSaveStatus('Saved')
      })
      .catch(err => {
        console.error(err)
      })
  }, 3000)

  const containerRef = useRef<HTMLDivElement>(null)

  // Define separately because it relies on this string state
  // to decide what color to show, define in this way to avoid
  // accidentally changing the string and break the functionality
  const unsavedDisplayText = 'Unsaved'

  const isExampleConversation =
    conversationStatus === ConversationStatus.EXAMPLE

  const editor = useEditor({
    extensions: TiptapExtensions,
    // Can only edit the content if it's not an example conversation
    editable: !isExampleConversation,
    editorProps: TiptapEditorProps,
    onUpdate: e => {
      setSaveStatus(unsavedDisplayText)

      // Comment out the autocomplete part
      /*
        const selection = e.editor.state.selection;
        const lastTwo = getPrevText(e.editor, {
          chars: 2,
        });
        if (lastTwo === "++" && !isLoading) {
          e.editor.commands.deleteRange({
            from: selection.from - 2,
            to: selection.from,
          });
          // In the api/generate, we have bounded the max char
          // this won't change the API behavior unless this num is smaller than the API's
          complete(
            getPrevText(e.editor, {
              chars: 5000,
            })
          );
          // complete(e.editor.storage.markdown.getMarkdown());
          // va.track("Autocomplete Shortcut Used");
        } else {
        */
      // Only save if it's not an example conversation
      if (conversationStatus !== ConversationStatus.EXAMPLE) {
        debouncedUpdates(e)
      }
      // }
    },
    autofocus: 'start',
  })

  const { complete, completion, isLoading, stop } = useCompletion({
    id: 'clarify',
    api: '/api/generate',
    onFinish: (_prompt, completion) => {
      editor?.commands.setTextSelection({
        from: editor.state.selection.from - completion.length,
        to: editor.state.selection.from,
      })
    },
    onError: err => {
      toast.error(err.message)
      if (err.message === 'You have reached your request limit for the day.') {
        // va.track("Rate Limit Reached");
      }
    },
    body: { conversationId: draft?.conversation_id, currentUser: currentUser },
  })

  const prev = useRef('')

  // Insert chunks of the generated text
  useEffect(() => {
    const diff = completion.slice(prev.current.length)
    prev.current = completion
    editor?.commands.insertContent(diff)
  }, [isLoading, editor, completion])

  useEffect(() => {
    // if user presses escape or cmd + z and it's loading,
    // stop the request, delete the completion, and insert back the "++"
    const onKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' || (e.metaKey && e.key === 'z')) {
        stop()
        if (e.key === 'Escape') {
          editor?.commands.deleteRange({
            from: editor.state.selection.from - completion.length,
            to: editor.state.selection.from,
          })
        }
        editor?.commands.insertContent('++')
      }
    }
    const mousedownHandler = (e: MouseEvent) => {
      e.preventDefault()
      e.stopPropagation()
      stop()
      if (window.confirm('AI writing paused. Continue?')) {
        complete(editor?.getText() || '')
      }
    }
    if (isLoading) {
      document.addEventListener('keydown', onKeyDown)
      window.addEventListener('mousedown', mousedownHandler)
    } else {
      document.removeEventListener('keydown', onKeyDown)
      window.removeEventListener('mousedown', mousedownHandler)
    }
    return () => {
      document.removeEventListener('keydown', onKeyDown)
      window.removeEventListener('mousedown', mousedownHandler)
    }
  }, [stop, isLoading, editor, complete, completion.length])

  // Hydrate the editor with the content from DB.
  useEffect(() => {
    if (editor && content && !hydrated) {
      editor.commands.setContent(content)
      setHydrated(true)
    }
  }, [editor, content, hydrated])

  // console.log("Editor text", editor?.getText());

  return (
    <>
      {/* The alert, display when the component is mounted */}
      {hydrated && content && (
        <AlertDialog defaultOpen={true}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>
                AI generated contents{' '}
                <strong>may contain inaccurate information</strong>
              </AlertDialogTitle>
              <AlertDialogTitle>please review before use 🙏</AlertDialogTitle>
            </AlertDialogHeader>
            <AlertDialogFooter>
              {/* Force it to scroll to the top after user clicks */}
              <AlertDialogCancel
                onClick={() => {
                  containerRef?.current?.scrollIntoView()
                }}
              >
                👌 I understand the risk
              </AlertDialogCancel>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}

      {/* The header, currently showing the save status and some buttons */}
      <div className="flex flex-row justify-end max-w-screen-xl sticky top-0 z-10 py-2">
        <Link
          href={`/conversations/${draft?.conversation_id || ''}`}
          className="lg:hidden hover:bg-blue-200 mb-5 rounded-lg bg-blue-100 px-2 py-1 text-sm text-gray-500 mr-2"
        >
          Prev
        </Link>
        <div
          className={`mb-5 rounded-lg px-2 py-1 text-sm text-gray-500 mr-2 ${
            saveStatus === unsavedDisplayText ? 'bg-red-100' : 'bg-stone-100'
          }`}
        >
          {saveStatus}
        </div>
        <button
          onClick={openSheet}
          className="hover:bg-green-300 mb-5 rounded-lg bg-green-200 px-2 py-1 text-sm text-gray-500 mr-2"
        >
          Experimental features
        </button>
      </div>
      <div ref={containerRef} />

      {/* Expandable sheet after click */}
      <ExpandSheet
        showSheet={showSheet}
        closeSheet={closeSheet}
        modal={false}
        draft={draft}
        editorText={editor?.getText() || ''}
        issueTrees={issueTrees}
      />

      {/* Editor */}
      <div
        onClick={() => {
          editor?.chain().focus().run()
        }}
        className="relative min-h-[500px] w-full max-w-screen-xl border-stone-200 bg-white p-12 px-8 sm:mb-[calc(20vh)] sm:rounded-lg sm:border sm:px-12 sm:shadow-lg"
      >
        {editor && <EditorBubbleMenu editor={editor} />}
        <EditorContent editor={editor} />
      </div>
    </>
  )
}
