export const markdownSample = `
# Example Markdown
## Data Collection
### Customer Data Types
#### What types of customer data will be collected for segmentation?
- Examples: 1. Demographic data (age, gender, location); 2. Purchase history (products, frequency); 3. Online behavior (website visits, clickstream data)

#### How will data accuracy be ensured during data collection?
- Examples: 1. Double-entry validation; 2. Real-time data validation checks; 3. Regular data cleaning routines

### Data Sources
#### What are the sources of customer data?
- Examples: 1. Surveys and questionnaires; 2. Website tracking tools (e.g., Google Analytics); 3. Point of Sale (POS) systems

#### How will data be collected from these sources?
- Examples: 1. Online surveys with consent mechanisms; 2. Integration with website tracking tools; 3. Automated data retrieval from POS systems

## Data Privacy and Security
### Compliance
#### How will the system comply with data privacy regulations like GDPR and CCPA?
- Examples: 1. Implementing consent mechanisms for data collection; 2. Providing opt-out options for customers; 3. Conducting regular compliance audits

### Data Protection
#### How will sensitive data, especially passwords, be protected?
- Examples: 1. Hashing techniques for password storage; 2. Encryption of sensitive data in transit; 3. Access controls and authentication

## Data Storage
### Storage Solutions
#### Where will customer data be stored, and why?
- Examples: 1. Cloud-based warehouses like AWS Redshift for scalability; 2. Google BigQuery for accessibility; 3. Data redundancy and disaster recovery

### Backup and Recovery
#### How will data redundancy and disaster recovery be ensured?
- Examples: 1. Regular backups and snapshots; 2. Geographically distributed data centers for redundancy

## Segmentation Criteria
### Segmentation Methods
#### What methods will be used for customer segmentation?
- Examples: 1. Behavioral segmentation (purchasing patterns); 2. Demographic segmentation (age, gender); 3. Psychographic segmentation (lifestyle, interests); 4. Machine learning or statistical models?

### Decision Trees
#### How will decision trees be used in segmentation, and what factors will they consider?
- Examples: 1. Criteria for branching in decision trees; 2. Incorporating machine learning for decision tree optimization

## Integration with Marketing Tools
### Marketing Platforms
#### Which marketing tools and platforms will be integrated with the segmented data?
- Examples: 1. Marketo for marketing automation; 2. CRM systems; 3. Email marketing platforms

### Data Consistency
#### How will data consistency and accuracy be maintained during integration?
- Examples: 1. Automated data syncing between systems; 2. Regular data reconciliation procedures

## Measurement and Optimization
### Key Performance Indicators (KPIs)
#### What KPIs will be used to measure the system's effectiveness?
- Examples: 1. Conversion rate; 2. Customer retention rate; 3. Return on investment (ROI)

### A/B Testing
#### How will A/B testing be conducted to optimize marketing strategies?
- Examples: 1. A/B testing of email subject lines; 2. A/B testing of ad creatives

### Continuous Improvement
#### What mechanisms will support continuous improvement?1
- Examples: 1. Customer feedback surveys; 2. Regular reviews of segmentation models; 3. Benchmarking against industry standards
`

export const markdownSample2 = `
# Data
## Customer Data
### What types of customer data will be collected for segmentation?
- **Examples123**: 1. Demographic data (age, gender, location); 2. Purchase history (products purchased, frequency); 3. Online behavior data (website visits, clickstream information)
### How will the system ensure data accuracy during collection?
- Examples: 1. Double-entry validation; 2. Real-time data validation checks; 3. Regular data cleaning routines
### What data privacy measures will be implemented to comply with GDPR and CCPA?
- Examples: 1. Consent mechanisms for data collection; 2. Opt-out options for customers; 3. Regular audits and compliance reviews
## Data Security
### How will sensitive data like passwords be protected?
- Examples: 1. Hashing techniques; 2. Encryption protocols
### Where will customer data be stored, and how will data redundancy be ensured?
- Examples: 1. Cloud-based warehouses (AWS Redshift, Google BigQuery); 2. Regular backups and snapshots; 3. Geographically distributed data centers

# Segmentation
## Criteria
### What criteria will be used for customer segmentation?
- Examples: 1. Behavioral segmentation (purchasing patterns); 2. Demographic segmentation (age, gender); 3. Psychographic segmentation (lifestyle, interests)
### Will machine learning or statistical models be applied in segmentation?
- Examples: 1. Decision trees as primary method; 2. Potential integration of machine learning models
## Integration
### How will segmented data be integrated with marketing tools like Marketo?
- Examples: 1. Automated data syncing; 2. Regular data reconciliation procedures
### What steps will be taken to ensure data consistency and accuracy during integration?
- Examples: 1. Data mapping and transformation processes; 2. Data quality checks

# Measurement and Improvement
## KPIs
### What key performance indicators (KPIs) will be used to measure system effectiveness?
- Examples: 1. Conversion rate; 2. Customer retention rate; 3. Return on investment (ROI)
### How will A/B testing be utilized in optimizing marketing strategies?
- Examples: 1. Testing email subject lines; 2. Testing ad creatives
## Continuous Improvement
### Hello
#### World
##### What mechanisms will support continuous improvement of the segmentation models?
- **Examples**: 1. Customer feedback surveys; 2. Regular reviews of segmentation models; 3. Benchmarking against industry standards
`
