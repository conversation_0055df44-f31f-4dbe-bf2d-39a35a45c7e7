import { useState, useCallback, useRef } from 'react'
import { getNodeContentOnDemand } from '@/app/server-actions/drag-tree/get-tree-structure'
import { performanceMonitor } from '@/app/utils/performance-monitor'
import { useDragTreeStore } from '@/app/stores/dragtree_store'

/**
 * Hook for lazy loading node content on-demand
 * Implements caching and performance monitoring for content fetching
 *
 * Usage:
 * const { loadNodeContent, isLoading, error } = useLazyNodeContent()
 * const content = await loadNodeContent(nodeId)
 */
export function useLazyNodeContent() {
  const [loadingNodes, setLoadingNodes] = useState<Set<string>>(new Set())
  const [errors, setErrors] = useState<Map<string, string>>(new Map())
  const contentCache = useRef<Map<string, any>>(new Map())
  const requestCache = useRef<Map<string, Promise<any>>>(new Map())

  // Store methods for updating node content
  const addNodeContent = useDragTreeStore(state => state.addNodeContent)
  const nodeContent = useDragTreeStore(state => state.nodeContent)

  /**
   * Load content for a specific node with caching and deduplication
   */
  const loadNodeContent = useCallback(
    async (nodeId: string) => {
      // Check if content is already in store
      const existingContent = nodeContent.get(nodeId)
      if (existingContent && existingContent.size > 0) {
        console.log(
          `🎯 [useLazyNodeContent] Using cached content for node: ${nodeId}`
        )
        return Array.from(existingContent.values())
      }

      // Check if request is already in progress
      const existingRequest = requestCache.current.get(nodeId)
      if (existingRequest) {
        console.log(
          `⏳ [useLazyNodeContent] Waiting for existing request for node: ${nodeId}`
        )
        return existingRequest
      }

      // Start performance monitoring
      const endTiming = performanceMonitor.startTiming(
        'loadNodeContent.onDemand'
      )

      // Create new request
      const requestPromise = (async () => {
        try {
          setLoadingNodes(prev => new Set(prev).add(nodeId))
          setErrors(prev => {
            const newErrors = new Map(prev)
            newErrors.delete(nodeId)
            return newErrors
          })

          console.log(
            `🔍 [useLazyNodeContent] Fetching content for node: ${nodeId}`
          )

          const result = await getNodeContentOnDemand(nodeId)

          if (!result.success) {
            throw new Error(result.error || 'Failed to load node content')
          }

          // Record performance metrics
          endTiming({
            payloadSize: result.metrics.payloadSize,
            nodeCount: 1,
          })

          console.log(
            `✅ [useLazyNodeContent] Loaded content for node: ${nodeId}`,
            {
              contentItems: result.data.content_items.length,
              payloadSize: `${(result.metrics.payloadSize / 1024).toFixed(2)}KB`,
              queryTime: `${result.metrics.queryTime.toFixed(2)}ms`,
            }
          )

          // Transform content items to store format
          const contentItems = result.data.content_items.map((item: any) => ({
            contentId: item.id,
            contentType: item.content_type || 'QUICK_RESEARCH',
            contentVersion: item.content_version || 'v1',
            status: item.status,
            contentText: item.content_text || '',
            metadata: item.content_metadata || {},
            messages: item.messages || [],
          }))

          // Update store with loaded content
          if (contentItems.length > 0) {
            contentItems.forEach(item => {
              addNodeContent(nodeId, item)
            })
          }

          // Cache the result
          contentCache.current.set(nodeId, contentItems)

          return contentItems
        } catch (error) {
          endTiming({ errorCount: 1 })

          const errorMessage =
            error instanceof Error ? error.message : 'Unknown error'
          console.error(
            `❌ [useLazyNodeContent] Error loading content for node ${nodeId}:`,
            error
          )

          setErrors(prev => {
            const newErrors = new Map(prev)
            newErrors.set(nodeId, errorMessage)
            return newErrors
          })

          throw error
        } finally {
          setLoadingNodes(prev => {
            const newSet = new Set(prev)
            newSet.delete(nodeId)
            return newSet
          })

          // Clean up request cache
          requestCache.current.delete(nodeId)
        }
      })()

      // Cache the request promise to prevent duplicates
      requestCache.current.set(nodeId, requestPromise)

      return requestPromise
    },
    [nodeContent, addNodeContent]
  )

  /**
   * Preload content for multiple nodes (useful for prefetching)
   */
  const preloadNodeContent = useCallback(
    async (nodeIds: string[]) => {
      const endTiming = performanceMonitor.startTiming(
        'preloadNodeContent.batch'
      )

      try {
        console.log(
          `🚀 [useLazyNodeContent] Preloading content for ${nodeIds.length} nodes`
        )

        const promises = nodeIds.map(nodeId =>
          loadNodeContent(nodeId).catch(error => {
            console.warn(
              `⚠️ [useLazyNodeContent] Failed to preload content for node ${nodeId}:`,
              error
            )
            return null
          })
        )

        const results = await Promise.all(promises)
        const successCount = results.filter(result => result !== null).length

        endTiming({
          nodeCount: successCount,
          payloadSize: 0, // Individual requests already tracked
        })

        console.log(
          `✅ [useLazyNodeContent] Preloaded content for ${successCount}/${nodeIds.length} nodes`
        )

        return results
      } catch (error) {
        endTiming({ errorCount: 1 })
        console.error('❌ [useLazyNodeContent] Error in batch preload:', error)
        throw error
      }
    },
    [loadNodeContent]
  )

  /**
   * Clear cached content for a node (useful for refreshing)
   */
  const clearNodeContent = useCallback((nodeId: string) => {
    contentCache.current.delete(nodeId)
    requestCache.current.delete(nodeId)
    setErrors(prev => {
      const newErrors = new Map(prev)
      newErrors.delete(nodeId)
      return newErrors
    })
    console.log(`🗑️ [useLazyNodeContent] Cleared cache for node: ${nodeId}`)
  }, [])

  /**
   * Get loading state for a specific node
   */
  const isNodeLoading = useCallback(
    (nodeId: string) => {
      return loadingNodes.has(nodeId)
    },
    [loadingNodes]
  )

  /**
   * Get error for a specific node
   */
  const getNodeError = useCallback(
    (nodeId: string) => {
      return errors.get(nodeId) || null
    },
    [errors]
  )

  /**
   * Get cache statistics for monitoring
   */
  const getCacheStats = useCallback(() => {
    return {
      cachedNodes: contentCache.current.size,
      activeRequests: requestCache.current.size,
      errorCount: errors.size,
      loadingCount: loadingNodes.size,
    }
  }, [errors.size, loadingNodes.size])

  return {
    loadNodeContent,
    preloadNodeContent,
    clearNodeContent,
    isNodeLoading,
    getNodeError,
    getCacheStats,
    // Aggregate states
    isLoading: loadingNodes.size > 0,
    hasErrors: errors.size > 0,
    loadingNodes: Array.from(loadingNodes),
    errors: Object.fromEntries(errors),
  }
}
