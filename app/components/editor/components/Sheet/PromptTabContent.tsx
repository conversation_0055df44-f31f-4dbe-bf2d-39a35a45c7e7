import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  SheetD<PERSON><PERSON>,
  Sheet<PERSON>eader,
  SheetTitle,
} from "@/components/ui/sheet";

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

import { chatgptSiteUrl } from "@/app/configs";
import mixpanel from "@/app/libs/mixpanel";

interface PromptTabContentProps {
  handleCopyText: (text: string) => void;
  editorText?: string;
}

export const PromptTabContent = ({
  handleCopyText,
  editorText,
}: PromptTabContentProps) => {
  const trackButtonClick = () => {
    mixpanel.track("buy_me_a_coffee_click", { location: "prompt_tab" });
  };

  const trackPHClick = () => {
    mixpanel.track("product_hunt_click", { location: "prompt_tab" });
  };

  const handleCopyExamplePrompt = (text: string, action: string) => {
    mixpanel.track("copy_example_prompt_click", { text: text });
    handleCopyText(
      action + ", contexts start below:\n{\n" + editorText + "\n}"
    );
    // Add a delay before opening the new window
    if (newWindow) {
      setTimeout(() => {
        window.open(chatgptSiteUrl, "_blank");
      }, 2000); // delay in milliseconds, 1000 ms = 1 second
    }
  };

  const baseUrl = process.env.NEXT_PUBLIC_SERVER_URL || "http://localhost:3000";

  const [newWindow, setNewWindow] = useState(true);

  const handleSwitch = () => {
    setNewWindow(!newWindow);
  };

  const prompts = [
    {
      text: "Criticize the project - pretty interesting 👍",
      action:
        "As a successful VC investor, based on the context below, carefully think and criticize the project, your goal is to find the risks asap, you have to think more and cover the spots that I have not considered before. Since we are all one team and love constructive feedback, you are straight and direct, or even mean, but still can well explain your rationale clearly, try to use a mean and picky tone",
    },
    {
      text: "Summarize to executive summary",
      action: "Summarize this context into 300 characters executive summary",
    },
    {
      text: "Translate to Chinese 中文",
      action: "Translate this context into Chinese with markdown format",
    },
    {
      text: "Give the action plan",
      action:
        "As the experienced product marketing manager, based on the context below, carefully think and plan the optimal step-by-step action plan with corresponding dependencies, remember to consider budget, time, people, and explain your rationale",
    },
    {
      text: "Do a SWOT analysis",
      action:
        "As a seasoned business analyst, do SWOT analysis based on the context provided",
    },
    {
      text: "Ask whatever you want",
      action:
        "After you finish reading the content, reply: I am ready, feel free to ask me anything",
    },
    // Add more prompts here
  ];

  return (
    <>
      <div className="py-2">
        <SheetHeader>
          <SheetTitle className="text-left">
            <p>Feedback is welcome!</p>
          </SheetTitle>
        </SheetHeader>
        <SheetDescription>
          <a
            target="_blank"
            href={`${baseUrl}/feedback/`}
            rel="noopener noreferrer"
            className="underline"
          >
            New tab to feedback page
          </a>
        </SheetDescription>
        <br />
        <SheetHeader>
          <SheetTitle className="text-left">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <p>Experimental: Draft as context</p>
                </TooltipTrigger>
                <TooltipContent>
                  <p>We cannot do better than ChatGPT and this saves us $</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </SheetTitle>
        </SheetHeader>
        <SheetDescription>
          <a
            target="_blank"
            href="https://platform.openai.com/docs/guides/gpt-best-practices"
            rel="noopener noreferrer"
            className="underline"
          >
            Learn more from OpenAI
          </a>
        </SheetDescription>
        <div className="flex items-center space-x-2 py-2">
          <Label>Copy Only</Label>
          <Switch checked={newWindow} onCheckedChange={() => handleSwitch()} />
          <Label>Copy & Open ChatGPT</Label>
        </div>
        <p className="text-xs p-1">
          Click to copy, Paste to ChatGPT, have fun yourself
        </p>
        {/* Prompt */}
        <div className="py-2">
          {prompts.map((prompt, index) => (
            <PromptButton
              key={index}
              text={prompt.text}
              action={prompt.action}
              onClick={handleCopyExamplePrompt}
            />
          ))}
        </div>
        <p className="text-xs p-1 text-red-500">
          To deep dive, try GPT-4 with <strong>Browse with Bing</strong>
        </p>
      </div>
      <div className="py-2">
        <a
          href="https://www.producthunt.com/posts/clarify-ai?utm_source=badge-featured&utm_medium=badge&utm_souce=badge-clarify&#0045;ai"
          target="_blank"
          onClick={trackPHClick}
        >
          <img
            src="https://api.producthunt.com/widgets/embed-image/v1/featured.svg?post_id=421464&theme=light"
            alt="Clarify&#0032;AI - Transform&#0032;vague&#0032;ideas&#0032;into&#0032;structured&#0032;insights | Product Hunt"
            style={{ width: "250px", height: "54px" }}
            width="250"
            height="54"
          />
        </a>
        <a
          href="https://www.buymeacoffee.com/clarifyai"
          target="_blank"
          rel="noreferrer"
          onClick={trackButtonClick}
        >
          <img
            src="https://cdn.buymeacoffee.com/buttons/v2/default-yellow.png"
            alt="Buy Me A Coffee"
            style={{ height: "60px", width: "217px" }}
          />
        </a>
      </div>
    </>
  );
};

const PromptButton: React.FC<{
  text: string;
  action: string;
  onClick: (text: string, action: string) => void;
}> = ({ text, action, onClick }) => (
  <>
    <Button
      className="bg-green-100 hover:bg-green-200 text-gray-500 m-2"
      size={"sm"}
      onClick={() => onClick(text, action)}
    >
      {text}
    </Button>
  </>
);
