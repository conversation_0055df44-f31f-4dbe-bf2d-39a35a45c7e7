// For loading issue tree

import { NextResponse } from 'next/server'
import getIssueTreeById from '@/app/(legacy)/_server-actions/getIssueTreeById'
import { UserStatus } from '@prisma/client'

export async function POST(req: Request) {
  try {
    const { conversationId, currentUser, isReturnAll } = await req.json()
    console.log('api/issuetree/load:', conversationId, currentUser, isReturnAll)

    // Validate user authentication and authorization
    if (!isValidUser(currentUser)) {
      return new NextResponse('Unauthorized', { status: 400 })
    }

    if (!isActiveUser(currentUser)) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    const issueTree = await getIssueTreeById(conversationId, currentUser)

    if (!issueTree) {
      return NextResponse.json({ error: 'Issue tree not found' })
    }

    return isReturnAll
      ? NextResponse.json(issueTree)
      : NextResponse.json(getFilteredIssueTree(issueTree))
  } catch (error) {
    console.error('Error in api/issuetree/load:', error)
    return new NextResponse('Internal Error', { status: 500 })
  }
}

// Helper functions

function isValidUser(user: any): boolean {
  return user?.id && user?.email
}

function isActiveUser(user: any): boolean {
  return user?.status === UserStatus.ACTIVE
}

function getFilteredIssueTree(issueTree: any) {
  // Exclude 'prompt' field from the response
  const { prompt: _prompt, ...safeIssueTree } = issueTree
  return safeIssueTree
}
