import { TreeNode } from "@/app/types";
import {
  TreeNodeType,
  DEFAULT_NODE_LABELS,
  treeNodeTypeToDragTreeType,
} from "@/app/(conv)/dragTree/[dragTreeId]/constants/node-types";
import { generateDragTreeNodeId } from "@/app/(conv)/dragTree/[dragTreeId]/utils/id-generation";

/**
 * Create metadata for a node including level and parent information
 */
export function createNodeMetadata(
  parentId: string,
  level: number
): Record<string, any> {
  return {
    parentId,
    level,
    createdAt: new Date().toISOString(),
  };
}

/**
 * Create a new TreeNode with proper structure
 */
export function createNewTreeNode(
  dragTreeId: string,
  type: TreeNodeType,
  label?: string,
  forceUnique: boolean = true
): TreeNode {
  const nodeLabel =
    label ||
    (type === TreeNodeType.CATEGORY
      ? DEFAULT_NODE_LABELS.NEW_CATEGORY
      : DEFAULT_NODE_LABELS.NEW_QUESTION);

  const nodeType = treeNodeTypeToDragTreeType(type);
  const nodeId = generateDragTreeNodeId(
    dragTreeId,
    nodeLabel,
    nodeType,
    12,
    forceUnique
  );

  const newNode: TreeNode = {
    id: nodeId,
    label: nodeLabel,
    type,
    children: [],
    isInterestedIn: false,
  };

  // If adding a category, also add a default question child
  if (type === TreeNodeType.CATEGORY) {
    const childQuestionId = generateDragTreeNodeId(
      dragTreeId,
      DEFAULT_NODE_LABELS.NEW_QUESTION,
      "QUESTION",
      12,
      true
    );

    const childQuestion: TreeNode = {
      id: childQuestionId,
      label: DEFAULT_NODE_LABELS.NEW_QUESTION,
      type: TreeNodeType.QUESTION,
      children: [],
      isInterestedIn: false,
    };

    newNode.children.push(childQuestion);
  }

  return newNode;
}

/**
 * Create multiple nodes from text input (for similar questions/categories)
 */
export function createNodesFromText(
  dragTreeId: string,
  texts: string[],
  type: TreeNodeType
): TreeNode[] {
  return texts.map((text) =>
    createNewTreeNode(dragTreeId, type, text.trim(), true)
  );
}
