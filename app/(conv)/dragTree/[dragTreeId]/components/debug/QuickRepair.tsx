"use client";

import React from "react";
import { useDragTreeStore } from "@/app/stores/dragtree_store";
import { createDragTreeNode } from "@/app/server-actions/drag-tree";
import { Button } from "@/components/ui/button";
import { toast } from "react-hot-toast";

const QuickRepair: React.FC = () => {
  const { dragTreeId, loadFromDatabase } = useDragTreeStore();

  const fixMissingNode = async () => {
    if (!dragTreeId) {
      toast.error("No tree ID");
      return;
    }

    try {
      // Create the missing node cat_34rkhgslem55
      const result = await createDragTreeNode({
        dragTreeId,
        nodeType: "CATEGORY",
        label: "New Category",
        nodeId: "cat_34rkhgslem55", // Use the exact missing ID
      });

      if (result.success) {
        toast.success("Fixed missing node!");
        // Reload the tree
        setTimeout(() => {
          loadFromDatabase();
        }, 500);
      } else {
        toast.error(`Failed: ${result.error}`);
      }
    } catch (error) {
      toast.error(`Error: ${error}`);
    }
  };

  return (
    <div className="p-4 bg-yellow-100 border border-yellow-300 rounded">
      <h3 className="font-bold mb-2">Quick Fix</h3>
      <p className="text-sm mb-3">
        Create the missing node cat_34rkhgslem55 directly.
      </p>
      <Button onClick={fixMissingNode} size="sm">
        Fix Missing Node
      </Button>
    </div>
  );
};

export default QuickRepair;
