// Import the getCurrentUser function from the specified path
import getCurrentUser from '@/app/server-actions/_legacy/getCurrentUser'

// Import the DesktopSidebar and MobileFooter components from the current directory
import DesktopSidebar from './DesktopSidebar'
import MobileFooter from './MobileFooter'

// Define an async function called Sidebar that takes in a children prop of type React.ReactNode
async function Sidebar({ children }: { children: React.ReactNode }) {
  // Call the getCurrentUser function and wait for it to resolve before assigning the result to the currentUser variable
  const currentUser = await getCurrentUser()

  // Return a JSX element that contains the DesktopSidebar, MobileFooter, and children components
  return (
    <div className="h-full">
      <DesktopSidebar currentUser={currentUser!} />
      <MobileFooter />
      <main className="lg:pl-20 h-full">{children}</main>
    </div>
  )
}

// Export the Sidebar component as the default export of this module
export default Sidebar
