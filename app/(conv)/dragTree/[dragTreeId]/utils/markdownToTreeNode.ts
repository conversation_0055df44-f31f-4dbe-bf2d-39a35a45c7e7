import { unified } from 'unified'
import remarkParse from 'remark-parse'
import { TreeNode } from '@/app/types'
import { customAl<PERSON>bet } from 'nanoid'

// Create a custom ID generator that uses lowercase letters and numbers
const generateShortId = customAlphabet(
  'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789',
  6
)

// Function to collect text from a node
const collectText = (node: any): string => {
  if (node.type === 'text') return node.value
  if (node.children) return node.children.map(collectText).join('')
  return ''
}

/**
 * Converts markdown to a TreeNode structure.
 * Assumes the markdown is a well-formed tree with proper heading levels.
 * @param markdown - The markdown string to parse
 * @param startingLevel - Optional starting level for the root node (defaults to 1)
 */
const markdownToTreeNode = (
  markdown: string,
  startingLevel: number = 1
): TreeNode | null => {
  const processor = unified().use(remarkParse)
  const ast = processor.parse(markdown)

  // Initialize stack
  const stack: { level: number; node: TreeNode }[] = []

  // Initialize root as null; it will be set to the first heading
  let root: TreeNode | null = null

  ast.children.forEach((node: any) => {
    if (node.type === 'heading') {
      const level = node.depth
      const label = collectText(node).trim()

      // Generate ID based on level, adjusted by startingLevel
      const actualLevel = startingLevel + (level - 1)
      // Special case: preserve L1:1 as the root node ID for React Flow centering
      const id =
        actualLevel === 1 && !root
          ? 'L1:1'
          : `L${actualLevel}:${generateShortId()}`

      const newTreeNode: TreeNode = {
        id,
        label,
        type: 'category', // Default type; will adjust later
        children: [],
        isInterestedIn: false,
      }

      if (!root) {
        // The first heading becomes the root
        root = newTreeNode
        stack.push({ level, node: root })
        return
      }

      // Find the correct parent based on the current level
      while (stack.length > 0 && stack[stack.length - 1].level >= level) {
        stack.pop()
      }

      if (stack.length === 0) {
        // If no parent found, attach to root
        if (root) {
          root.children.push(newTreeNode)
          stack.push({ level, node: newTreeNode })
        }
      } else {
        // Attach as child to the last item in the stack
        const parent = stack[stack.length - 1].node
        parent.children.push(newTreeNode)
        stack.push({ level, node: newTreeNode })
      }
    }
  })

  if (!root) {
    console.warn('No headings found in the markdown.')
    return null
  }

  // Function to assign types based on children
  const assignTypes = (node: TreeNode): void => {
    if (node.children.length === 0) {
      node.type = 'question'
    } else {
      node.type = 'category'
      node.children.forEach(assignTypes)
    }
  }

  // Assign types starting from the root
  assignTypes(root)

  return root
}

export default markdownToTreeNode
