import { useState, useCallback, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { toast } from 'react-hot-toast'
import { useScreeningDiagnosis } from './useScreeningDiagnosis'
import { useScreeningRephrase } from './useScreeningRephrase'
import type { ScreenObjectType } from '@/app/(conv)/screening/constants/mockData'
import type { SupportedLanguageCode } from '@/app/(conv)/screening/constants/languages'
import { SUPPORTED_LANGUAGES } from '@/app/(conv)/screening/constants/languages'
import {
  screeningRequestSchema,
  initializeDragTreeSchema,
} from '@/app/libs/api-schemas'
import { isDevelopment } from '@/lib/environment'
import { addNewDragTreeToSidebar } from '@/app/libs/sidebar-events'
import { useRouter } from 'next/navigation'
import { SubscriptionTier } from '@prisma/client'
import { getTierPermissions } from '@/app/configs/tier-permissions'
import { logEventWithContext } from '@/app/libs/logging'

export type UseScreeningActionsReturn = {
  // State
  description: string
  selectedLanguage: SupportedLanguageCode
  useSimulator: boolean // Renamed from useStaticData
  screenObject: ScreenObjectType | null
  rephrased: string[]
  selectedSuggestion: string

  // Workflow state
  canStartClarification: boolean // Only true after paraphrase AND user selection
  hasUserMadeSelection: boolean // Tracks if user selected a suggestion or "use my input"
  showUseMyInputButton: boolean // Show "Use My Input" button only when suggestions exist and no selection made

  // Streaming states
  isStreamingDiagnosis: boolean
  isStreamingRephrase: boolean
  isProcessing: boolean // Combined processing state

  // Actions
  setDescription: (value: string) => void
  setSelectedLanguage: (value: SupportedLanguageCode) => void
  setUseSimulator: (value: boolean) => void // Renamed from setUseStaticData
  clearResults: () => void
  handleParaphraseAndAnalyze: () => void // Step 1: Main action - calls both APIs
  handleStartClarification: () => void // Step 2: Navigate to drag tree (only available after step 1 + selection)
  handleSuggestionSelection: (suggestion: string) => void // Handles suggestion clicks
  handleUseMyInput: () => void // Handles "use my input" clicks
}

/**
 * Main custom hook for coordinating screening actions
 * Orchestrates diagnosis and rephrase operations with both real API and simulator modes
 * Enforces proper workflow: Step 1 (Paraphrase) → User Selection → Step 2 (Clarification)
 */
export const useScreeningActions = (): UseScreeningActionsReturn => {
  const { data: session } = useSession()
  const router = useRouter()

  // Core state
  const [description, setDescription] = useState<string>('')
  const [selectedLanguage, setSelectedLanguage] =
    useState<SupportedLanguageCode>(SUPPORTED_LANGUAGES.EN)
  const [useSimulator, setUseSimulator] = useState<boolean>(false) // Renamed and default to real API
  const [screenObject, setScreenObject] = useState<ScreenObjectType | null>(
    null
  )
  const [rephrased, setRephrased] = useState<string[]>([])
  const [selectedSuggestion, setSelectedSuggestion] = useState<string>('')

  // Selection tracking state - users must pick a suggestion or "use my input"
  const [hasUserMadeSelection, setHasUserMadeSelection] =
    useState<boolean>(false)

  // Placeholder declarations – will be defined after we know the processing status.
  let isAnalysisComplete = false
  let canStartClarification = false

  // Create stable callback wrappers to prevent hook re-initialization
  const handleScreenObjectUpdate = useCallback(
    (screenObject: ScreenObjectType | null) => {
      setScreenObject(screenObject)
    },
    []
  )

  const handleRephrasedUpdate = useCallback((rephrased: string[]) => {
    setRephrased(rephrased)
  }, [])

  // Diagnosis streaming hook with simulator support
  const diagnosisHook = useScreeningDiagnosis({
    onScreenObjectUpdate: handleScreenObjectUpdate,
    useSimulator,
  })

  // Rephrase streaming hook with simulator support
  const rephraseHook = useScreeningRephrase({
    description,
    selectedLanguage,
    onRephrasedUpdate: handleRephrasedUpdate,
    useSimulator,
  })

  // Extract stable functions to prevent useCallback dependency issues
  const { submit: diagnosisSubmit, isStreaming: diagnosisIsStreaming } =
    diagnosisHook
  const { startRephrase: rephraseStart, isStreaming: rephraseIsStreaming } =
    rephraseHook

  // Combined processing state
  const isProcessing = diagnosisIsStreaming || rephraseIsStreaming

  // Now that we know processing status, compute derived workflow states
  isAnalysisComplete =
    !isProcessing && screenObject !== null && rephrased.length > 0

  canStartClarification =
    isAnalysisComplete &&
    description.trim().length >= 10 &&
    hasUserMadeSelection

  // Helper function to log only in development
  const devLog = (message: string, data?: any) => {
    if (isDevelopment()) {
      if (data) {
        console.log(message, data)
      } else {
        console.log(message)
      }
    }
  }

  // Processing state tracking
  useEffect(() => {
    devLog('🔄 [SCREENING] Processing state:', {
      isProcessing,
      diagnosisStreaming: diagnosisIsStreaming,
      rephraseStreaming: rephraseIsStreaming,
      mode: useSimulator ? 'Simulator' : 'Real API',
    })
  }, [isProcessing, diagnosisIsStreaming, rephraseIsStreaming, useSimulator])

  // Screen object updates
  useEffect(() => {
    if (screenObject) {
      devLog('📊 [SCREENING] Screen analysis received:', {
        problem_ambiguity:
          screenObject.problem_ambiguity?.slice(0, 100) + '...',
        intention_count: screenObject.intention?.length || 0,
        entity_count: screenObject.entity?.length || 0,
        mode: useSimulator ? 'Simulator' : 'Real API',
      })
    }
  }, [screenObject, useSimulator])

  // Suggestions updates
  useEffect(() => {
    if (rephrased.length > 0) {
      devLog('📝 [SCREENING] Suggestions received:', {
        count: rephrased.length,
        firstSuggestion: rephrased[0]?.slice(0, 100) + '...',
        mode: useSimulator ? 'Simulator' : 'Real API',
      })
    }
  }, [rephrased.length, useSimulator])

  // Workflow state tracking
  useEffect(() => {
    devLog('✅ [SCREENING] Workflow state:', {
      canStartClarification,
      isAnalysisComplete,
      hasUserMadeSelection,
      showUseMyInputButton: rephrased.length > 0 && !hasUserMadeSelection,
      mode: useSimulator ? 'Simulator' : 'Real API',
    })
  }, [
    canStartClarification,
    isAnalysisComplete,
    hasUserMadeSelection,
    useSimulator,
  ])

  // Clear all results and reset workflow
  const clearResults = useCallback(() => {
    setScreenObject(null)
    setRephrased([])
    setHasUserMadeSelection(false) // Reset selection state
    setSelectedSuggestion('') // Reset selected suggestion
  }, [])

  // Standard setDescription without aggressive workflow reset
  const handleSetDescription = useCallback(
    (newDescription: string) => {
      setDescription(newDescription)
      // Only clear results if user completely clears the description
      if (newDescription.trim().length === 0) {
        clearResults()
      }
      // Reset selection when description changes significantly
      if (Math.abs(newDescription.length - description.length) > 10) {
        setHasUserMadeSelection(false)
      }
    },
    [clearResults, description.length]
  )

  // STEP 1: Paraphrase and analyze the user's description
  const handleParaphraseAndAnalyze = useCallback(async () => {
    devLog('🔍 [SCREENING] handleParaphraseAndAnalyze called', {
      description: description?.slice(0, 100) + '...',
      useSimulator,
      selectedLanguage,
      isCurrentlyProcessing: isProcessing,
      diagnosisStreaming: diagnosisIsStreaming,
      rephraseStreaming: rephraseIsStreaming,
      hasUserSession: !!session?.user?.id,
    })

    // Block VIEWER or users who have used up quota from initiating any API actions
    try {
      const userTier = ((session?.user as any)?.subscription_tier ||
        (session?.user as any)?.subscription?.tier ||
        SubscriptionTier.FREE) as SubscriptionTier
      const { maxDragTrees } = getTierPermissions(userTier)
      if (userTier === SubscriptionTier.VIEWER || maxDragTrees <= 0) {
        toast.error(
          'Your current plan does not allow creating drag trees. Please upgrade.'
        )
        return
      }
    } catch {}

    if (!description.trim()) {
      console.warn('❌ [SCREENING] Cannot analyze - no description provided')
      toast.error('Please enter a description first.')
      return
    }

    // Frontend validation using shared schema
    const screeningData = {
      userId: session?.user?.id || '',
      description: description.trim(),
      preferredLanguage: selectedLanguage,
    }

    const validationResult = screeningRequestSchema.safeParse(screeningData)
    if (!validationResult.success) {
      console.warn(
        '❌ [SCREENING] Frontend validation failed:',
        validationResult.error.flatten()
      )
      const errors = validationResult.error.flatten()
      const errorMessage =
        errors.fieldErrors.description?.[0] ||
        errors.fieldErrors.userId?.[0] ||
        'Please check your input and try again.'
      toast.error(errorMessage)
      return
    }

    if (isProcessing) {
      console.warn('⏳ [SCREENING] Cannot analyze - already processing')
      toast.error('Please wait for the current analysis to complete.')
      return
    }

    try {
      // Log the paraphrase and analyze event
      logEventWithContext(
        'click_screening_paraphraseAndAnalyze',
        session?.user?.id,
        undefined, // No dragTreeId yet
        {
          description_length: description.trim().length,
          selected_language: selectedLanguage,
          use_simulator: useSimulator,
        }
      )

      // Reset selection state when starting new analysis
      setHasUserMadeSelection(false)
      setSelectedSuggestion('')

      devLog(
        '🚀 [SCREENING] Starting real API calls for diagnosis and rephrase'
      )

      // Start both diagnosis and rephrase streams in parallel
      // Use validated data to ensure consistency
      diagnosisSubmit({
        ...validationResult.data,
        preferredLanguage: validationResult.data
          .preferredLanguage as SupportedLanguageCode,
      })

      rephraseStart()

      devLog('✅ [SCREENING] Both streams initiated successfully')
    } catch (error) {
      console.error(
        '❌ [SCREENING] Error during paraphrase and analyze:',
        error
      )
      const mode = useSimulator ? 'simulator' : 'real API'
      toast.error(`Failed to start ${mode} analysis. Please try again.`)
    }
  }, [
    description,
    useSimulator,
    selectedLanguage,
    isProcessing,
    diagnosisSubmit,
    rephraseStart,
    session?.user?.id,
    diagnosisIsStreaming,
    rephraseIsStreaming,
  ])

  // STEP 2: Start clarification flow (navigation to drag tree)
  const handleStartClarification = useCallback(async () => {
    devLog('🎯 [SCREENING] handleStartClarification called', {
      canStartClarification,
      selectedSuggestion: selectedSuggestion?.slice(0, 100) + '...',
    })

    // Block VIEWER or users at quota from starting clarification
    try {
      const userTier = ((session?.user as any)?.subscription_tier ||
        (session?.user as any)?.subscription?.tier ||
        SubscriptionTier.FREE) as SubscriptionTier
      const { maxDragTrees } = getTierPermissions(userTier)
      if (userTier === SubscriptionTier.VIEWER || maxDragTrees <= 0) {
        toast.error(
          'Your current plan does not allow creating drag trees. Please upgrade.'
        )
        return
      }
    } catch {}

    if (!canStartClarification) {
      console.warn(
        '❌ [SCREENING] Cannot start clarification - requirements not met'
      )
      toast.error('Please complete paraphrasing and select a suggestion first.')
      return
    }

    if (!session?.user?.id) {
      toast.error('Please sign in to continue.')
      return
    }

    try {
      // Log the start clarification event
      logEventWithContext(
        'click_screening_startClarification',
        session?.user?.id,
        undefined, // No dragTreeId yet
        {
          description_length: description.trim().length,
          selected_suggestion: selectedSuggestion.slice(0, 100), // Truncate for logging
          selected_language: selectedLanguage,
        }
      )

      devLog('🚀 [SCREENING] Creating drag tree record...')

      // Ensure the title does not exceed the backend validation limit (255 chars)
      const MAX_TITLE_LENGTH = 255
      const trimmedTitle =
        selectedSuggestion.length > MAX_TITLE_LENGTH
          ? selectedSuggestion.slice(0, MAX_TITLE_LENGTH)
          : selectedSuggestion

      // Frontend validation for drag tree initialization using shared schema
      const dragTreeData = {
        // Use trimmed title to satisfy zod validation and DB constraints
        title: trimmedTitle,
        description: selectedSuggestion,
        preferredLanguage: selectedLanguage,
        analysisData: screenObject
          ? {
              is_problem_clear: screenObject.is_problem_clear || false,
              problem_ambiguity: screenObject.problem_ambiguity || '',
              intention: screenObject.intention || [],
              entity: screenObject.entity || [],
              score: screenObject.score || 3, // Use actual score from screenObject, fallback to 3
              pass: screenObject.pass || false,
            }
          : undefined,
      }

      const dragTreeValidation =
        initializeDragTreeSchema.safeParse(dragTreeData)
      if (!dragTreeValidation.success) {
        console.warn(
          '❌ [SCREENING] Drag tree validation failed:',
          dragTreeValidation.error.flatten()
        )
        const errors = dragTreeValidation.error.flatten()
        const errorMessage =
          errors.fieldErrors.title?.[0] ||
          errors.fieldErrors.description?.[0] ||
          'Invalid data for creating clarification tree.'
        toast.error(errorMessage)
        return
      }

      // Create drag tree record in the backend using validated data
      const response = await fetch('/api/dragtree/initialize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dragTreeValidation.data),
      })

      const result = await response.json()

      if (!response.ok || !result.success) {
        // Handle quota enforcement errors for upsell messaging
        if (result?.code === 'DRAGTREE_LIMIT_REACHED') {
          try {
            const tier = ((session?.user as any)?.subscription?.tier ||
              (session?.user as any)?.subscription_tier ||
              SubscriptionTier.FREE) as SubscriptionTier
            const { maxDragTrees } = getTierPermissions(tier)
            toast.error(
              tier === SubscriptionTier.FREE
                ? `Under Free tier, you can create up to ${maxDragTrees} drag trees. Upgrade to PRO to unlock more.`
                : 'You have reached the maximum number of drag trees for your plan.'
            )
          } catch {}
        }
        throw new Error(result.error || 'Failed to initialize drag tree')
      }

      const { dragTreeId, title, status } = result.data
      devLog('✅ [SCREENING] Drag tree created:', dragTreeId)

      // Add the new drag tree to the sidebar immediately
      addNewDragTreeToSidebar({
        id: dragTreeId,
        title: title || selectedSuggestion || 'Untitled Drag Tree',
        status: status || 'INITIALIZED',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })

      toast.success('Drag tree initialized! Redirecting...')

      // Navigate to drag tree page using Next.js client-side routing for smoother UX
      router.push(`/dragTree/${dragTreeId}`)
    } catch (error) {
      console.error('❌ [SCREENING] Error creating drag tree:', error)
      toast.error(`Failed to start clarification: ${error}`)
    }
  }, [
    canStartClarification,
    selectedSuggestion,
    session?.user?.id,
    screenObject,
    selectedLanguage,
    router,
  ])

  // Handler for when a user clicks a rephrased suggestion
  const handleSuggestionSelection = useCallback((suggestion: string) => {
    devLog('👍 [SCREENING] User selected suggestion:', {
      suggestion: suggestion.slice(0, 100) + '...',
    })
    setSelectedSuggestion(suggestion)
    setDescription(suggestion) // Also update the main description
    setHasUserMadeSelection(true)
  }, [])

  // Handle "use my input" selection
  const handleUseMyInput = useCallback(() => {
    devLog('📝 [SCREENING] User chose to use original input')
    setSelectedSuggestion(description.trim())
    setHasUserMadeSelection(true)
  }, [description])

  return {
    // State
    description,
    selectedLanguage,
    useSimulator, // Renamed from useStaticData
    screenObject,
    rephrased,
    selectedSuggestion,

    // Workflow state
    canStartClarification,
    hasUserMadeSelection,
    showUseMyInputButton: rephrased.length > 0 && !hasUserMadeSelection,

    // Streaming states
    isStreamingDiagnosis: diagnosisIsStreaming,
    isStreamingRephrase: rephraseIsStreaming,
    isProcessing,

    // Actions
    setDescription: handleSetDescription,
    setSelectedLanguage,
    setUseSimulator, // Renamed from setUseStaticData
    clearResults,
    handleParaphraseAndAnalyze,
    handleStartClarification,
    handleSuggestionSelection,
    handleUseMyInput,
  }
}
