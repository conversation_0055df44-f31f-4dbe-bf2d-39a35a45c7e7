'use client'

import { useState, useEffect, useMemo } from 'react'
import { cn } from '@/lib/utils'

type LoadingPhase = 'initializing' | 'generating' | 'finalizing' | 'retrieving'

type FuturisticLoadingProps = {
  completion: string
  isComplete: boolean
  onGenerationComplete: () => void
}

type Entity = {
  id: string
  phrase: string
  x: number
  y: number
  vx: number
  vy: number
  opacity: number
  size: number
}

type ConceptConstellationProps = {
  entities: Entity[]
}

// Helper function to extract categories and filter content - moved outside component to prevent re-creation
function extractCategories(text: string): string[] {
  return text
    .split('\n')
    .map(line => line.trim())
    .filter(line => line.startsWith('#')) // only markdown headings
    .map(line => line.replace(/^#+\s*/, '')) // remove leading # and space
    .filter(Boolean)
}

// Subtle floating particles
const FloatingParticles = () => {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {Array.from({ length: 15 }).map((_, i) => (
        <div
          key={i}
          className={cn(
            'absolute w-0.5 h-0.5 bg-cyan-200/40 rounded-full', // Softer color
            'animate-float-random'
          )}
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            animationDelay: `${Math.random() * 5}s`,
            animationDuration: `${4 + Math.random() * 3}s`,
          }}
        />
      ))}
    </div>
  )
}

// More subtle, abstract data flow lines
const FlowingLines = () => {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      <svg
        className="w-full h-full opacity-10"
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
      >
        <defs>
          <linearGradient id="flowGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="transparent" />
            <stop offset="50%" stopColor="rgb(107 114 128)" />
            <stop offset="100%" stopColor="transparent" />
          </linearGradient>
        </defs>
        {Array.from({ length: 4 }).map((_, i) => (
          <path
            key={i}
            d={`M -10 ${Math.random() * 100} C ${Math.random() * 100} ${Math.random() * 100} ${Math.random() * 100} ${Math.random() * 100} 110 ${Math.random() * 100}`}
            stroke="url(#flowGradient)"
            strokeWidth="1"
            fill="none"
            className="animate-flow-line"
            style={{
              animationDelay: `${i * 1.5}s`,
              animationDuration: `${6 + i * 2}s`,
            }}
          />
        ))}
      </svg>
    </div>
  )
}

// Progress wave animation (adjusted for new palette)
const ProgressWave = ({ progress }: { progress: number }) => {
  return (
    <div className="relative w-full h-1 bg-gray-700/50 rounded-full overflow-hidden">
      <div
        className="absolute inset-0 bg-gradient-to-r from-teal-500 via-cyan-500 to-teal-400 rounded-full transition-all duration-1000 ease-out"
        style={{
          width: `${progress}%`,
          backgroundSize: '200% 100%',
          animation: 'wave-flow 2s linear infinite',
        }}
      />
      <div
        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-full animate-shimmer"
        style={{
          width: `${progress}%`,
        }}
      />
    </div>
  )
}

// Refined "Concept Constellation" visualization
const ConceptConstellation = ({ entities }: ConceptConstellationProps) => {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      <svg
        className="w-full h-full"
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
      >
        <defs>
          <linearGradient
            id="connection-gradient-refined"
            x1="0%"
            y1="0%"
            x2="100%"
            y2="100%"
          >
            <stop offset="0%" stopColor="rgba(209, 213, 219, 0.1)" />
            <stop offset="100%" stopColor="rgba(209, 213, 219, 0.3)" />
          </linearGradient>
        </defs>

        {/* Subtle connection lines */}
        {entities.map(entity =>
          entities
            .filter(otherEntity => {
              const distance = Math.sqrt(
                Math.pow(entity.x - otherEntity.x, 2) +
                  Math.pow(entity.y - otherEntity.y, 2)
              )
              return (
                distance < 18 && distance > 5 && entity.id !== otherEntity.id
              )
            })
            .slice(0, 1) // Only one connection for a cleaner look
            .map(connectedEntity => (
              <path
                key={`${entity.id}-${connectedEntity.id}`}
                d={`M ${entity.x} ${entity.y} L ${connectedEntity.x} ${connectedEntity.y}`}
                stroke="url(#connection-gradient-refined)"
                strokeWidth="0.5"
                fill="none"
                opacity={
                  Math.min(entity.opacity, connectedEntity.opacity) * 0.5
                }
              />
            ))
        )}
      </svg>

      {/* Concept Orbs */}
      {entities.map(entity => (
        <div
          key={entity.id}
          className="absolute flex items-center justify-center pointer-events-none select-none"
          style={{
            left: `${entity.x}%`,
            top: `${entity.y}%`,
            opacity: entity.opacity,
            transform: `translate(-50%, -50%) scale(${entity.size})`,
          }}
        >
          <div className="absolute w-16 h-16 bg-cyan-500/10 rounded-full blur-lg" />
          <div className="absolute w-8 h-8 bg-cyan-500/20 rounded-full blur-md" />
          <span
            className="relative text-xs font-medium text-cyan-100"
            style={{ textShadow: '0 0 5px rgba(207, 250, 254, 0.5)' }}
          >
            {entity.phrase}
          </span>
        </div>
      ))}
    </div>
  )
}

export default function FuturisticLoadingDemo({
  completion,
  isComplete,
  onGenerationComplete,
}: FuturisticLoadingProps) {
  const [progress, setProgress] = useState<number>(0)
  const [currentPhase, setCurrentPhase] = useState<LoadingPhase>('initializing')
  const [uiProgress, setUiProgress] = useState<number>(0)

  // State for the concept constellation animation
  const [entities, setEntities] = useState<Entity[]>([])
  const [lastProcessedIndex, setLastProcessedIndex] = useState(0)

  // Memoize streamed lines to avoid re-calculating on every render
  const streamedLines = useMemo(
    () => (completion ? extractCategories(completion) : []),
    [completion]
  )

  // Add new entities as streaming data comes in
  useEffect(() => {
    if (streamedLines.length > lastProcessedIndex) {
      const newItems = streamedLines.slice(lastProcessedIndex)

      newItems.forEach((phrase, index) => {
        setTimeout(() => {
          if (!phrase) return

          const edges: Array<'top' | 'bottom' | 'left' | 'right'> = [
            'top',
            'bottom',
            'left',
            'right',
          ]
          const entryEdge = edges[Math.floor(Math.random() * edges.length)]

          let startX, startY, vx, vy
          switch (entryEdge) {
            case 'top':
              startX = Math.random() * 100
              startY = -5
              vx = (Math.random() - 0.5) * 0.1
              vy = 0.05 + Math.random() * 0.1
              break
            case 'bottom':
              startX = Math.random() * 100
              startY = 105
              vx = (Math.random() - 0.5) * 0.1
              vy = -(0.05 + Math.random() * 0.1)
              break
            case 'left':
              startX = -15
              startY = Math.random() * 100
              vx = 0.05 + Math.random() * 0.1
              vy = (Math.random() - 0.5) * 0.1
              break
            case 'right':
              startX = 115
              startY = Math.random() * 100
              vx = -(0.05 + Math.random() * 0.1)
              vy = (Math.random() - 0.5) * 0.1
              break
          }

          setEntities(prev => [
            ...prev,
            {
              id: `${phrase}-${Date.now()}-${Math.random()}`,
              phrase,
              x: startX,
              y: startY,
              vx,
              vy,
              opacity: 0.8 + Math.random() * 0.2,
              size: 0.7 + Math.random() * 0.6,
            },
          ])
        }, index * 800) // Stagger new entities by 800ms each
      })

      setLastProcessedIndex(streamedLines.length)
    }
  }, [streamedLines, lastProcessedIndex])

  // Update entity positions (animation loop)
  useEffect(() => {
    const interval = setInterval(() => {
      setEntities(prev =>
        prev.map(entity => {
          const randomForceX = (Math.random() - 0.5) * 0.005
          const randomForceY = (Math.random() - 0.5) * 0.005

          // Add a gentle force pulling entities toward the center
          const centerForceX = (50 - entity.x) * 0.0002
          const centerForceY = (50 - entity.y) * 0.0002

          const newVx = (entity.vx + randomForceX + centerForceX) * 0.98
          const newVy = (entity.vy + randomForceY + centerForceY) * 0.98

          let newX = entity.x + newVx
          let newY = entity.y + newVy

          if (newX < -25) newX = 125
          if (newX > 125) newX = -25
          if (newY < -15) newY = 115
          if (newY > 115) newY = -15

          return { ...entity, x: newX, y: newY, vx: newVx, vy: newVy }
        })
      )
    }, 50)

    return () => clearInterval(interval)
  }, [])

  // Update progress based on completion length
  useEffect(() => {
    if (completion) {
      // Estimate progress based on content length (more realistic approximation)
      const estimatedProgress = Math.min((completion.length / 3000) * 100, 95)
      setProgress(estimatedProgress)

      // Update phase based on progress (simplified phases)
      if (estimatedProgress > 70) setCurrentPhase('finalizing')
      else if (estimatedProgress > 10) setCurrentPhase('generating')
      else setCurrentPhase('initializing')
    }
  }, [completion])

  // When parent signals completion, set progress to 100
  useEffect(() => {
    if (isComplete) {
      setProgress(100)
      setCurrentPhase('retrieving') // Or a new 'complete' phase
    }
  }, [isComplete])

  // Handle UI progress throttling for fast API responses
  useEffect(() => {
    if (progress > uiProgress) {
      // Throttle UI progress to be more gradual even with fast API
      const interval = setInterval(() => {
        setUiProgress(prev => {
          const target = progress
          if (prev >= target) {
            clearInterval(interval)
            return target
          }
          // Smooth increment - adjust speed as needed
          const increment = Math.max(1, (target - prev) * 0.1)
          return Math.min(prev + increment, target)
        })
      }, 50) // Update every 50ms for smooth animation

      return () => clearInterval(interval)
    }
  }, [progress, uiProgress])

  // Simplified color palette
  const phaseColors = {
    initializing: 'from-gray-500 to-blue-500',
    generating: 'from-blue-500 to-cyan-500',
    finalizing: 'from-cyan-500 to-teal-400',
    retrieving: 'from-teal-400 to-green-500',
  }

  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center p-6 relative overflow-hidden">
      {/* Animated content that is always visible */}
      <div className="absolute inset-0 transition-opacity duration-700 z-20">
        <FloatingParticles />
        <FlowingLines />
        <ConceptConstellation entities={entities} />
      </div>

      {/* Main Loading Container */}
      <div className="relative max-w-sm w-full transition-all duration-700 z-30">
        <div className="relative backdrop-blur-lg bg-gray-800/40 border border-white/10 rounded-xl p-4 shadow-2xl">
          <div
            className={cn(
              'absolute inset-0 rounded-xl opacity-20',
              'bg-gradient-to-r',
              phaseColors[currentPhase]
            )}
            style={{
              animation: 'pulse-soft 4s ease-in-out infinite',
            }}
          />

          <div className="relative z-10 space-y-4">
            <div className="text-center space-y-2">
              <div className="inline-flex items-center gap-2 px-3 py-1 rounded-full bg-teal-500/10 border border-teal-500/20">
                <div
                  className={cn(
                    'w-2 h-2 rounded-full',
                    isComplete ? 'bg-green-400' : 'bg-teal-400 animate-pulse'
                  )}
                />
                <span className="text-teal-300 text-xs font-medium">
                  {isComplete
                    ? 'Complete'
                    : currentPhase.charAt(0).toUpperCase() +
                      currentPhase.slice(1)}
                </span>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-xs">
                <span className="text-gray-400">Status</span>
                <span className="text-gray-300 font-mono">
                  {Math.round(progress)}%
                </span>
              </div>
              <ProgressWave progress={progress} />
            </div>

            {isComplete ? (
              <>
                {/* View Results Button (replaces log when complete) */}
                <div className="bg-gray-900/50 rounded p-3 border border-gray-700/50 flex items-center justify-center min-h-[120px]">
                  <button
                    onClick={onGenerationComplete}
                    className="group relative overflow-hidden bg-gradient-to-r from-teal-500 to-cyan-500 hover:from-teal-400 hover:to-cyan-400 text-white font-medium py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                  >
                    <span className="relative z-10 flex items-center gap-2">
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                        />
                      </svg>
                      View Results
                    </span>
                    <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700" />
                  </button>
                </div>

                <div className="grid grid-cols-3 gap-2 pt-2 border-t border-white/10 text-center">
                  <div>
                    <div className="text-sm font-mono text-cyan-400">
                      {Math.round(progress * 8.47)}ms
                    </div>
                    <div className="text-xs text-gray-500">Elapsed</div>
                  </div>
                  <div>
                    <div className="text-sm font-mono text-cyan-400">
                      {streamedLines.length}
                    </div>
                    <div className="text-xs text-gray-500">Concepts</div>
                  </div>
                  <div>
                    <div className="text-sm font-mono text-cyan-400">100%</div>
                    <div className="text-xs text-gray-500">Complete</div>
                  </div>
                </div>
              </>
            ) : (
              <>
                {/* Generated Categories Display */}
                <div className="bg-gray-900/50 rounded p-3 border border-gray-700/50 min-h-[120px]">
                  {streamedLines.length > 0 ? (
                    <div className="space-y-1">
                      <div className="text-xs text-gray-400 mb-2">
                        Generated categories:
                      </div>
                      {streamedLines
                        .slice(-4)
                        .map((line: string, index: number) => (
                          <div
                            key={index}
                            className="text-xs text-gray-300 font-mono break-words truncate"
                          >
                            {line}
                          </div>
                        ))}
                      {streamedLines.length > 4 && (
                        <div className="text-xs text-gray-500">
                          ... and {streamedLines.length - 4} more
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <div className="text-center space-y-2">
                        <div className="text-sm text-gray-300">
                          {'Generating content...'}
                        </div>

                        <div className="flex items-center justify-center gap-1">
                          <div className="w-1 h-1 bg-cyan-400 rounded-full animate-pulse" />
                          <div
                            className="w-1 h-1 bg-cyan-400 rounded-full animate-pulse"
                            style={{ animationDelay: '0.2s' }}
                          />
                          <div
                            className="w-1 h-1 bg-cyan-400 rounded-full animate-pulse"
                            style={{ animationDelay: '0.4s' }}
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-3 gap-2 pt-2 border-t border-white/10 text-center">
                  <div>
                    <div className="text-sm font-mono text-cyan-400">
                      {Math.round(progress * 8.47)}ms
                    </div>
                    <div className="text-xs text-gray-500">Elapsed</div>
                  </div>
                  <div>
                    <div className="text-sm font-mono text-cyan-400">
                      {streamedLines.length}
                    </div>
                    <div className="text-xs text-gray-500">Concepts</div>
                  </div>
                  <div>
                    <div className="text-sm font-mono text-cyan-400">
                      {Math.round(uiProgress)}%
                    </div>
                    <div className="text-xs text-gray-500">Complete</div>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Custom Animations Styles */}
      <style jsx>{`
        @keyframes float-random {
          0%,
          100% {
            transform: translateY(0px) translateX(0px);
            opacity: 0.2;
          }
          25% {
            transform: translateY(-15px) translateX(8px);
            opacity: 0.7;
          }
          50% {
            transform: translateY(-8px) translateX(-10px);
            opacity: 0.5;
          }
          75% {
            transform: translateY(-20px) translateX(5px);
            opacity: 0.7;
          }
        }
        @keyframes flow-line {
          from {
            stroke-dashoffset: 1000;
          }
          to {
            stroke-dashoffset: 0;
          }
        }
        @keyframes wave-flow {
          0% {
            background-position: 0% 50%;
          }
          100% {
            background-position: 200% 50%;
          }
        }
        @keyframes shimmer {
          0% {
            transform: translateX(-100%);
          }
          100% {
            transform: translateX(100%);
          }
        }
        @keyframes pulse-soft {
          0%,
          100% {
            opacity: 0.2;
          }
          50% {
            opacity: 0.4;
          }
        }
        .animate-float-random {
          animation: float-random linear infinite;
        }
        .animate-flow-line {
          stroke-dasharray: 500;
          animation: flow-line linear infinite;
        }
        .animate-shimmer {
          animation: shimmer 2s ease-in-out infinite;
        }
      `}</style>
    </div>
  )
}
