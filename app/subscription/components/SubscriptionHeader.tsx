import React from "react";

type SubscriptionHeaderProps = {
  isSubscribed: boolean;
};

export const SubscriptionHeader: React.FC<SubscriptionHeaderProps> = ({
  isSubscribed,
}) => {
  if (isSubscribed) return null;

  return (
    <>
      <h2 className="text-2xl font-bold text-center pt-2">
        🎉 Woohoo! Thanks for considering subscription!
      </h2>
      <h2 className="text-xl text-center pt-2">
        Our goal is to assist everyone in thinking clearly, and we are thrilled
        to have your support 🤗✨
      </h2>
    </>
  );
};
