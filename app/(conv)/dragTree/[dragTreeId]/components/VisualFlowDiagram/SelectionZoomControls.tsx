'use client'
import React from 'react'
import { Button } from '@/components/ui/button'
import { Plus, Minus, Maximize2 } from 'lucide-react'
import { useReactFlow } from 'reactflow'
import { cn } from '@/lib/utils'
import { useSelectionStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useSelectionStore'
import { useKeyboardFeedback } from '@/app/(conv)/dragTree/[dragTreeId]/hooks/useSelectionKeyboard'

type SelectionZoomControlsProps = {
  className?: string
}

/**
 * SelectionZoomControls Component
 *
 * Custom zoom controls that work properly in selection mode
 * Replaces the default React Flow controls when in selection mode
 */
export const SelectionZoomControls: React.FC<SelectionZoomControlsProps> = ({
  className,
}) => {
  const { zoomIn, zoomOut, fitView } = useReactFlow()
  const isSelectionMode = useSelectionStore(state => state.isSelectionMode)
  const keyboardFeedback = useKeyboardFeedback()

  // Only show in selection mode
  if (!isSelectionMode) {
    return null
  }

  const handleZoomIn = () => {
    zoomIn()
  }

  const handleZoomOut = () => {
    zoomOut()
  }

  const handleFitView = () => {
    fitView({ padding: 0.1 })
  }

  return (
    <div
      className={cn(
        'absolute bottom-4 left-4 z-30',
        'bg-white/90 backdrop-blur-sm border border-gray-200 rounded-lg shadow-lg',
        'flex flex-col gap-1 p-2',
        className
      )}
      style={{ pointerEvents: 'auto' }}
    >
      <div className="relative">
        <Button
          variant="outline"
          size="sm"
          onClick={handleZoomIn}
          className={cn(
            'h-8 w-8 p-0 hover:bg-blue-50 transition-all duration-200',
            keyboardFeedback?.key === 'ZoomIn' && 'bg-blue-100 scale-95'
          )}
          title="Zoom in (+)"
          aria-label="Zoom in"
        >
          <Plus className="w-4 h-4" />
        </Button>
        {/* Hint removed */}
      </div>

      <div className="relative">
        <Button
          variant="outline"
          size="sm"
          onClick={handleZoomOut}
          className={cn(
            'h-8 w-8 p-0 hover:bg-blue-50 transition-all duration-200',
            keyboardFeedback?.key === 'ZoomOut' && 'bg-blue-100 scale-95'
          )}
          title="Zoom out (-)"
          aria-label="Zoom out"
        >
          <Minus className="w-4 h-4" />
        </Button>
        {/* Hint removed */}
      </div>

      <div className="relative">
        <Button
          variant="outline"
          size="sm"
          onClick={handleFitView}
          className={cn(
            'h-8 w-8 p-0 hover:bg-blue-50 transition-all duration-200',
            keyboardFeedback?.key === 'FitView' && 'bg-blue-100 scale-95'
          )}
          title="Fit view (0)"
          aria-label="Fit view"
        >
          <Maximize2 className="w-4 h-4" />
        </Button>
        {/* Hint removed */}
      </div>

      {/* Footer hint removed */}
    </div>
  )
}
