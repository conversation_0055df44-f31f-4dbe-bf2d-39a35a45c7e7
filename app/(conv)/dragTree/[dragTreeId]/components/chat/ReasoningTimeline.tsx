/* eslint-disable react/display-name */
'use client'

import React, { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  FiChevronDown,
  FiChevronRight,
  FiActivity,
  FiSearch,
} from 'react-icons/fi'
// AiStepType enum no longer needed - using raw provider types

type ExecutionStep = {
  id: string
  stepOrder: number
  type: string // Now accepts raw provider types like 'tool-call', 'thinking', etc.
  metadata: any
  parallelKey?: string | null
  parentStepId?: string | null
  createdAt: string
  updatedAt: string
}

type ReasoningTimelineProps = {
  messageId: string
  stepCount: number
  isStreaming?: boolean
  liveSteps?: ExecutionStep[] // Real-time steps during streaming
  className?: string
}

// Optimized timeline item component with numbered circles and essential info
const OptimizedTimelineItem: React.FC<{
  step: ExecutionStep
  stepNumber: number
  isLast: boolean
  isStreaming: boolean
}> = ({ step, stepNumber, isLast, isStreaming }) => {
  const getStepInfo = () => {
    if (step.type === 'tool-call' || step.type === 'TOOL_CALL') {
      const metadata = step.metadata as any
      const toolName = metadata?.toolName || 'tool'
      const args = metadata?.args || {}

      // Extract key information based on tool type
      if (toolName === 'web_search') {
        const query = args.query || args.input || ''
        return {
          label: 'Web Search',
          detail: query ? `"${query}"` : '',
          icon: <FiSearch className="w-3 h-3" />,
        }
      }

      // Generic tool call
      const keyArg =
        args.query ||
        args.url ||
        args.input ||
        args.text ||
        Object.values(args)[0]
      return {
        label: toolName
          .replace(/_/g, ' ')
          .replace(/\b\w/g, (l: string) => l.toUpperCase()),
        detail: keyArg
          ? `"${String(keyArg).substring(0, 50)}${String(keyArg).length > 50 ? '...' : ''}"`
          : '',
        icon: <FiActivity className="w-3 h-3" />,
      }
    }

    if (step.type === 'thinking' || step.type === 'THOUGHT') {
      const metadata = step.metadata as any
      const summary = metadata?.summary || metadata?.content || ''
      return {
        label: 'Reasoning',
        detail: summary
          ? `${summary.substring(0, 60)}${summary.length > 60 ? '...' : ''}`
          : '',
        icon: <FiActivity className="w-3 h-3" />,
      }
    }

    return {
      label: 'Processing',
      detail: '',
      icon: <FiActivity className="w-3 h-3" />,
    }
  }

  const stepInfo = getStepInfo()
  const isActive = isStreaming && isLast

  return (
    <div className="flex items-start gap-3 py-2">
      {/* Numbered circle */}
      <div
        className={cn(
          'flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium transition-colors',
          isActive
            ? 'bg-blue-500 text-white animate-pulse'
            : 'bg-gray-200 text-gray-600'
        )}
      >
        {isActive ? (
          <FiActivity className="w-3 h-3 animate-spin" />
        ) : (
          stepNumber
        )}
      </div>

      {/* Step content */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          {stepInfo.icon}
          <span
            className={cn(
              'text-sm font-medium',
              isActive ? 'text-blue-700' : 'text-gray-700'
            )}
          >
            {stepInfo.label}
          </span>
        </div>
        {stepInfo.detail && (
          <div className="text-xs text-gray-500 mt-1 break-words">
            {stepInfo.detail}
          </div>
        )}
      </div>
    </div>
  )
}

// Optimized hook to fetch execution steps with caching and performance improvements
function useExecutionSteps(messageId: string) {
  const [steps, setSteps] = useState<ExecutionStep[]>([])
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [isLoaded, setIsLoaded] = useState<boolean>(false)

  // Simple in-memory cache to avoid refetching the same steps
  const cacheRef = React.useRef<Map<string, ExecutionStep[]>>(new Map())

  const fetchSteps = async () => {
    if (isLoaded || isLoading) return

    // Check cache first
    const cachedSteps = cacheRef.current.get(messageId)
    if (cachedSteps) {
      setSteps(cachedSteps)
      setIsLoaded(true)
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      console.log(
        `🔍 [ReasoningTimeline] Fetching steps for message: ${messageId}`
      )

      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 10000) // 10s timeout

      const response = await fetch(`/api/aipane/messages/${messageId}/steps`, {
        signal: controller.signal,
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`Failed to fetch steps: ${response.status}`)
      }

      const data = await response.json()
      const fetchedSteps = data.steps || []

      // Cache the results
      cacheRef.current.set(messageId, fetchedSteps)
      setSteps(fetchedSteps)
      setIsLoaded(true)

      console.log(`✅ [ReasoningTimeline] Loaded ${fetchedSteps.length} steps`)
    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        setError('Request timed out')
      } else {
        console.error('Error fetching execution steps:', err)
        setError(err instanceof Error ? err.message : 'Unknown error')
      }
    } finally {
      setIsLoading(false)
    }
  }

  return {
    steps,
    isLoading,
    error,
    isLoaded,
    fetchSteps,
  }
}

export const ReasoningTimeline: React.FC<ReasoningTimelineProps> = React.memo(
  ({
    messageId,
    stepCount,
    isStreaming = false,
    liveSteps = [],
    className,
  }) => {
    const [isExpanded, setIsExpanded] = useState<boolean>(false) // Initially collapsed
    const { steps, isLoading, error, isLoaded, fetchSteps } =
      useExecutionSteps(messageId)

    // Combine loaded steps with live streaming steps
    const allSteps = isStreaming ? liveSteps : steps

    // Calculate the actual displayable step count (excluding TOOL_RESULT steps)
    // This ensures the count matches what users actually see in the UI
    const getDisplayableStepCount = (stepsToCount: any[]) => {
      return stepsToCount.filter(
        step => step.type !== 'tool-result' && step.type !== 'TOOL_RESULT'
      ).length
    }

    // ───────────────────────────────────────────────
    // Determine step count to display **before** we have full step data.
    // Many messages store TOOL_CALL + TOOL_RESULT pairs (2 entries) plus
    // an optional reasoning summary. Users only care about the "unique"
    // logical steps (tool call + summary). A quick heuristic is:
    //   summary (1) + number_of_pairs  →  storedStepCount ≈ pairs*2 + 1
    // So we halve the stored count and round up to get the display count.
    const approximateUniqueSteps = Math.ceil(stepCount / 2)

    const totalStepCount = isStreaming
      ? getDisplayableStepCount(liveSteps)
      : isLoaded && steps.length > 0
        ? getDisplayableStepCount(steps)
        : approximateUniqueSteps

    // Auto-expand during streaming
    useEffect(() => {
      if (isStreaming && liveSteps.length > 0) {
        setIsExpanded(true)
      }
    }, [isStreaming, liveSteps.length])

    const handleToggle = () => {
      if (!isExpanded && !isLoaded && !isStreaming) {
        // First time expanding - fetch steps
        fetchSteps()
      }
      setIsExpanded(!isExpanded)
    }

    // Don't render if no steps
    if (totalStepCount === 0 && !isStreaming) {
      return null
    }

    const renderToggleButton = () => {
      const hasSteps = totalStepCount > 0
      const showSpinner = isStreaming || isLoading

      return (
        <Button
          variant="ghost"
          size="sm"
          onClick={handleToggle}
          disabled={isLoading}
          aria-expanded={isExpanded}
          aria-label={
            isStreaming
              ? 'AI reasoning in progress'
              : isExpanded
                ? 'Hide reasoning steps'
                : `Show ${totalStepCount} reasoning steps`
          }
          className={cn(
            'h-5 px-2 text-xs font-normal transition-colors',
            isExpanded
              ? 'text-blue-600 bg-blue-50/70 hover:bg-blue-100/70'
              : 'text-gray-400 hover:text-gray-600 hover:bg-gray-50/70'
          )}
        >
          <div className="flex items-center gap-1.5">
            {/* Icon */}
            {showSpinner ? (
              <FiActivity className="w-3 h-3 animate-spin" />
            ) : (
              <FiActivity className="w-3 h-3" />
            )}

            {/* Label */}
            <span>
              {isStreaming
                ? `Thinking... (${totalStepCount})`
                : `${totalStepCount} steps`}
            </span>

            {/* Chevron */}
            {hasSteps &&
              !showSpinner &&
              (isExpanded ? (
                <FiChevronDown className="w-3 h-3" />
              ) : (
                <FiChevronRight className="w-3 h-3" />
              ))}
          </div>
        </Button>
      )
    }

    const renderContent = () => {
      if (!isExpanded) return null

      if (error) {
        return (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="text-sm text-red-600">
              Failed to load reasoning steps: {error}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={fetchSteps}
              className="mt-2 h-7 text-xs text-red-600 hover:text-red-700"
            >
              Try Again
            </Button>
          </div>
        )
      }

      if (isStreaming && liveSteps.length === 0) {
        return (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center gap-2 text-sm text-blue-600">
              <FiActivity className="w-4 h-4 animate-spin" />
              <span>AI is thinking...</span>
            </div>
          </div>
        )
      }

      return (
        <div className="mt-2" role="region" aria-label="AI reasoning steps">
          {/* Optimized timeline view with numbered steps */}
          <div className="max-h-60 overflow-y-auto">
            <div className="bg-gray-50 border border-gray-200 rounded p-3">
              {allSteps
                .filter(
                  step =>
                    step.type !== 'tool-result' && step.type !== 'TOOL_RESULT'
                )
                .map((step, index, filteredSteps) => (
                  <OptimizedTimelineItem
                    key={step.id}
                    step={step}
                    stepNumber={index + 1}
                    isLast={index === filteredSteps.length - 1}
                    isStreaming={isStreaming}
                  />
                ))}
              {isStreaming &&
                allSteps.filter(
                  step =>
                    step.type !== 'tool-result' && step.type !== 'TOOL_RESULT'
                ).length === 0 && (
                  <div className="flex items-start gap-3 py-2">
                    <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-500 text-white flex items-center justify-center text-xs font-medium animate-pulse">
                      <FiActivity className="w-3 h-3 animate-spin" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <FiActivity className="w-3 h-3" />
                        <span className="text-sm font-medium text-blue-700">
                          Starting...
                        </span>
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        AI is beginning to process your request
                      </div>
                    </div>
                  </div>
                )}
            </div>
          </div>
        </div>
      )
    }

    return (
      <div
        className={cn(
          'border border-gray-200/60 rounded-md bg-gray-50/50 p-1.5',
          className
        )}
      >
        {/* Toggle Button */}
        <div className="flex items-center justify-between">
          {renderToggleButton()}
        </div>

        {/* Content */}
        {renderContent()}
      </div>
    )
  },
  (prevProps, nextProps) => {
    // Custom comparison function for memo optimization
    return (
      prevProps.messageId === nextProps.messageId &&
      prevProps.stepCount === nextProps.stepCount &&
      prevProps.isStreaming === nextProps.isStreaming &&
      (prevProps.liveSteps?.length ?? 0) ===
        (nextProps.liveSteps?.length ?? 0) &&
      prevProps.className === nextProps.className
    )
  }
)

export default ReasoningTimeline
