'use client'

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'

export type QuickResearchStatusFooterProps = {
  isStreaming: boolean
  isProcessing: boolean
  isEditable: boolean
  isSaving: boolean
  justSaved: boolean
  hasContent: boolean
}

export const QuickResearchStatusFooter: React.FC<
  QuickResearchStatusFooterProps
> = ({
  isStreaming,
  isProcessing,
  isEditable,
  isSaving,
  justSaved,
  hasContent,
}) => {
  if (!(isStreaming || isProcessing)) return null
  return (
    <div className="flex items-center justify-end text-xs text-gray-400 h-5 pr-2">
      <AnimatePresence mode="wait">
        {isSaving ? (
          <motion.div
            key="saving"
            initial={{ opacity: 0, y: 5 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -5 }}
            style={{ display: 'flex', alignItems: 'center' }}
          >
            <span className="mr-1.5 w-1.5 h-1.5 rounded-full bg-amber-500 animate-pulse"></span>
            Saving...
          </motion.div>
        ) : justSaved ? (
          <motion.div
            key="saved"
            initial={{ opacity: 0, y: 5 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -5 }}
            style={{ color: '#10b981' }}
          >
            Saved
          </motion.div>
        ) : !isEditable && !isStreaming && hasContent ? (
          <motion.div
            key="edit"
            initial={{ opacity: 0, y: 5 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -5 }}
          >
            Click to edit
          </motion.div>
        ) : null}
      </AnimatePresence>
    </div>
  )
}
