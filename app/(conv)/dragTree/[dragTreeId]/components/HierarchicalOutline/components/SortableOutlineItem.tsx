import React from 'react'
import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'

type SortableOutlineItemProps = {
  id: string
  children: React.ReactNode
}

export const SortableOutlineItem: React.FC<SortableOutlineItemProps> = ({
  id,
  children,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id })

  const style: React.CSSProperties = {
    transform: CSS.Transform.toString(transform),
    transition: transition ?? undefined,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 10 : 1,
    position: 'relative',
  }

  // Clone children and pass drag props to them
  const childrenWithProps = React.cloneElement(children as any, {
    dragAttributes: attributes,
    dragListeners: listeners,
    isDragging,
  })

  return (
    <div ref={setNodeRef} style={style} {...attributes}>
      {childrenWithProps}
    </div>
  )
}
