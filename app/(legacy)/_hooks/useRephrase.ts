import { useEffect } from 'react'
import { useChat } from '@ai-sdk/react'
import { DefaultChatTransport } from 'ai'
import mixpanel from '@/app/libs/mixpanel'
import { RephraseRequestType } from '@/app/types/api'
import { extractTextContent } from '@/app/types/ai-sdk5'
import type { Msg } from '@/app/types/ai-sdk5'

type UseRephraseProps = {
  description: string
  userId: string
  onRephraseSuccess: (rephrased: string[]) => void
  lastDescription: string
}

const extractNumberedSuggestions = (text: string): string[] => {
  const lines = text.split('\n')
  const numberedLines = lines.filter(line => /^\d+\./.test(line.trim()))
  return numberedLines.map(line => line.replace(/^\d+\.\s*/, '').trim())
}

const useRephrase = ({
  description,
  userId,
  onRephraseSuccess,
  lastDescription,
}: UseRephraseProps) => {
  const requestBody: RephraseRequestType = {
    userId,
    description,
  }

  const { messages, sendMessage, status } = useChat<Msg>({
    transport: new DefaultChatTransport({
      api: '/api/rephrase',
      body: requestBody,
    }),
  })

  useEffect(() => {
    if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1]
      if (lastMessage) {
        const content = extractTextContent(lastMessage)
        const parsed = [lastDescription, ...extractNumberedSuggestions(content)]
        onRephraseSuccess(parsed)
      }
    }
  }, [messages, onRephraseSuccess, description, lastDescription])

  const isLoading = status === 'streaming' || status === 'submitted'

  const handleRephrase = () => {
    mixpanel.track('start_rephrase_click')
    sendMessage({ text: 'start rephrasing questions' })
  }

  return {
    handleRephrase,
    isLoading,
  }
}

export default useRephrase
