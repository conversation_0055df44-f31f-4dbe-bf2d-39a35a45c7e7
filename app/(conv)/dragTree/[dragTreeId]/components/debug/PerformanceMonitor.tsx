'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useDragTreeStore } from '@/app/stores/dragtree_store'

/**
 * Performance monitoring component for debugging optimization improvements
 * Tracks tree operations, conversions, and database syncs
 */
const PerformanceMonitor: React.FC = () => {
  // Optimized: Use individual selectors to prevent unnecessary re-renders
  const frontendTreeStructure = useDragTreeStore(
    state => state.frontendTreeStructure
  )
  const nodeMap = useDragTreeStore(state => state.nodeMap)
  const [conversionCount, setConversionCount] = useState(0)
  const [lastOperationTime, setLastOperationTime] = useState<number | null>(
    null
  )
  const [operationHistory, setOperationHistory] = useState<
    Array<{
      type: string
      timestamp: number
      duration?: number
      nodeCount?: number
    }>
  >([])

  // Monitor tree structure changes
  useEffect(() => {
    if (frontendTreeStructure) {
      const startTime = performance.now()
      const nodeCount = nodeMap.size
      const endTime = performance.now()
      const duration = endTime - startTime

      setLastOperationTime(duration)
      setOperationHistory(prev => [
        ...prev.slice(-9), // Keep last 10 operations
        {
          type: 'Tree Update',
          timestamp: Date.now(),
          duration,
          nodeCount,
        },
      ])
    }
  }, [frontendTreeStructure, nodeMap])

  // Listen for conversion events (from console logs)
  useEffect(() => {
    const originalConsoleLog = console.log
    const originalConsoleWarn = console.warn

    console.log = (...args) => {
      const message = args.join(' ')
      if (
        message.includes('Converting tree to React Flow format') ||
        message.includes('Converting new tree')
      ) {
        setConversionCount(prev => prev + 1)
      }
      originalConsoleLog.apply(console, args)
    }

    console.warn = (...args) => {
      const message = args.join(' ')
      if (message.includes('Duplicate conversion detected')) {
        setOperationHistory(prev => [
          ...prev.slice(-9),
          {
            type: 'Duplicate Conversion',
            timestamp: Date.now(),
          },
        ])
      }
      originalConsoleWarn.apply(console, args)
    }

    return () => {
      console.log = originalConsoleLog
      console.warn = originalConsoleWarn
    }
  }, [])

  const getOperationColor = (type: string) => {
    switch (type) {
      case 'Tree Update':
        return 'bg-blue-100 text-blue-800'
      case 'Duplicate Conversion':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString()
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          📊 Performance Monitor
          <Badge variant="outline">{nodeMap.size} nodes</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Metrics */}
        <div className="grid grid-cols-2 gap-4">
          <div className="p-3 border rounded-lg">
            <div className="text-sm text-gray-600">Tree Conversions</div>
            <div className="text-xl font-bold">{conversionCount}</div>
          </div>
          <div className="p-3 border rounded-lg">
            <div className="text-sm text-gray-600">Last Operation</div>
            <div className="text-xl font-bold">
              {lastOperationTime ? `${lastOperationTime.toFixed(2)}ms` : '-'}
            </div>
          </div>
        </div>

        {/* Operation History */}
        <div>
          <h3 className="text-sm font-medium mb-2">Recent Operations</h3>
          <div className="space-y-1 max-h-40 overflow-y-auto">
            {operationHistory.length === 0 ? (
              <p className="text-sm text-gray-500">
                No operations recorded yet
              </p>
            ) : (
              operationHistory
                .slice()
                .reverse()
                .map((op, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 border rounded text-sm"
                  >
                    <div className="flex items-center gap-2">
                      <Badge className={getOperationColor(op.type)}>
                        {op.type}
                      </Badge>
                      {op.nodeCount && (
                        <span className="text-gray-600">
                          {op.nodeCount} nodes
                        </span>
                      )}
                    </div>
                    <div className="flex items-center gap-2 text-gray-500">
                      {op.duration && <span>{op.duration.toFixed(2)}ms</span>}
                      <span>{formatTime(op.timestamp)}</span>
                    </div>
                  </div>
                ))
            )}
          </div>
        </div>

        {/* Performance Tips */}
        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
          <h4 className="text-sm font-medium text-blue-800 mb-1">
            💡 Optimization Status
          </h4>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>✅ Duplicate conversion prevention active</li>
            <li>✅ Node map rebuilding monitored</li>
            <li>✅ Database sync optimization enabled</li>
            <li>✅ React Flow navigation optimized</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}

export default PerformanceMonitor
