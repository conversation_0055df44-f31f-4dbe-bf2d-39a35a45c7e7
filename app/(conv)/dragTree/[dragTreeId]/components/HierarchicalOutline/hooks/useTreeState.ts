import { useState } from 'react'
import { TreeNode } from '@/app/types'

export const useTreeState = (
  node: TreeNode,
  collapsedNodes?: Set<string>,
  setCollapsedNodes?: (nodes: Set<string>) => void
) => {
  // Initialize collapsed nodes state if not provided (for root component)
  const [internalCollapsedNodes, setInternalCollapsedNodes] = useState<
    Set<string>
  >(() => {
    // Initialize with nodes that should be collapsed by default
    const initialCollapsed = new Set<string>()

    const shouldCollapseByDefault = (node: TreeNode): boolean => {
      if (node.type !== 'category') return false

      // If the category has no children, don't collapse it
      if (node.children.length === 0) return false

      // Check if all children are question nodes (no child categories)
      const hasChildCategories = node.children.some(
        child => child.type === 'category'
      )

      // Collapse categories that only have question children
      return !hasChildCategories
    }

    const collectCollapsibleNodes = (node: TreeNode) => {
      if (shouldCollapseByDefault(node)) {
        initialCollapsed.add(node.id)
      }
      node.children.forEach(collectCollapsibleNodes)
    }

    collectCollapsibleNodes(node)

    // If collapsedNodes is provided but empty, use our calculated initial state
    if (collapsedNodes && collapsedNodes.size === 0) {
      return initialCollapsed
    }

    // If collapsedNodes is provided and has data, use it
    if (collapsedNodes && collapsedNodes.size > 0) {
      return new Set(collapsedNodes)
    }

    // Otherwise use our calculated initial state
    return initialCollapsed
  })

  // Use provided collapsed state or internal state
  const currentCollapsedNodes = collapsedNodes || internalCollapsedNodes
  const updateCollapsedNodes = setCollapsedNodes || setInternalCollapsedNodes

  // Check if current node is collapsed
  const isCollapsed = currentCollapsedNodes.has(node.id)

  // FIXED: Calculate number of question children when collapsed (for backward compatibility)
  // This function is kept for any components that might still use it
  const getCollapsedChildrenCount = (): number => {
    // Only return count if the node is collapsed
    if (!isCollapsed || node.children.length === 0) return 0

    // Count only question children, not all children
    return node.children.filter(child => child.type === 'question').length
  }

  return {
    currentCollapsedNodes,
    updateCollapsedNodes,
    isCollapsed,
    getCollapsedChildrenCount,
  }
}
