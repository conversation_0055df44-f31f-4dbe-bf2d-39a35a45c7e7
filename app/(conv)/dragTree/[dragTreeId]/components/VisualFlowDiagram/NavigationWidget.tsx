'use client'
import React from 'react'
import { Button } from '@/components/ui/button'
import {
  ChevronUp,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  Navigation,
} from 'lucide-react'
import { useReactFlow } from 'reactflow'
import { cn } from '@/lib/utils'
import { useSelectionStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useSelectionStore'
import { useKeyboardFeedback } from '@/app/(conv)/dragTree/[dragTreeId]/hooks/useSelectionKeyboard'

type NavigationWidgetProps = {
  className?: string
}

/**
 * NavigationWidget Component
 *
 * Custom navigation controls for React Flow when in selection mode
 * Replaces the minimap with directional controls and keyboard hints
 */
export const NavigationWidget: React.FC<NavigationWidgetProps> = ({
  className,
}) => {
  const { setViewport, getViewport } = useReactFlow()
  const isSelectionMode = useSelectionStore(state => state.isSelectionMode)
  const keyboardFeedback = useKeyboardFeedback()

  // Only show in selection mode
  if (!isSelectionMode) {
    return null
  }

  const panStep = 100 // pixels to pan per button click

  const handlePan = (direction: 'up' | 'down' | 'left' | 'right') => {
    const currentViewport = getViewport()

    let newViewport = { ...currentViewport }

    switch (direction) {
      case 'up':
        newViewport.y = currentViewport.y + panStep
        break
      case 'down':
        newViewport.y = currentViewport.y - panStep
        break
      case 'left':
        newViewport.x = currentViewport.x + panStep
        break
      case 'right':
        newViewport.x = currentViewport.x - panStep
        break
    }

    setViewport(newViewport)
  }

  return (
    <div
      className={cn(
        'absolute bottom-4 right-4 z-30',
        'bg-white/90 backdrop-blur-sm border border-gray-200 rounded-lg shadow-lg',
        'p-3 flex flex-col items-center gap-2',
        className
      )}
      style={{ pointerEvents: 'auto' }}
    >
      {/* Header */}
      <div className="flex items-center gap-2 text-xs text-gray-600 font-medium">
        <Navigation className="w-3 h-3" />
        <span>Navigate</span>
      </div>

      {/* Directional controls */}
      <div className="grid grid-cols-3 gap-1">
        {/* Top row */}
        <div></div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePan('up')}
          className={cn(
            'h-8 w-8 p-0 hover:bg-blue-50 transition-all duration-200',
            keyboardFeedback?.key === 'ArrowUp' && 'bg-blue-100 scale-95'
          )}
          title="Pan up (↑)"
          aria-label="Pan up"
        >
          <ChevronUp className="w-4 h-4" />
        </Button>
        <div></div>

        {/* Middle row */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePan('left')}
          className={cn(
            'h-8 w-8 p-0 hover:bg-blue-50 transition-all duration-200',
            keyboardFeedback?.key === 'ArrowLeft' && 'bg-blue-100 scale-95'
          )}
          title="Pan left (←)"
          aria-label="Pan left"
        >
          <ChevronLeft className="w-4 h-4" />
        </Button>
        <div className="h-8 w-8 flex items-center justify-center">
          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePan('right')}
          className={cn(
            'h-8 w-8 p-0 hover:bg-blue-50 transition-all duration-200',
            keyboardFeedback?.key === 'ArrowRight' && 'bg-blue-100 scale-95'
          )}
          title="Pan right (→)"
          aria-label="Pan right"
        >
          <ChevronRight className="w-4 h-4" />
        </Button>

        {/* Bottom row */}
        <div></div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePan('down')}
          className={cn(
            'h-8 w-8 p-0 hover:bg-blue-50 transition-all duration-200',
            keyboardFeedback?.key === 'ArrowDown' && 'bg-blue-100 scale-95'
          )}
          title="Pan down (↓)"
          aria-label="Pan down"
        >
          <ChevronDown className="w-4 h-4" />
        </Button>
        <div></div>
      </div>

      {/* Keyboard hint footer */}
      <div className="text-center text-[10px] text-gray-400 mt-1 px-1 max-w-[120px]">
        Use ↑ ↓ ← →, +, -, 0 to navigate
      </div>
    </div>
  )
}
