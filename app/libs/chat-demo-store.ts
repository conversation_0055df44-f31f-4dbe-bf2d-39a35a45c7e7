/**
 * Simple in-memory chat store for chat-demo
 * Mimics the conversation persistence pattern used by AI pane chat
 * In production, this would use a database like the AI pane chat system
 */

import { UIMessage, generateId } from 'ai'

// In-memory storage for demo purposes
// In production, this would be a database
const chatStore = new Map<string, UIMessage[]>()

/**
 * Create a new chat conversation
 * @returns The new chat ID
 */
export function createChat(): string {
  const id = generateId()
  chatStore.set(id, [])
  console.log(`📝 [Chat Demo Store] Created new chat: ${id}`)
  return id
}

/**
 * Load chat messages for a conversation
 * @param chatId - The chat conversation ID
 * @returns Array of UIMessage objects
 */
export function loadChat(chatId: string): UIMessage[] {
  const messages = chatStore.get(chatId) || []
  console.log(
    `📖 [Chat Demo Store] Loaded ${messages.length} messages for chat: ${chatId}`
  )
  return messages
}

/**
 * Save chat messages for a conversation
 * @param chatId - The chat conversation ID
 * @param messages - Array of UIMessage objects to save
 */
export function saveChat(chatId: string, messages: UIMessage[]): void {
  chatStore.set(chatId, messages)
  console.log(
    `💾 [Chat Demo Store] Saved ${messages.length} messages for chat: ${chatId}`
  )
}

/**
 * Check if a chat exists
 * @param chatId - The chat conversation ID
 * @returns True if the chat exists
 */
export function chatExists(chatId: string): boolean {
  return chatStore.has(chatId)
}

/**
 * Get all chat IDs (for debugging)
 * @returns Array of chat IDs
 */
export function getAllChatIds(): string[] {
  return Array.from(chatStore.keys())
}

/**
 * Clear all chats (for debugging)
 */
export function clearAllChats(): void {
  chatStore.clear()
  console.log('🗑️ [Chat Demo Store] Cleared all chats')
}
