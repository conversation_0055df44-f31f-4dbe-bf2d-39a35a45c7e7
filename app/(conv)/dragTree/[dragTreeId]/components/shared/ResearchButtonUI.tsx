/**
 * Research Button UI Component
 *
 * Pure UI component for research functionality.
 * Separated from business logic for better maintainability.
 *
 * This component only handles:
 * - UI rendering and styling
 * - User interaction events
 * - Visual states and animations
 *
 * Business logic is handled by:
 * - useResearchActions hook
 * - useResearchState hook
 */

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Search } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
// Removed useUIStore import since we no longer use global state
import { cn } from '@/lib/utils'
import { toast } from 'react-hot-toast'
import { ClarifyIcon } from './ClarifyIcon'
import {
  RESEARCH_OPTIONS_CONFIG,
  type ResearchOptionConfig,
} from '@/app/(conv)/dragTree/constants'
import type { ResearchActionHandlers } from './hooks/useResearchActions'

// Helper function to render icons based on iconType
const renderIcon = (iconType: 'clarify' | null) => {
  if (iconType === 'clarify') {
    return <ClarifyIcon />
  }
  return null
}

export type ResearchButtonUIProps = {
  // State
  hasExistingContent: boolean
  isStreaming: boolean
  searchResults: any[] | null

  // Configuration
  variant?: 'reactflow' | 'treeview'
  className?: string

  // Action handlers (injected from parent)
  actionHandlers: ResearchActionHandlers

  // Event handlers
  onMouseEnter?: () => void
  onMouseLeave?: () => void
}

/**
 * Pure UI component for research button functionality.
 * Does not contain business logic - only handles UI concerns.
 */
const ResearchButtonUI: React.FC<ResearchButtonUIProps> = ({
  hasExistingContent,
  isStreaming,
  // searchResults unused
  variant = 'treeview',
  className = '',
  actionHandlers,
  onMouseEnter,
  onMouseLeave,
}) => {
  // Removed useUIStore to fix shared state issues

  // Local UI state
  const [dropdownOpen, setDropdownOpen] = useState<boolean>(false)
  const [alertOpen, setAlertOpen] = useState<boolean>(false)
  const [pendingOption, setPendingOption] =
    useState<ResearchOptionConfig | null>(null)

  // Local component state - each button is now independent
  // We avoid using the global setHoverCollapseLock to prevent shared state issues
  // The dropdown state is now completely isolated per button instance

  const handleResearchOption = (option: ResearchOptionConfig) => {
    const validation = actionHandlers.validateResearchAction(option)

    if (!validation.canExecute) {
      toast.error(validation.reason || 'Cannot execute this research action')
      setDropdownOpen(false)
      return
    }

    // Show warning for external sites
    if (option.isExternal) {
      setPendingOption(option)
      setAlertOpen(true)
      setDropdownOpen(false)
      return
    }

    // Execute internal research
    executeResearchOption(option)
    setDropdownOpen(false)
  }

  const executeResearchOption = async (option: ResearchOptionConfig) => {
    const displayText = actionHandlers.getFullQuestionPath()

    try {
      if (option.isExternal) {
        actionHandlers.executeExternalResearch(option, displayText)
      } else {
        await actionHandlers.executeInternalResearch(displayText)
      }
    } catch (error) {
      // Error handling is done in the action handlers
      console.error('Research execution failed:', error)
    }
  }

  const handleConfirmExternal = () => {
    if (pendingOption) {
      executeResearchOption(pendingOption)
    }
    setAlertOpen(false)
    setPendingOption(null)
  }

  const getButtonClasses = () => {
    return cn('h-6 w-6 p-0 transition-all duration-200', {
      'hover:bg-blue-100 hover:scale-105': variant === 'reactflow',
      'rounded-full bg-gradient-to-r from-purple-50 to-violet-50 border-2 border-purple-200 shadow-sm hover:from-purple-100 hover:to-violet-100 hover:border-purple-300 hover:shadow-md hover:scale-105':
        variant === 'treeview',
    })
  }

  const getIconClasses = () => {
    return cn('h-3 w-3', {
      [className || 'text-slate-600 hover:text-slate-700']:
        variant === 'reactflow',
      'text-purple-600': variant === 'treeview',
    })
  }

  return (
    <>
      <div
        className="flex items-center gap-1"
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
      >
        <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
          <DropdownMenuTrigger asChild>
            <Button
              variant={variant === 'reactflow' ? 'ghost' : 'outline'}
              size={variant === 'reactflow' ? 'sm' : 'icon'}
              className={cn(getButtonClasses(), {
                'animate-pulse': isStreaming,
              })}
              onClick={e => {
                e.preventDefault()
                e.stopPropagation()
              }}
              onMouseDown={e => e.stopPropagation()}
              onMouseUp={e => e.stopPropagation()}
              title={
                isStreaming ? 'Research in progress...' : 'Research Options'
              }
            >
              <Search
                className={cn(getIconClasses(), {
                  'text-blue-500': isStreaming,
                })}
              />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            align="start"
            side="bottom"
            className="w-52 rounded-xl border-0 shadow-xl bg-white/95 backdrop-blur-sm z-50"
            sideOffset={4}
          >
            {RESEARCH_OPTIONS_CONFIG.map((option, index) => (
              <React.Fragment key={option.id}>
                {/* Add separator before external options */}
                {index === 1 && (
                  <div className="flex items-center gap-2 px-2 py-1.5 text-xs text-gray-500">
                    <div className="flex-1 h-px bg-gray-200" />
                    <span className="px-2 bg-gray-100 rounded-full">
                      External Tools
                    </span>
                    <div className="flex-1 h-px bg-gray-200" />
                  </div>
                )}
                <DropdownMenuItem
                  onClick={e => {
                    e.preventDefault()
                    e.stopPropagation()
                    handleResearchOption(option)
                  }}
                  className={cn('cursor-pointer rounded-lg', {
                    'opacity-50 cursor-not-allowed':
                      option.id === 'clarify' &&
                      (hasExistingContent || isStreaming),
                  })}
                  disabled={
                    option.id === 'clarify' &&
                    (hasExistingContent || isStreaming)
                  }
                >
                  {renderIcon(option.iconType)}
                  {option.iconType && <span className="mr-2" />}
                  {option.label}
                  {option.id === 'clarify' && hasExistingContent && (
                    <span className="ml-auto text-xs text-gray-400">
                      ✓ Done
                    </span>
                  )}
                  {option.id === 'clarify' && isStreaming && (
                    <span className="ml-auto text-xs text-blue-500 animate-pulse">
                      ⏳ Researching...
                    </span>
                  )}
                </DropdownMenuItem>
              </React.Fragment>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <AlertDialog open={alertOpen} onOpenChange={setAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              Continue to {pendingOption?.label}?
            </AlertDialogTitle>
            <AlertDialogDescription className="text-gray-600">
              You&apos;re about to open an external site. While it&apos;s fine
              to do your own research, please note that any context or results
              from external sites are <strong>not</strong> automatically synced
              back here.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => {
                setAlertOpen(false)
                setPendingOption(null)
              }}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmExternal}>
              Continue
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

export default ResearchButtonUI
