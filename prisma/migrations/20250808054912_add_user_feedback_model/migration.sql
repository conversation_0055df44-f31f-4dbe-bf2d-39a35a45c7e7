-- CreateEnum
CREATE TYPE "UserFeedbackType" AS ENUM ('DRAGTREE_FLOATING_BUTTON');

-- CreateTable
CREATE TABLE "user_feedback" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "feedback_type" "UserFeedbackType" NOT NULL,
    "entity_type" TEXT NOT NULL,
    "entity_id" TEXT NOT NULL,
    "can_contact" BOOLEAN NOT NULL DEFAULT false,
    "feedback_text" TEXT DEFAULT '',
    "metadata" JSONB NOT NULL DEFAULT '{}',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "user_feedback_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "user_feedback_user_id_idx" ON "user_feedback" USING HASH ("user_id");

-- CreateIndex
CREATE INDEX "user_feedback_feedback_type_idx" ON "user_feedback" USING HASH ("feedback_type");

-- CreateIndex
CREATE INDEX "user_feedback_entity_type_entity_id_idx" ON "user_feedback"("entity_type", "entity_id");

-- CreateIndex
CREATE INDEX "user_feedback_created_at_idx" ON "user_feedback"("created_at");

-- AddForeignKey
ALTER TABLE "user_feedback" ADD CONSTRAINT "user_feedback_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
