{"extends": ["next/core-web-vitals"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "destructuredArrayIgnorePattern": "^_"}], "no-unused-vars": "off", "react/jsx-uses-react": "error", "react/jsx-uses-vars": "error", "react/no-unescaped-entities": "warn"}, "overrides": [{"files": ["**/*legacy*/**/*.{ts,tsx}", "**/(legacy)/**/*.{ts,tsx}", "**/legacy/**/*.{ts,tsx}"], "rules": {"@typescript-eslint/no-unused-vars": "off"}}]}