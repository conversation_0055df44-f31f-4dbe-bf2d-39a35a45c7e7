/// <reference lib="webworker" />

// This worker receives a TreeNode JSON object and converts it into
// React Flow compatible `nodes` and `edges` arrays. The heavy work is
// done here so that the main UI thread remains responsive when dealing
// with very large trees.

// NOTE: Only type‐level imports are pulled from `reactflow` to avoid
// bundling the full library inside the worker (which would fail due to
// `window` references). We replicate the minimal runtime values we need
// (Position & MarkerType) locally.
import type { TreeNode } from '@/app/types'

// We keep the payloads as `any` inside the worker to avoid shipping React Flow
// types/enums (which depend on a browser environment) into the worker bundle.
type RFNode = any
type RFEdge = any

// Minimal runtime enums compatible with React Flow values.
// See: https://reactflow.dev/docs/api/types/
const enum Position {
  Left = 'left',
  Right = 'right',
}

const enum MarkerType {
  ArrowClosed = 'arrowclosed',
}

const treeNodeToReactFlow = (
  tree: TreeNode
): { nodes: RFNode[]; edges: RFEdge[] } => {
  const nodes: RFNode[] = []
  const edges: RFEdge[] = []

  const traverse = (
    node: TreeNode,
    parentId: string | null = null,
    depth: number = 1
  ) => {
    const rfNode: RFNode = {
      id: node.id,
      data: {
        label: node.label,
        level: depth,
        parentId: parentId,
        // width & height are appended below for performance reasons
      },
      position: {
        x: Math.max(0, depth * 200),
        y: Math.max(50, Math.random() * 1000 + 50),
      },
      sourcePosition: Position.Right,
      targetPosition: Position.Left,
      type:
        node.type === 'question' ? 'customQuestionNode' : 'customCategoryNode',
    }

    // Estimate dimensions once so React Flow can perform rough layouting
    const labelLines = rfNode.data.label.split('\n').length
    const baseWidth = 350
    rfNode.data = {
      ...rfNode.data,
      width: baseWidth + rfNode.data.label.length * 2.5,
      height: 80 + labelLines * 20,
    }

    nodes.push(rfNode)

    if (parentId) {
      edges.push({
        id: `e-${parentId}-${node.id}`,
        source: parentId,
        target: node.id,
        type: 'smoothstep',
        style: {
          stroke: '#64748b',
          strokeWidth: 2,
        },
        markerEnd: {
          // React Flow will understand this string in the browser context.
          type: MarkerType.ArrowClosed as unknown as any,
          color: '#64748b',
        },
      })
    }

    node.children.forEach(child => traverse(child, node.id, depth + 1))
  }

  traverse(tree)
  return { nodes, edges }
}

// ---------------- Worker Wiring ----------------
self.onmessage = (event: MessageEvent<TreeNode>) => {
  const tree = event.data
  try {
    const result = treeNodeToReactFlow(tree)
    // Post result back to main thread. The structured clone algorithm
    // efficiently transfers plain objects/arrays.
    ;(self as DedicatedWorkerGlobalScope).postMessage(result)
  } catch (err) {
    console.error('[treeNodeToReactFlow.worker] Failed:', err)
    // Even on error, respond with empty arrays to prevent main-thread hangs.
    ;(self as DedicatedWorkerGlobalScope).postMessage({ nodes: [], edges: [] })
  }
}

export {}
