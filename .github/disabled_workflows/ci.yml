name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

permissions:
  contents: read
  pull-requests: write
  actions: read

# Cancel previous runs for the same workflow
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  # Combined job for unit tests, linting, and build - saves on job overhead
  test-and-build:
    name: Test & Build
    runs-on: ubuntu-latest
    outputs:
      # Determine if E2E tests should run based on file changes
      run-e2e: ${{ steps.changes.outputs.src == 'true' }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      # Check for relevant changes to decide on E2E tests
      - name: Check for source changes
        uses: dorny/paths-filter@v3
        id: changes
        with:
          filters: |
            src:
              - 'app/**'
              - 'components/**'
              - 'e2e/**'
              - 'package*.json'
              - 'playwright.config.ts'

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          registry-url: 'https://registry.npmjs.org'

      # Configure npm for better network resilience
      - name: Configure npm for stability
        run: |
          npm config set fetch-retries 5
          npm config set fetch-retry-mintimeout 20000
          npm config set fetch-retry-maxtimeout 120000
          npm config set fetch-timeout 600000

      # Install dependencies with retry mechanism
      - name: Install dependencies
        uses: nick-fields/retry@v3
        with:
          timeout_minutes: 10
          max_attempts: 3
          retry_wait_seconds: 30
          command: |
            npm ci --prefer-offline --no-audit --no-fund

      # Run all checks in parallel using npm-run-all or background processes
      - name: Run linting
        run: npm run lint

      - name: Run unit tests with coverage
        run: npm run test:coverage

      - name: Build application
        run: npm run build
        env:
          # Provide dummy environment variables for build process
          OPENAI_API_KEY: 'dummy-key-for-build'
          NEXTAUTH_SECRET: 'dummy-secret-for-build'
          NEXTAUTH_URL: 'http://localhost:3000'
          DATABASE_URL: 'postgresql://dummy:dummy@localhost:5432/dummy'
          NEXTAUTH_GITHUB_ID: 'dummy-github-id'
          NEXTAUTH_GITHUB_SECRET: 'dummy-github-secret'
          UPSTASH_REDIS_REST_URL: 'http://localhost:6379'
          UPSTASH_REDIS_REST_TOKEN: 'dummy-token'
          STRIPE_SECRET_KEY: 'sk_test_dummy'
          STRIPE_WEBHOOK_SECRET: 'whsec_dummy'

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        if: github.event_name == 'pull_request'
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
          fail_ci_if_error: false
          token: ${{ secrets.CODECOV_TOKEN }}

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results
          path: |
            coverage/
            test-results/
          retention-days: 7

  # E2E tests run conditionally and in parallel
  e2e-tests:
    name: E2E Tests (${{ matrix.project }})
    runs-on: ubuntu-latest
    needs: test-and-build
    if: needs.test-and-build.outputs.run-e2e == 'true'

    strategy:
      fail-fast: false
      matrix:
        # Reduced matrix to save resources - focus on most important browsers
        project: [chromium, webkit]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      # Configure npm for stability
      - name: Configure npm for stability
        run: |
          npm config set fetch-retries 5
          npm config set fetch-retry-mintimeout 20000
          npm config set fetch-retry-maxtimeout 120000

      # Install dependencies with retry
      - name: Install dependencies
        uses: nick-fields/retry@v3
        with:
          timeout_minutes: 10
          max_attempts: 3
          retry_wait_seconds: 30
          command: npm ci --prefer-offline --no-audit --no-fund

      # Cache Playwright browsers to speed up installation
      - name: Cache Playwright browsers
        uses: actions/cache@v4
        id: playwright-cache
        with:
          path: ~/.cache/ms-playwright
          key: ${{ runner.os }}-playwright-${{ hashFiles('**/package-lock.json') }}

      # Install only the specific browser to save time and resources
      - name: Install Playwright browser
        if: steps.playwright-cache.outputs.cache-hit != 'true'
        run: npx playwright install --with-deps ${{ matrix.project }}

      # Install deps only if cache miss
      - name: Install browser deps
        if: steps.playwright-cache.outputs.cache-hit == 'true'
        run: npx playwright install-deps ${{ matrix.project }}

      - name: Run E2E tests
        run: npx playwright test --project=${{ matrix.project }} --reporter=html,junit
        env:
          CI: true

      - name: Upload E2E results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: e2e-results-${{ matrix.project }}
          path: |
            playwright-report/
            test-results/
          retention-days: 7

  # Security scan - runs only on PRs to save resources
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      # Configure npm for stability
      - name: Configure npm for stability
        run: |
          npm config set fetch-retries 3
          npm config set fetch-retry-mintimeout 10000

      - name: Install dependencies
        uses: nick-fields/retry@v3
        with:
          timeout_minutes: 5
          max_attempts: 2
          command: npm ci --prefer-offline --no-audit --no-fund --production=false

      - name: Run security audit
        run: npm audit --audit-level=moderate
        continue-on-error: true

  # Status reporting job
  status-report:
    name: Status Report
    runs-on: ubuntu-latest
    needs: [test-and-build, e2e-tests]
    if: always() && github.event_name == 'pull_request'

    steps:
      - name: Create status report
        uses: actions/github-script@v7
        with:
          script: |
            const testStatus = '${{ needs.test-and-build.result }}'
            const e2eStatus = '${{ needs.e2e-tests.result }}'
            const e2eRan = '${{ needs.test-and-build.outputs.run-e2e }}' === 'true'

            const statusIcon = (status) => {
              switch(status) {
                case 'success': return '✅'
                case 'failure': return '❌'
                case 'cancelled': return '⏹️'
                case 'skipped': return '⏭️'
                default: return '🔄'
              }
            }

            const overallStatus = testStatus === 'success' && (e2eStatus === 'success' || !e2eRan)
              ? '✅ All checks passed'
              : '❌ Some checks failed'

            const report = `
            ## 🚀 CI/CD Status Report

            ### ${overallStatus}

            | Check | Status | Details |
            |-------|--------|---------|
            | Tests & Build | ${statusIcon(testStatus)} | Unit tests, linting, build |
            | E2E Tests | ${e2eRan ? statusIcon(e2eStatus) : '⏭️'} | ${e2eRan ? 'Ran on chromium, webkit' : 'Skipped (no relevant changes)'} |

            ${testStatus === 'failure' ? '❌ **Tests or build failed** - Check the logs for details\n' : ''}
            ${e2eStatus === 'failure' ? '❌ **E2E tests failed** - Check the test reports in artifacts\n' : ''}

            📊 **Test artifacts** are available in the Actions tab for detailed reports.
            `

            // Update or create PR comment
            const comments = await github.rest.issues.listComments({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
            })

            const botComment = comments.data.find(comment =>
              comment.user.type === 'Bot' && comment.body.includes('🚀 CI/CD Status Report'))

            const commentData = {
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: report
            }

            if (botComment) {
              await github.rest.issues.updateComment({
                ...commentData,
                comment_id: botComment.id
              })
            } else {
              await github.rest.issues.createComment({
                ...commentData,
                issue_number: context.issue.number
              })
            }
