import type { AiMessageRole, AiStepType } from '@prisma/client'
import { AiChatIdPrefix } from '@/lib/id'

/**
 * Validates and maps a string role to AiMessageRole enum
 */
export function validateMessageRole(role: string): AiMessageRole {
  const upperRole = role.toUpperCase()

  switch (upperRole) {
    case 'USER':
      return 'USER'
    case 'ASSISTANT':
      return 'ASSISTANT'
    case 'SYSTEM':
      return 'SYSTEM'
    default:
      throw new Error(
        `Invalid message role: ${role}. Must be one of: USER, ASSISTANT, SYSTEM`
      )
  }
}

/**
 * Validates that an ID has the correct prefix format
 */
export function assertAiIdPrefix(
  id: string,
  expectedPrefix: AiChatIdPrefix
): void {
  if (!id || typeof id !== 'string') {
    throw new Error(`Invalid ID: ${id}. Must be a non-empty string`)
  }

  const expectedPrefixString = `${expectedPrefix}_`

  if (!id.startsWith(expectedPrefixString)) {
    throw new Error(
      `Invalid ID format: ${id}. Expected prefix: ${expectedPrefixString}`
    )
  }

  // Validate the ID has content after the prefix
  const idSuffix = id.slice(expectedPrefixString.length)
  if (!idSuffix || idSuffix.length < 10) {
    throw new Error(
      `Invalid ID format: ${id}. ID suffix too short (minimum 10 characters after prefix)`
    )
  }
}

/**
 * Validates that a conversation ID has the correct format
 */
export function validateConversationId(id: string): void {
  assertAiIdPrefix(id, AiChatIdPrefix.THREAD)
}

/**
 * Validates that a message ID has the correct format
 */
export function validateMessageId(id: string): void {
  assertAiIdPrefix(id, AiChatIdPrefix.MESSAGE)
}

/**
 * Validates that a step ID has the correct format
 */
export function validateStepId(id: string): void {
  assertAiIdPrefix(id, AiChatIdPrefix.STEP)
}

/**
 * Validates that an attachment ID has the correct format
 */
export function validateAttachmentId(id: string): void {
  assertAiIdPrefix(id, AiChatIdPrefix.FILE)
}

/**
 * Validates step order is a non-negative integer
 */
export function validateStepOrder(stepOrder: number): void {
  if (!Number.isInteger(stepOrder) || stepOrder < 0) {
    throw new Error(
      `Invalid step order: ${stepOrder}. Must be a non-negative integer`
    )
  }
}

/**
 * @deprecated No longer used - raw provider types are accepted directly
 * Validates that a step type is valid
 */
export function validateStepType(type: string): AiStepType {
  const validTypes: AiStepType[] = [
    'THOUGHT',
    'TOOL_CALL',
    'TOOL_RESULT',
    'REASONING_SUMMARY',
    'SUB_AGENT_INVOCATION',
  ]

  if (!validTypes.includes(type as AiStepType)) {
    throw new Error(
      `Invalid step type: ${type}. Must be one of: ${validTypes.join(', ')}`
    )
  }

  return type as AiStepType
}

/**
 * Validates attachment data
 */
export function validateAttachmentData(data: {
  fileName: string
  fileType: string
  fileSize: number
  url: string
}): void {
  if (!data.fileName || typeof data.fileName !== 'string') {
    throw new Error('Invalid fileName: must be a non-empty string')
  }

  if (!data.fileType || typeof data.fileType !== 'string') {
    throw new Error('Invalid fileType: must be a non-empty string')
  }

  if (!Number.isInteger(data.fileSize) || data.fileSize <= 0) {
    throw new Error('Invalid fileSize: must be a positive integer')
  }

  if (!data.url || typeof data.url !== 'string') {
    throw new Error('Invalid url: must be a non-empty string')
  }

  // Basic URL validation
  try {
    new URL(data.url)
  } catch {
    throw new Error(`Invalid url format: ${data.url}`)
  }
}

/**
 * Validates that user ID exists and is properly formatted
 */
export function validateUserId(userId: string): void {
  if (!userId || typeof userId !== 'string') {
    throw new Error('Invalid userId: must be a non-empty string')
  }

  // Basic cuid validation - should start with 'c' and be 25 characters
  if (!userId.startsWith('c') || userId.length !== 25) {
    throw new Error(`Invalid userId format: ${userId}. Must be a valid cuid`)
  }
}
