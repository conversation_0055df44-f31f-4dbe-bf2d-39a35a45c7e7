'use client'

import { useSession } from 'next-auth/react'
import { toast } from 'react-hot-toast'
import { useCallback, useState } from 'react'

/**
 * Custom hook for refreshing NextAuth session data
 * Triggers JWT callback to pull latest user data from database
 * Useful after subscription changes, profile updates, etc.
 */
export const useSessionRefresh = () => {
  const { update } = useSession()
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false)

  /**
   * Refresh the current session by triggering NextAuth session.update()
   * This will re-run the JWT callback and pull latest data from database
   * Also clears server-side caches to ensure fresh data
   *
   * @param options - Configuration options for the refresh
   * @param options.showToast - Whether to show toast notifications (default: true)
   * @param options.toastId - Custom toast ID for managing toast state
   * @param options.successMessage - Custom success message
   * @param options.errorMessage - Custom error message
   */
  const refreshSession = useCallback(
    async (options?: {
      showToast?: boolean
      toastId?: string
      successMessage?: string
      errorMessage?: string
    }) => {
      const {
        showToast = true,
        toastId = 'refresh-session',
        successMessage = 'Account refreshed successfully',
        errorMessage = 'Failed to refresh account. Please try again.',
      } = options || {}

      setIsRefreshing(true)

      try {
        if (showToast) {
          toast.loading('Refreshing account…', { id: toastId })
        }

        // Step 1: Clear server-side caches
        try {
          await fetch('/api/user/refresh-subscription', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
          })
          console.log('🗑️ [Session Refresh] Server caches cleared')
        } catch (cacheError) {
          console.warn(
            '⚠️ [Session Refresh] Failed to clear server caches:',
            cacheError
          )
          // Continue with session refresh even if cache clearing fails
        }

        // Step 2: Trigger NextAuth session update - this will re-run JWT callback
        // Passing a payload ensures trigger === 'update' and avoids edge cases
        await update({ refresh: true } as any)

        if (showToast) {
          toast.success(successMessage, { id: toastId })
        }

        return { success: true }
      } catch (error) {
        console.error('Failed to refresh session:', error)

        if (showToast) {
          toast.error(errorMessage, { id: toastId })
        }

        return { success: false, error }
      } finally {
        setIsRefreshing(false)
      }
    },
    [update]
  )

  /**
   * Silent refresh without any user feedback
   * Useful for automatic refreshes after authentication or subscription changes
   */
  const refreshSessionSilently = useCallback(async () => {
    return refreshSession({ showToast: false })
  }, [refreshSession])

  return {
    refreshSession,
    refreshSessionSilently,
    isRefreshing,
  }
}
