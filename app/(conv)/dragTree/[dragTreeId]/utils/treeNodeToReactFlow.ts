import { TreeNode, Node } from '@/app/types'
import { Position, MarkerType, Edge } from 'reactflow'

/**
 * Converts TreeNode to ReactFlow nodes and edges.
 */
const treeNodeToReactFlow = (
  tree: TreeNode
): { nodes: Node[]; edges: Edge[] } => {
  const nodes: Node[] = []
  const edges: Edge[] = []

  const traverse = (
    node: TreeNode,
    parentId: string | null = null,
    depth: number = 1
  ) => {
    const rfNode: Node = {
      id: node.id,
      data: {
        label: node.label,
        level: depth,
        parentId: parentId,
      },
      position: {
        x: Math.max(0, depth * 200),
        y: Math.max(50, Math.random() * 1000 + 50),
      },
      sourcePosition: Position.Right,
      targetPosition: Position.Left,
      type:
        node.type === 'question' ? 'customQuestionNode' : 'customCategoryNode',
    }

    // ------------------------------------------------------------
    // Performance optimisation – pre-calculate node dimensions once
    // ------------------------------------------------------------
    const labelLines = rfNode.data.label.split('\n').length
    const baseWidth = 350
    rfNode.data = {
      ...rfNode.data,
      width: baseWidth + rfNode.data.label.length * 2.5,
      height: 80 + labelLines * 20,
    }

    // Root node created (removed verbose logging for performance)

    nodes.push(rfNode)

    if (parentId) {
      edges.push({
        id: `e-${parentId}-${node.id}`,
        source: parentId,
        target: node.id,
        type: 'smoothstep',
        style: {
          stroke: '#64748b',
          strokeWidth: 2,
        },
        markerEnd: {
          type: MarkerType.ArrowClosed,
          color: '#64748b',
        },
      })
    }

    node.children.forEach(child => traverse(child, node.id, depth + 1))
  }

  traverse(tree)
  return { nodes, edges }
}

export default treeNodeToReactFlow
