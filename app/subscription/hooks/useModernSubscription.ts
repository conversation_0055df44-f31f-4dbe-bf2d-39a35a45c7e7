'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { toast } from 'react-hot-toast'
import { SubscriptionTier } from '@prisma/client'
import { hasPaidFeatures } from '@/app/configs/tier-permissions'
import { checkoutWithStripe } from '@/lib/stripe/server'
import { getStripe } from '@/lib/stripe/client'
import { isLocalOrDevEnv } from '@/lib/utils'
import { logEventWithContext } from '@/app/libs/logging'

export const useModernSubscription = () => {
  const { data: session } = useSession()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  // Get subscription info from session
  const subscriptionInfo = session?.user?.subscription
  const currentTier = subscriptionInfo?.tier || SubscriptionTier.FREE
  const isPaidUser = hasPaidFeatures(currentTier)
  const isLocalOrDev = isLocalOrDevEnv()

  const handleStripeCheckout = async (
    planId: string,
    isYearly: boolean = false
  ) => {
    if (!session?.user?.id) {
      toast.error('Please sign in to subscribe')
      router.push('/api/auth/signin')
      return
    }

    if (planId === 'FREE') {
      toast.error('You are already on the free plan')
      return
    }

    if (isPaidUser) {
      toast.error('You already have a Pro subscription')
      return
    }

    setIsLoading(true)

    try {
      // Log checkout started event
      logEventWithContext(
        'start_subscription_checkout',
        session.user.id, // userId
        undefined, // dragTreeId not required for subscription events
        {
          plan: planId,
          billing_cycle: isYearly ? 'yearly' : 'monthly',
        }
      )

      // Fetch active prices from DB (synced via webhooks)
      const priceRes = await fetch('/api/subscription/prices', {
        method: 'GET',
        cache: 'no-store',
      })
      if (!priceRes.ok) {
        throw new Error('Failed to load pricing')
      }
      const { prices } = (await priceRes.json()) as {
        prices: Array<{ id: string; interval?: string; active: boolean }>
      }
      const targetInterval = isYearly ? 'year' : 'month'
      const price = prices.find(p => p.active && p.interval === targetInterval)
      if (!price?.id) {
        throw new Error('No matching price found')
      }
      const priceId = price.id

      // Create a user object compatible with the checkoutWithStripe function
      const userForCheckout = {
        id: session.user.id,
        email: session.user.email || '',
        subscription_customer_id: null, // Will be set by Stripe if needed
      } as any

      const result = await checkoutWithStripe(userForCheckout, priceId)

      // Handle the result - it could be a sessionId object or NextResponse
      if ('sessionId' in result) {
        const sessionId = result.sessionId

        // Redirect to Stripe Checkout
        const stripe = await getStripe()
        if (!stripe) {
          toast.error('Failed to load payment system')
          return
        }

        const { error: stripeError } = await stripe.redirectToCheckout({
          sessionId,
        })

        if (stripeError) {
          console.error('Stripe redirect error:', stripeError)
          toast.error('Failed to redirect to checkout')
        }
      } else {
        // Handle NextResponse error case
        toast.error('Failed to create checkout session')
        return
      }
    } catch (error) {
      console.error('Checkout error:', error)
      toast.error('Something went wrong. Please try again.')

      // Log checkout error event
      logEventWithContext(
        'error_subscription_checkout',
        session.user.id, // userId
        undefined, // dragTreeId not required for subscription events
        {
          plan: planId,
          billing_cycle: isYearly ? 'yearly' : 'monthly',
          error: error instanceof Error ? error.message : 'Unknown error',
        }
      )
    } finally {
      setIsLoading(false)
    }
  }

  const handleManageSubscription = async () => {
    if (!session?.user?.id) {
      toast.error('Please sign in to manage subscription')
      return
    }

    // This would need to be implemented with the user's customer ID
    // For now, we'll show a placeholder
    toast.success('Subscription management coming soon!')

    // Log manage subscription click event
    logEventWithContext(
      'click_subscription_manage',
      session.user.id, // userId
      undefined, // dragTreeId not required for subscription events
      {
        current_tier: currentTier,
      }
    )
  }

  return {
    // User state
    session,
    currentTier,
    isPaidUser,
    isLocalOrDev,

    // Loading state
    isLoading,

    // Actions
    handleStripeCheckout,
    handleManageSubscription,

    // Subscription info
    subscriptionInfo,
  }
}
