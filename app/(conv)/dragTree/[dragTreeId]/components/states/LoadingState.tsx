'use client'

import React from 'react'
import { Session } from 'next-auth'
import Header from '@/app/(conv)/dragTree/[dragTreeId]/components/layout/Header'
import { Loader2 } from 'lucide-react'

type LoadingStateProps = {
  session: Session | null
  showSyncTest: boolean
  onToggleSyncTest: () => void
  dragTreeId?: string | null
  message?: string
  size?: 'sm' | 'md' | 'lg'
  showMessage?: boolean
}

/**
 * Loading State Component
 *
 * Displays loading states with customizable size and message.
 * Used across the application for consistent loading indicators.
 */
const LoadingState: React.FC<LoadingStateProps> = ({
  session,
  showSyncTest,
  onToggleSyncTest,
  dragTreeId,
  message = 'Loading...',
  size = 'md',
  showMessage = true,
}) => {
  const sizeClasses = {
    sm: 'h-6 w-6',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
  }

  return (
    <div className="flex flex-col h-screen w-full bg-white overflow-hidden animate-in fade-in duration-300">
      <div className="flex-shrink-0 border-b">
        <Header
          session={session}
          showSyncTest={showSyncTest}
          onToggleSyncTest={onToggleSyncTest}
          isLoading={true}
        />
      </div>
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center animate-in slide-in-from-bottom-4 duration-500 delay-100">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4">
            <Loader2
              className={`${sizeClasses[size]} animate-spin text-blue-500`}
            />
          </div>
          <h2 className="text-xl font-semibold text-gray-700 mb-2">
            Loading Tree
          </h2>
          <p className="text-gray-500">
            {dragTreeId
              ? `loading ${dragTreeId}`
              : 'Preparing your clarification tree...'}
          </p>
          {showMessage && <p className="text-sm text-gray-600">{message}</p>}
        </div>
      </div>
    </div>
  )
}

// Memoize to prevent unnecessary re-renders when props haven't changed
export default React.memo(LoadingState)
