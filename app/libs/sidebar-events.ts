// Event system for sidebar updates
// This allows other parts of the app to notify the sidebar when new drag trees are created

import type { DragTreeStatus } from '@prisma/client'

type SidebarEvent = 'NEW_DRAG_TREE' | 'REFRESH_SIDEBAR'

type DragTreeData = {
  id: string
  title: string | null
  status: DragTreeStatus
  created_at: string | Date
  updated_at: string | Date
}

type SidebarEventData = {
  NEW_DRAG_TREE: DragTreeData
  REFRESH_SIDEBAR: void
}

type SidebarEventListener<T extends SidebarEvent> = (
  data: SidebarEventData[T]
) => void

class SidebarEventManager {
  private listeners: { [K in SidebarEvent]?: SidebarEventListener<K>[] } = {}

  // Subscribe to sidebar events
  on<T extends SidebarEvent>(event: T, listener: SidebarEventListener<T>) {
    if (!this.listeners[event]) {
      this.listeners[event] = []
    }
    this.listeners[event]!.push(listener)

    // Return unsubscribe function
    return () => {
      const eventListeners = this.listeners[event]
      if (eventListeners) {
        const index = eventListeners.indexOf(listener)
        if (index > -1) {
          eventListeners.splice(index, 1)
        }
      }
    }
  }

  // Emit sidebar events
  emit<T extends SidebarEvent>(event: T, data: SidebarEventData[T]) {
    const eventListeners = this.listeners[event]
    if (eventListeners) {
      eventListeners.forEach(listener => {
        try {
          listener(data)
        } catch (error) {
          console.error(`Error in sidebar event listener for ${event}:`, error)
        }
      })
    }
  }

  // Clear all listeners (useful for cleanup)
  clear() {
    this.listeners = {}
  }
}

// Create a singleton instance
export const sidebarEvents = new SidebarEventManager()

// Convenience functions
export const addNewDragTreeToSidebar = (dragTree: DragTreeData) => {
  sidebarEvents.emit('NEW_DRAG_TREE', dragTree)
}

export const refreshSidebar = () => {
  sidebarEvents.emit('REFRESH_SIDEBAR', undefined)
}

// Type exports
export type {
  DragTreeData,
  SidebarEvent,
  SidebarEventData,
  SidebarEventListener,
}
