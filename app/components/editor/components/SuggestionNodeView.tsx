'use client'

import { NodeViewWrapper, NodeViewProps } from '@tiptap/react'
import { Check, X } from 'lucide-react'
import { useState } from 'react'

export default function SuggestionNodeView({
  node,
  editor,
  getPos,
}: NodeViewProps) {
  const { oldText, newText, id } = node.attrs
  const [isProcessing, setIsProcessing] = useState<boolean>(false)

  const replaceWith = async (text: string) => {
    if (isProcessing) return
    setIsProcessing(true)

    try {
      const pos = typeof getPos === 'function' ? getPos() : null
      if (pos === null) return

      // Create a text node with the replacement text
      const textNode = editor.schema.text(text)

      // Replace the suggestion node with the text
      editor
        .chain()
        .focus()
        .insertContentAt({ from: pos, to: pos + node.nodeSize }, textNode)
        .run()
    } finally {
      setIsProcessing(false)
    }
  }

  const accept = () => replaceWith(newText)
  const reject = () => replaceWith(oldText)

  return (
    <NodeViewWrapper
      className="suggestion-node inline-flex items-center gap-1 my-1 rounded-md border border-gray-200 bg-gray-50 px-1 py-0.5"
      data-suggestion-id={id}
    >
      {/* Old text - red with strikethrough */}
      <span className="old-text bg-red-100 text-red-800 line-through px-1 rounded text-sm">
        {oldText}
      </span>

      {/* Arrow or separator */}
      <span className="text-gray-400 text-xs">→</span>

      {/* New text - green */}
      <span className="new-text bg-green-100 text-green-800 px-1 rounded text-sm">
        {newText}
      </span>

      {/* Action buttons */}
      <div className="actions flex gap-1 ml-1">
        <button
          onClick={accept}
          disabled={isProcessing}
          className="accept-btn flex items-center justify-center w-5 h-5 rounded-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 transition-colors"
          title="Accept suggestion"
          type="button"
        >
          <Check className="w-3 h-3 text-white" />
        </button>
        <button
          onClick={reject}
          disabled={isProcessing}
          className="reject-btn flex items-center justify-center w-5 h-5 rounded-full bg-red-600 hover:bg-red-700 disabled:bg-gray-400 transition-colors"
          title="Reject suggestion"
          type="button"
        >
          <X className="w-3 h-3 text-white" />
        </button>
      </div>
    </NodeViewWrapper>
  )
}
