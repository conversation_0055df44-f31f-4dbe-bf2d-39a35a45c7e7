/**
 * Tests for ResearchDisplay Components
 *
 * Tests the research display functionality including:
 * - Search progress indicators
 * - Source citations
 * - Content rendering
 * - Loading states
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import {
  SearchProgressIndicator,
  SourceCitations,
  type SearchMetadata,
} from '@/app/(conv)/dragTree/[dragTreeId]/components/SearchResultsDisplay'

// Mock createPortal for popup testing
jest.mock('react-dom', () => ({
  ...jest.requireActual('react-dom'),
  createPortal: (children: React.ReactNode) => children,
}))

describe('ResearchDisplay Components', () => {
  const mockSearchResults: SearchMetadata[] = [
    {
      keyword: 'test query 1',
      url: 'https://example1.com',
      icon: 'https://example1.com/favicon.ico',
      snippets: ['First test snippet', 'Another snippet from first source'],
      timestamp: '2024-01-01T10:00:00Z',
      source: 'brave_search',
    },
    {
      keyword: 'test query 2',
      url: 'https://example2.com',
      icon: 'https://example2.com/favicon.ico',
      snippets: ['Second test snippet'],
      timestamp: '2024-01-01T10:05:00Z',
      source: 'brave_search',
    },
    {
      keyword: 'test query 3',
      url: 'https://example3.com',
      // No icon
      snippets: ['Third test snippet', 'More content from third source'],
      timestamp: '2024-01-01T10:10:00Z',
      source: 'brave_search',
    },
  ]

  describe('SearchProgressIndicator', () => {
    it('should render when searching is active', () => {
      render(
        <SearchProgressIndicator isSearching={true} currentQuery="test query" />
      )

      expect(screen.getByText(/Searching/)).toBeInTheDocument()
      expect(screen.getByText(/test query/)).toBeInTheDocument()

      // Should show loading spinner (SVG with specific classes)
      const spinner = document.querySelector('.animate-spin')
      expect(spinner).toBeInTheDocument()
    })

    it('should not render when not searching', () => {
      render(
        <SearchProgressIndicator
          isSearching={false}
          currentQuery="test query"
        />
      )

      expect(screen.queryByText(/Searching for:/)).not.toBeInTheDocument()
    })

    it('should render with default message when no query provided', () => {
      render(<SearchProgressIndicator isSearching={true} />)

      expect(screen.getByText(/Searching/)).toBeInTheDocument()
    })

    it('should apply custom className', () => {
      const { container } = render(
        <SearchProgressIndicator isSearching={true} className="custom-class" />
      )

      expect(container.firstChild).toHaveClass('custom-class')
    })

    it('should show animated elements when searching', () => {
      render(<SearchProgressIndicator isSearching={true} />)

      const animatedElements = document.querySelectorAll('.animate-spin')
      expect(animatedElements.length).toBeGreaterThan(0)
    })
  })

  describe('SourceCitations', () => {
    it('should render source icons when provided', () => {
      render(<SourceCitations searchResults={mockSearchResults} />)

      const button = screen.getByRole('button')
      expect(button).toBeInTheDocument()

      // Should show favicon images
      const favicons = screen.getAllByRole('img')
      expect(favicons).toHaveLength(2) // Only first two have icons
    })

    it('should render numbered placeholders for sources without icons', () => {
      const resultsWithoutIcons: SearchMetadata[] = [
        {
          keyword: 'test',
          url: 'https://example.com',
          snippets: ['test'],
          timestamp: '2024-01-01T00:00:00Z',
          source: 'brave_search',
          // No icon
        },
      ]

      render(<SourceCitations searchResults={resultsWithoutIcons} />)

      expect(screen.getByText('1')).toBeInTheDocument()
    })

    it('should limit displayed sources to maxSources', () => {
      render(
        <SourceCitations searchResults={mockSearchResults} maxSources={2} />
      )

      screen.getByRole('button')

      // Should show +1 indicator for extra sources
      expect(screen.getByText('+1')).toBeInTheDocument()
    })

    it('should handle empty search results', () => {
      render(<SourceCitations searchResults={[]} />)

      expect(screen.queryByRole('button')).not.toBeInTheDocument()
    })

    it('should handle undefined search results', () => {
      render(<SourceCitations />)

      expect(screen.queryByRole('button')).not.toBeInTheDocument()
    })

    it('should deduplicate sources by URL', () => {
      const duplicateResults: SearchMetadata[] = [
        ...mockSearchResults,
        {
          keyword: 'duplicate query',
          url: 'https://example1.com', // Same URL as first result
          snippets: ['duplicate content'],
          timestamp: '2024-01-01T10:15:00Z',
          source: 'brave_search',
        },
      ]

      render(<SourceCitations searchResults={duplicateResults} />)

      // Should still show 3 unique sources, not 4
      const button = screen.getByRole('button')
      expect(button).toHaveAttribute('title', 'View 3 sources')
    })

    it('should apply custom className', () => {
      const { container } = render(
        <SourceCitations
          searchResults={mockSearchResults}
          className="custom-citations"
        />
      )

      const button = container.querySelector('button')
      expect(button).toHaveClass('custom-citations')
    })
  })

  describe('Source Menu Interaction', () => {
    it('should open popup menu when citations are clicked', async () => {
      const user = userEvent.setup()
      render(<SourceCitations searchResults={mockSearchResults} />)

      const button = screen.getByRole('button')
      await user.click(button)

      await waitFor(() => {
        // Should show source details in popup
        expect(screen.getByText('example1.com')).toBeInTheDocument()
        expect(screen.getByText('example2.com')).toBeInTheDocument()
        expect(screen.getByText('example3.com')).toBeInTheDocument()
      })
    })

    it('should close popup when clicking outside', async () => {
      const user = userEvent.setup()
      render(
        <div>
          <SourceCitations searchResults={mockSearchResults} />
          <div data-testid="outside">Outside content</div>
        </div>
      )

      const button = screen.getByRole('button')
      await user.click(button)

      // Modal should open (we can't test exact content due to portal rendering)
      expect(button).toBeInTheDocument()

      // Click outside
      await user.click(screen.getByTestId('outside'))

      // Modal should close (we can't test exact modal content due to portal rendering)
    })

    it('should close popup when pressing Escape', async () => {
      const user = userEvent.setup()
      render(<SourceCitations searchResults={mockSearchResults} />)

      const button = screen.getByRole('button')
      await user.click(button)

      // Modal should open (we can't test exact content due to portal rendering)
      expect(button).toBeInTheDocument()

      // Press Escape
      fireEvent.keyDown(document, { key: 'Escape', code: 'Escape' })

      // Modal should close (we can't test exact modal content due to portal rendering)
    })

    it('should open external links when source is clicked', async () => {
      const user = userEvent.setup()

      // Mock window.open
      const mockOpen = jest.fn()
      Object.defineProperty(window, 'open', {
        value: mockOpen,
        writable: true,
      })

      render(<SourceCitations searchResults={mockSearchResults} />)

      const button = screen.getByRole('button')
      await user.click(button)

      // Modal should open (we can't test exact content due to portal rendering)
      expect(button).toBeInTheDocument()

      // Click on the first source
      const sourceLink =
        screen.getByText('example1.com').closest('a') ||
        screen.getByText('example1.com').closest('button')

      if (sourceLink) {
        await user.click(sourceLink)
        expect(mockOpen).toHaveBeenCalledWith(
          'https://example1.com',
          '_blank',
          'noopener,noreferrer'
        )
      }
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels for source citations', () => {
      render(<SourceCitations searchResults={mockSearchResults} />)

      const button = screen.getByRole('button')
      expect(button).toHaveAttribute('title', 'View 3 sources')
    })

    it('should handle favicon loading errors gracefully', () => {
      render(<SourceCitations searchResults={mockSearchResults} />)

      const favicons = screen.getAllByRole('img')

      // Simulate favicon loading error
      fireEvent.error(favicons[0])

      // Icon should be hidden but component should still work
      expect(favicons[0]).toHaveStyle('display: none')
    })

    it('should be keyboard navigable', async () => {
      render(<SourceCitations searchResults={mockSearchResults} />)

      const button = screen.getByRole('button')

      // Focus the button
      button.focus()
      expect(button).toHaveFocus()

      // Press Enter to open menu
      fireEvent.keyDown(button, { key: 'Enter', code: 'Enter' })

      // The menu should open (we can't test exact content due to portal rendering)
      expect(button).toBeInTheDocument()
    })
  })

  describe('Performance Considerations', () => {
    it('should handle large numbers of sources efficiently', () => {
      const largeSources: SearchMetadata[] = Array.from(
        { length: 100 },
        (_, i) => ({
          keyword: `query ${i}`,
          url: `https://example${i}.com`,
          snippets: [`snippet ${i}`],
          timestamp: '2024-01-01T00:00:00Z',
          source: 'brave_search',
        })
      )

      const { container } = render(
        <SourceCitations searchResults={largeSources} maxSources={5} />
      )

      // Should still render efficiently
      expect(container.firstChild).toBeInTheDocument()

      // Should show +97 for extra sources (100 - 3 displayed icons)
      expect(screen.getByText('+97')).toBeInTheDocument()
    })

    it('should not re-render unnecessarily with same props', () => {
      const { rerender } = render(
        <SourceCitations searchResults={mockSearchResults} />
      )

      const firstButton = screen.getByRole('button')

      // Rerender with same props
      rerender(<SourceCitations searchResults={mockSearchResults} />)

      const secondButton = screen.getByRole('button')

      // Should be the same element (React should not recreate it)
      expect(firstButton).toBe(secondButton)
    })
  })
})
