import { NextRequest, NextResponse } from 'next/server'
import prisma from '@/app/libs/prismadb'

export const runtime = 'nodejs'
export const dynamic = 'force-dynamic'

export async function GET(_req: NextRequest) {
  try {
    const prices = await prisma.price.findMany({
      where: { active: true },
      select: {
        id: true,
        active: true,
        currency: true,
        unit_amount: true,
        interval: true,
        interval_count: true,
        product_id: true,
      },
      orderBy: [{ interval: 'asc' }, { unit_amount: 'asc' }],
    })

    return NextResponse.json({ prices })
  } catch (error) {
    console.error('Failed to fetch prices:', error)
    return NextResponse.json(
      { error: 'Failed to load pricing' },
      { status: 500 }
    )
  }
}
