import React from 'react'
import { OutlineViewProps } from '@/app/(conv)/dragTree/[dragTreeId]/types'
import {
  useTreeState,
  useNodeInteraction,
} from '@/app/(conv)/dragTree/[dragTreeId]/components/HierarchicalOutline/hooks'
import { OutlineNode } from '@/app/(conv)/dragTree/[dragTreeId]/components/HierarchicalOutline/components'
import SortableChildList from './components/SortableChildList'

const OutlineView: React.FC<OutlineViewProps> = props => {
  const {
    node,
    onAdd,
    onDelete,
    onEdit,
    onReorder,
    editingNode,
    setEditingNode,
    collapsedNodes,
    setCollapsedNodes,
    targetNodeId,
    onGenerateSimilarQuestions,
    onGenerateSimilarCategories,
    isLoading,
    loadingNodeId,
    isGloballyExpanded,
    dragAttributes,
    dragListeners,
    isDragging,
  } = props

  // State management for the current node's collapsed state
  const { currentCollapsedNodes, updateCollapsedNodes, isCollapsed } =
    useTreeState(node, collapsedNodes, setCollapsedNodes)

  // All interaction logic (hover, click, persistent pinning) is managed here
  const interactionState = useNodeInteraction(
    node,
    isLoading ?? false,
    currentCollapsedNodes,
    updateCollapsedNodes
  )

  // Debug logging for loading state changes (root only to reduce noise)
  React.useEffect(() => {
    if (node.id === 'root') {
      console.log(
        `🌳 TreeView[ROOT]: isLoading=${props.isLoading}, loadingNodeId="${props.loadingNodeId}"`
      )
    }
  }, [props.isLoading, props.loadingNodeId, node.id])

  return (
    // This div creates the "extended hover zone". By applying the mouse handlers here,
    // we ensure that the hover state is maintained even when the cursor moves from the
    // parent node onto the child list area, preventing unwanted collapsing.
    <div
      onMouseEnter={interactionState.handleMouseEnter}
      onMouseLeave={interactionState.handleMouseLeave}
      className="relative"
    >
      {/* Render the main node itself */}
      <OutlineNode
        node={node}
        onAdd={onAdd}
        onDelete={onDelete}
        onEdit={onEdit}
        onReorder={onReorder}
        editingNode={editingNode}
        setEditingNode={setEditingNode}
        collapsedNodes={collapsedNodes}
        setCollapsedNodes={setCollapsedNodes}
        targetNodeId={targetNodeId}
        onGenerateSimilarQuestions={onGenerateSimilarQuestions}
        onGenerateSimilarCategories={onGenerateSimilarCategories}
        isLoading={isLoading}
        loadingNodeId={loadingNodeId}
        isGloballyExpanded={isGloballyExpanded}
        dragAttributes={dragAttributes}
        dragListeners={dragListeners}
        isDragging={isDragging}
        isClicked={interactionState.isClicked}
        isHovered={interactionState.isHovered}
        isExpanding={interactionState.isExpanding}
        isPersistent={interactionState.isPersistent}
        lastExpansionTime={interactionState.lastExpansionTime}
        lastClickTime={interactionState.lastClickTime}
        handleNodeClick={interactionState.handleNodeClick}
        handleToggleCollapse={interactionState.handleToggleCollapse}
      />

      {/* Render the list of children with drag-and-drop capabilities */}
      <SortableChildList
        parentNode={node}
        isParentCollapsed={isCollapsed}
        collapsedNodes={currentCollapsedNodes}
        setCollapsedNodes={updateCollapsedNodes}
        onAdd={onAdd}
        onDelete={onDelete}
        onEdit={onEdit}
        onReorder={onReorder}
        editingNode={editingNode}
        setEditingNode={setEditingNode}
        targetNodeId={targetNodeId}
        onGenerateSimilarQuestions={onGenerateSimilarQuestions}
        onGenerateSimilarCategories={onGenerateSimilarCategories}
        isLoading={isLoading ?? false}
        loadingNodeId={loadingNodeId}
        isGloballyExpanded={isGloballyExpanded}
      />
    </div>
  )
}

export default React.memo(OutlineView)
