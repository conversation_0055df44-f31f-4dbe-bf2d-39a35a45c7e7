/**
 * Error Boundary for Pane Components
 *
 * Provides error isolation and fallback UI for pane-related components,
 * following the established error boundary pattern in the codebase.
 * Used to wrap HierarchicalOutline and VisualFlowDiagram panes.
 */

import React, { Component, ReactNode, ErrorInfo } from 'react'
import { AlertTriangle, RefreshCw, TreePine, Workflow } from 'lucide-react'
import { Button } from '@/components/ui/button'

export type PaneType = 'outline' | 'visualization'

type PaneErrorBoundaryProps = {
  children: ReactNode
  paneType: PaneType
  fallback?: ReactNode
  onRetry?: () => void
}

type PaneErrorBoundaryState = {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
}

/**
 * Get pane-specific configuration for error display
 */
const getPaneConfig = (paneType: PaneType) => {
  switch (paneType) {
    case 'outline':
      return {
        icon: TreePine,
        title: 'Tree outline temporarily unavailable',
        description:
          'The hierarchical tree view encountered an error. You can still use the visualization pane.',
        color: 'green',
      }
    case 'visualization':
      return {
        icon: Workflow,
        title: 'Flow diagram temporarily unavailable',
        description:
          'The visual flow diagram encountered an error. You can still use the tree outline pane.',
        color: 'blue',
      }
    default:
      return {
        icon: AlertTriangle,
        title: 'Pane temporarily unavailable',
        description: 'This pane encountered an error. Please try refreshing.',
        color: 'red',
      }
  }
}

class PaneErrorBoundary extends Component<
  PaneErrorBoundaryProps,
  PaneErrorBoundaryState
> {
  constructor(props: PaneErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(
    error: Error
  ): Partial<PaneErrorBoundaryState> {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log the error for debugging purposes with pane context
    console.error(
      `[${this.props.paneType.charAt(0).toUpperCase() + this.props.paneType.slice(1)} Pane] Error boundary caught error:`,
      error,
      errorInfo
    )

    // Store error info for potential debugging
    this.setState({ errorInfo })

    // You could also log to an external error reporting service here
    // Example: errorReportingService.captureException(error, {
    //   extra: { ...errorInfo, paneType: this.props.paneType }
    // })
  }

  handleRetry = () => {
    // Reset error state to retry rendering
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })

    // Call optional retry callback
    if (this.props.onRetry) {
      this.props.onRetry()
    }
  }

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided, otherwise show default error UI
      if (this.props.fallback) {
        return this.props.fallback
      }

      const config = getPaneConfig(this.props.paneType)
      const Icon = config.icon

      return (
        <div className="h-full flex items-center justify-center p-6">
          <div className="text-center max-w-sm">
            <div className={`text-${config.color}-500 mb-4`}>
              <Icon className="h-12 w-12 mx-auto mb-4" />
            </div>
            <h3 className="font-medium text-gray-800 mb-2">{config.title}</h3>
            <p className="text-sm text-gray-600 mb-4">{config.description}</p>
            <div className="flex flex-col items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={this.handleRetry}
                className={`text-${config.color}-700 border-${config.color}-300 hover:bg-${config.color}-50`}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry {this.props.paneType === 'outline' ? 'Tree' : 'Diagram'}
              </Button>

              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="text-xs w-full">
                  <summary
                    className={`cursor-pointer text-${config.color}-500 hover:text-${config.color}-700`}
                  >
                    Show Error Details
                  </summary>
                  <pre
                    className={`mt-2 p-2 bg-${config.color}-50 rounded text-${config.color}-800 overflow-auto max-h-32 text-left`}
                  >
                    {this.state.error.message}
                    {this.state.error.stack && (
                      <>
                        {'\n\n'}
                        {this.state.error.stack}
                      </>
                    )}
                  </pre>
                </details>
              )}
            </div>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

export default PaneErrorBoundary
