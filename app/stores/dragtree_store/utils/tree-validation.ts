import { TreeNode } from '@/app/types'
import { performanceMonitor } from '@/app/utils/performance-monitor'

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
  warnings: ValidationWarning[]
  stats: ValidationStats
}

export interface ValidationError {
  type:
    | 'DUPLICATE_ID'
    | 'CIRCULAR_REFERENCE'
    | 'INVALID_STRUCTURE'
    | 'MISSING_REQUIRED_FIELD'
    | 'INVALID_TYPE'
  message: string
  nodeId?: string
  path?: string[]
  severity: 'error' | 'warning'
}

export interface ValidationWarning {
  type: 'EMPTY_LABEL' | 'DEEP_NESTING' | 'LARGE_TREE' | 'PERFORMANCE_CONCERN'
  message: string
  nodeId?: string
  path?: string[]
}

export interface ValidationStats {
  totalNodes: number
  maxDepth: number
  duplicateIds: number
  emptyLabels: number
  validationTime: number
}

/**
 * Comprehensive tree structure validation
 * Checks for data integrity, circular references, and performance concerns
 */
export function validateTreeStructure(tree: TreeNode | null): ValidationResult {
  const startTime = performance.now()
  const endTiming = performanceMonitor.startTiming('validateTreeStructure')

  const errors: ValidationError[] = []
  const warnings: ValidationWarning[] = []
  const nodeIds = new Set<string>()
  const nodeMap = new Map<string, { node: TreeNode; path: string[] }>()

  let totalNodes = 0
  let maxDepth = 0
  let duplicateIds = 0
  let emptyLabels = 0

  if (!tree) {
    const validationTime = performance.now() - startTime
    endTiming({ errorCount: 1 })

    return {
      isValid: false,
      errors: [
        {
          type: 'INVALID_STRUCTURE',
          message: 'Tree is null or undefined',
          severity: 'error',
        },
      ],
      warnings: [],
      stats: {
        totalNodes: 0,
        maxDepth: 0,
        duplicateIds: 0,
        emptyLabels: 0,
        validationTime,
      },
    }
  }

  /**
   * Recursive validation function
   */
  function validateNode(
    node: TreeNode,
    path: string[],
    depth: number = 0
  ): void {
    totalNodes++
    maxDepth = Math.max(maxDepth, depth)

    // Validate node structure
    if (!node || typeof node !== 'object') {
      errors.push({
        type: 'INVALID_STRUCTURE',
        message: `Invalid node structure at path: ${path.join('/')}`,
        path,
        severity: 'error',
      })
      return
    }

    // Check required fields
    if (!node.id) {
      errors.push({
        type: 'MISSING_REQUIRED_FIELD',
        message: `Node missing required 'id' field at path: ${path.join('/')}`,
        path,
        severity: 'error',
      })
    } else {
      // Check for duplicate IDs
      if (nodeIds.has(node.id)) {
        duplicateIds++
        errors.push({
          type: 'DUPLICATE_ID',
          message: `Duplicate node ID: ${node.id} at path: ${path.join('/')}`,
          nodeId: node.id,
          path,
          severity: 'error',
        })
      } else {
        nodeIds.add(node.id)
        nodeMap.set(node.id, { node, path })
      }
    }

    // Check label
    if (!node.label || typeof node.label !== 'string') {
      errors.push({
        type: 'MISSING_REQUIRED_FIELD',
        message: `Node missing or invalid 'label' field: ${node.id}`,
        nodeId: node.id,
        path,
        severity: 'error',
      })
    } else if (!node.label.trim()) {
      emptyLabels++
      warnings.push({
        type: 'EMPTY_LABEL',
        message: `Node has empty label: ${node.id}`,
        nodeId: node.id,
        path,
      })
    }

    // Check type
    if (!node.type || !['category', 'question'].includes(node.type)) {
      errors.push({
        type: 'INVALID_TYPE',
        message: `Node has invalid type '${node.type}': ${node.id}`,
        nodeId: node.id,
        path,
        severity: 'error',
      })
    }

    // Check children array
    if (!Array.isArray(node.children)) {
      errors.push({
        type: 'INVALID_STRUCTURE',
        message: `Node children is not an array: ${node.id}`,
        nodeId: node.id,
        path,
        severity: 'error',
      })
      return
    }

    // Performance warnings
    if (depth > 10) {
      warnings.push({
        type: 'DEEP_NESTING',
        message: `Deep nesting detected (depth ${depth}): ${node.id}`,
        nodeId: node.id,
        path,
      })
    }

    if (node.children.length > 50) {
      warnings.push({
        type: 'PERFORMANCE_CONCERN',
        message: `Node has many children (${node.children.length}): ${node.id}`,
        nodeId: node.id,
        path,
      })
    }

    // Validate children recursively
    node.children.forEach((child, index) => {
      if (child) {
        validateNode(child, [...path, `child[${index}]`], depth + 1)
      } else {
        errors.push({
          type: 'INVALID_STRUCTURE',
          message: `Null/undefined child at index ${index} in node: ${node.id}`,
          nodeId: node.id,
          path: [...path, `child[${index}]`],
          severity: 'error',
        })
      }
    })
  }

  // Start validation from root
  validateNode(tree, ['root'])

  // Check for circular references (additional pass)
  const visitedInPath = new Set<string>()
  const maxDepthForCircularCheck = 50 // Prevent infinite recursion

  function checkCircularReferences(
    node: TreeNode,
    path: string[],
    depth: number = 0
  ): void {
    if (depth > maxDepthForCircularCheck) {
      errors.push({
        type: 'CIRCULAR_REFERENCE',
        message: `Possible circular reference detected (max depth exceeded): ${node.id}`,
        nodeId: node.id,
        path,
        severity: 'error',
      })
      return
    }

    if (visitedInPath.has(node.id)) {
      errors.push({
        type: 'CIRCULAR_REFERENCE',
        message: `Circular reference detected: ${node.id} at path: ${path.join('/')}`,
        nodeId: node.id,
        path,
        severity: 'error',
      })
      return
    }

    visitedInPath.add(node.id)

    if (Array.isArray(node.children)) {
      node.children.forEach((child, index) => {
        if (child) {
          checkCircularReferences(
            child,
            [...path, `child[${index}]`],
            depth + 1
          )
        }
      })
    }

    visitedInPath.delete(node.id)
  }

  try {
    checkCircularReferences(tree, ['root'])
  } catch (error) {
    errors.push({
      type: 'INVALID_STRUCTURE',
      message: `Error during circular reference check: ${error instanceof Error ? error.message : 'Unknown error'}`,
      severity: 'error',
    })
  }

  // Performance warnings for large trees
  if (totalNodes > 1000) {
    warnings.push({
      type: 'LARGE_TREE',
      message: `Large tree detected (${totalNodes} nodes). Consider optimization.`,
    })
  }

  const validationTime = performance.now() - startTime
  const isValid = errors.length === 0

  // Record performance metrics
  endTiming({
    nodeCount: totalNodes,
    errorCount: errors.length,
  })

  const result: ValidationResult = {
    isValid,
    errors,
    warnings,
    stats: {
      totalNodes,
      maxDepth,
      duplicateIds,
      emptyLabels,
      validationTime,
    },
  }

  // Log validation results
  if (process.env.NODE_ENV === 'development') {
    console.log(
      `🔍 [Tree Validation] Completed in ${validationTime.toFixed(2)}ms:`,
      {
        isValid,
        totalNodes,
        maxDepth,
        errors: errors.length,
        warnings: warnings.length,
        duplicateIds,
        emptyLabels,
      }
    )

    if (errors.length > 0) {
      console.error('❌ [Tree Validation] Errors found:', errors)
    }

    if (warnings.length > 0) {
      console.warn('⚠️ [Tree Validation] Warnings:', warnings)
    }
  }

  return result
}

/**
 * Quick validation for performance-critical paths
 * Only checks essential integrity without deep analysis
 */
export function quickValidateTree(tree: TreeNode | null): boolean {
  if (!tree) return false

  const nodeIds = new Set<string>()

  function quickCheck(node: TreeNode): boolean {
    if (
      !node.id ||
      !node.label ||
      !node.type ||
      !Array.isArray(node.children)
    ) {
      return false
    }

    if (nodeIds.has(node.id)) {
      return false // Duplicate ID
    }

    nodeIds.add(node.id)

    return node.children.every(child => (child ? quickCheck(child) : false))
  }

  return quickCheck(tree)
}

/**
 * Sanitize tree structure by removing invalid nodes
 * Returns a cleaned version of the tree
 */
export function sanitizeTreeStructure(tree: TreeNode | null): TreeNode | null {
  if (!tree) return null

  const endTiming = performanceMonitor.startTiming('sanitizeTreeStructure')
  const seenIds = new Set<string>()

  function sanitizeNode(node: TreeNode): TreeNode | null {
    // Check basic structure
    if (
      !node ||
      typeof node !== 'object' ||
      !node.id ||
      !node.label ||
      !node.type
    ) {
      return null
    }

    // Check for duplicate ID
    if (seenIds.has(node.id)) {
      console.warn(`🧹 [Tree Sanitization] Removing duplicate node: ${node.id}`)
      return null
    }

    seenIds.add(node.id)

    // Sanitize children
    const sanitizedChildren = Array.isArray(node.children)
      ? node.children
          .map(child => sanitizeNode(child))
          .filter((child): child is TreeNode => child !== null)
      : []

    const sanitizedNode: TreeNode = {
      id: node.id,
      label: node.label.trim() || 'Untitled',
      type: ['category', 'question'].includes(node.type)
        ? node.type
        : 'category',
      children: sanitizedChildren,
      isInterestedIn: Boolean(node.isInterestedIn),
    }

    return sanitizedNode
  }

  const result = sanitizeNode(tree)
  endTiming({ nodeCount: seenIds.size })

  console.log(`🧹 [Tree Sanitization] Processed ${seenIds.size} nodes`)
  return result
}
