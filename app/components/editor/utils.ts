import { marked } from 'marked'
import { generateJSON } from '@tiptap/html'
import DOMPurify from 'isomorphic-dompurify'
import { NotebookStatus } from '@prisma/client'
import { TiptapExtensions } from './extensions'
import { Editor, JSONContent } from '@tiptap/core'
import toast from 'react-hot-toast'

export const replaceMermaidTags = (html: string) => {
  return html.replace(
    /<pre><code class="language-mermaid">([\s\S]*?)<\/code><\/pre>/g,
    '<mermaid-component>$1</mermaid-component>'
  )
}

export type GetInitialContentType = {
  status: NotebookStatus
  content: string
  generation_output: string
}

export const getInitialContent = (notebook: GetInitialContentType) => {
  if (
    notebook.status === NotebookStatus.ACTIVE &&
    notebook.content.length > 0
  ) {
    return JSON.parse(notebook.content)
  } else if (
    notebook.status === NotebookStatus.ACTIVE &&
    notebook.generation_output.length > 0
  ) {
    const rawHTML = marked.parse(notebook.generation_output) as string
    const sanitizedHTML = DOMPurify.sanitize(rawHTML)
    const notebookHTML = replaceMermaidTags(sanitizedHTML)
    return generateJSON(notebookHTML, TiptapExtensions)
  } else {
    return JSON.parse('{}')
  }
}

export const processMessageContent = (content: string) => {
  const rawHTML = marked.parse(content) as string
  const sanitizedHTML = DOMPurify.sanitize(rawHTML)
  const notebookHTML = replaceMermaidTags(sanitizedHTML)
  return generateJSON(notebookHTML, TiptapExtensions)
}

export const extractMermaidCodes = (nodes: JSONContent[]): string[] => {
  const mermaidCodes: string[] = []
  const extract = (nodes: JSONContent[]) => {
    nodes.map((node: JSONContent) => {
      if (node.type === 'mermaidComponent') {
        const { attrs, content } = node
        if (attrs?.code) {
          mermaidCodes.push(attrs.code)
        } else if (content?.[0]?.content?.[0]?.text) {
          mermaidCodes.push(content[0].content[0].text)
        }
      }
    })
  }
  extract(nodes)
  return mermaidCodes
}

// Function to escape RegEx special characters
export const escapeRegExp = (string: string) => {
  return string.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')
}

// Utility function to convert editor content to Markdown with Mermaid code blocks
export const convertEditorContentToMarkdown = (editor: Editor): string => {
  const json = editor.getJSON()
  const mermaidCodes = extractMermaidCodes(json.content || [])
  const markdown = editor.storage.markdown.getMarkdown()

  const placeholder = '[mermaidComponent]'
  const escapedPlaceholder = escapeRegExp(placeholder)
  const regex = new RegExp(escapedPlaceholder, 'g')

  const placeholdersCount = (markdown.match(regex) || []).length

  if (placeholdersCount === mermaidCodes.length && placeholdersCount > 0) {
    let currentIndex = 0
    return markdown.replace(regex, () => {
      const code = mermaidCodes[currentIndex]
      currentIndex += 1
      return `\`\`\`mermaid\n${code}\n\`\`\``
    })
  }

  return markdown
}

// Utility function to copy text to clipboard
export const copyToClipboard = async (text: string): Promise<void> => {
  try {
    await navigator.clipboard.writeText(text)
    toast.success('Notebook content copied to clipboard!')
  } catch (err) {
    console.error('Failed to copy notebook content: ', err)
    toast.error('Failed to copy notebook')
  }
}

// Utility function to convert Tiptap JSON to Markdown without a full editor instance
export const convertTiptapJsonToMarkdown = (json: JSONContent): string => {
  // Return early if the content is not a valid Tiptap document
  if (!json || !json.type || json.type !== 'doc' || !json.content) {
    return ''
  }

  // Create a headless editor instance
  const editor = new Editor({
    extensions: TiptapExtensions, // Use the project's standard extensions for consistency
    content: json,
    editable: false, // This is crucial for a headless setup
  })

  // Use the tiptap-markdown extension to get the markdown output
  const markdown = editor.storage.markdown.getMarkdown()

  // Destroy the editor instance to prevent memory leaks
  editor.destroy()

  return markdown
}
