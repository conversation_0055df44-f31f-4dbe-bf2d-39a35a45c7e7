'use client'

import React, {
  useEffect,
  useRef,
  useState,
  useLayoutEffect,
  forwardRef,
  useImperativeHandle,
} from 'react'
import { useDebouncedCallback } from 'use-debounce'
import mermaid from 'mermaid'

interface MermaidChartRendererProps {
  chart: string
  onError: (msg: string | null) => void
}

export interface MermaidChartRendererHandle {
  exportSvg: () => void
}

const MermaidChartRenderer = forwardRef<
  MermaidChartRendererHandle,
  MermaidChartRendererProps
>(({ chart, onError }, ref) => {
  const chartRef = useRef<HTMLDivElement>(null)
  const [errorMsg, setErrorMsg] = useState<string | null>(null)

  // Initialize mermaid with default configuration
  useEffect(() => {
    const mermaidConfig: any = {
      startOnLoad: false, // We'll manually render charts
      theme: 'default',
      securityLevel: 'strict', // Harden against XSS; disallow unsafe HTML/event handlers
      suppressErrorRendering: true, // Hide the default error banner
      fontFamily: 'Fira Code',
    }
    mermaid.initialize(mermaidConfig as any)
  }, [])

  // Generate a random ID for the SVG element
  const generateRandomId = (length: number = 10): string => {
    const characters =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    return (
      'id_' +
      Array.from({ length }, () =>
        characters.charAt(Math.floor(Math.random() * characters.length))
      ).join('')
    )
  }

  // Render the Mermaid chart
  const renderMermaid = async (): Promise<void> => {
    if (!chartRef.current) return

    try {
      // console.log("Rendering Mermaid with chart code:", chart); // Debugging
      const svgId = generateRandomId()
      const { svg, bindFunctions } = await mermaid.render(svgId, chart)

      chartRef.current.innerHTML = svg
      if (bindFunctions) bindFunctions(chartRef.current)

      setErrorMsg(null)
    } catch (error: unknown) {
      setErrorMsg(error instanceof Error ? error.message : 'An error occurred')
      // console.log("Mermaid rendering error:", error);
    }
  }

  // Debounce the rendering to avoid unnecessary re-renders
  const debouncedRenderMermaid = useDebouncedCallback(renderMermaid, 150)

  // Trigger render when chart changes
  useLayoutEffect(() => {
    debouncedRenderMermaid()
  }, [chart])

  // Propagate error to parent component
  useEffect(() => {
    onError(errorMsg)
  }, [errorMsg, onError])

  // Expose the exportSvg method to parent component
  useImperativeHandle(ref, () => ({
    exportSvg: () => {
      if (!chartRef.current) return
      const svgElement = chartRef.current.querySelector('svg')
      if (svgElement) {
        const serializer = new XMLSerializer()
        const svgString = serializer.serializeToString(svgElement)
        const svgBlob = new Blob([svgString], {
          type: 'image/svg+xml;charset=utf-8',
        })
        const svgUrl = URL.createObjectURL(svgBlob)
        const downloadLink = document.createElement('a')
        downloadLink.href = svgUrl
        downloadLink.download = `mermaid_chart_${Date.now()}.svg`
        document.body.appendChild(downloadLink)
        downloadLink.click()
        document.body.removeChild(downloadLink)
      } else {
        setErrorMsg('SVG element not found for export.')
      }
    },
  }))

  return (
    <div className="flex flex-col">
      <div
        ref={chartRef}
        className="mermaid flex-1 transition-opacity duration-500 ease-in-out"
        // Removed key={chart}
      />
    </div>
  )
})

MermaidChartRenderer.displayName = 'MermaidChartRenderer'

export default MermaidChartRenderer
