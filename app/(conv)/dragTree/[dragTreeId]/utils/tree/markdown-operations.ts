import { TreeNode } from "@/app/types";

/**
 * Markdown conversion operations
 * Functions for converting between tree structures and markdown format
 */

/**
 * Convert a tree node to markdown format
 */
export const treeNodeToMarkdown = (
  node: TreeNode,
  level: number = 1
): string => {
  const prefix = "#".repeat(level);
  let markdown = `${prefix} ${node.label}\n`;

  for (const child of node.children) {
    if (child.type === "category") {
      markdown += treeNodeToMarkdown(child, level + 1);
    } else {
      markdown += `${"#".repeat(level + 1)} ${child.label}\n`;
    }
  }

  return markdown;
};

/**
 * Extract context information from tree for AI prompts
 */
export const getOriginalContextFromTree = (rootNode: TreeNode): string => {
  // Extract the root title and first level categories as context
  let context = `Project: ${rootNode.label}\n\n`;

  const firstLevelCategories = rootNode.children
    .filter((child) => child.type === "category")
    .map((child) => child.label);

  if (firstLevelCategories.length > 0) {
    context += "Main Areas:\n";
    firstLevelCategories.forEach((cat) => {
      context += `- ${cat}\n`;
    });
  }

  return context;
};

/**
 * Parse questions from markdown text
 */
export const parseQuestionsFromMarkdown = (markdown: string): string[] => {
  const lines = markdown.split("\n");
  const questions: string[] = [];

  for (const line of lines) {
    const trimmed = line.trim();
    // Parse both bullet points and markdown headers as questions
    if (trimmed.startsWith("- ") && !trimmed.startsWith("- Examples:")) {
      questions.push(trimmed.substring(2));
    } else if (trimmed.match(/^#{4,}\s+/)) {
      // Parse #### or more as questions
      const match = trimmed.match(/^#{4,}\s+(.+)$/);
      if (match) {
        questions.push(match[1]);
      }
    }
  }

  return questions;
};

/**
 * Parse a subtree from markdown content
 */
export const parseSubtreeFromMarkdown = (
  markdown: string,
  parentNodeId: string
): TreeNode[] => {
  const lines = markdown.split("\n");
  const newNodes: TreeNode[] = [];
  let questionCounter = 1;
  let categoryCounter = 1;

  // Stack to keep track of current category hierarchy
  const categoryStack: TreeNode[] = [];

  for (const line of lines) {
    const trimmed = line.trim();

    // Skip the root heading (# ParentCategory)
    if (trimmed.match(/^#\s+/)) {
      continue;
    }

    // Parse different heading levels
    const headingMatch = trimmed.match(/^(#{2,})\s+(.+)$/);
    if (headingMatch) {
      const level = headingMatch[1].length;
      const label = headingMatch[2];

      // ### (3 hashes) are questions, ## and #### and above are categories
      if (level === 3) {
        // This is a question
        const questionNode: TreeNode = {
          id: `${parentNodeId}_q_${questionCounter++}`,
          label: label,
          type: "question",
          children: [],
          isInterestedIn: false,
        };

        if (categoryStack.length > 0) {
          categoryStack[categoryStack.length - 1].children.push(questionNode);
        } else {
          // If no category stack, create a default category
          const defaultCategory: TreeNode = {
            id: `${parentNodeId}_cat_${categoryCounter++}`,
            label: "Additional Questions",
            type: "category",
            children: [questionNode],
            isInterestedIn: false,
          };
          newNodes.push(defaultCategory);
        }
      } else {
        // This is a category (## or #### and above)
        const categoryNode: TreeNode = {
          id: `${parentNodeId}_cat_${categoryCounter++}`,
          label: label,
          type: "category",
          children: [],
          isInterestedIn: false,
        };

        // Adjust the stack based on heading level
        while (categoryStack.length >= level - 1) {
          categoryStack.pop();
        }

        if (categoryStack.length === 0) {
          // Top-level category under parent
          newNodes.push(categoryNode);
        } else {
          // Add to current parent category
          categoryStack[categoryStack.length - 1].children.push(categoryNode);
        }

        categoryStack.push(categoryNode);
      }
    }
    // Parse bullet points as questions
    else if (trimmed.startsWith("- ") && !trimmed.startsWith("- Examples:")) {
      const questionLabel = trimmed.substring(2);
      const questionNode: TreeNode = {
        id: `${parentNodeId}_q_${questionCounter++}`,
        label: questionLabel,
        type: "question",
        children: [],
        isInterestedIn: false,
      };

      if (categoryStack.length > 0) {
        categoryStack[categoryStack.length - 1].children.push(questionNode);
      } else {
        // If no category stack, create a default category
        const defaultCategory: TreeNode = {
          id: `${parentNodeId}_cat_${categoryCounter++}`,
          label: "Additional Questions",
          type: "category",
          children: [questionNode],
          isInterestedIn: false,
        };
        newNodes.push(defaultCategory);
      }
    }
  }

  return newNodes;
};

/**
 * Convert markdown to a simplified tree structure for preview
 */
export const markdownToTreePreview = (
  markdown: string
): { title: string; categories: string[]; questionCount: number } => {
  const lines = markdown
    .split("\n")
    .map((line) => line.trim())
    .filter(Boolean);

  let title = "Untitled";
  const categories: string[] = [];
  let questionCount = 0;

  for (const line of lines) {
    // Extract title (first # heading)
    if (line.match(/^#\s+/) && title === "Untitled") {
      title = line.replace(/^#\s+/, "");
    }
    // Extract categories (## headings)
    else if (line.match(/^##\s+/)) {
      categories.push(line.replace(/^##\s+/, ""));
    }
    // Count questions (### headings or bullet points)
    else if (
      line.match(/^#{3,}\s+/) ||
      (line.startsWith("- ") && !line.startsWith("- Examples:"))
    ) {
      questionCount++;
    }
  }

  return { title, categories, questionCount };
};
