import { render, screen } from '@testing-library/react'
import { LoadingSkeleton } from '@/app/components/LoadingSkeleton'

// Mock the ui/skeleton component since it's external
jest.mock('@/components/ui/skeleton', () => ({
  Skeleton: ({
    className,
    ...props
  }: {
    className?: string
    [key: string]: any
  }) => <div data-testid="skeleton" className={className} {...props} />,
}))

// Mock the cn utility function
jest.mock('@/lib/utils', () => ({
  cn: (...classes: any[]) => classes.filter(Boolean).join(' '),
}))

describe('LoadingSkeleton Component', () => {
  it('renders loading text', () => {
    render(<LoadingSkeleton />)

    const loadingText = screen.getByText('Loading...')
    expect(loadingText).toBeInTheDocument()
    expect(loadingText).toHaveClass('text-lg', 'text-gray-600')
  })

  it('renders skeleton elements', () => {
    render(<LoadingSkeleton />)

    const skeletons = screen.getAllByTestId('skeleton')
    expect(skeletons).toHaveLength(3) // One main skeleton + two smaller ones
  })

  it('has proper container styling', () => {
    const { container } = render(<LoadingSkeleton />)

    const mainContainer = container.firstChild as HTMLElement
    expect(mainContainer).toHaveClass(
      'flex',
      'flex-col',
      'items-center',
      'justify-center',
      'min-h-screen',
      'space-y-3'
    )
  })

  it('renders main skeleton with correct classes', () => {
    render(<LoadingSkeleton />)

    const skeletons = screen.getAllByTestId('skeleton')
    const mainSkeleton = skeletons[0]
    expect(mainSkeleton).toHaveClass('h-[125px]', 'w-[250px]', 'rounded-xl')
  })

  it('renders smaller skeletons with correct classes', () => {
    render(<LoadingSkeleton />)

    const skeletons = screen.getAllByTestId('skeleton')
    expect(skeletons[1]).toHaveClass('h-4', 'w-[250px]')
    expect(skeletons[2]).toHaveClass('h-4', 'w-[200px]')
  })
})
