<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with subtle gradient -->
  <defs>
    <radialGradient id="bgGradient" cx="50%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#1e293b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0f172a;stop-opacity:1" />
    </radialGradient>
    <linearGradient id="nodeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#60a5fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Background circle -->
  <circle cx="64" cy="64" r="60" fill="url(#bgGradient)" stroke="#3b82f6" stroke-width="3"/>

  <!-- Main tree structure -->
  <!-- Root node (main thinking hub) -->
  <circle cx="64" cy="32" r="12" fill="url(#nodeGradient)" stroke="#60a5fa" stroke-width="2"/>
  <circle cx="64" cy="32" r="6" fill="#60a5fa" opacity="0.8"/>

  <!-- Primary connection lines -->
  <line x1="64" y1="44" x2="64" y2="60" stroke="#64748b" stroke-width="3" stroke-linecap="round"/>
  <line x1="64" y1="60" x2="40" y2="76" stroke="#64748b" stroke-width="3" stroke-linecap="round"/>
  <line x1="64" y1="60" x2="88" y2="76" stroke="#64748b" stroke-width="3" stroke-linecap="round"/>

  <!-- Secondary connection lines -->
  <line x1="40" y1="88" x2="40" y2="100" stroke="#64748b" stroke-width="2" stroke-linecap="round"/>
  <line x1="88" y1="88" x2="88" y2="100" stroke="#64748b" stroke-width="2" stroke-linecap="round"/>
  <line x1="40" y1="88" x2="28" y2="100" stroke="#64748b" stroke-width="2" stroke-linecap="round"/>
  <line x1="88" y1="88" x2="100" y2="100" stroke="#64748b" stroke-width="2" stroke-linecap="round"/>

  <!-- Secondary nodes (questions/categories) -->
  <circle cx="40" cy="76" r="8" fill="#10b981" stroke="#34d399" stroke-width="2"/>
  <circle cx="88" cy="76" r="8" fill="#10b981" stroke="#34d399" stroke-width="2"/>

  <!-- Tertiary nodes (insights/answers) -->
  <circle cx="40" cy="100" r="6" fill="#f59e0b" stroke="#fbbf24" stroke-width="1.5"/>
  <circle cx="88" cy="100" r="6" fill="#f59e0b" stroke="#fbbf24" stroke-width="1.5"/>
  <circle cx="28" cy="100" r="5" fill="#ef4444" stroke="#f87171" stroke-width="1.5"/>
  <circle cx="100" cy="100" r="5" fill="#ef4444" stroke="#f87171" stroke-width="1.5"/>

  <!-- Drag interaction indicator -->
  <g transform="translate(96, 16)" opacity="0.8">
    <path d="M4 8L12 8L12 4C12 3 13 2 14 2C15 2 16 3 16 4L16 12L16 16C16 17 15 18 14 18L6 18C5 18 4 17 4 16L4 8Z"
          fill="#94a3b8" stroke="#64748b" stroke-width="0.5"/>
    <circle cx="8" cy="12" r="1" fill="#1e293b"/>
  </g>

  <!-- AI thinking indicators (animated dots concept) -->
  <circle cx="56" cy="24" r="2" fill="#60a5fa" opacity="0.6"/>
  <circle cx="64" cy="20" r="2" fill="#60a5fa" opacity="0.9"/>
  <circle cx="72" cy="24" r="2" fill="#60a5fa" opacity="0.6"/>

  <!-- Subtle connection highlights -->
  <circle cx="64" cy="60" r="3" fill="#64748b" opacity="0.7"/>
  <circle cx="40" cy="88" r="2" fill="#64748b" opacity="0.5"/>
  <circle cx="88" cy="88" r="2" fill="#64748b" opacity="0.5"/>
</svg>