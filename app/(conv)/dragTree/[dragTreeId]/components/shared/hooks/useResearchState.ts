/**
 * Research State Management Hook
 *
 * Separates research state logic from UI components.
 * Handles:
 * - Content lifecycle management
 * - Search result processing
 * - Status tracking
 * - Auto-save functionality
 */

import { useState, useEffect, useMemo } from 'react'
import { DragTreeNodeContentStatus } from '@prisma/client'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
// import { getFormattedNodePath } from '@/app/(conv)/dragTree/[dragTreeId]/utils/findNodePath' // Currently unused
import { hasSearchResults } from '@/app/(conv)/dragTree/[dragTreeId]/components/SearchResultsDisplay'

export type ResearchState = {
  // Content state
  nodeContentMap: Map<string, any> | undefined
  workingContentId: string | null
  storedNodeContent: any | null
  status: DragTreeNodeContentStatus | null
  storeContent: string
  searchResults: any[] | null

  // UI state
  showContent: boolean
  isPersistent: boolean
  isEditable: boolean
  isSaving: boolean
  justSaved: boolean

  // Search state
  currentSearchQuery: string
  isSearching: boolean

  // Actions
  setShowContent: (show: boolean) => void
  setIsPersistent: (persistent: boolean) => void
  setIsEditable: (editable: boolean) => void
  setIsSaving: (saving: boolean) => void
  setJustSaved: (saved: boolean) => void
  setCurrentSearchQuery: (query: string) => void
  setIsSearching: (searching: boolean) => void
}

export type UseResearchStateProps = {
  nodeId: string
  questionText: string
  contentId?: string
}

/**
 * Hook to manage research-related state separated from UI concerns
 */
export const useResearchState = ({
  nodeId,
  questionText: _questionText, // Currently unused
  contentId,
}: UseResearchStateProps): ResearchState => {
  // Use selector to avoid subscribing to entire store
  const getNodeContent = useDragTreeStore(state => state.getNodeContent)
  // const frontendTreeStructure = useDragTreeStore().frontendTreeStructure // Currently unused

  // Derive the full question path for better context
  // Only recalculate when tree ID changes, not on every tree update
  // Currently unused but available for future enhancements
  // const _fullQuestionPath = useMemo(() => {
  //   return frontendTreeStructure
  //     ? getFormattedNodePath(frontendTreeStructure, nodeId)
  //     : questionText
  // }, [frontendTreeStructure?.id, nodeId, questionText])

  // Get content data from store
  const nodeContentMap = getNodeContent(nodeId)
  const workingContentId = useMemo(() => {
    return (
      contentId ||
      (nodeContentMap && nodeContentMap.size > 0
        ? Array.from(nodeContentMap.keys())[0]
        : null)
    )
  }, [contentId, nodeContentMap?.size]) // Only recalculate when map size changes

  const storedNodeContent = useMemo(() => {
    return workingContentId ? nodeContentMap?.get(workingContentId) : null
  }, [workingContentId, nodeContentMap?.size]) // Only recalculate when content ID or map size changes

  const status = storedNodeContent?.status || null
  const storeContent = storedNodeContent?.contentText || ''

  // Get search results from stored content metadata
  const searchResults = useMemo(() => {
    return storedNodeContent?.metadata &&
      hasSearchResults(storedNodeContent.metadata)
      ? storedNodeContent.metadata.searchResults
      : null
  }, [storedNodeContent?.metadata])

  // UI State
  const [showContent, setShowContent] = useState<boolean>(
    status === DragTreeNodeContentStatus.PROCESSING ||
      (status === DragTreeNodeContentStatus.ACTIVE &&
        storeContent.trim().length > 0)
  )
  const [isPersistent, setIsPersistent] = useState<boolean>(false)
  const [isEditable, setIsEditable] = useState<boolean>(false)
  const [isSaving, setIsSaving] = useState<boolean>(false)
  const [justSaved, setJustSaved] = useState<boolean>(false)

  // Search State
  const [currentSearchQuery, setCurrentSearchQuery] = useState<string>('')
  const [isSearching, setIsSearching] = useState<boolean>(false)

  // Auto-show content when processing starts
  useEffect(() => {
    if (status === DragTreeNodeContentStatus.PROCESSING) {
      setShowContent(true)
    }
  }, [status])

  // Reset search state when nodeId changes to prevent state leakage between nodes
  useEffect(() => {
    setCurrentSearchQuery('')
    setIsSearching(false)
  }, [nodeId])

  return {
    // Content state
    nodeContentMap,
    workingContentId,
    storedNodeContent,
    status,
    storeContent,
    searchResults,

    // UI state
    showContent,
    isPersistent,
    isEditable,
    isSaving,
    justSaved,

    // Search state
    currentSearchQuery,
    isSearching,

    // Actions
    setShowContent,
    setIsPersistent,
    setIsEditable,
    setIsSaving,
    setJustSaved,
    setCurrentSearchQuery,
    setIsSearching,
  }
}
