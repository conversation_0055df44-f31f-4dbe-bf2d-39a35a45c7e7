/**
 * @jest-environment node
 */
import {
  validateMessageRole,
  assertAiIdPrefix,
  validateConversationId,
  validateMessageId,
  validateStepId,
  validateAttachmentId,
  validateStepOrder,
  validateStepType,
  validateAttachmentData,
  validateUserId,
} from '../validation'
import { AiChatIdPrefix } from '@/lib/id'

describe('AI Chat Validation Utilities', () => {
  describe('validateMessageRole', () => {
    it('should validate correct message roles', () => {
      expect(validateMessageRole('user')).toBe('USER')
      expect(validateMessageRole('USER')).toBe('USER')
      expect(validateMessageRole('assistant')).toBe('ASSISTANT')
      expect(validateMessageRole('ASSISTANT')).toBe('ASSISTANT')
      expect(validateMessageRole('system')).toBe('SYSTEM')
      expect(validateMessageRole('SYSTEM')).toBe('SYSTEM')
    })

    it('should throw error for invalid roles', () => {
      expect(() => validateMessageRole('invalid')).toThrow(
        'Invalid message role: invalid'
      )
      expect(() => validateMessageRole('')).toThrow('Invalid message role: ')
      expect(() => validateMessageRole('admin')).toThrow(
        'Invalid message role: admin'
      )
    })
  })

  describe('assertAiIdPrefix', () => {
    it('should validate correct ID formats', () => {
      expect(() =>
        assertAiIdPrefix('thread_1234567890', AiChatIdPrefix.THREAD)
      ).not.toThrow()
      expect(() =>
        assertAiIdPrefix('msg_abcdefghij', AiChatIdPrefix.MESSAGE)
      ).not.toThrow()
      expect(() =>
        assertAiIdPrefix('step_xyz1234567', AiChatIdPrefix.STEP)
      ).not.toThrow()
      expect(() =>
        assertAiIdPrefix('file_test123456', AiChatIdPrefix.FILE)
      ).not.toThrow()
    })

    it('should throw error for invalid ID formats', () => {
      expect(() => assertAiIdPrefix('invalid', AiChatIdPrefix.THREAD)).toThrow(
        'Invalid ID format'
      )
      expect(() => assertAiIdPrefix('thread_', AiChatIdPrefix.THREAD)).toThrow(
        'ID suffix too short'
      )
      expect(() =>
        assertAiIdPrefix('thread_short', AiChatIdPrefix.THREAD)
      ).toThrow('ID suffix too short')
      expect(() =>
        assertAiIdPrefix('msg_1234567890', AiChatIdPrefix.THREAD)
      ).toThrow('Expected prefix: thread_')
      expect(() => assertAiIdPrefix('', AiChatIdPrefix.THREAD)).toThrow(
        'Invalid ID'
      )
    })

    it('should validate non-string IDs', () => {
      expect(() =>
        assertAiIdPrefix(null as any, AiChatIdPrefix.THREAD)
      ).toThrow('Invalid ID')
      expect(() =>
        assertAiIdPrefix(undefined as any, AiChatIdPrefix.THREAD)
      ).toThrow('Invalid ID')
      expect(() => assertAiIdPrefix(123 as any, AiChatIdPrefix.THREAD)).toThrow(
        'Invalid ID'
      )
    })
  })

  describe('ID validation functions', () => {
    it('should validate conversation IDs', () => {
      expect(() => validateConversationId('thread_1234567890')).not.toThrow()
      expect(() => validateConversationId('msg_1234567890')).toThrow(
        'Expected prefix: thread_'
      )
    })

    it('should validate message IDs', () => {
      expect(() => validateMessageId('msg_1234567890')).not.toThrow()
      expect(() => validateMessageId('thread_1234567890')).toThrow(
        'Expected prefix: msg_'
      )
    })

    it('should validate step IDs', () => {
      expect(() => validateStepId('step_1234567890')).not.toThrow()
      expect(() => validateStepId('msg_1234567890')).toThrow(
        'Expected prefix: step_'
      )
    })

    it('should validate attachment IDs', () => {
      expect(() => validateAttachmentId('file_1234567890')).not.toThrow()
      expect(() => validateAttachmentId('msg_1234567890')).toThrow(
        'Expected prefix: file_'
      )
    })
  })

  describe('validateStepOrder', () => {
    it('should validate correct step orders', () => {
      expect(() => validateStepOrder(0)).not.toThrow()
      expect(() => validateStepOrder(1)).not.toThrow()
      expect(() => validateStepOrder(10)).not.toThrow()
      expect(() => validateStepOrder(999)).not.toThrow()
    })

    it('should throw error for invalid step orders', () => {
      expect(() => validateStepOrder(-1)).toThrow('Invalid step order: -1')
      expect(() => validateStepOrder(1.5)).toThrow('Invalid step order: 1.5')
      expect(() => validateStepOrder(NaN)).toThrow('Invalid step order: NaN')
      expect(() => validateStepOrder(Infinity)).toThrow(
        'Invalid step order: Infinity'
      )
    })
  })

  describe('validateStepType', () => {
    it('should validate correct step types', () => {
      expect(validateStepType('THOUGHT')).toBe('THOUGHT')
      expect(validateStepType('TOOL_CALL')).toBe('TOOL_CALL')
      expect(validateStepType('TOOL_RESULT')).toBe('TOOL_RESULT')
      expect(validateStepType('REASONING_SUMMARY')).toBe('REASONING_SUMMARY')
      expect(validateStepType('SUB_AGENT_INVOCATION')).toBe(
        'SUB_AGENT_INVOCATION'
      )
    })

    it('should throw error for invalid step types', () => {
      expect(() => validateStepType('INVALID')).toThrow(
        'Invalid step type: INVALID'
      )
      expect(() => validateStepType('')).toThrow('Invalid step type: ')
      expect(() => validateStepType('thought')).toThrow(
        'Invalid step type: thought'
      )
    })
  })

  describe('validateAttachmentData', () => {
    const validAttachment = {
      fileName: 'test.txt',
      fileType: 'text/plain',
      fileSize: 1024,
      url: 'https://example.com/test.txt',
    }

    it('should validate correct attachment data', () => {
      expect(() => validateAttachmentData(validAttachment)).not.toThrow()

      expect(() =>
        validateAttachmentData({
          fileName: 'image.png',
          fileType: 'image/png',
          fileSize: 2048,
          url: 'https://cdn.example.com/image.png',
        })
      ).not.toThrow()
    })

    it('should throw error for invalid fileName', () => {
      expect(() =>
        validateAttachmentData({
          ...validAttachment,
          fileName: '',
        })
      ).toThrow('Invalid fileName')

      expect(() =>
        validateAttachmentData({
          ...validAttachment,
          fileName: null as any,
        })
      ).toThrow('Invalid fileName')
    })

    it('should throw error for invalid fileType', () => {
      expect(() =>
        validateAttachmentData({
          ...validAttachment,
          fileType: '',
        })
      ).toThrow('Invalid fileType')

      expect(() =>
        validateAttachmentData({
          ...validAttachment,
          fileType: 123 as any,
        })
      ).toThrow('Invalid fileType')
    })

    it('should throw error for invalid fileSize', () => {
      expect(() =>
        validateAttachmentData({
          ...validAttachment,
          fileSize: 0,
        })
      ).toThrow('Invalid fileSize')

      expect(() =>
        validateAttachmentData({
          ...validAttachment,
          fileSize: -1,
        })
      ).toThrow('Invalid fileSize')

      expect(() =>
        validateAttachmentData({
          ...validAttachment,
          fileSize: 1.5,
        })
      ).toThrow('Invalid fileSize')
    })

    it('should throw error for invalid URL', () => {
      expect(() =>
        validateAttachmentData({
          ...validAttachment,
          url: '',
        })
      ).toThrow('Invalid url')

      expect(() =>
        validateAttachmentData({
          ...validAttachment,
          url: 'invalid-url',
        })
      ).toThrow('Invalid url format')

      expect(() =>
        validateAttachmentData({
          ...validAttachment,
          url: 'ftp://example.com/file.txt',
        })
      ).not.toThrow() // URL constructor accepts ftp
    })
  })

  describe('validateUserId', () => {
    it('should validate correct user IDs', () => {
      expect(() => validateUserId('c12345678901234567890abcd')).not.toThrow()
      expect(() => validateUserId('cAbCdEfGhIjKlMnOpQrStUvWx')).not.toThrow()
    })

    it('should throw error for invalid user IDs', () => {
      expect(() => validateUserId('')).toThrow('Invalid userId')
      expect(() => validateUserId('invalid')).toThrow('Invalid userId format')
      expect(() => validateUserId('d12345678901234567890abcd')).toThrow(
        'Invalid userId format'
      )
      expect(() => validateUserId('c123456789012345678901234567890')).toThrow(
        'Invalid userId format'
      )
      expect(() => validateUserId('c1234567890123456789')).toThrow(
        'Invalid userId format'
      )
      expect(() => validateUserId(null as any)).toThrow('Invalid userId')
      expect(() => validateUserId(undefined as any)).toThrow('Invalid userId')
    })
  })
})
