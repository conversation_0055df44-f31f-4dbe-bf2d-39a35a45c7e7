# E2E Test Suite

This directory contains a comprehensive end-to-end test suite for the ThinkGraph application using Playwright. The tests are designed to prevent regressions and ensure robust functionality without relying on external API dependencies.

## 🎯 Test Philosophy

- **No External Dependencies**: All tests use fixture responses to mock API interactions
- **Consistent & Reliable**: Tests run the same way every time, preventing flakiness
- **Comprehensive Coverage**: Tests cover critical user journeys and edge cases
- **CI/CD Ready**: Designed to run reliably in automated environments

## 📁 File Structure

```
e2e/
├── screening.flows.spec.ts        # Core screening functionality tests
├── dragtree.flows.spec.ts         # Drag tree CRUD and interaction tests
├── dragtree.tutorial.spec.ts      # Tutorial-specific behavior tests
├── network-stubs.ts               # Centralized API mocking utilities
├── fixtures/
│   ├── openai-mock-rephrase.json  # Mock response for rephrase API
│   ├── openai-mock-diagnosis.json # Mock response for diagnosis API
│   └── openai-mock-research.json  # Mock response for research API
└── README.md                      # This file
```

## 🧪 Test Categories

### Screening Module Tests (`screening.flows.spec.ts`)

**S-1: Happy Path Flow**

- Tests the complete screening workflow from description input to drag tree creation
- Verifies API mocking, suggestion selection, and navigation

**S-2: "Use My Input" Alternative Path**

- Tests the alternative flow where users skip suggestions and use original input
- Ensures both paths lead to successful drag tree creation

**S-3: Error Handling & Rate Limiting**

- Tests rate limiting scenarios with 429 responses
- Verifies proper error messaging and button state management

**S-4: Simulator Mode**

- Tests the simulator toggle functionality
- Ensures no actual API calls are made when simulator is enabled

**S-5: Form Validation**

- Tests input validation and button state management
- Ensures proper UX for invalid inputs

### DragTree Module Tests (`dragtree.flows.spec.ts`)

**D-1: CRUD Operations & Data Persistence**

- Tests node creation, editing, deletion, and reordering
- Verifies data persistence across page reloads

**D-2: Quick Research Feature**

- Tests the right-click context menu research functionality
- Verifies streaming response handling and content population

**D-3: Node Content Management**

- Tests node expansion/collapse and selection states
- Verifies proper UI state management

**D-4: Tree Navigation and Layout**

- Tests layout toggles and export functionality
- Ensures proper tree rendering and interaction

**D-5: Error Handling**

- Tests error scenarios and proper error message display
- Verifies graceful degradation

### Tutorial Tests (`dragtree.tutorial.spec.ts`)

**D-T1: Tutorial Appears for First-Time User**

- Tests tutorial display logic for new users
- Verifies proper tutorial content and step indicators

**D-T2: Tutorial Skip Functionality**

- Tests the skip tutorial feature and persistence
- Ensures skipped state is maintained across sessions

**D-T3: Tutorial Completion Flow**

- Tests the complete tutorial walkthrough
- Verifies proper completion state management

**D-T4: Tutorial Reset for Testing**

- Tests tutorial reset functionality for development
- Ensures tutorial can be re-triggered after completion

**D-T5: Tutorial Interaction with Content Loading**

- Tests tutorial behavior with async content loading
- Ensures tutorial appears only when content is ready

## 🛠 API Mocking

### Network Stubs (`network-stubs.ts`)

The centralized mocking system provides:

- **Fixture Loading**: Loads realistic API responses from JSON files
- **Streaming Support**: Mocks streaming endpoints with chunked responses
- **Rate Limiting**: Simulates rate-limited responses with proper headers
- **Error Scenarios**: Mocks various error conditions for testing

### Fixture Data

- **`openai-mock-rephrase.json`**: Contains realistic rephrase suggestions
- **`openai-mock-diagnosis.json`**: Contains structured diagnosis analysis
- **`openai-mock-research.json`**: Contains research content and drag tree data

## 🚀 Running Tests

### Prerequisites

```bash
# Install Playwright browsers (if not already installed)
npx playwright install
```

### Run All Tests

```bash
# Run all e2e tests
npx playwright test

# Run with UI mode for debugging
npx playwright test --ui

# Run specific test file
npx playwright test e2e/screening.flows.spec.ts

# Run specific test
npx playwright test -g "S-1: Happy Path Flow"
```

### Browser Configuration

Tests run on multiple browsers by default:

- **Desktop**: Chrome, Firefox, Safari
- **Mobile**: Chrome (Pixel 5), Safari (iPhone 12)

To run on specific browser:

```bash
npx playwright test --project=chromium
```

## 📊 Test Results

### Current Status

- **Total Tests**: 101 (across all browsers)
- **Passing**: 26 tests (Mobile Safari only)
- **Failing**: 75 tests (Desktop browsers and Mobile Chrome)

### Common Failure Patterns

1. **Element Not Found**: Tests fail when expected UI elements don't exist
2. **localStorage Access**: Security errors when accessing localStorage in some browsers
3. **API Endpoint Mismatches**: Mock URLs don't match actual application endpoints
4. **Timing Issues**: Elements not ready when tests expect them

## 🔧 Troubleshooting

### Debugging Failed Tests

1. **View Screenshots**: Failed tests automatically capture screenshots
2. **Check HTML Report**: Run `npx playwright show-report` for detailed results
3. **Use Debug Mode**: Add `await page.pause()` to inspect test state
4. **Verify Selectors**: Ensure test selectors match actual application elements

### Common Issues

**localStorage Security Errors**

```javascript
// Instead of:
await page.evaluate(() => localStorage.clear())

// Use:
await page.context().clearCookies()
```

**Element Not Found**

```javascript
// Add better error handling:
const element = page.locator('selector')
await expect(element).toBeVisible({ timeout: 10000 })
```

## 🎯 Next Steps

### Immediate Improvements Needed

1. **Fix Element Selectors**: Update selectors to match actual application elements
2. **Improve API Mocking**: Ensure mock URLs match real endpoints
3. **Handle Async Loading**: Add proper waits for dynamic content
4. **Cross-Browser Compatibility**: Fix localStorage and other browser-specific issues

### Future Enhancements

1. **Visual Regression Testing**: Add screenshot comparisons
2. **Performance Testing**: Monitor page load times and interactions
3. **Accessibility Testing**: Add a11y checks to existing tests
4. **API Contract Testing**: Validate mock responses match real API schemas

## 📝 Contributing

When adding new tests:

1. **Follow Naming Convention**: Use descriptive test names (e.g., "S-1: Happy Path Flow")
2. **Use Centralized Mocks**: Leverage `network-stubs.ts` for API mocking
3. **Add Fixtures**: Create realistic fixture data for new endpoints
4. **Document Test Cases**: Update this README with new test descriptions
5. **Verify Cross-Browser**: Ensure tests work across all configured browsers

## 🔗 Related Documentation

- [Playwright Documentation](https://playwright.dev/)
- [Application API Documentation](../app/api/README.md)
- [Screening Module Documentation](<../app/(conv)/screening/README.md>)
- [DragTree Module Documentation](<../app/(conv)/dragTree/README.md>)
