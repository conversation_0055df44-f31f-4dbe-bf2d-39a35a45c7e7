'use client'

import { create } from 'zustand'
import { persist } from 'zustand/middleware'

// Types for batch research state management
export type BatchResearchItem = {
  nodeId: string
  questionText: string
  displayText: string
  categoryPath: string[]
  categoryId: string
  hasExistingResearch: boolean
  status: 'pending' | 'processing' | 'completed' | 'failed'
  error?: string
}

export type BatchResearchState = {
  // Persistent selections per dragTreeId
  selections: Record<string, Set<string>>
  expandedNodes: Record<string, Set<string>>
  coachModeOpen: Record<string, boolean>
  currentQuestionId: Record<string, string | null>

  // Actions
  toggleSelection: (dragTreeId: string, nodeId: string) => void
  setSelections: (dragTreeId: string, selections: Set<string>) => void
  clearSelections: (dragTreeId: string) => void
  selectAll: (dragTreeId: string, nodeIds: string[]) => void

  // Tree state actions
  toggleExpanded: (dragTreeId: string, nodeId: string) => void
  setExpandedNodes: (dragTreeId: string, expanded: Set<string>) => void

  // Coach mode actions
  setCoachModeOpen: (dragTreeId: string, isOpen: boolean) => void
  setCurrentQuestionId: (dragTreeId: string, questionId: string | null) => void

  // Utility functions
  getSelections: (dragTreeId: string) => Set<string>
  getExpandedNodes: (dragTreeId: string) => Set<string>
  isCoachModeOpen: (dragTreeId: string) => boolean
  getCurrentQuestionId: (dragTreeId: string) => string | null
}

export const useBatchResearchStore = create<BatchResearchState>()(
  persist(
    (set, get) => ({
      // Initial state
      selections: {},
      expandedNodes: {},
      coachModeOpen: {},
      currentQuestionId: {},

      // Selection actions
      toggleSelection: (dragTreeId: string, nodeId: string) =>
        set(state => {
          const currentSelections = state.selections[dragTreeId] || new Set()
          const newSelections = new Set(currentSelections)

          if (newSelections.has(nodeId)) {
            newSelections.delete(nodeId)
          } else {
            newSelections.add(nodeId)
          }

          return {
            selections: {
              ...state.selections,
              [dragTreeId]: newSelections,
            },
          }
        }),

      setSelections: (dragTreeId: string, selections: Set<string>) =>
        set(state => ({
          selections: {
            ...state.selections,
            [dragTreeId]: new Set(selections),
          },
        })),

      clearSelections: (dragTreeId: string) =>
        set(state => ({
          selections: {
            ...state.selections,
            [dragTreeId]: new Set(),
          },
        })),

      selectAll: (dragTreeId: string, nodeIds: string[]) =>
        set(state => ({
          selections: {
            ...state.selections,
            [dragTreeId]: new Set(nodeIds),
          },
        })),

      // Tree state actions
      toggleExpanded: (dragTreeId: string, nodeId: string) =>
        set(state => {
          const currentExpanded = state.expandedNodes[dragTreeId] || new Set()
          const newExpanded = new Set(currentExpanded)

          if (newExpanded.has(nodeId)) {
            newExpanded.delete(nodeId)
          } else {
            newExpanded.add(nodeId)
          }

          return {
            expandedNodes: {
              ...state.expandedNodes,
              [dragTreeId]: newExpanded,
            },
          }
        }),

      setExpandedNodes: (dragTreeId: string, expanded: Set<string>) =>
        set(state => ({
          expandedNodes: {
            ...state.expandedNodes,
            [dragTreeId]: new Set(expanded),
          },
        })),

      // Coach mode actions
      setCoachModeOpen: (dragTreeId: string, isOpen: boolean) =>
        set(state => ({
          coachModeOpen: {
            ...state.coachModeOpen,
            [dragTreeId]: isOpen,
          },
        })),

      setCurrentQuestionId: (dragTreeId: string, questionId: string | null) =>
        set(state => ({
          currentQuestionId: {
            ...state.currentQuestionId,
            [dragTreeId]: questionId,
          },
        })),

      // Utility functions
      getSelections: (dragTreeId: string) => {
        const state = get()
        return state.selections[dragTreeId] || new Set()
      },

      getExpandedNodes: (dragTreeId: string) => {
        const state = get()
        return state.expandedNodes[dragTreeId] || new Set()
      },

      isCoachModeOpen: (dragTreeId: string) => {
        const state = get()
        return state.coachModeOpen[dragTreeId] || false
      },

      getCurrentQuestionId: (dragTreeId: string) => {
        const state = get()
        return state.currentQuestionId[dragTreeId] || null
      },
    }),
    {
      name: 'batch-research-storage',
      // Custom serialization to handle Set objects
      serialize: state => {
        return JSON.stringify({
          ...state.state,
          selections: Object.fromEntries(
            Object.entries(state.state.selections).map(([key, value]) => [
              key,
              Array.from(value),
            ])
          ),
          expandedNodes: Object.fromEntries(
            Object.entries(state.state.expandedNodes).map(([key, value]) => [
              key,
              Array.from(value),
            ])
          ),
        })
      },
      deserialize: str => {
        const parsed = JSON.parse(str)
        return {
          state: {
            ...parsed,
            selections: Object.fromEntries(
              Object.entries(parsed.selections || {}).map(([key, value]) => [
                key,
                new Set(value as string[]),
              ])
            ),
            expandedNodes: Object.fromEntries(
              Object.entries(parsed.expandedNodes || {}).map(([key, value]) => [
                key,
                new Set(value as string[]),
              ])
            ),
            currentQuestionId: parsed.currentQuestionId || {},
          },
        }
      },
    }
  )
)
