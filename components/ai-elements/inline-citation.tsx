'use client'

import { Badge } from '@/components/ui/badge'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  type CarouselApi,
} from '@/components/ui/carousel'
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from '@/components/ui/hover-card'
import { cn } from '@/lib/utils'
import { ArrowLeftIcon, ArrowRightIcon } from 'lucide-react'
import {
  type ComponentProps,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react'

export type InlineCitationProps = ComponentProps<'span'>

export const InlineCitation = ({
  className,
  ...props
}: InlineCitationProps) => (
  <span className={cn('inline-block group', className)} {...props} />
)

export type InlineCitationTextProps = ComponentProps<'span'>

export const InlineCitationText = ({
  className,
  ...props
}: InlineCitationTextProps) => (
  <span
    className={cn('transition-colors group-hover:bg-accent', className)}
    {...props}
  />
)

export type InlineCitationCardProps = ComponentProps<'div'>

export const InlineCitationCard = ({
  className,
  ...props
}: InlineCitationCardProps) => (
  <HoverCard>
    <div className={cn('inline-block', className)} {...props} />
  </HoverCard>
)

export type InlineCitationCardTriggerProps = ComponentProps<'div'> & {
  sources: string[]
}

export const InlineCitationCardTrigger = ({
  sources,
  className,
  children,
  ...props
}: InlineCitationCardTriggerProps) => (
  <HoverCardTrigger asChild>
    <Badge
      variant="secondary"
      className={cn(
        'cursor-pointer text-xs px-1.5 py-0.5 h-auto font-normal',
        className
      )}
      {...props}
    >
      {children}
    </Badge>
  </HoverCardTrigger>
)

export type InlineCitationCardBodyProps = ComponentProps<'div'>

export const InlineCitationCardBody = ({
  className,
  ...props
}: InlineCitationCardBodyProps) => (
  <HoverCardContent className={cn('w-80 p-0', className)} {...props} />
)

const InlineCitationCarouselContext = createContext<{
  api: CarouselApi | undefined
  current: number
  count: number
}>({
  api: undefined,
  current: 0,
  count: 0,
})

export type InlineCitationCarouselProps = ComponentProps<'div'>

export const InlineCitationCarousel = ({
  className,
  ...props
}: InlineCitationCarouselProps) => {
  const [api, setApi] = useState<CarouselApi>()
  const [current, setCurrent] = useState(0)
  const [count, setCount] = useState(0)

  useEffect(() => {
    if (!api) return

    setCount(api.scrollSnapList().length)
    setCurrent(api.selectedScrollSnap() + 1)

    api.on('select', () => {
      setCurrent(api.selectedScrollSnap() + 1)
    })
  }, [api])

  return (
    <InlineCitationCarouselContext.Provider value={{ api, current, count }}>
      <Carousel
        setApi={setApi}
        className={cn('w-full', className)}
        {...props}
      />
    </InlineCitationCarouselContext.Provider>
  )
}

export type InlineCitationCarouselContentProps = ComponentProps<'div'>

export const InlineCitationCarouselContent = ({
  className,
  ...props
}: InlineCitationCarouselContentProps) => (
  <CarouselContent className={cn('', className)} {...props} />
)

export type InlineCitationCarouselItemProps = ComponentProps<'div'>

export const InlineCitationCarouselItem = ({
  className,
  ...props
}: InlineCitationCarouselItemProps) => (
  <CarouselItem className={cn('', className)} {...props} />
)

export type InlineCitationCarouselHeaderProps = ComponentProps<'div'>

export const InlineCitationCarouselHeader = ({
  className,
  ...props
}: InlineCitationCarouselHeaderProps) => (
  <div
    className={cn('flex items-center justify-between p-4 border-b', className)}
    {...props}
  />
)

export type InlineCitationCarouselIndexProps = ComponentProps<'div'>

export const InlineCitationCarouselIndex = ({
  children,
  className,
  ...props
}: InlineCitationCarouselIndexProps) => {
  const { current, count } = useContext(InlineCitationCarouselContext)

  return (
    <div className={cn('text-sm text-muted-foreground', className)} {...props}>
      {children || `${current} of ${count}`}
    </div>
  )
}

export type InlineCitationCarouselPrevProps = ComponentProps<'button'>

export const InlineCitationCarouselPrev = ({
  className,
  ...props
}: InlineCitationCarouselPrevProps) => {
  const { api } = useContext(InlineCitationCarouselContext)

  const scrollPrev = useCallback(() => {
    api?.scrollPrev()
  }, [api])

  return (
    <button
      onClick={scrollPrev}
      className={cn(
        'inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-8 w-8',
        className
      )}
      {...props}
    >
      <ArrowLeftIcon className="h-4 w-4" />
    </button>
  )
}

export type InlineCitationCarouselNextProps = ComponentProps<'button'>

export const InlineCitationCarouselNext = ({
  className,
  ...props
}: InlineCitationCarouselNextProps) => {
  const { api } = useContext(InlineCitationCarouselContext)

  const scrollNext = useCallback(() => {
    api?.scrollNext()
  }, [api])

  return (
    <button
      onClick={scrollNext}
      className={cn(
        'inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-8 w-8',
        className
      )}
      {...props}
    >
      <ArrowRightIcon className="h-4 w-4" />
    </button>
  )
}

export type InlineCitationSourceProps = ComponentProps<'div'> & {
  title?: string
  url?: string
  description?: string
}

export const InlineCitationSource = ({
  title,
  url,
  description,
  className,
  children,
  ...props
}: InlineCitationSourceProps) => (
  <div className={cn('p-4', className)} {...props}>
    {title && (
      <h4 className="font-semibold text-sm mb-1 line-clamp-2">{title}</h4>
    )}
    {url && (
      <p className="text-xs text-muted-foreground mb-2 truncate">{url}</p>
    )}
    {description && (
      <p className="text-sm text-muted-foreground line-clamp-3">
        {description}
      </p>
    )}
    {children}
  </div>
)

export type InlineCitationQuoteProps = ComponentProps<'blockquote'>

export const InlineCitationQuote = ({
  children,
  className,
  ...props
}: InlineCitationQuoteProps) => (
  <blockquote
    className={cn(
      'border-l-4 border-muted pl-4 py-2 mt-2 text-sm italic text-muted-foreground bg-muted/30',
      className
    )}
    {...props}
  >
    {children}
  </blockquote>
)
