'use client'

import React from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { AlertTriangle } from 'lucide-react'
import {
  getLanguageName,
  type SupportedLanguageCode,
} from '@/app/constants/languages'

type LanguageChangeConfirmDialogProps = {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  newLanguage: SupportedLanguageCode
  isLoading?: boolean
}

/**
 * Language Change Confirmation Dialog
 *
 * Shows a warning dialog when users attempt to change their language preference,
 * clearly stating that changes will only apply to future AI generations, not existing content.
 */
const LanguageChangeConfirmDialog: React.FC<
  LanguageChangeConfirmDialogProps
> = ({ isOpen, onClose, onConfirm, newLanguage, isLoading = false }) => {
  const languageName = getLanguageName(newLanguage)

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <AlertTriangle className="h-6 w-6 text-amber-500" />
            </div>
            <div>
              <DialogTitle className="text-lg font-semibold text-gray-900">
                Change Language Preference
              </DialogTitle>
            </div>
          </div>
          <DialogDescription className="text-sm text-gray-600 mt-3">
            You are about to change the language preference for{' '}
            <span className="font-medium text-gray-900">
              this drag tree only
            </span>{' '}
            to <span className="font-medium text-gray-900">{languageName}</span>
            . This will not affect other drag trees or the entire application.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-5 w-5 text-amber-400" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-amber-800">
                  Important Notice
                </h3>
                <div className="mt-2 text-sm text-amber-700">
                  <p>
                    This change will{' '}
                    <strong>
                      only apply to future AI generations in this drag tree
                    </strong>
                    . Existing content will remain unchanged, and other drag
                    trees will maintain their own language preferences.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="flex space-x-2">
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button
            onClick={onConfirm}
            disabled={isLoading}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            {isLoading ? 'Updating...' : 'Confirm Change'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default LanguageChangeConfirmDialog
