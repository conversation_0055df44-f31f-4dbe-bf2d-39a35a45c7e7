/**
 * Research Action Handlers Hook
 *
 * Separates research action logic from UI components.
 * Handles:
 * - Research option execution
 * - External URL generation
 * - Error handling and validation
 * - Store updates
 */

import { useCallback, useMemo } from 'react'
import { toast } from 'react-hot-toast'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { NodeContentType } from '@/app/(conv)/dragTree/[dragTreeId]/constants/node-types'
import { DragTreeNodeContentStatus } from '@prisma/client'
// getFormattedNodePath is now available via the store's getNodePath method
import { createExternalToolPrompt } from '@/app/api/dragtree/shared/research-prompts'
import { type ResearchOptionConfig } from '@/app/(conv)/dragTree/constants'
import {
  getLanguageName,
  type SupportedLanguageCode,
} from '@/app/(conv)/screening/constants/languages'

export type ResearchActionHandlers = {
  executeInternalResearch: (questionText: string) => Promise<void>
  executeExternalResearch: (
    option: ResearchOptionConfig,
    questionText: string
  ) => void
  validateResearchAction: (option: ResearchOptionConfig) => {
    canExecute: boolean
    reason?: string
  }
  getFullQuestionPath: () => string
}

export type UseResearchActionsProps = {
  nodeId: string
  questionText: string
  hasExistingContent: boolean
  isStreaming: boolean
}

/**
 * Hook to handle research actions separated from UI components
 */
export const useResearchActions = ({
  nodeId,
  questionText,
  hasExistingContent,
  isStreaming,
}: UseResearchActionsProps): ResearchActionHandlers => {
  // Optimized: Use individual selectors to prevent unnecessary re-renders
  const screeningQuestion = useDragTreeStore(state => state.screeningQuestion)
  const markNodeAsInterested = useDragTreeStore(
    state => state.markNodeAsInterested
  )
  const addNodeContent = useDragTreeStore(state => state.addNodeContent)
  const getNodePath = useDragTreeStore(state => state.getNodePath)
  const preferredLanguage = useDragTreeStore(state => state.preferredLanguage)

  const getFullQuestionPath = useCallback(() => {
    try {
      const fullPath = getNodePath(nodeId)
      if (
        fullPath &&
        fullPath !== 'Unknown Node' &&
        fullPath !== questionText
      ) {
        return fullPath
      }
    } catch (error) {
      console.warn('Error getting node path:', error)
    }
    return questionText
  }, [getNodePath, nodeId, questionText])

  const validateResearchAction = useCallback(
    (option: ResearchOptionConfig) => {
      // Prevent Clarify research if content already exists
      if (option.id === 'clarify' && hasExistingContent) {
        return {
          canExecute: false,
          reason:
            'Research already exists for this question. Please view the existing research below.',
        }
      }

      // Prevent Clarify research if already streaming
      if (option.id === 'clarify' && isStreaming) {
        return {
          canExecute: false,
          reason: 'Research is already in progress.',
        }
      }

      return { canExecute: true }
    },
    [hasExistingContent, isStreaming]
  )

  const executeInternalResearch = useCallback(
    async (displayText: string) => {
      try {
        // Mark the node as interested when user performs research action
        markNodeAsInterested(nodeId)

        // Create the research content record
        const createPayload = {
          dragTreeNodeId: nodeId,
          questionText: displayText,
          researchType: 'QUICK_RESEARCH',
        }

        const createResponse = await fetch('/api/dragtree/research_create', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(createPayload),
        })

        if (!createResponse.ok) {
          throw new Error('Failed to create research content record')
        }

        const createResult = await createResponse.json()
        const contentId = createResult.data.contentId

        // Add to store to trigger ResearchDisplay
        addNodeContent(nodeId, {
          contentId,
          contentType: NodeContentType.QUICK_RESEARCH,
          contentVersion: 'v1',
          status: DragTreeNodeContentStatus.INITIALIZED,
          contentText: '',
          metadata: {
            questionText: displayText,
            originalQuestion: displayText,
          },
        })

        toast.success('Research started...')
      } catch (error) {
        console.error('Error starting research:', error)
        toast.error('Failed to start research')
        throw error
      }
    },
    [nodeId, markNodeAsInterested, addNodeContent]
  )

  const executeExternalResearch = useCallback(
    (option: ResearchOptionConfig, displayText: string) => {
      // Mark the node as interested when user performs any research action
      markNodeAsInterested(nodeId)

      const language = preferredLanguage
        ? getLanguageName(preferredLanguage as SupportedLanguageCode)
        : 'English'

      // External sites - simplified prompt with language directive
      const formattedPrompt = createExternalToolPrompt(
        displayText,
        screeningQuestion,
        language
      )

      const url = option.urlPattern.replace(
        '{query}',
        encodeURIComponent(formattedPrompt)
      )

      window.open(url, '_blank', 'noopener,noreferrer')
      toast.success(option.successMessage)
    },
    [nodeId, markNodeAsInterested, screeningQuestion, preferredLanguage]
  )

  // Memoize the return object to prevent unnecessary re-renders
  return useMemo(
    () => ({
      executeInternalResearch,
      executeExternalResearch,
      validateResearchAction,
      getFullQuestionPath,
    }),
    [
      executeInternalResearch,
      executeExternalResearch,
      validateResearchAction,
      getFullQuestionPath,
    ]
  )
}
