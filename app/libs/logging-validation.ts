/**
 * Logging Validation and Completeness Checking System
 *
 * This module provides:
 * 1. Runtime property validation for logging calls
 * 2. TypeScript-based completeness checking for event implementations
 * 3. Development-time warnings for missing optional properties
 */

import { EventName, getEventMetadata } from '@/app/configs/logging-config'
import type { EventProperties, LoggingContext } from '@/app/libs/logging'

// ============================================================================
// RUNTIME PROPERTY VALIDATION
// ============================================================================

/**
 * Validates that all expected properties are present in logging calls
 * Shows warnings in development when optional properties are missing
 */
export function validateEventProperties(
  eventName: EventName,
  properties: EventProperties = {},
  context: LoggingContext = {}
): {
  isValid: boolean
  warnings: string[]
  errors: string[]
} {
  const metadata = getEventMetadata(eventName)
  const warnings: string[] = []
  const errors: string[] = []

  // Check required context properties
  if (metadata.requiresUserId && !context.userId) {
    errors.push(`Event '${eventName}' requires userId but none provided`)
  }

  if (metadata.requiresDragTreeId && !context.dragTreeId) {
    errors.push(`Event '${eventName}' requires dragTreeId but none provided`)
  }

  // Check optional properties - warn if missing in development
  if (process.env.NODE_ENV === 'development' && metadata.optionalProperties) {
    const missingOptionalProps = metadata.optionalProperties.filter(
      prop => !(prop in properties)
    )

    if (missingOptionalProps.length > 0) {
      warnings.push(
        `Event '${eventName}' is missing optional properties: ${missingOptionalProps.join(', ')}`
      )
    }
  }

  return {
    isValid: errors.length === 0,
    warnings,
    errors,
  }
}

/**
 * Enhanced logging function with validation
 */
export function logEventWithValidation(
  eventName: EventName,
  properties: EventProperties = {},
  context: LoggingContext = {}
): void {
  const validation = validateEventProperties(eventName, properties, context)

  // Log errors and warnings in development
  if (process.env.NODE_ENV === 'development') {
    validation.errors.forEach(error =>
      console.error(`[Logging Validation] ${error}`)
    )
    validation.warnings.forEach(warning =>
      console.warn(`[Logging Validation] ${warning}`)
    )
  }

  // Still proceed with logging even if validation fails (non-blocking)
  // The actual logging will be handled by the main logging system
}

// ============================================================================
// TYPESCRIPT-BASED COMPLETENESS CHECKING
// ============================================================================

/**
 * Type-level validation to ensure all events are implemented
 * This creates a compile-time check for event completeness
 */
export type EventImplementationMap = {
  [K in EventName]: {
    eventName: K
    isImplemented: boolean
    implementationLocation?: string
    lastChecked?: string
  }
}

/**
 * Note: Event implementation tracking is now handled by the simple test
 * in __tests__/libs/logging-presence-simple.test.ts which scans the codebase
 * directly rather than maintaining a manual registry.
 */
