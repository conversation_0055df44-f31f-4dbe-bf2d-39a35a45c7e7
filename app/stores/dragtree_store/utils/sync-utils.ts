// Debounced database sync - prevents overwhelming the database
let dbSyncTimeout: NodeJS.Timeout | null = null

/**
 * Debounce database operations to prevent overwhelming the database
 * @param operation - The async operation to execute
 * @param delay - Delay in milliseconds (default: 800ms)
 */
export const debouncedDbSync = (
  operation: () => Promise<void>,
  delay: number = 800
): void => {
  if (dbSyncTimeout) {
    clearTimeout(dbSyncTimeout)
  }

  dbSyncTimeout = setTimeout(async () => {
    try {
      await operation()
    } catch (error) {
      console.error('💥 [SyncUtils] Database sync error:', error)
    }
  }, delay)
}

/**
 * Execute multiple database operations atomically
 * @param operations - Array of async operations to execute
 * @returns Results of all operations
 */
export async function executeAtomicOperations(
  operations: Array<() => Promise<any>>
): Promise<any[]> {
  try {
    const results = await Promise.all(operations.map(op => op()))
    return results
  } catch (error) {
    console.error('💥 [SyncUtils] Atomic operation failed:', error)
    throw error
  }
}

/**
 * Handle pending operations tracking
 */
export class PendingOperationsTracker {
  private operations = new Set<string>()

  add(operationKey: string): void {
    this.operations.add(operationKey)
  }

  remove(operationKey: string): void {
    this.operations.delete(operationKey)
  }

  has(operationKey: string): boolean {
    return this.operations.has(operationKey)
  }

  getAll(): Set<string> {
    return new Set(this.operations)
  }

  clear(): void {
    this.operations.clear()
  }
}
