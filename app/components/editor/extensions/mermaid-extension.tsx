"use client";

import { mergeAttributes, Node } from "@tiptap/core";
import { ReactNodeViewRenderer } from "@tiptap/react";
import TiptapMermaidComponent from "@/app/components/editor/components/tiptapMermaidComponent";

export default Node.create({
  name: "mermaidComponent",

  group: "block",

  content: "block*",

  code: true,
  atom: true,
  isolating: true,

  addAttributes() {
    return {
      code: {
        parseHTML: (element) => element.getAttribute("data-code"),
        renderHTML: (attributes) => ({
          "data-code": attributes.code,
        }),
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: "mermaid-component",
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return ["mermaid-component", mergeAttributes(HTMLAttributes)];
  },

  addNodeView() {
    return ReactNodeViewRenderer(TiptapMermaidComponent);
  },
});
``;
