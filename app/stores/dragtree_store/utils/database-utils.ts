import { TreeNode } from "@/app/types";
import {
  DragTreeNodeType,
  dragTreeTypeToTreeNodeType,
} from "@/app/(conv)/dragTree/[dragTreeId]/constants/node-types";

// Database drag tree with nodes - simplified to handle actual Prisma response
export type DatabaseDragTree = {
  tree_structure?: any; // JsonValue from Prisma, more flexible
  nodes?: Array<{
    id: string;
    label: string;
    node_type: DragTreeNodeType;
    is_interested_in: boolean;
    status?: string;
    [key: string]: any; // Allow additional properties from Prisma
  }>;
  [key: string]: any; // Allow additional properties from Prisma response
};

/**
 * Reconstruct TreeNode from database hierarchy format
 * Converts the flat hierarchy back to the TreeNode structure used by the frontend
 * Only includes ACTIVE nodes, filtering out INACTIVE ones
 */
export function reconstructTreeFromDatabase(
  dbData: DatabaseDragTree
): TreeNode | null {
  if (!dbData.tree_structure || !dbData.nodes) {
    console.warn(
      "🗂️ [DatabaseUtils] Invalid database tree data: missing tree_structure or nodes",
      {
        hasTreeStructure: !!dbData.tree_structure,
        hasNodes: !!dbData.nodes,
        nodeCount: dbData.nodes?.length || 0,
      }
    );
    return null;
  }

  const { root_id, hierarchy } = dbData.tree_structure;
  console.log("🗂️ [DatabaseUtils] Reconstructing tree with root_id:", root_id);

  // Create a map of node ID to node data for quick lookup
  // Only include ACTIVE nodes
  const nodeMap = new Map();
  dbData.nodes.forEach((node) => {
    // Only include ACTIVE nodes in the frontend tree
    if (node.status === "ACTIVE") {
      const isInterestedIn = Boolean(node.is_interested_in);
      // Debug: Log interest status for verification
      if (isInterestedIn) {
        console.log(
          `🌟 [DatabaseUtils] Node ${node.id} is interested: ${node.is_interested_in} -> ${isInterestedIn}`
        );
      }

      nodeMap.set(node.id, {
        id: node.id,
        label: node.label,
        type: dragTreeTypeToTreeNodeType(node.node_type),
        children: [],
        isInterestedIn,
      });
    }
  });

  console.log(
    "🗂️ [DatabaseUtils] Created node map with",
    nodeMap.size,
    "ACTIVE nodes (filtered from",
    dbData.nodes.length,
    "total nodes)"
  );

  // Build the tree structure recursively
  function buildTreeNode(nodeId: string): TreeNode | null {
    const nodeData = nodeMap.get(nodeId);
    if (!nodeData) {
      console.warn(
        `🗂️ [DatabaseUtils] Node ${nodeId} not found in ACTIVE nodes - skipping`
      );
      return null;
    }

    const treeNode: TreeNode = { ...nodeData };

    // Add children based on hierarchy, but only if they are ACTIVE
    const childIds = hierarchy[nodeId] || [];
    treeNode.children = childIds
      .map((childId: string) => buildTreeNode(childId))
      .filter((child: TreeNode | null): child is TreeNode => child !== null);

    return treeNode;
  }

  const result = buildTreeNode(root_id);
  console.log("🗂️ [DatabaseUtils] Final reconstructed tree:", result);
  return result;
}

/**
 * Convert TreeNode to database tree structure format
 * Generates the hierarchy object for database storage
 */
export function convertTreeToDbStructure(treeNode: TreeNode): {
  root_id: string;
  hierarchy: Record<string, string[]>;
} {
  const hierarchy: Record<string, string[]> = {};

  function buildHierarchy(node: TreeNode): void {
    // Add this node's children to hierarchy
    hierarchy[node.id] = node.children.map((child) => child.id);

    // Recursively process children
    node.children.forEach((child) => buildHierarchy(child));
  }

  buildHierarchy(treeNode);

  return {
    root_id: treeNode.id,
    hierarchy,
  };
}
