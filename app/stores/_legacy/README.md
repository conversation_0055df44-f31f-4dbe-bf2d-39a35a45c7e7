# Legacy Stores

This folder contains Zustand stores that are no longer part of the main application architecture but are kept for legacy route support.

## Files Moved Here

### Legacy System Stores

- `conversation_store.ts` - Managed conversation state in legacy system (minimal active usage)
- `issuetree_store.ts` - Managed issue tree state in legacy system
- `notebook_store.ts` - Managed notebook state in legacy system
- `panel_store.ts` - Managed panel state in legacy conversation system
- `subtree_store.ts` - Managed subtree state in legacy system

### Previously Legacy

- `current_user.ts` - Already in legacy folder (user state management)

## Current Usage

These stores are still used by:

- Legacy conversation components in `/app/(legacy)/_conversations/`
- Legacy issue tree system components
- Some bridge components (starter, editor metadata) with minimal usage
- Legacy hooks that support backwards compatibility

## Modern Alternatives

The current application uses these active stores:

- `batchResearchStore.ts` - Core drag tree batch research functionality
- `dragtree_store/` - Primary state management for modern drag tree system
- `navigation_store.ts` - Navigation state for drag tree interface
- `ui_store.ts` - UI state management for active features

## Architecture Transition

**Legacy Architecture (OLD):**

- Conversation-based UI with separate stores for conversations, issue trees, panels, notebooks, and subtrees
- Complex state management across multiple specialized stores
- Panel-based interface with contextual information display

**Modern Architecture (NEW):**

- DragTree-focused interface with unified state management
- Batch research with selection-based operations
- Simplified navigation and UI state management
- Screening system for initial problem analysis

## Usage Patterns

- **Legacy stores**: Used only in `/app/(legacy)/` folder and bridge components
- **Active stores**: Used in `/app/(conv)/dragTree/` and `/app/(conv)/screening/`

## Cleanup Strategy

These legacy stores can be safely removed once:

1. Legacy conversation routes (`/app/(legacy)/_conversations/`) are deprecated
2. Bridge components (starter, editor metadata) are refactored to use modern state
3. All legacy route handlers and components are removed
4. Remaining minimal usage is migrated to modern alternatives

## State Management Evolution

The app has evolved from complex multi-store architecture to a cleaner, more focused state management system aligned with the modern drag tree workflow.
