'use client'

import React from 'react'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'

type BillingToggleProps = {
  isYearly: boolean
  onToggle: (isYearly: boolean) => void
}

export const BillingToggle: React.FC<BillingToggleProps> = ({
  isYearly,
  onToggle,
}) => {
  return (
    <div className="flex items-center justify-center mb-8">
      <div className="flex items-center bg-gray-100 rounded-lg p-1">
        <button
          onClick={() => onToggle(false)}
          className={cn(
            'px-4 py-2 rounded-md text-sm font-medium transition-all duration-200',
            !isYearly
              ? 'bg-white text-gray-900 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          )}
        >
          Monthly
        </button>
        <button
          onClick={() => onToggle(true)}
          className={cn(
            'px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 flex items-center gap-2',
            isYearly
              ? 'bg-white text-gray-900 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          )}
        >
          Yearly
          <Badge
            variant="outline"
            className="text-green-600 border-green-200 text-xs"
          >
            Save 17%
          </Badge>
        </button>
      </div>
    </div>
  )
}
