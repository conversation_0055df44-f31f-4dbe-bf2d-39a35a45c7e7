import {
  generateDeterministicId,
  generateUniqueNodeId,
  generateDragTreeNodeId,
  HASH_LENGTHS,
} from '@/lib/id'

describe('ID generation utilities', () => {
  const treeId = 'tree_test123'

  it('deterministic IDs are stable for same inputs', () => {
    const a1 = generateDeterministicId('cat', treeId, 'Finance', 'CATEGORY')
    const a2 = generateDeterministicId('cat', treeId, 'Finance', 'CATEGORY')
    expect(a1).toEqual(a2)
  })

  it('unique node IDs differ for generic labels (format/length check)', () => {
    const u1 = generateUniqueNodeId('cat', treeId, 'New Category', 'CATEGORY')
    expect(u1.startsWith('cat_')).toBe(true)
    expect(u1.length).toBeGreaterThan(5)
  })

  it('dragTreeNodeId respects node type and hash length', () => {
    const idShort = generateDragTreeNodeId(
      treeId,
      'What is budget?',
      'QUESTION',
      HASH_LENGTHS.SHORT
    )
    const idLong = generateDragTreeNodeId(
      treeId,
      'What is budget?',
      'QUESTION',
      HASH_LENGTHS.LONG
    )
    expect(idShort.startsWith('que_')).toBe(true)
    expect(idLong.startsWith('que_')).toBe(true)
    expect(idLong.length).toBeGreaterThan(idShort.length)
  })
})

import {
  generateIdWithPrefix,
  generateDeterministicId as genDet2,
  generateUniqueNodeId as genUniq2,
  generateDragTreeId,
  generateDragTreeNodeId as genNode2,
  DragTreeIdPrefix,
  HASH_LENGTHS as HASH2,
} from '@/app/(conv)/dragTree/[dragTreeId]/utils/id-generation'

// Mocks are configured in jest.setup.js

describe('ID Generation Utilities', () => {
  beforeEach(() => {
    // Reset any global state or mocks
    jest.clearAllMocks()
  })

  describe('generateIdWithPrefix', () => {
    it('should generate IDs with correct prefix format', () => {
      const id = generateIdWithPrefix('test')
      expect(id).toMatch(/^test_/)
      expect(id.length).toBeGreaterThan(5) // At least prefix + underscore + some chars
    })

    it('should generate IDs with custom length parameter', () => {
      const id = generateIdWithPrefix('custom', 10)
      expect(id).toMatch(/^custom_/)
    })

    it('should handle different prefixes correctly', () => {
      const treeId = generateIdWithPrefix(DragTreeIdPrefix.TREE)
      const catId = generateIdWithPrefix(DragTreeIdPrefix.CATEGORY_NODE)
      const queId = generateIdWithPrefix(DragTreeIdPrefix.QUESTION_NODE)

      expect(treeId.startsWith('tree_')).toBe(true)
      expect(catId.startsWith('cat_')).toBe(true)
      expect(queId.startsWith('que_')).toBe(true)
    })

    it('should only contain valid characters in the random part', () => {
      const id = generateIdWithPrefix('alphanumeric')
      const randomPart = id.split('_')[1]

      // Should not contain invalid characters
      expect(randomPart).not.toMatch(/[-_~.!@#$%^&*()+=\[\]{}|\\:";'<>?,./]/)
    })
  })

  describe('generateDeterministicId', () => {
    it('should generate same ID for same inputs', () => {
      const id1 = genDet2('cat', 'tree_123', 'Financial Planning')
      const id2 = genDet2('cat', 'tree_123', 'Financial Planning')

      expect(id1).toBe(id2)
      expect(id1.startsWith('cat_')).toBe(true)
    })

    it('should include node type in hash when provided', () => {
      const id1 = genDet2('node', 'tree_123', 'Test Label', 'CATEGORY')
      const id2 = genDet2('node', 'tree_123', 'Test Label', 'QUESTION')

      // Different node types should produce different IDs
      expect(id1).not.toBe(id2)
    })

    it('should respect custom hash length', () => {
      const shortId = genDet2(
        'test',
        'tree_123',
        'Test',
        undefined,
        HASH2.SHORT
      )
      const longId = genDet2('test', 'tree_123', 'Test', undefined, HASH2.LONG)

      const shortHash = shortId.split('_')[1]
      const longHash = longId.split('_')[1]

      expect(shortHash.length).toBe(HASH2.SHORT)
      expect(longHash.length).toBe(HASH2.LONG)
    })

    it('should trim whitespace from labels', () => {
      const id1 = genDet2('cat', 'tree_123', '  Financial Planning  ')
      const id2 = genDet2('cat', 'tree_123', 'Financial Planning')

      expect(id1).toBe(id2)
    })

    it('should handle empty node type gracefully', () => {
      const id1 = genDet2('cat', 'tree_123', 'Test Label')
      const id2 = genDet2('cat', 'tree_123', 'Test Label', '')

      expect(id1).toBe(id2)
    })
  })

  describe('generateUniqueNodeId', () => {
    it('should use deterministic approach for named labels', () => {
      const id1 = genUniq2('cat', 'tree_123', 'Budget Planning', 'CATEGORY')
      const id2 = genUniq2('cat', 'tree_123', 'Budget Planning', 'CATEGORY')

      expect(id1).toBe(id2) // Should be deterministic for named labels
    })

    it('should handle different hash lengths', () => {
      const shortId = genUniq2(
        'cat',
        'tree_123',
        'Custom Label',
        'CATEGORY',
        HASH2.SHORT
      )
      const hash = shortId.split('_')[1]

      expect(hash.length).toBe(HASH2.SHORT)
    })
  })

  describe('generateDragTreeId', () => {
    it('should generate drag tree IDs with correct prefix', () => {
      const id = generateDragTreeId()
      expect(id).toMatch(/^tree_/)
    })
  })

  describe('generateDragTreeNodeId', () => {
    it('should generate category node IDs with correct prefix', () => {
      const id = genNode2('tree_123', 'Financial Planning', 'CATEGORY')
      expect(id.startsWith('cat_')).toBe(true)
    })

    it('should generate question node IDs with correct prefix', () => {
      const id = genNode2('tree_123', 'What is your budget?', 'QUESTION')
      expect(id.startsWith('que_')).toBe(true)
    })

    it('should be deterministic for normal labels', () => {
      const id1 = genNode2('tree_123', 'Budget Planning', 'CATEGORY')
      const id2 = genNode2('tree_123', 'Budget Planning', 'CATEGORY')

      expect(id1).toBe(id2)
    })

    it('should respect custom hash length', () => {
      const shortId = genNode2(
        'tree_123',
        'Test Label',
        'CATEGORY',
        HASH2.SHORT
      )
      const longId = genNode2('tree_123', 'Test Label', 'CATEGORY', HASH2.LONG)

      const shortHash = shortId.split('_')[1]
      const longHash = longId.split('_')[1]

      expect(shortHash.length).toBe(HASH2.SHORT)
      expect(longHash.length).toBe(HASH2.LONG)
    })
  })

  describe('Hash Length Constants', () => {
    it('should have expected hash length values', () => {
      expect(HASH_LENGTHS.SHORT).toBe(8)
      expect(HASH_LENGTHS.MEDIUM).toBe(12)
      expect(HASH_LENGTHS.LONG).toBe(16)
      expect(HASH_LENGTHS.FULL).toBe(32)
    })
  })

  describe('DragTreeIdPrefix Enum', () => {
    it('should have expected prefix values', () => {
      expect(DragTreeIdPrefix.TREE).toBe('tree')
      expect(DragTreeIdPrefix.CATEGORY_NODE).toBe('cat')
      expect(DragTreeIdPrefix.QUESTION_NODE).toBe('que')
      expect(DragTreeIdPrefix.NODE_CONTENT).toBe('cont')
    })
  })

  describe('Edge Cases', () => {
    it('should handle empty strings gracefully', () => {
      expect(() => {
        generateDeterministicId('test', 'tree_123', '')
      }).not.toThrow()
    })

    it('should handle very long labels', () => {
      const longLabel = 'A'.repeat(1000)
      const id = generateDeterministicId('test', 'tree_123', longLabel)

      expect(id.startsWith('test_')).toBe(true)
      expect(id.length).toBeLessThan(100) // Should be reasonably short despite long input
    })

    it('should handle special characters in labels', () => {
      const specialLabel = 'Test & Special chars: @#$%^&*()!'
      const id = generateDeterministicId('test', 'tree_123', specialLabel)

      expect(id.startsWith('test_')).toBe(true)
      // Hash part should only contain alphanumeric characters
      const hashPart = id.split('_')[1]
      expect(hashPart).toMatch(/^[a-z0-9]+$/)
    })

    it('should handle unicode characters in labels', () => {
      const unicodeLabel = 'Test 测试 🌟 émojis'
      const id = generateDeterministicId('test', 'tree_123', unicodeLabel)

      expect(id.startsWith('test_')).toBe(true)
    })

    it('should generate valid IDs for different tree IDs', () => {
      const id1 = generateDeterministicId('cat', 'tree_abc', 'Same Label')
      const id2 = generateDeterministicId('cat', 'tree_xyz', 'Same Label')

      expect(id1).not.toBe(id2) // Different tree IDs should produce different hashes
      expect(id1.startsWith('cat_')).toBe(true)
      expect(id2.startsWith('cat_')).toBe(true)
    })

    it('should handle null and undefined inputs gracefully', () => {
      expect(() => {
        generateDeterministicId('test', 'tree_123', 'label', undefined)
      }).not.toThrow()
    })
  })
})
